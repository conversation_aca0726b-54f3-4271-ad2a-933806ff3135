"""
Install Yahoo Finance Integration
Sets up the necessary dependencies and configuration for Yahoo Finance data source
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def install_dependencies():
    """Install required dependencies for Yahoo Finance integration."""
    print("🚀 Installing Yahoo Finance Integration Dependencies...")
    
    # Core dependencies for Yahoo Finance
    dependencies = [
        "yfinance>=0.2.18",
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "aiohttp>=3.8.0",
        "pydantic>=2.0.0",
        "pydantic-settings>=2.0.0"
    ]
    
    success = True
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            success = False
    
    return success

def create_config_updates():
    """Create configuration updates for Yahoo Finance."""
    print("⚙️ Creating configuration updates...")
    
    config_update = """
# Yahoo Finance Data Source Configuration
data_sources:
  yahoo_finance:
    enabled: true
    rate_limit:
      requests_per_minute: 30
      burst_protection: true
      market_hours_only: true
    
    # IP Protection settings
    ip_protection:
      human_behavior: true
      random_delays: true
      weekend_enabled: false
      night_mode: true
      base_delay: 2.0
      max_delay: 10.0
    
    # Caching settings
    cache:
      quote_ttl: 5      # seconds
      bars_ttl: 300     # seconds
      info_ttl: 3600    # seconds
    
    # Supported intervals
    intervals:
      - "1m"
      - "5m" 
      - "15m"
      - "30m"
      - "1h"
      - "1d"
      - "1wk"
      - "1mo"
    
    # Supported periods
    periods:
      - "1d"
      - "5d"
      - "1mo"
      - "3mo"
      - "6mo"
      - "1y"
      - "2y"
      - "5y"
      - "10y"
      - "ytd"
      - "max"

# Update trading configuration to use Yahoo Finance
trading:
  data_source: "yahoo_finance"  # Switch from webull to yahoo_finance
  
  # Watchlist for Yahoo Finance (these symbols work well)
  watchlist:
    - "AAPL"   # Apple Inc.
    - "MSFT"   # Microsoft Corporation
    - "GOOGL"  # Alphabet Inc.
    - "TSLA"   # Tesla Inc.
    - "NVDA"   # NVIDIA Corporation
    - "AMZN"   # Amazon.com Inc.
    - "META"   # Meta Platforms Inc.
    - "NFLX"   # Netflix Inc.
    - "AMD"    # Advanced Micro Devices
    - "INTC"   # Intel Corporation
"""
    
    # Write to a new config file
    config_path = Path("config/yahoo_finance.yaml")
    config_path.parent.mkdir(exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_update)
    
    print(f"✅ Configuration written to {config_path}")
    return True

def create_example_usage():
    """Create example usage script."""
    print("📝 Creating example usage script...")
    
    example_script = '''"""
Example: Using Yahoo Finance Data Source
Run this script to test the Yahoo Finance integration
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.trading_bot.core.config import settings
from src.trading_bot.data.yahoo_data_source import YahooDataSource

async def test_yahoo_integration():
    """Test Yahoo Finance integration."""
    print("🧪 Testing Yahoo Finance Integration...")
    
    # Initialize data source
    data_source = YahooDataSource(settings)
    
    # Test initialization
    if not await data_source.initialize():
        print("❌ Failed to initialize Yahoo Finance data source")
        return False
    
    print("✅ Yahoo Finance data source initialized")
    
    # Test getting a quote
    print("📊 Testing quote retrieval...")
    quote = await data_source.get_realtime_quote("AAPL")
    
    if quote:
        print(f"✅ AAPL Quote: ${quote.price}")
        print(f"   Volume: {quote.volume:,}")
        print(f"   Timestamp: {quote.timestamp}")
    else:
        print("❌ Failed to get quote")
        return False
    
    # Test getting historical data
    print("📈 Testing historical data retrieval...")
    bars = await data_source.get_historical_bars("AAPL", interval="1d", period="1mo")
    
    if bars:
        print(f"✅ Retrieved {len(bars)} historical bars for AAPL")
        latest_bar = bars[-1]
        print(f"   Latest: Open=${latest_bar.open}, Close=${latest_bar.close}")
    else:
        print("❌ Failed to get historical data")
        return False
    
    # Test multiple quotes
    print("📊 Testing multiple quotes...")
    symbols = ["AAPL", "MSFT", "GOOGL"]
    quotes = await data_source.get_multiple_quotes(symbols)
    
    if quotes:
        print(f"✅ Retrieved quotes for {len(quotes)} symbols:")
        for symbol, quote in quotes.items():
            print(f"   {symbol}: ${quote.price}")
    else:
        print("❌ Failed to get multiple quotes")
        return False
    
    # Test IP protection stats
    stats = data_source.ip_protection.get_protection_stats()
    print(f"🛡️ IP Protection Stats:")
    print(f"   Total requests: {stats['total_requests']}")
    print(f"   Requests last minute: {stats['requests_last_minute']}")
    print(f"   Safe time: {stats['safe_time']}")
    
    print("🎉 All tests passed! Yahoo Finance integration is working.")
    return True

if __name__ == "__main__":
    success = asyncio.run(test_yahoo_integration())
    if not success:
        sys.exit(1)
'''
    
    example_path = Path("examples/test_yahoo_integration.py")
    example_path.parent.mkdir(exist_ok=True)
    
    with open(example_path, 'w', encoding='utf-8') as f:
        f.write(example_script)
    
    print(f"✅ Example script written to {example_path}")
    return True

def main():
    """Main installation function."""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║            Yahoo Finance Integration Installer               ║
    ║                                                              ║
    ║  🚀 Installing reliable market data source                   ║
    ║  📊 Replacing browser automation with API calls             ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    success = True
    
    # Install dependencies
    if not install_dependencies():
        success = False
    
    # Create configuration
    if not create_config_updates():
        success = False
    
    # Create example usage
    if not create_example_usage():
        success = False
    
    if success:
        print("""
        ✅ Yahoo Finance Integration Installed Successfully!
        
        Next steps:
        1. Run the example: python examples/test_yahoo_integration.py
        2. Train models: python scripts/train_models_yahoo.py
        3. Start the bot: python src/trading_bot/main_yahoo.py
        
        Configuration files created:
        - config/yahoo_finance.yaml
        - examples/test_yahoo_integration.py
        """)
    else:
        print("❌ Installation failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
