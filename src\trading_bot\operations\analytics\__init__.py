"""
Analytics Module

Provides advanced analytics and optimization:
- Deep trade analysis and attribution
- Market regime detection and adaptation
- Competitive intelligence
- Profitability optimization
"""

from .trade_analytics import TradeAnalytics
from .market_regime import MarketRegimeDetector
from .competitor_analysis import CompetitorAnalysis
from .profitability_optimizer import ProfitabilityOptimizer

__all__ = [
    'TradeAnalytics',
    'MarketRegimeDetector',
    'CompetitorAnalysis',
    'ProfitabilityOptimizer'
]
