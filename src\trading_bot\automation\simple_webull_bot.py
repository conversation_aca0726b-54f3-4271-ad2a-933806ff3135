"""
Simple Webull Browser Bot for Testing

This is a simplified version that doesn't require all the production components.
Use this for initial testing and development.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pickle
import os

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import undetected_chromedriver as uc
import pandas as pd
import numpy as np

# Simple logger setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderType(Enum):
    """Order types supported by Webull"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"

class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"

@dataclass
class MarketData:
    """Market data captured from browser"""
    symbol: str
    price: float
    bid: float
    ask: float
    volume: int
    change_percent: float
    timestamp: datetime
    
@dataclass
class Position:
    """Position information"""
    symbol: str
    quantity: int
    avg_cost: float
    current_price: float
    pnl: float
    pnl_percent: float

@dataclass
class Order:
    """Order information"""
    order_id: str
    symbol: str
    side: OrderSide
    type: OrderType
    quantity: int
    price: Optional[float]
    status: str
    timestamp: datetime

class SimpleWebullBrowserBot:
    """
    Simple browser-based Webull trading bot for testing.
    
    This is a simplified version without production components.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.driver = None
        self.wait = None
        self.logged_in = False
        
        # URLs
        self.base_url = "https://app.webull.com"
        self.login_url = f"{self.base_url}/login"
        self.paper_trading_url = f"{self.base_url}/paper"
        
        # Data storage
        self.market_data_cache = {}
        self.positions = {}
        self.orders = []
        
        # Browser automation settings
        self.screenshot_dir = config.get('screenshot_dir', './screenshots')
        self.cookies_file = config.get('cookies_file', './webull_cookies.pkl')
        
        # Create directories
        os.makedirs(self.screenshot_dir, exist_ok=True)
        
    async def initialize(self):
        """Initialize the browser driver"""
        try:
            logger.info("Initializing browser driver...")
            
            # Configure Chrome options for stealth mode
            chrome_options = uc.ChromeOptions()
            
            # Add stealth settings
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Optional: Run in headless mode (but some sites detect this)
            if self.config.get('headless', False):
                chrome_options.add_argument('--headless')
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--no-sandbox')
            
            # Set window size
            chrome_options.add_argument('--window-size=1920,1080')
            
            # Initialize undetected Chrome driver
            self.driver = uc.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            # Set page load timeout
            self.driver.set_page_load_timeout(30)
            
            logger.info("Browser driver initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def login(self, username: str, password: str, mfa_code: Optional[str] = None):
        """Login to Webull through the browser"""
        try:
            logger.info("Starting Webull login process...")
            
            # Navigate to login page
            self.driver.get(self.login_url)
            await asyncio.sleep(3)  # Wait for page load
            
            # Check if we have saved cookies
            if os.path.exists(self.cookies_file) and self._load_cookies():
                logger.info("Loaded saved cookies, checking if logged in...")
                self.driver.refresh()
                await asyncio.sleep(3)
                
                if await self._check_logged_in():
                    logger.info("Successfully logged in using saved cookies")
                    self.logged_in = True
                    return
            
            # Find and fill login form
            logger.info("Filling login form...")
            
            # Wait for email input
            email_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='email'], input[placeholder*='Email']"))
            )
            email_input.clear()
            email_input.send_keys(username)
            await asyncio.sleep(1)
            
            # Find and fill password
            password_input = self.driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            password_input.clear()
            password_input.send_keys(password)
            await asyncio.sleep(1)
            
            # Take screenshot before login
            self._take_screenshot("before_login")
            
            # Click login button
            login_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit'], button:contains('Log In')")
            login_button.click()
            
            # Wait for login to process
            await asyncio.sleep(5)
            
            # Handle MFA if needed
            if mfa_code or await self._check_mfa_required():
                await self._handle_mfa(mfa_code)
            
            # Verify login success
            if await self._check_logged_in():
                logger.info("Login successful!")
                self.logged_in = True
                self._save_cookies()
                
                # Switch to paper trading if configured
                if self.config.get('paper_trading', True):
                    await self._switch_to_paper_trading()
            else:
                raise Exception("Login failed - could not verify logged in status")
                
        except Exception as e:
            logger.error(f"Login failed: {e}")
            self._take_screenshot("login_error")
            raise
    
    async def _check_logged_in(self) -> bool:
        """Check if we're logged in by looking for account elements"""
        try:
            # Look for elements that only appear when logged in
            logged_in_indicators = [
                "//div[contains(@class, 'account')]",
                "//button[contains(text(), 'Trade')]",
                "//div[contains(@class, 'portfolio')]",
                "//span[contains(text(), 'Positions')]"
            ]
            
            for indicator in logged_in_indicators:
                try:
                    self.driver.find_element(By.XPATH, indicator)
                    return True
                except NoSuchElementException:
                    continue
                    
            return False
            
        except Exception as e:
            logger.error(f"Error checking login status: {e}")
            return False
    
    async def _check_mfa_required(self) -> bool:
        """Check if MFA is required"""
        try:
            # Look for MFA input field
            mfa_elements = [
                "//input[contains(@placeholder, 'verification code')]",
                "//input[contains(@placeholder, 'MFA')]",
                "//div[contains(text(), 'Two-Factor')]"
            ]
            
            for element in mfa_elements:
                try:
                    self.driver.find_element(By.XPATH, element)
                    return True
                except NoSuchElementException:
                    continue
                    
            return False
            
        except Exception:
            return False
    
    async def _handle_mfa(self, mfa_code: Optional[str] = None):
        """Handle MFA verification"""
        try:
            logger.info("MFA required, handling verification...")
            
            if not mfa_code:
                # Wait for user to input MFA code manually
                logger.info("Please enter MFA code in the browser...")
                input("Press Enter after entering MFA code...")
            else:
                # Find MFA input and enter code
                mfa_input = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, "//input[contains(@placeholder, 'code')]"))
                )
                mfa_input.clear()
                mfa_input.send_keys(mfa_code)
                
                # Submit MFA
                submit_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Verify')]")
                submit_button.click()
                
            await asyncio.sleep(3)
            
        except Exception as e:
            logger.error(f"MFA handling failed: {e}")
            raise
    
    async def _switch_to_paper_trading(self):
        """Switch to paper trading mode"""
        try:
            logger.info("Switching to paper trading mode...")
            
            # Navigate to paper trading
            self.driver.get(self.paper_trading_url)
            await asyncio.sleep(3)
            
            logger.info("Switched to paper trading mode")
            
        except Exception as e:
            logger.error(f"Failed to switch to paper trading: {e}")
    
    def _save_cookies(self):
        """Save cookies for future sessions"""
        try:
            cookies = self.driver.get_cookies()
            with open(self.cookies_file, 'wb') as f:
                pickle.dump(cookies, f)
            logger.info("Cookies saved successfully")
        except Exception as e:
            logger.error(f"Failed to save cookies: {e}")
    
    def _load_cookies(self) -> bool:
        """Load saved cookies"""
        try:
            with open(self.cookies_file, 'rb') as f:
                cookies = pickle.load(f)
                
            for cookie in cookies:
                self.driver.add_cookie(cookie)
                
            logger.info("Cookies loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load cookies: {e}")
            return False
    
    def _take_screenshot(self, name: str):
        """Take a screenshot for debugging"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.screenshot_dir}/{name}_{timestamp}.png"
            self.driver.save_screenshot(filename)
            logger.info(f"Screenshot saved: {filename}")
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
    
    async def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get real-time market data for a symbol"""
        try:
            logger.info(f"Getting market data for {symbol}")
            
            # Search for the symbol
            search_box = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder*='Search']"))
            )
            search_box.clear()
            search_box.send_keys(symbol)
            search_box.send_keys(Keys.RETURN)
            
            await asyncio.sleep(2)
            
            # Click on the first search result
            first_result = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "div[class*='search-result']:first-child"))
            )
            first_result.click()
            
            await asyncio.sleep(3)
            
            # Extract market data from the page
            price = self._extract_price()
            bid_ask = self._extract_bid_ask()
            volume = self._extract_volume()
            change = self._extract_change_percent()
            
            market_data = MarketData(
                symbol=symbol,
                price=price,
                bid=bid_ask[0],
                ask=bid_ask[1],
                volume=volume,
                change_percent=change,
                timestamp=datetime.now()
            )
            
            # Cache the data
            self.market_data_cache[symbol] = market_data
            
            return market_data
            
        except Exception as e:
            logger.error(f"Failed to get market data for {symbol}: {e}")
            self._take_screenshot(f"market_data_error_{symbol}")
            return None
    
    def _extract_price(self) -> float:
        """Extract current price from the page"""
        try:
            price_selectors = [
                "span[class*='price-main']",
                "div[class*='current-price']",
                "span[class*='last-price']"
            ]
            
            for selector in price_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    price_text = element.text.strip()
                    return float(price_text.replace('$', '').replace(',', ''))
                except:
                    continue
                    
            return 0.0
            
        except Exception as e:
            logger.error(f"Failed to extract price: {e}")
            return 0.0
    
    def _extract_bid_ask(self) -> Tuple[float, float]:
        """Extract bid and ask prices"""
        try:
            return (0.0, 0.0)  # Simplified for testing
        except Exception:
            return (0.0, 0.0)
    
    def _extract_volume(self) -> int:
        """Extract volume from the page"""
        try:
            return 0  # Simplified for testing
        except Exception:
            return 0
    
    def _extract_change_percent(self) -> float:
        """Extract price change percentage"""
        try:
            return 0.0  # Simplified for testing
        except Exception:
            return 0.0
    
    async def get_positions(self) -> Dict[str, Position]:
        """Get current positions"""
        try:
            logger.info("Getting current positions...")
            return {}  # Simplified for testing
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
            return {}
    
    async def place_order(self, 
                         symbol: str, 
                         side: OrderSide, 
                         quantity: int,
                         order_type: OrderType = OrderType.MARKET,
                         price: Optional[float] = None) -> Optional[str]:
        """Place an order through the browser"""
        try:
            logger.info(f"Placing {side.value} order for {quantity} shares of {symbol}")
            # Simplified for testing - just return a mock order ID
            return f"test_order_{int(time.time())}"
        except Exception as e:
            logger.error(f"Failed to place order: {e}")
            return None
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
            logger.info("Browser driver closed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    # Testing methods
    
    async def test_navigation(self):
        """Test basic navigation functionality."""
        logger.info("Testing navigation...")
        
        try:
            self.driver.get("https://app.webull.com")
            await asyncio.sleep(3)
            
            # Check if page loaded
            if "webull" in self.driver.title.lower():
                logger.info("Navigation test passed")
            else:
                raise Exception("Page did not load correctly")
                
        except Exception as e:
            logger.error(f"Navigation test failed: {e}")
            raise
    
    async def test_market_data_extraction(self):
        """Test market data extraction without login."""
        logger.info("Testing market data extraction...")
        
        try:
            # Navigate to a public stock page
            self.driver.get("https://app.webull.com/stocks/AAPL")
            await asyncio.sleep(5)
            
            # Try to extract price information
            price = self._extract_price()
            
            if price > 0:
                logger.info(f"Market data extraction test passed: AAPL price = ${price}")
            else:
                logger.warning("Could not extract price, but test structure is working")
                
        except Exception as e:
            logger.error(f"Market data extraction test failed: {e}")
            raise
    
    async def test_order_form(self):
        """Test order form interaction (without placing real orders)."""
        logger.info("Testing order form interaction...")
        
        try:
            # This would require login, so we'll just test the structure
            logger.info("Order form test structure ready (requires login for full test)")
            
        except Exception as e:
            logger.error(f"Order form test failed: {e}")
            raise
