"""
Extended Testing Framework

This module provides comprehensive testing capabilities for production trading systems:
- Latency spike simulation and testing
- Data corruption detection and handling
- Partial fill scenario testing
- Clock drift and time synchronization testing
- API version compatibility testing
- Stress testing under extreme conditions
- Chaos engineering for resilience testing
"""

import asyncio
import random
import time
import json
import hashlib
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
from unittest.mock import Mock, patch
import aiohttp
import pytest

from ..core.logger import get_logger
from ..core.config import settings
from ..data.models import Order, Trade, Quote

logger = get_logger(__name__)


class TestScenario(Enum):
    """Types of test scenarios."""
    LATENCY_SPIKE = "latency_spike"
    DATA_CORRUPTION = "data_corruption"
    PARTIAL_FILLS = "partial_fills"
    CLOCK_DRIFT = "clock_drift"
    API_VERSION_CHANGE = "api_version_change"
    NETWORK_PARTITION = "network_partition"
    MEMORY_PRESSURE = "memory_pressure"
    CPU_SATURATION = "cpu_saturation"
    DISK_FULL = "disk_full"
    DATABASE_FAILURE = "database_failure"


class TestSeverity(Enum):
    """Test severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class TestResult:
    """Test execution result."""
    test_id: str
    scenario: TestScenario
    severity: TestSeverity
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    passed: bool
    error_message: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None
    recovery_time_seconds: Optional[float] = None


@dataclass
class LatencyTestConfig:
    """Configuration for latency testing."""
    baseline_latency_ms: float
    spike_latency_ms: float
    spike_duration_seconds: float
    spike_frequency: float  # spikes per minute
    acceptable_degradation_percent: float


@dataclass
class DataCorruptionConfig:
    """Configuration for data corruption testing."""
    corruption_rate: float  # 0.0 to 1.0
    corruption_types: List[str]  # ['bit_flip', 'truncation', 'injection']
    detection_timeout_seconds: float
    recovery_timeout_seconds: float


class ExtendedTestFramework:
    """Comprehensive testing framework for trading systems."""
    
    def __init__(self):
        self.test_results: List[TestResult] = []
        self.active_tests: Dict[str, asyncio.Task] = {}
        self.chaos_mode = False
        
        # Test configurations
        self.latency_config = LatencyTestConfig(
            baseline_latency_ms=10.0,
            spike_latency_ms=500.0,
            spike_duration_seconds=5.0,
            spike_frequency=2.0,  # 2 spikes per minute
            acceptable_degradation_percent=20.0
        )
        
        self.corruption_config = DataCorruptionConfig(
            corruption_rate=0.01,  # 1% corruption rate
            corruption_types=['bit_flip', 'truncation', 'injection'],
            detection_timeout_seconds=30.0,
            recovery_timeout_seconds=60.0
        )
        
        # Mock objects for testing
        self.mock_broker = None
        self.mock_data_feed = None
        self.original_functions = {}
        
        # Monitoring
        self.is_testing = False
        self.test_tasks: List[asyncio.Task] = []
    
    async def start_testing(self, scenarios: List[TestScenario] = None):
        """Start extended testing framework."""
        if self.is_testing:
            logger.warning("Testing framework already running")
            return
        
        self.is_testing = True
        scenarios = scenarios or list(TestScenario)
        
        logger.info(f"Starting extended testing framework with {len(scenarios)} scenarios...")
        
        # Start test scenarios
        self.test_tasks = []
        for scenario in scenarios:
            task = asyncio.create_task(self._run_test_scenario(scenario))
            self.test_tasks.append(task)
        
        logger.info("Extended testing framework started")
        
        try:
            await asyncio.gather(*self.test_tasks)
        except Exception as e:
            logger.error(f"Testing framework error: {e}")
        finally:
            self.is_testing = False
    
    async def stop_testing(self):
        """Stop extended testing framework."""
        self.is_testing = False
        
        # Cancel active tests
        for test_id, task in self.active_tests.items():
            if not task.done():
                logger.info(f"Cancelling test {test_id}")
                task.cancel()
        
        # Cancel test tasks
        for task in self.test_tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete
        all_tasks = list(self.active_tests.values()) + self.test_tasks
        if all_tasks:
            await asyncio.gather(*all_tasks, return_exceptions=True)
        
        self.active_tests.clear()
        self.test_tasks.clear()
        
        # Restore original functions
        await self._restore_original_functions()
        
        logger.info("Extended testing framework stopped")
    
    async def _run_test_scenario(self, scenario: TestScenario):
        """Run a specific test scenario."""
        while self.is_testing:
            try:
                test_id = f"{scenario.value}_{int(time.time())}"
                
                if scenario == TestScenario.LATENCY_SPIKE:
                    await self._test_latency_spikes(test_id)
                elif scenario == TestScenario.DATA_CORRUPTION:
                    await self._test_data_corruption(test_id)
                elif scenario == TestScenario.PARTIAL_FILLS:
                    await self._test_partial_fills(test_id)
                elif scenario == TestScenario.CLOCK_DRIFT:
                    await self._test_clock_drift(test_id)
                elif scenario == TestScenario.API_VERSION_CHANGE:
                    await self._test_api_version_changes(test_id)
                elif scenario == TestScenario.NETWORK_PARTITION:
                    await self._test_network_partition(test_id)
                elif scenario == TestScenario.MEMORY_PRESSURE:
                    await self._test_memory_pressure(test_id)
                elif scenario == TestScenario.CPU_SATURATION:
                    await self._test_cpu_saturation(test_id)
                elif scenario == TestScenario.DATABASE_FAILURE:
                    await self._test_database_failure(test_id)
                
                # Wait before next test cycle
                await asyncio.sleep(300)  # 5 minutes between test cycles
                
            except Exception as e:
                logger.error(f"Error in test scenario {scenario.value}: {e}")
                await asyncio.sleep(60)
    
    async def _test_latency_spikes(self, test_id: str):
        """Test system behavior under latency spikes."""
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"Starting latency spike test {test_id}")
            
            # Inject latency into network calls
            original_sleep = asyncio.sleep
            
            async def delayed_sleep(delay):
                # Add random latency spikes
                if random.random() < (self.latency_config.spike_frequency / 60):
                    spike_delay = self.latency_config.spike_latency_ms / 1000
                    logger.warning(f"Injecting latency spike: {spike_delay:.3f}s")
                    await original_sleep(spike_delay)
                
                await original_sleep(delay)
            
            # Patch asyncio.sleep
            with patch('asyncio.sleep', delayed_sleep):
                # Run trading operations for test duration
                test_duration = 60  # 1 minute test
                end_test_time = time.time() + test_duration
                
                latencies = []
                while time.time() < end_test_time:
                    # Measure operation latency
                    op_start = time.time()
                    await self._simulate_trading_operation()
                    op_end = time.time()
                    
                    latency_ms = (op_end - op_start) * 1000
                    latencies.append(latency_ms)
                    
                    await asyncio.sleep(1)
                
                # Analyze results
                avg_latency = np.mean(latencies)
                max_latency = np.max(latencies)
                p95_latency = np.percentile(latencies, 95)
                
                # Check if performance is acceptable
                baseline = self.latency_config.baseline_latency_ms
                acceptable_degradation = baseline * (1 + self.latency_config.acceptable_degradation_percent / 100)
                
                passed = p95_latency <= acceptable_degradation
                
                end_time = datetime.utcnow()
                
                result = TestResult(
                    test_id=test_id,
                    scenario=TestScenario.LATENCY_SPIKE,
                    severity=TestSeverity.HIGH,
                    start_time=start_time,
                    end_time=end_time,
                    duration_seconds=(end_time - start_time).total_seconds(),
                    passed=passed,
                    metrics={
                        'avg_latency_ms': avg_latency,
                        'max_latency_ms': max_latency,
                        'p95_latency_ms': p95_latency,
                        'baseline_latency_ms': baseline,
                        'acceptable_latency_ms': acceptable_degradation,
                        'total_operations': len(latencies)
                    }
                )
                
                self.test_results.append(result)
                
                if passed:
                    logger.info(f"Latency test {test_id} PASSED: P95 latency {p95_latency:.1f}ms <= {acceptable_degradation:.1f}ms")
                else:
                    logger.error(f"Latency test {test_id} FAILED: P95 latency {p95_latency:.1f}ms > {acceptable_degradation:.1f}ms")
        
        except Exception as e:
            end_time = datetime.utcnow()
            result = TestResult(
                test_id=test_id,
                scenario=TestScenario.LATENCY_SPIKE,
                severity=TestSeverity.HIGH,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
                passed=False,
                error_message=str(e)
            )
            self.test_results.append(result)
            logger.error(f"Latency test {test_id} ERROR: {e}")
    
    async def _test_data_corruption(self, test_id: str):
        """Test data corruption detection and recovery."""
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"Starting data corruption test {test_id}")
            
            # Create test data
            test_data = {
                'orders': [
                    {'id': f'order_{i}', 'symbol': 'AAPL', 'quantity': 100, 'price': 150.0 + i}
                    for i in range(100)
                ],
                'trades': [
                    {'id': f'trade_{i}', 'symbol': 'AAPL', 'quantity': 50, 'price': 150.0 + i}
                    for i in range(50)
                ]
            }
            
            # Calculate original checksum
            original_checksum = self._calculate_checksum(test_data)
            
            # Inject corruption
            corrupted_data = self._inject_corruption(test_data.copy())
            corrupted_checksum = self._calculate_checksum(corrupted_data)
            
            # Test corruption detection
            corruption_detected = original_checksum != corrupted_checksum
            
            if corruption_detected:
                logger.info(f"Data corruption successfully detected in test {test_id}")
                
                # Test recovery
                recovery_start = time.time()
                recovered_data = await self._simulate_data_recovery(test_data)
                recovery_end = time.time()
                
                recovery_time = recovery_end - recovery_start
                recovery_checksum = self._calculate_checksum(recovered_data)
                
                recovery_successful = recovery_checksum == original_checksum
                
                passed = corruption_detected and recovery_successful
                
                end_time = datetime.utcnow()
                
                result = TestResult(
                    test_id=test_id,
                    scenario=TestScenario.DATA_CORRUPTION,
                    severity=TestSeverity.CRITICAL,
                    start_time=start_time,
                    end_time=end_time,
                    duration_seconds=(end_time - start_time).total_seconds(),
                    passed=passed,
                    recovery_time_seconds=recovery_time,
                    metrics={
                        'corruption_detected': corruption_detected,
                        'recovery_successful': recovery_successful,
                        'original_checksum': original_checksum,
                        'corrupted_checksum': corrupted_checksum,
                        'recovered_checksum': recovery_checksum,
                        'recovery_time_seconds': recovery_time
                    }
                )
                
                self.test_results.append(result)
                
                if passed:
                    logger.info(f"Data corruption test {test_id} PASSED: Detection and recovery successful")
                else:
                    logger.error(f"Data corruption test {test_id} FAILED: Recovery unsuccessful")
            else:
                logger.error(f"Data corruption test {test_id} FAILED: Corruption not detected")
                
                end_time = datetime.utcnow()
                result = TestResult(
                    test_id=test_id,
                    scenario=TestScenario.DATA_CORRUPTION,
                    severity=TestSeverity.CRITICAL,
                    start_time=start_time,
                    end_time=end_time,
                    duration_seconds=(end_time - start_time).total_seconds(),
                    passed=False,
                    error_message="Corruption not detected"
                )
                self.test_results.append(result)
        
        except Exception as e:
            end_time = datetime.utcnow()
            result = TestResult(
                test_id=test_id,
                scenario=TestScenario.DATA_CORRUPTION,
                severity=TestSeverity.CRITICAL,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
                passed=False,
                error_message=str(e)
            )
            self.test_results.append(result)
            logger.error(f"Data corruption test {test_id} ERROR: {e}")
    
    async def _test_partial_fills(self, test_id: str):
        """Test partial fill handling."""
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"Starting partial fills test {test_id}")
            
            # Simulate orders with partial fills
            test_orders = [
                {'id': f'order_{i}', 'symbol': 'AAPL', 'quantity': 1000, 'filled': 0}
                for i in range(10)
            ]
            
            partial_fill_scenarios = [
                {'fill_ratio': 0.1, 'expected_behavior': 'continue_working'},
                {'fill_ratio': 0.5, 'expected_behavior': 'continue_working'},
                {'fill_ratio': 0.9, 'expected_behavior': 'continue_working'},
                {'fill_ratio': 1.0, 'expected_behavior': 'complete'}
            ]
            
            successful_scenarios = 0
            
            for i, scenario in enumerate(partial_fill_scenarios):
                order = test_orders[i]
                fill_quantity = int(order['quantity'] * scenario['fill_ratio'])
                
                # Simulate partial fill
                fill_result = await self._simulate_partial_fill(order, fill_quantity)
                
                # Check if system handled partial fill correctly
                if self._validate_partial_fill_handling(fill_result, scenario):
                    successful_scenarios += 1
                    logger.info(f"Partial fill scenario {i+1} handled correctly")
                else:
                    logger.error(f"Partial fill scenario {i+1} handled incorrectly")
            
            passed = successful_scenarios == len(partial_fill_scenarios)
            
            end_time = datetime.utcnow()
            
            result = TestResult(
                test_id=test_id,
                scenario=TestScenario.PARTIAL_FILLS,
                severity=TestSeverity.HIGH,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
                passed=passed,
                metrics={
                    'total_scenarios': len(partial_fill_scenarios),
                    'successful_scenarios': successful_scenarios,
                    'success_rate': successful_scenarios / len(partial_fill_scenarios)
                }
            )
            
            self.test_results.append(result)
            
            if passed:
                logger.info(f"Partial fills test {test_id} PASSED: All scenarios handled correctly")
            else:
                logger.error(f"Partial fills test {test_id} FAILED: {successful_scenarios}/{len(partial_fill_scenarios)} scenarios successful")
        
        except Exception as e:
            end_time = datetime.utcnow()
            result = TestResult(
                test_id=test_id,
                scenario=TestScenario.PARTIAL_FILLS,
                severity=TestSeverity.HIGH,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
                passed=False,
                error_message=str(e)
            )
            self.test_results.append(result)
            logger.error(f"Partial fills test {test_id} ERROR: {e}")

    async def _test_clock_drift(self, test_id: str):
        """Test clock drift and time synchronization."""
        start_time = datetime.utcnow()

        try:
            logger.info(f"Starting clock drift test {test_id}")

            # Simulate clock drift by modifying time functions
            original_time = time.time
            drift_seconds = 5.0  # 5 second drift

            def drifted_time():
                return original_time() + drift_seconds

            # Test time-sensitive operations with drift
            with patch('time.time', drifted_time):
                # Test timestamp validation
                timestamp_errors = 0
                for i in range(10):
                    try:
                        await self._simulate_timestamped_operation()
                    except Exception as e:
                        if "timestamp" in str(e).lower() or "time" in str(e).lower():
                            timestamp_errors += 1

                # Test order timing
                timing_errors = await self._test_order_timing_with_drift()

                # Test data synchronization
                sync_errors = await self._test_data_sync_with_drift()

            total_errors = timestamp_errors + timing_errors + sync_errors
            passed = total_errors == 0

            end_time = datetime.utcnow()

            result = TestResult(
                test_id=test_id,
                scenario=TestScenario.CLOCK_DRIFT,
                severity=TestSeverity.MEDIUM,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
                passed=passed,
                metrics={
                    'drift_seconds': drift_seconds,
                    'timestamp_errors': timestamp_errors,
                    'timing_errors': timing_errors,
                    'sync_errors': sync_errors,
                    'total_errors': total_errors
                }
            )

            self.test_results.append(result)

            if passed:
                logger.info(f"Clock drift test {test_id} PASSED: No timing errors detected")
            else:
                logger.error(f"Clock drift test {test_id} FAILED: {total_errors} timing errors detected")

        except Exception as e:
            end_time = datetime.utcnow()
            result = TestResult(
                test_id=test_id,
                scenario=TestScenario.CLOCK_DRIFT,
                severity=TestSeverity.MEDIUM,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
                passed=False,
                error_message=str(e)
            )
            self.test_results.append(result)
            logger.error(f"Clock drift test {test_id} ERROR: {e}")

    async def _test_api_version_changes(self, test_id: str):
        """Test API version compatibility."""
        start_time = datetime.utcnow()

        try:
            logger.info(f"Starting API version change test {test_id}")

            # Test different API versions
            api_versions = ['v1.0', 'v1.1', 'v2.0', 'v2.1']
            compatibility_results = {}

            for version in api_versions:
                try:
                    # Simulate API call with different version
                    response = await self._simulate_api_call_with_version(version)
                    compatibility_results[version] = {
                        'compatible': True,
                        'response_time_ms': response.get('response_time_ms', 0),
                        'features_supported': response.get('features_supported', [])
                    }
                    logger.info(f"API version {version} is compatible")

                except Exception as e:
                    compatibility_results[version] = {
                        'compatible': False,
                        'error': str(e)
                    }
                    logger.warning(f"API version {version} is not compatible: {e}")

            # Check backward compatibility
            compatible_versions = [v for v, r in compatibility_results.items() if r['compatible']]
            passed = len(compatible_versions) >= len(api_versions) * 0.8  # 80% compatibility required

            end_time = datetime.utcnow()

            result = TestResult(
                test_id=test_id,
                scenario=TestScenario.API_VERSION_CHANGE,
                severity=TestSeverity.MEDIUM,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
                passed=passed,
                metrics={
                    'total_versions_tested': len(api_versions),
                    'compatible_versions': len(compatible_versions),
                    'compatibility_rate': len(compatible_versions) / len(api_versions),
                    'compatibility_results': compatibility_results
                }
            )

            self.test_results.append(result)

            if passed:
                logger.info(f"API version test {test_id} PASSED: {len(compatible_versions)}/{len(api_versions)} versions compatible")
            else:
                logger.error(f"API version test {test_id} FAILED: Only {len(compatible_versions)}/{len(api_versions)} versions compatible")

        except Exception as e:
            end_time = datetime.utcnow()
            result = TestResult(
                test_id=test_id,
                scenario=TestScenario.API_VERSION_CHANGE,
                severity=TestSeverity.MEDIUM,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=(end_time - start_time).total_seconds(),
                passed=False,
                error_message=str(e)
            )
            self.test_results.append(result)
            logger.error(f"API version test {test_id} ERROR: {e}")

    # Helper methods for testing

    async def _simulate_trading_operation(self):
        """Simulate a trading operation for testing."""
        # Simulate order placement
        await asyncio.sleep(0.01)  # 10ms base operation time

        # Simulate market data processing
        await asyncio.sleep(0.005)  # 5ms data processing

        # Simulate risk checks
        await asyncio.sleep(0.002)  # 2ms risk validation

        return {'status': 'completed', 'latency_ms': 17}

    def _calculate_checksum(self, data: Any) -> str:
        """Calculate checksum for data integrity verification."""
        json_str = json.dumps(data, sort_keys=True)
        return hashlib.sha256(json_str.encode()).hexdigest()

    def _inject_corruption(self, data: Dict) -> Dict:
        """Inject corruption into test data."""
        corrupted_data = data.copy()

        # Randomly corrupt some data
        if random.random() < self.corruption_config.corruption_rate:
            corruption_type = random.choice(self.corruption_config.corruption_types)

            if corruption_type == 'bit_flip':
                # Flip a random bit in a price field
                if 'orders' in corrupted_data and corrupted_data['orders']:
                    order = random.choice(corrupted_data['orders'])
                    order['price'] = order['price'] + 0.01  # Small price change

            elif corruption_type == 'truncation':
                # Truncate some data
                if 'trades' in corrupted_data:
                    corrupted_data['trades'] = corrupted_data['trades'][:-1]

            elif corruption_type == 'injection':
                # Inject malicious data
                if 'orders' in corrupted_data:
                    corrupted_data['orders'].append({
                        'id': 'malicious_order',
                        'symbol': 'HACK',
                        'quantity': -999999,
                        'price': 0.0
                    })

        return corrupted_data

    async def _simulate_data_recovery(self, original_data: Dict) -> Dict:
        """Simulate data recovery process."""
        # Simulate recovery delay
        await asyncio.sleep(0.1)

        # Return original data (simulating successful recovery)
        return original_data.copy()

    async def _simulate_partial_fill(self, order: Dict, fill_quantity: int) -> Dict:
        """Simulate partial fill of an order."""
        order['filled'] = fill_quantity
        order['remaining'] = order['quantity'] - fill_quantity
        order['status'] = 'partially_filled' if fill_quantity < order['quantity'] else 'filled'

        return {
            'order_id': order['id'],
            'filled_quantity': fill_quantity,
            'remaining_quantity': order['remaining'],
            'status': order['status'],
            'fill_price': 150.0,  # Simulated fill price
            'timestamp': datetime.utcnow().isoformat()
        }

    def _validate_partial_fill_handling(self, fill_result: Dict, scenario: Dict) -> bool:
        """Validate that partial fill was handled correctly."""
        expected_behavior = scenario['expected_behavior']

        if expected_behavior == 'continue_working':
            return fill_result['status'] == 'partially_filled' and fill_result['remaining_quantity'] > 0
        elif expected_behavior == 'complete':
            return fill_result['status'] == 'filled' and fill_result['remaining_quantity'] == 0

        return False

    async def _simulate_timestamped_operation(self):
        """Simulate operation that depends on accurate timestamps."""
        current_time = time.time()

        # Simulate timestamp validation (would fail with significant drift)
        if abs(current_time - time.time()) > 1.0:  # 1 second tolerance
            raise Exception("Timestamp drift detected")

        return {'timestamp': current_time, 'status': 'success'}

    async def _test_order_timing_with_drift(self) -> int:
        """Test order timing with clock drift."""
        errors = 0

        for i in range(5):
            try:
                # Simulate time-sensitive order
                order_time = time.time()
                await asyncio.sleep(0.1)

                # Check if order timing is within acceptable range
                elapsed = time.time() - order_time
                if elapsed > 0.2:  # Should be ~0.1 seconds
                    errors += 1

            except Exception:
                errors += 1

        return errors

    async def _test_data_sync_with_drift(self) -> int:
        """Test data synchronization with clock drift."""
        errors = 0

        # Simulate data synchronization checks
        for i in range(3):
            try:
                sync_start = time.time()
                await asyncio.sleep(0.05)  # Simulate sync operation
                sync_end = time.time()

                # Check sync timing
                if (sync_end - sync_start) > 0.1:  # Should be ~0.05 seconds
                    errors += 1

            except Exception:
                errors += 1

        return errors

    async def _simulate_api_call_with_version(self, version: str) -> Dict:
        """Simulate API call with specific version."""
        # Simulate version-specific behavior
        if version in ['v1.0', 'v1.1']:
            # Older versions - limited features
            return {
                'version': version,
                'response_time_ms': 50,
                'features_supported': ['basic_orders', 'market_data']
            }
        elif version in ['v2.0', 'v2.1']:
            # Newer versions - full features
            return {
                'version': version,
                'response_time_ms': 30,
                'features_supported': ['basic_orders', 'market_data', 'advanced_orders', 'real_time_data']
            }
        else:
            raise Exception(f"Unsupported API version: {version}")

    async def _restore_original_functions(self):
        """Restore any patched functions."""
        # Restore original functions if they were patched
        for func_name, original_func in self.original_functions.items():
            # This would restore patched functions in a real implementation
            pass

    def get_test_summary(self) -> Dict[str, Any]:
        """Get summary of all test results."""
        if not self.test_results:
            return {'message': 'No tests have been run yet'}

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.passed)
        failed_tests = total_tests - passed_tests

        # Group by scenario
        scenario_summary = {}
        for result in self.test_results:
            scenario = result.scenario.value
            if scenario not in scenario_summary:
                scenario_summary[scenario] = {'total': 0, 'passed': 0, 'failed': 0}

            scenario_summary[scenario]['total'] += 1
            if result.passed:
                scenario_summary[scenario]['passed'] += 1
            else:
                scenario_summary[scenario]['failed'] += 1

        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'scenario_summary': scenario_summary,
            'latest_results': [asdict(result) for result in self.test_results[-10:]]  # Last 10 results
        }


# Global instance
_test_framework = None

def get_test_framework() -> ExtendedTestFramework:
    """Get global test framework instance."""
    global _test_framework

    if _test_framework is None:
        _test_framework = ExtendedTestFramework()

    return _test_framework
