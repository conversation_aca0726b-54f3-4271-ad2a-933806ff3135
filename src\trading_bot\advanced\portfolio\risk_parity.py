"""Risk parity portfolio optimization and analysis."""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from scipy.optimize import minimize
from scipy import linalg
import warnings

from ...core.config import settings
from ...core.logger import get_logger

logger = get_logger(__name__)


@dataclass
class RiskParityResult:
    """Risk parity optimization result."""
    weights: Dict[str, float]
    risk_contributions: Dict[str, float]
    portfolio_volatility: float
    concentration_ratio: float
    diversification_ratio: float
    effective_number_of_assets: float


@dataclass
class RiskBudget:
    """Risk budget specification."""
    asset: str
    target_risk_contribution: float


@dataclass
class RiskParityConstraints:
    """Risk parity optimization constraints."""
    min_weight: float = 0.0
    max_weight: float = 1.0
    risk_budgets: Optional[List[RiskBudget]] = None
    leverage_limit: float = 1.0
    turnover_limit: Optional[float] = None


class RiskParityOptimizer:
    """Risk parity portfolio optimization with various approaches."""
    
    def __init__(self):
        self.tolerance = 1e-8
        self.max_iterations = 1000
        
    def optimize_equal_risk_contribution(
        self,
        covariance_matrix: pd.DataFrame,
        constraints: RiskParityConstraints = None
    ) -> RiskParityResult:
        """
        Optimize portfolio for equal risk contribution (ERC).
        
        Args:
            covariance_matrix: Asset covariance matrix
            constraints: Optimization constraints
            
        Returns:
            Risk parity optimization result
        """
        try:
            if constraints is None:
                constraints = RiskParityConstraints()
            
            n_assets = len(covariance_matrix)
            cov_matrix = covariance_matrix.values
            
            # Target equal risk contributions
            target_risk_contrib = np.ones(n_assets) / n_assets
            
            # Optimize using risk budgeting approach
            weights = self._optimize_risk_budgets(
                cov_matrix, target_risk_contrib, constraints
            )
            
            # Calculate risk contributions
            risk_contributions = self._calculate_risk_contributions(weights, cov_matrix)
            
            # Calculate portfolio metrics
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            concentration_ratio = self._calculate_concentration_ratio(risk_contributions)
            diversification_ratio = self._calculate_diversification_ratio(weights, cov_matrix)
            effective_assets = self._calculate_effective_number_of_assets(weights)
            
            # Convert to dictionaries
            weights_dict = {asset: weight for asset, weight in 
                          zip(covariance_matrix.columns, weights)}
            risk_contrib_dict = {asset: contrib for asset, contrib in 
                               zip(covariance_matrix.columns, risk_contributions)}
            
            return RiskParityResult(
                weights=weights_dict,
                risk_contributions=risk_contrib_dict,
                portfolio_volatility=portfolio_vol,
                concentration_ratio=concentration_ratio,
                diversification_ratio=diversification_ratio,
                effective_number_of_assets=effective_assets
            )
            
        except Exception as e:
            logger.error(f"Error optimizing equal risk contribution: {e}")
            return self._empty_result(covariance_matrix.columns)
    
    def optimize_risk_budgets(
        self,
        covariance_matrix: pd.DataFrame,
        risk_budgets: List[RiskBudget],
        constraints: RiskParityConstraints = None
    ) -> RiskParityResult:
        """
        Optimize portfolio for specified risk budgets.
        
        Args:
            covariance_matrix: Asset covariance matrix
            risk_budgets: Target risk budget allocations
            constraints: Optimization constraints
            
        Returns:
            Risk parity optimization result
        """
        try:
            if constraints is None:
                constraints = RiskParityConstraints()
            
            n_assets = len(covariance_matrix)
            cov_matrix = covariance_matrix.values
            
            # Create target risk contribution vector
            target_risk_contrib = np.zeros(n_assets)
            
            for budget in risk_budgets:
                if budget.asset in covariance_matrix.columns:
                    idx = covariance_matrix.columns.get_loc(budget.asset)
                    target_risk_contrib[idx] = budget.target_risk_contribution
            
            # Normalize to sum to 1
            if np.sum(target_risk_contrib) > 0:
                target_risk_contrib = target_risk_contrib / np.sum(target_risk_contrib)
            else:
                target_risk_contrib = np.ones(n_assets) / n_assets
            
            # Optimize
            weights = self._optimize_risk_budgets(
                cov_matrix, target_risk_contrib, constraints
            )
            
            # Calculate metrics
            risk_contributions = self._calculate_risk_contributions(weights, cov_matrix)
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            concentration_ratio = self._calculate_concentration_ratio(risk_contributions)
            diversification_ratio = self._calculate_diversification_ratio(weights, cov_matrix)
            effective_assets = self._calculate_effective_number_of_assets(weights)
            
            # Convert to dictionaries
            weights_dict = {asset: weight for asset, weight in 
                          zip(covariance_matrix.columns, weights)}
            risk_contrib_dict = {asset: contrib for asset, contrib in 
                               zip(covariance_matrix.columns, risk_contributions)}
            
            return RiskParityResult(
                weights=weights_dict,
                risk_contributions=risk_contrib_dict,
                portfolio_volatility=portfolio_vol,
                concentration_ratio=concentration_ratio,
                diversification_ratio=diversification_ratio,
                effective_number_of_assets=effective_assets
            )
            
        except Exception as e:
            logger.error(f"Error optimizing risk budgets: {e}")
            return self._empty_result(covariance_matrix.columns)
    
    def optimize_hierarchical_risk_parity(
        self,
        returns_data: pd.DataFrame,
        constraints: RiskParityConstraints = None
    ) -> RiskParityResult:
        """
        Optimize using Hierarchical Risk Parity (HRP) approach.
        
        Args:
            returns_data: Historical returns data
            constraints: Optimization constraints
            
        Returns:
            Risk parity optimization result
        """
        try:
            if constraints is None:
                constraints = RiskParityConstraints()
            
            # Calculate correlation matrix
            correlation_matrix = returns_data.corr()
            
            # Hierarchical clustering
            clusters = self._hierarchical_clustering(correlation_matrix)
            
            # Recursive bisection for weight allocation
            weights = self._recursive_bisection(
                returns_data, clusters, correlation_matrix
            )
            
            # Calculate covariance matrix for risk metrics
            cov_matrix = returns_data.cov().values
            
            # Calculate risk contributions
            risk_contributions = self._calculate_risk_contributions(weights, cov_matrix)
            
            # Calculate portfolio metrics
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            concentration_ratio = self._calculate_concentration_ratio(risk_contributions)
            diversification_ratio = self._calculate_diversification_ratio(weights, cov_matrix)
            effective_assets = self._calculate_effective_number_of_assets(weights)
            
            # Convert to dictionaries
            weights_dict = {asset: weight for asset, weight in 
                          zip(returns_data.columns, weights)}
            risk_contrib_dict = {asset: contrib for asset, contrib in 
                               zip(returns_data.columns, risk_contributions)}
            
            return RiskParityResult(
                weights=weights_dict,
                risk_contributions=risk_contrib_dict,
                portfolio_volatility=portfolio_vol,
                concentration_ratio=concentration_ratio,
                diversification_ratio=diversification_ratio,
                effective_number_of_assets=effective_assets
            )
            
        except Exception as e:
            logger.error(f"Error optimizing hierarchical risk parity: {e}")
            return self._empty_result(returns_data.columns)
    
    def _optimize_risk_budgets(
        self,
        cov_matrix: np.ndarray,
        target_risk_contrib: np.ndarray,
        constraints: RiskParityConstraints
    ) -> np.ndarray:
        """Optimize portfolio for target risk contributions."""
        try:
            n_assets = len(cov_matrix)
            
            def risk_budget_objective(weights):
                """Objective function for risk budgeting."""
                weights = np.array(weights)
                
                # Calculate portfolio volatility
                portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
                
                if portfolio_vol < self.tolerance:
                    return 1e6  # Large penalty for zero volatility
                
                # Calculate risk contributions
                marginal_contrib = np.dot(cov_matrix, weights) / portfolio_vol
                risk_contrib = weights * marginal_contrib / portfolio_vol
                
                # Sum of squared deviations from target
                deviation = risk_contrib - target_risk_contrib
                return np.sum(deviation ** 2)
            
            # Constraints
            cons = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}  # Weights sum to 1
            ]
            
            # Add leverage constraint if specified
            if constraints.leverage_limit < float('inf'):
                cons.append({
                    'type': 'ineq',
                    'fun': lambda x: constraints.leverage_limit - np.sum(np.abs(x))
                })
            
            # Bounds
            bounds = [(constraints.min_weight, constraints.max_weight) 
                     for _ in range(n_assets)]
            
            # Initial guess (equal weights)
            x0 = np.ones(n_assets) / n_assets
            
            # Optimize
            result = minimize(
                risk_budget_objective,
                x0,
                method='SLSQP',
                bounds=bounds,
                constraints=cons,
                options={'maxiter': self.max_iterations, 'ftol': self.tolerance}
            )
            
            if result.success:
                return result.x
            else:
                logger.warning("Risk parity optimization failed, using equal weights")
                return np.ones(n_assets) / n_assets
                
        except Exception as e:
            logger.error(f"Error in risk budget optimization: {e}")
            return np.ones(len(cov_matrix)) / len(cov_matrix)
    
    def _calculate_risk_contributions(
        self,
        weights: np.ndarray,
        cov_matrix: np.ndarray
    ) -> np.ndarray:
        """Calculate risk contributions for each asset."""
        try:
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            
            if portfolio_vol < self.tolerance:
                return np.zeros(len(weights))
            
            # Marginal risk contributions
            marginal_contrib = np.dot(cov_matrix, weights) / portfolio_vol
            
            # Risk contributions (normalized)
            risk_contrib = weights * marginal_contrib / portfolio_vol
            
            return risk_contrib
            
        except Exception as e:
            logger.error(f"Error calculating risk contributions: {e}")
            return np.zeros(len(weights))
    
    def _hierarchical_clustering(self, correlation_matrix: pd.DataFrame) -> List[List[int]]:
        """Perform hierarchical clustering on correlation matrix."""
        try:
            from scipy.cluster.hierarchy import linkage, fcluster
            from scipy.spatial.distance import squareform
            
            # Convert correlation to distance
            distance_matrix = np.sqrt(0.5 * (1 - correlation_matrix))
            
            # Perform hierarchical clustering
            condensed_distances = squareform(distance_matrix, checks=False)
            linkage_matrix = linkage(condensed_distances, method='ward')
            
            # Get clusters (simplified - return pairs)
            n_assets = len(correlation_matrix)
            clusters = [[i] for i in range(n_assets)]
            
            return clusters
            
        except Exception as e:
            logger.error(f"Error in hierarchical clustering: {e}")
            # Fallback: return individual assets as clusters
            return [[i] for i in range(len(correlation_matrix))]
    
    def _recursive_bisection(
        self,
        returns_data: pd.DataFrame,
        clusters: List[List[int]],
        correlation_matrix: pd.DataFrame
    ) -> np.ndarray:
        """Recursive bisection for HRP weight allocation."""
        try:
            n_assets = len(returns_data.columns)
            weights = np.zeros(n_assets)
            
            # Simplified HRP - equal weight within clusters, inverse volatility between clusters
            cov_matrix = returns_data.cov()
            
            for cluster in clusters:
                if len(cluster) == 1:
                    # Single asset cluster
                    asset_idx = cluster[0]
                    asset_vol = np.sqrt(cov_matrix.iloc[asset_idx, asset_idx])
                    weights[asset_idx] = 1 / asset_vol if asset_vol > 0 else 0
                else:
                    # Multi-asset cluster - equal weights
                    cluster_weight = 1 / len(cluster)
                    for asset_idx in cluster:
                        weights[asset_idx] = cluster_weight
            
            # Normalize weights
            if np.sum(weights) > 0:
                weights = weights / np.sum(weights)
            else:
                weights = np.ones(n_assets) / n_assets
            
            return weights
            
        except Exception as e:
            logger.error(f"Error in recursive bisection: {e}")
            return np.ones(len(returns_data.columns)) / len(returns_data.columns)
    
    def _calculate_concentration_ratio(self, risk_contributions: np.ndarray) -> float:
        """Calculate concentration ratio (Herfindahl index)."""
        try:
            return np.sum(risk_contributions ** 2)
        except Exception as e:
            logger.error(f"Error calculating concentration ratio: {e}")
            return 1.0
    
    def _calculate_diversification_ratio(
        self,
        weights: np.ndarray,
        cov_matrix: np.ndarray
    ) -> float:
        """Calculate diversification ratio."""
        try:
            # Weighted average volatility
            individual_vols = np.sqrt(np.diag(cov_matrix))
            weighted_avg_vol = np.dot(weights, individual_vols)
            
            # Portfolio volatility
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            
            if portfolio_vol > 0:
                return weighted_avg_vol / portfolio_vol
            else:
                return 1.0
                
        except Exception as e:
            logger.error(f"Error calculating diversification ratio: {e}")
            return 1.0
    
    def _calculate_effective_number_of_assets(self, weights: np.ndarray) -> float:
        """Calculate effective number of assets (inverse of concentration)."""
        try:
            # Effective number of assets = 1 / sum(w_i^2)
            return 1 / np.sum(weights ** 2) if np.sum(weights ** 2) > 0 else 1.0
        except Exception as e:
            logger.error(f"Error calculating effective number of assets: {e}")
            return 1.0
    
    def analyze_risk_decomposition(
        self,
        weights: Dict[str, float],
        covariance_matrix: pd.DataFrame
    ) -> Dict[str, Any]:
        """
        Analyze detailed risk decomposition.
        
        Args:
            weights: Portfolio weights
            covariance_matrix: Asset covariance matrix
            
        Returns:
            Risk decomposition analysis
        """
        try:
            # Convert weights to array
            weights_array = np.array([weights.get(asset, 0) 
                                    for asset in covariance_matrix.columns])
            cov_matrix = covariance_matrix.values
            
            # Portfolio volatility
            portfolio_vol = np.sqrt(np.dot(weights_array, np.dot(cov_matrix, weights_array)))
            
            # Risk contributions
            risk_contributions = self._calculate_risk_contributions(weights_array, cov_matrix)
            
            # Marginal risk contributions
            marginal_contrib = np.dot(cov_matrix, weights_array)
            if portfolio_vol > 0:
                marginal_contrib = marginal_contrib / portfolio_vol
            
            # Component contributions to variance
            component_variance = weights_array * marginal_contrib
            
            # Pairwise risk contributions
            pairwise_contrib = {}
            for i, asset1 in enumerate(covariance_matrix.columns):
                for j, asset2 in enumerate(covariance_matrix.columns):
                    if i <= j:  # Avoid double counting
                        contrib = weights_array[i] * weights_array[j] * cov_matrix[i, j]
                        if portfolio_vol > 0:
                            contrib = contrib / (portfolio_vol ** 2)
                        
                        if i == j:
                            pairwise_contrib[f"{asset1}"] = contrib
                        else:
                            pairwise_contrib[f"{asset1}-{asset2}"] = contrib * 2  # Double for off-diagonal
            
            return {
                'portfolio_volatility': portfolio_vol,
                'risk_contributions': {asset: contrib for asset, contrib in 
                                     zip(covariance_matrix.columns, risk_contributions)},
                'marginal_contributions': {asset: contrib for asset, contrib in 
                                         zip(covariance_matrix.columns, marginal_contrib)},
                'component_variance': {asset: contrib for asset, contrib in 
                                     zip(covariance_matrix.columns, component_variance)},
                'pairwise_contributions': pairwise_contrib,
                'concentration_ratio': self._calculate_concentration_ratio(risk_contributions),
                'diversification_ratio': self._calculate_diversification_ratio(weights_array, cov_matrix),
                'effective_assets': self._calculate_effective_number_of_assets(weights_array)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing risk decomposition: {e}")
            return {}
    
    def _empty_result(self, asset_names: List[str]) -> RiskParityResult:
        """Return empty risk parity result."""
        return RiskParityResult(
            weights={asset: 0.0 for asset in asset_names},
            risk_contributions={asset: 0.0 for asset in asset_names},
            portfolio_volatility=0.0,
            concentration_ratio=1.0,
            diversification_ratio=1.0,
            effective_number_of_assets=1.0
        )
