"""Social media sentiment analysis for trading signals."""

import asyncio
import aiohttp
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from textblob import TextBlob
import pandas as pd
import numpy as np

from ...core.config import settings
from ...core.logger import get_logger
from ...utils.cache import cache_manager
from ...utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


@dataclass
class SocialPost:
    """Social media post data structure."""
    id: str
    text: str
    platform: str
    author: str
    created_at: datetime
    symbols: List[str]
    likes: int = 0
    shares: int = 0
    comments: int = 0
    followers: int = 0
    sentiment_score: Optional[float] = None
    influence_score: Optional[float] = None


@dataclass
class SocialSentiment:
    """Social media sentiment analysis results."""
    symbol: str
    sentiment_score: float
    confidence: float
    volume: int
    buzz_score: float
    engagement: int
    top_posts: List[SocialPost]
    trending_topics: List[str]


class SocialMediaAnalyzer:
    """Social media sentiment analysis with influence weighting."""
    
    def __init__(self):
        self.rate_limiter = RateLimiter(max_requests=300, time_window=900)  # 15 min window
        self.spam_detector = SpamDetector()
        self.influencer_weights = self._load_influencer_weights()
        
    async def analyze_social_sentiment(
        self,
        symbols: List[str],
        hours_back: int = 24,
        platforms: List[str] = None
    ) -> Dict[str, SocialSentiment]:
        """
        Analyze social media sentiment for given symbols.
        
        Args:
            symbols: List of stock symbols
            hours_back: Hours to look back
            platforms: Platforms to analyze (twitter, reddit)
            
        Returns:
            Dictionary mapping symbols to sentiment analysis
        """
        if platforms is None:
            platforms = ['twitter', 'reddit']
        
        try:
            # Fetch posts from all platforms
            all_posts = []
            
            for platform in platforms:
                if platform == 'twitter':
                    posts = await self._fetch_twitter_posts(symbols, hours_back)
                elif platform == 'reddit':
                    posts = await self._fetch_reddit_posts(symbols, hours_back)
                else:
                    continue
                
                all_posts.extend(posts)
            
            # Filter spam and low-quality posts
            filtered_posts = await self._filter_posts(all_posts)
            
            # Analyze sentiment for each post
            analyzed_posts = []
            for post in filtered_posts:
                sentiment = await self._analyze_post_sentiment(post)
                influence = self._calculate_influence_score(post)
                
                post.sentiment_score = sentiment
                post.influence_score = influence
                analyzed_posts.append(post)
            
            # Aggregate by symbol
            symbol_sentiment = {}
            for symbol in symbols:
                sentiment_data = self._aggregate_symbol_sentiment(
                    analyzed_posts, symbol
                )
                symbol_sentiment[symbol] = sentiment_data
            
            return symbol_sentiment
            
        except Exception as e:
            logger.error(f"Error analyzing social sentiment: {e}")
            return {symbol: self._empty_sentiment(symbol) for symbol in symbols}
    
    async def _fetch_twitter_posts(
        self,
        symbols: List[str],
        hours_back: int
    ) -> List[SocialPost]:
        """Fetch Twitter posts mentioning symbols."""
        if not hasattr(settings, 'twitter_bearer_token'):
            logger.warning("Twitter Bearer Token not configured")
            return []
        
        posts = []
        
        for symbol in symbols:
            await self.rate_limiter.acquire()
            
            try:
                # Build search query
                query = f"${symbol} OR #{symbol}"
                
                url = "https://api.twitter.com/2/tweets/search/recent"
                headers = {
                    'Authorization': f'Bearer {settings.twitter_bearer_token}'
                }
                params = {
                    'query': query,
                    'max_results': 100,
                    'tweet.fields': 'created_at,public_metrics,author_id',
                    'user.fields': 'public_metrics',
                    'expansions': 'author_id'
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, headers=headers, params=params) as response:
                        data = await response.json()
                
                if 'data' in data:
                    users = {user['id']: user for user in data.get('includes', {}).get('users', [])}
                    
                    for tweet in data['data']:
                        try:
                            created_at = datetime.fromisoformat(
                                tweet['created_at'].replace('Z', '+00:00')
                            )
                            
                            # Check if within time window
                            if created_at > datetime.utcnow().replace(tzinfo=created_at.tzinfo) - timedelta(hours=hours_back):
                                author_id = tweet.get('author_id')
                                user = users.get(author_id, {})
                                
                                post = SocialPost(
                                    id=tweet['id'],
                                    text=tweet['text'],
                                    platform='twitter',
                                    author=author_id,
                                    created_at=created_at.replace(tzinfo=None),
                                    symbols=[symbol],
                                    likes=tweet.get('public_metrics', {}).get('like_count', 0),
                                    shares=tweet.get('public_metrics', {}).get('retweet_count', 0),
                                    comments=tweet.get('public_metrics', {}).get('reply_count', 0),
                                    followers=user.get('public_metrics', {}).get('followers_count', 0)
                                )
                                posts.append(post)
                        except Exception as e:
                            logger.warning(f"Error parsing Twitter post: {e}")
                            continue
                
            except Exception as e:
                logger.error(f"Error fetching Twitter posts for {symbol}: {e}")
                continue
        
        return posts
    
    async def _fetch_reddit_posts(
        self,
        symbols: List[str],
        hours_back: int
    ) -> List[SocialPost]:
        """Fetch Reddit posts mentioning symbols."""
        posts = []
        
        # Reddit subreddits to monitor
        subreddits = [
            'wallstreetbets', 'investing', 'stocks', 'SecurityAnalysis',
            'ValueInvesting', 'StockMarket', 'pennystocks'
        ]
        
        for subreddit in subreddits:
            for symbol in symbols:
                await self.rate_limiter.acquire()
                
                try:
                    url = f"https://www.reddit.com/r/{subreddit}/search.json"
                    params = {
                        'q': f'${symbol}',
                        'sort': 'new',
                        'limit': 25,
                        't': 'day' if hours_back <= 24 else 'week'
                    }
                    
                    headers = {
                        'User-Agent': 'TradingBot/1.0'
                    }
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.get(url, params=params, headers=headers) as response:
                            data = await response.json()
                    
                    if 'data' in data and 'children' in data['data']:
                        for item in data['data']['children']:
                            try:
                                post_data = item['data']
                                created_at = datetime.fromtimestamp(post_data['created_utc'])
                                
                                # Check if within time window
                                if created_at > datetime.utcnow() - timedelta(hours=hours_back):
                                    # Combine title and selftext
                                    text = f"{post_data.get('title', '')} {post_data.get('selftext', '')}"
                                    
                                    post = SocialPost(
                                        id=post_data['id'],
                                        text=text,
                                        platform='reddit',
                                        author=post_data.get('author', ''),
                                        created_at=created_at,
                                        symbols=[symbol],
                                        likes=post_data.get('ups', 0),
                                        shares=0,  # Reddit doesn't have shares
                                        comments=post_data.get('num_comments', 0),
                                        followers=0  # Not available in basic API
                                    )
                                    posts.append(post)
                            except Exception as e:
                                logger.warning(f"Error parsing Reddit post: {e}")
                                continue
                
                except Exception as e:
                    logger.error(f"Error fetching Reddit posts for {symbol} in {subreddit}: {e}")
                    continue
        
        return posts
    
    async def _filter_posts(self, posts: List[SocialPost]) -> List[SocialPost]:
        """Filter out spam and low-quality posts."""
        filtered = []
        
        for post in posts:
            # Basic spam detection
            if await self.spam_detector.is_spam(post):
                continue
            
            # Minimum text length
            if len(post.text.strip()) < 10:
                continue
            
            # Filter out posts with excessive special characters
            special_char_ratio = len(re.findall(r'[^a-zA-Z0-9\s]', post.text)) / len(post.text)
            if special_char_ratio > 0.3:
                continue
            
            filtered.append(post)
        
        return filtered
    
    async def _analyze_post_sentiment(self, post: SocialPost) -> float:
        """Analyze sentiment of a social media post."""
        try:
            # Clean text
            text = self._clean_text(post.text)
            
            # Basic sentiment analysis
            blob = TextBlob(text)
            base_sentiment = blob.sentiment.polarity
            
            # Social media specific adjustments
            social_sentiment = self._calculate_social_sentiment(text)
            
            # Combine scores
            final_sentiment = (base_sentiment + social_sentiment) / 2
            
            return final_sentiment
            
        except Exception as e:
            logger.error(f"Error analyzing post sentiment: {e}")
            return 0.0
    
    def _clean_text(self, text: str) -> str:
        """Clean social media text for analysis."""
        # Remove URLs
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        
        # Remove mentions and hashtags for sentiment analysis
        text = re.sub(r'@\w+|#\w+', '', text)
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _calculate_social_sentiment(self, text: str) -> float:
        """Calculate social media specific sentiment."""
        text_lower = text.lower()
        
        # Social media specific positive indicators
        positive_indicators = [
            'moon', 'rocket', 'diamond hands', 'hodl', 'bullish',
            'to the moon', 'stonks', 'buy the dip', 'yolo'
        ]
        
        # Social media specific negative indicators
        negative_indicators = [
            'dump', 'crash', 'rekt', 'bag holder', 'paper hands',
            'bearish', 'sell off', 'dead cat bounce'
        ]
        
        positive_score = sum(1 for indicator in positive_indicators if indicator in text_lower)
        negative_score = sum(1 for indicator in negative_indicators if indicator in text_lower)
        
        # Normalize
        total_indicators = positive_score + negative_score
        if total_indicators == 0:
            return 0.0
        
        return (positive_score - negative_score) / total_indicators

    def _calculate_influence_score(self, post: SocialPost) -> float:
        """Calculate influence score based on author and engagement."""
        # Base influence from follower count
        follower_influence = min(np.log10(max(post.followers, 1)) / 6, 1.0)  # Log scale, max at 1M

        # Engagement influence
        total_engagement = post.likes + post.shares + post.comments
        engagement_influence = min(np.log10(max(total_engagement, 1)) / 4, 1.0)  # Log scale

        # Platform-specific weights
        platform_weights = {
            'twitter': 1.0,
            'reddit': 0.8
        }
        platform_weight = platform_weights.get(post.platform, 0.5)

        # Known influencer bonus
        influencer_bonus = self.influencer_weights.get(post.author, 0)

        # Combine scores
        influence = (
            follower_influence * 0.4 +
            engagement_influence * 0.4 +
            influencer_bonus * 0.2
        ) * platform_weight

        return min(influence, 1.0)

    def _aggregate_symbol_sentiment(
        self,
        posts: List[SocialPost],
        symbol: str
    ) -> SocialSentiment:
        """Aggregate sentiment for a specific symbol."""
        symbol_posts = [p for p in posts if symbol in p.symbols]

        if not symbol_posts:
            return self._empty_sentiment(symbol)

        # Calculate weighted sentiment
        total_weight = 0
        weighted_sentiment = 0
        total_engagement = 0

        for post in symbol_posts:
            weight = post.influence_score
            weighted_sentiment += post.sentiment_score * weight
            total_weight += weight
            total_engagement += post.likes + post.shares + post.comments

        avg_sentiment = weighted_sentiment / total_weight if total_weight > 0 else 0

        # Calculate buzz score (volume + engagement)
        buzz_score = len(symbol_posts) * np.log10(max(total_engagement, 1)) / 100

        # Get top posts by influence
        top_posts = sorted(
            symbol_posts,
            key=lambda p: p.influence_score,
            reverse=True
        )[:5]

        # Extract trending topics
        trending_topics = self._extract_trending_topics(symbol_posts)

        # Calculate confidence based on volume and consistency
        confidence = min(len(symbol_posts) / 50, 1.0)  # Max confidence at 50 posts

        return SocialSentiment(
            symbol=symbol,
            sentiment_score=avg_sentiment,
            confidence=confidence,
            volume=len(symbol_posts),
            buzz_score=buzz_score,
            engagement=total_engagement,
            top_posts=top_posts,
            trending_topics=trending_topics
        )

    def _extract_trending_topics(self, posts: List[SocialPost]) -> List[str]:
        """Extract trending topics from posts."""
        # Extract hashtags and common phrases
        hashtags = []
        for post in posts:
            hashtags.extend(re.findall(r'#(\w+)', post.text))

        # Count frequency
        from collections import Counter
        hashtag_counts = Counter(hashtags)

        # Return top trending hashtags
        return [tag for tag, count in hashtag_counts.most_common(10) if count > 1]

    def _load_influencer_weights(self) -> Dict[str, float]:
        """Load known influencer weights."""
        # This would typically be loaded from a database or config file
        return {
            # Twitter handles (without @)
            'elonmusk': 1.0,
            'chamath': 0.9,
            'cathiedwood': 0.9,
            'jimcramer': 0.8,
            # Reddit users
            'DeepFuckingValue': 1.0,
        }

    def _empty_sentiment(self, symbol: str) -> SocialSentiment:
        """Return empty sentiment result."""
        return SocialSentiment(
            symbol=symbol,
            sentiment_score=0.0,
            confidence=0.0,
            volume=0,
            buzz_score=0.0,
            engagement=0,
            top_posts=[],
            trending_topics=[]
        )


class SpamDetector:
    """Detect spam and low-quality social media posts."""

    def __init__(self):
        self.spam_patterns = [
            r'follow.*for.*tips',
            r'dm.*me.*for',
            r'check.*my.*bio',
            r'link.*in.*bio',
            r'pump.*and.*dump',
            r'guaranteed.*profit',
            r'100%.*return'
        ]

    async def is_spam(self, post: SocialPost) -> bool:
        """Check if post is likely spam."""
        text_lower = post.text.lower()

        # Check for spam patterns
        for pattern in self.spam_patterns:
            if re.search(pattern, text_lower):
                return True

        # Check for excessive repetition
        words = text_lower.split()
        if len(words) > 5:
            unique_words = set(words)
            if len(unique_words) / len(words) < 0.3:  # Less than 30% unique words
                return True

        # Check for excessive emojis
        emoji_count = len(re.findall(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]', post.text))
        if emoji_count > len(post.text) * 0.2:  # More than 20% emojis
            return True

        return False
