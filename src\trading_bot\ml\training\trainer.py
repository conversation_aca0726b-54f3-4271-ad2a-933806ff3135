"""Training orchestrator for ML models."""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import os
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import mlflow
import mlflow.sklearn
import mlflow.pytorch
from pathlib import Path

from ..models.lstm_predictor import LSTMTradingPredictor, LSTMConfig
from ..models.xgboost_classifier import XGBoostClassifier, XGBoostConfig
from ..models.transformer_model import PatternRecognitionTransformer, TransformerConfig
from ..models.reinforcement_agent import ReinforcementAgent, RLConfig
from ..models.ensemble import EnsembleCoordinator, EnsembleConfig
from ..features.feature_engineering import FeatureEngineering

logger = logging.getLogger(__name__)


@dataclass
class TrainingConfig:
    """Configuration for training pipeline."""
    # Data configuration
    train_start_date: str = "2020-01-01"
    train_end_date: str = "2023-12-31"
    validation_split: float = 0.2
    test_split: float = 0.1
    
    # Training parameters
    max_epochs: int = 100
    batch_size: int = 32
    learning_rate: float = 0.001
    early_stopping_patience: int = 10
    
    # Model selection
    models_to_train: List[str] = field(default_factory=lambda: ['lstm', 'xgboost', 'transformer', 'rl'])
    enable_ensemble: bool = True
    
    # Feature engineering
    use_technical_indicators: bool = True
    use_microstructure: bool = True
    use_sentiment: bool = True
    feature_selection_k: int = 50
    
    # Cross-validation
    use_time_series_cv: bool = True
    cv_folds: int = 5
    
    # Experiment tracking
    experiment_name: str = "trading_model_training"
    run_name: Optional[str] = None
    track_with_mlflow: bool = True
    
    # Resource management
    max_workers: int = 4
    gpu_enabled: bool = True
    
    # Output paths
    model_save_dir: str = "models"
    results_save_dir: str = "results"


class ModelTrainer:
    """Individual model trainer."""
    
    def __init__(self, model_type: str, config: TrainingConfig):
        self.model_type = model_type
        self.config = config
        self.model = None
        self.training_history = {}
        
    def train(self, 
              train_data: pd.DataFrame,
              val_data: Optional[pd.DataFrame] = None,
              target_column: str = 'target') -> Dict[str, Any]:
        """Train the model."""
        logger.info(f"Training {self.model_type} model...")
        
        try:
            if self.model_type == 'lstm':
                return self._train_lstm(train_data, val_data, target_column)
            elif self.model_type == 'xgboost':
                return self._train_xgboost(train_data, val_data, target_column)
            elif self.model_type == 'transformer':
                return self._train_transformer(train_data, val_data, target_column)
            elif self.model_type == 'rl':
                return self._train_rl(train_data, val_data, target_column)
            else:
                raise ValueError(f"Unknown model type: {self.model_type}")
                
        except Exception as e:
            logger.error(f"Error training {self.model_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _train_lstm(self, train_data: pd.DataFrame, val_data: Optional[pd.DataFrame], target_column: str) -> Dict[str, Any]:
        """Train LSTM model."""
        # Prepare data
        feature_cols = [col for col in train_data.columns if col != target_column and not col.startswith('target')]
        X_train = train_data[feature_cols].values
        y_train = train_data[target_column].values
        
        X_val = val_data[feature_cols].values if val_data is not None else None
        y_val = val_data[target_column].values if val_data is not None else None
        
        # Initialize model
        lstm_config = LSTMConfig(
            input_size=len(feature_cols),
            sequence_length=min(200, len(X_train) // 10)
        )
        self.model = LSTMTradingPredictor(lstm_config)
        
        # Train
        history = self.model.train(
            train_data=X_train,
            val_data=X_val,
            epochs=self.config.max_epochs,
            batch_size=self.config.batch_size,
            learning_rate=self.config.learning_rate
        )
        
        self.training_history = history
        
        return {
            'success': True,
            'model': self.model,
            'training_history': history,
            'model_type': 'lstm'
        }
    
    def _train_xgboost(self, train_data: pd.DataFrame, val_data: Optional[pd.DataFrame], target_column: str) -> Dict[str, Any]:
        """Train XGBoost model."""
        # Prepare data
        feature_cols = [col for col in train_data.columns if col != target_column and not col.startswith('target')]
        X_train = train_data[feature_cols]
        y_train = train_data[target_column].values
        
        # Initialize model
        xgb_config = XGBoostConfig(
            n_estimators=self.config.max_epochs,
            learning_rate=self.config.learning_rate
        )
        self.model = XGBoostClassifier(xgb_config)
        
        # Train
        metrics = self.model.train(
            X=X_train,
            y=y_train,
            validation_split=self.config.validation_split
        )
        
        self.training_history = metrics
        
        return {
            'success': True,
            'model': self.model,
            'training_history': metrics,
            'model_type': 'xgboost'
        }
    
    def _train_transformer(self, train_data: pd.DataFrame, val_data: Optional[pd.DataFrame], target_column: str) -> Dict[str, Any]:
        """Train Transformer model."""
        # Prepare data
        feature_cols = [col for col in train_data.columns if col != target_column and not col.startswith('target')]
        X_train = train_data[feature_cols].values
        y_train = train_data[target_column].values
        
        X_val = val_data[feature_cols].values if val_data is not None else None
        y_val = val_data[target_column].values if val_data is not None else None
        
        # Initialize model
        transformer_config = TransformerConfig(
            n_features=len(feature_cols),
            sequence_length=min(200, len(X_train) // 10)
        )
        self.model = PatternRecognitionTransformer(transformer_config)
        
        # Create pattern labels (simplified)
        pattern_labels = self._create_pattern_labels(y_train)
        val_pattern_labels = self._create_pattern_labels(y_val) if y_val is not None else None
        
        # Train
        history = self.model.train(
            train_data=X_train,
            train_labels=pattern_labels,
            val_data=X_val,
            val_labels=val_pattern_labels,
            epochs=self.config.max_epochs,
            batch_size=self.config.batch_size
        )
        
        self.training_history = history
        
        return {
            'success': True,
            'model': self.model,
            'training_history': history,
            'model_type': 'transformer'
        }
    
    def _train_rl(self, train_data: pd.DataFrame, val_data: Optional[pd.DataFrame], target_column: str) -> Dict[str, Any]:
        """Train Reinforcement Learning agent."""
        # Prepare data
        feature_cols = [col for col in train_data.columns if col != target_column and not col.startswith('target')]
        X_train = train_data[feature_cols].values
        
        # Initialize model
        rl_config = RLConfig(
            state_dim=len(feature_cols)
        )
        self.model = ReinforcementAgent(rl_config)
        
        # Train
        history = self.model.train(
            train_data=X_train,
            val_data=val_data[feature_cols].values if val_data is not None else None,
            n_episodes=self.config.max_epochs,
            max_steps_per_episode=1000
        )
        
        self.training_history = history
        
        return {
            'success': True,
            'model': self.model,
            'training_history': history,
            'model_type': 'rl'
        }
    
    def _create_pattern_labels(self, targets: np.ndarray) -> np.ndarray:
        """Create pattern labels for transformer training."""
        # Simplified pattern labeling based on returns
        patterns = np.zeros(len(targets), dtype=int)
        
        for i in range(len(targets)):
            if targets[i] > 0.02:  # Strong positive
                patterns[i] = 0  # breakout_up
            elif targets[i] < -0.02:  # Strong negative
                patterns[i] = 1  # breakout_down
            elif targets[i] > 0.005:  # Mild positive
                patterns[i] = 5  # trend_up
            elif targets[i] < -0.005:  # Mild negative
                patterns[i] = 6  # trend_down
            else:  # Neutral
                patterns[i] = 7  # sideways
        
        return patterns


class Trainer:
    """Main training orchestrator."""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.feature_engineer = FeatureEngineering()
        self.trained_models = {}
        self.ensemble = None
        self.training_results = {}
        
        # Setup directories
        self._setup_directories()
        
        # Setup MLflow if enabled
        if config.track_with_mlflow:
            self._setup_mlflow()
        
        logger.info("Initialized Trainer")
    
    def _setup_directories(self):
        """Setup output directories."""
        Path(self.config.model_save_dir).mkdir(parents=True, exist_ok=True)
        Path(self.config.results_save_dir).mkdir(parents=True, exist_ok=True)
    
    def _setup_mlflow(self):
        """Setup MLflow tracking."""
        try:
            mlflow.set_experiment(self.config.experiment_name)
            logger.info("MLflow tracking enabled")
        except Exception as e:
            logger.warning(f"Failed to setup MLflow: {e}")
            self.config.track_with_mlflow = False
    
    async def train_all_models(self, 
                              data: pd.DataFrame,
                              target_column: str = 'target',
                              symbol: str = 'UNKNOWN') -> Dict[str, Any]:
        """Train all specified models."""
        logger.info(f"Starting training pipeline for {symbol}")
        
        with mlflow.start_run(run_name=self.config.run_name or f"training_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}") if self.config.track_with_mlflow else nullcontext():
            
            # Log configuration
            if self.config.track_with_mlflow:
                mlflow.log_params(self.config.__dict__)
            
            # Prepare data
            prepared_data = await self._prepare_training_data(data, target_column)
            
            # Split data
            train_data, val_data, test_data = self._split_data(prepared_data)
            
            # Train models in parallel
            training_tasks = []
            
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                futures = {}
                
                for model_type in self.config.models_to_train:
                    trainer = ModelTrainer(model_type, self.config)
                    future = executor.submit(trainer.train, train_data, val_data, target_column)
                    futures[future] = (model_type, trainer)
                
                # Collect results
                for future in as_completed(futures):
                    model_type, trainer = futures[future]
                    try:
                        result = future.result()
                        if result['success']:
                            self.trained_models[model_type] = trainer.model
                            self.training_results[model_type] = result
                            logger.info(f"Successfully trained {model_type}")
                            
                            # Save model
                            model_path = os.path.join(self.config.model_save_dir, f"{model_type}_{symbol}.pkl")
                            trainer.model.save_model(model_path)
                            
                            # Log to MLflow
                            if self.config.track_with_mlflow:
                                self._log_model_to_mlflow(model_type, trainer.model, result)
                                
                        else:
                            logger.error(f"Failed to train {model_type}: {result.get('error', 'Unknown error')}")
                            
                    except Exception as e:
                        logger.error(f"Exception during {model_type} training: {e}")
            
            # Train ensemble if enabled
            if self.config.enable_ensemble and len(self.trained_models) > 1:
                ensemble_result = await self._train_ensemble(train_data, val_data, target_column)
                self.training_results['ensemble'] = ensemble_result
            
            # Evaluate all models
            evaluation_results = await self._evaluate_models(test_data, target_column)
            
            # Save results
            final_results = {
                'training_config': self.config.__dict__,
                'trained_models': list(self.trained_models.keys()),
                'training_results': self.training_results,
                'evaluation_results': evaluation_results,
                'data_info': {
                    'train_samples': len(train_data),
                    'val_samples': len(val_data) if val_data is not None else 0,
                    'test_samples': len(test_data) if test_data is not None else 0,
                    'features': len(prepared_data.columns) - 1
                }
            }
            
            # Save to file
            results_path = os.path.join(self.config.results_save_dir, f"training_results_{symbol}.json")
            with open(results_path, 'w') as f:
                json.dump(final_results, f, indent=2, default=str)
            
            logger.info(f"Training completed. Results saved to {results_path}")
            
            return final_results
    
    async def _prepare_training_data(self, data: pd.DataFrame, target_column: str) -> pd.DataFrame:
        """Prepare data for training."""
        logger.info("Preparing training data...")
        
        # Engineer features
        if any([self.config.use_technical_indicators, self.config.use_microstructure, self.config.use_sentiment]):
            data = self.feature_engineer.engineer_features(data)
        
        # Create target if not exists
        if target_column not in data.columns:
            # Create forward returns as target
            data[target_column] = data['close'].pct_change(5).shift(-5)  # 5-period forward return
        
        # Clean data
        data = data.dropna()
        
        # Feature selection
        if self.config.feature_selection_k > 0:
            feature_cols = [col for col in data.columns if col != target_column and not col.startswith('target')]
            if len(feature_cols) > self.config.feature_selection_k:
                X = data[feature_cols]
                y = data[target_column]
                
                # Select features
                selected_data = self.feature_engineer.select_features(
                    X, y, k=self.config.feature_selection_k
                )
                
                # Combine with target
                data = pd.concat([selected_data, data[target_column]], axis=1)
        
        logger.info(f"Prepared data shape: {data.shape}")
        return data
    
    def _split_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """Split data into train/validation/test sets."""
        # Time series split to maintain temporal order
        n_samples = len(data)
        
        # Calculate split indices
        test_size = int(n_samples * self.config.test_split)
        val_size = int(n_samples * self.config.validation_split)
        train_size = n_samples - test_size - val_size
        
        # Split data
        train_data = data.iloc[:train_size]
        val_data = data.iloc[train_size:train_size + val_size] if val_size > 0 else None
        test_data = data.iloc[train_size + val_size:] if test_size > 0 else None
        
        logger.info(f"Data split - Train: {len(train_data)}, Val: {len(val_data) if val_data is not None else 0}, Test: {len(test_data) if test_data is not None else 0}")
        
        return train_data, val_data, test_data
    
    async def _train_ensemble(self, train_data: pd.DataFrame, val_data: Optional[pd.DataFrame], target_column: str) -> Dict[str, Any]:
        """Train ensemble model."""
        logger.info("Training ensemble...")
        
        try:
            # Initialize ensemble
            ensemble_config = EnsembleConfig(
                ensemble_method='weighted_voting',
                confidence_threshold=0.7
            )
            self.ensemble = EnsembleCoordinator(ensemble_config)
            
            # Add trained models to ensemble
            for model_name, model in self.trained_models.items():
                self.ensemble.add_model(model_name, model)
            
            # Save ensemble
            ensemble_path = os.path.join(self.config.model_save_dir, "ensemble.pkl")
            self.ensemble.save_ensemble_state(ensemble_path)
            
            return {
                'success': True,
                'num_models': len(self.trained_models),
                'ensemble_method': ensemble_config.ensemble_method
            }
            
        except Exception as e:
            logger.error(f"Error training ensemble: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _evaluate_models(self, test_data: Optional[pd.DataFrame], target_column: str) -> Dict[str, Any]:
        """Evaluate all trained models."""
        if test_data is None or len(test_data) == 0:
            logger.warning("No test data available for evaluation")
            return {}
        
        logger.info("Evaluating models...")
        
        evaluation_results = {}
        
        # Evaluate individual models
        for model_name, model in self.trained_models.items():
            try:
                result = await self._evaluate_single_model(model, test_data, target_column, model_name)
                evaluation_results[model_name] = result
            except Exception as e:
                logger.error(f"Error evaluating {model_name}: {e}")
                evaluation_results[model_name] = {'error': str(e)}
        
        # Evaluate ensemble
        if self.ensemble is not None:
            try:
                result = await self._evaluate_ensemble(test_data, target_column)
                evaluation_results['ensemble'] = result
            except Exception as e:
                logger.error(f"Error evaluating ensemble: {e}")
                evaluation_results['ensemble'] = {'error': str(e)}
        
        return evaluation_results
    
    async def _evaluate_single_model(self, model, test_data: pd.DataFrame, target_column: str, model_name: str) -> Dict[str, Any]:
        """Evaluate a single model."""
        feature_cols = [col for col in test_data.columns if col != target_column and not col.startswith('target')]
        X_test = test_data[feature_cols]
        y_test = test_data[target_column].values
        
        # Get predictions
        predictions = []
        for i in range(len(X_test)):
            try:
                if hasattr(model, 'predict'):
                    pred = model.predict(X_test.iloc[[i]])
                    if isinstance(pred, dict):
                        pred_value = pred.get('signal', 0)
                    else:
                        pred_value = pred
                    predictions.append(pred_value)
                else:
                    predictions.append(0)
            except:
                predictions.append(0)
        
        # Calculate metrics
        predictions = np.array(predictions)
        
        # Convert to numerical if needed
        if any(isinstance(p, str) for p in predictions):
            pred_numerical = []
            for p in predictions:
                if p == 'BUY':
                    pred_numerical.append(1)
                elif p == 'SELL':
                    pred_numerical.append(-1)
                else:
                    pred_numerical.append(0)
            predictions = np.array(pred_numerical)
        
        # Metrics
        from sklearn.metrics import accuracy_score, mean_squared_error
        
        # Directional accuracy
        y_direction = np.sign(y_test)
        pred_direction = np.sign(predictions)
        directional_accuracy = accuracy_score(y_direction, pred_direction)
        
        # MSE
        mse = mean_squared_error(y_test, predictions)
        
        # Sharpe ratio (simplified)
        returns = predictions * y_test  # Strategy returns
        sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
        
        return {
            'directional_accuracy': directional_accuracy,
            'mse': mse,
            'sharpe_ratio': sharpe_ratio,
            'num_predictions': len(predictions)
        }
    
    async def _evaluate_ensemble(self, test_data: pd.DataFrame, target_column: str) -> Dict[str, Any]:
        """Evaluate ensemble model."""
        feature_cols = [col for col in test_data.columns if col != target_column and not col.startswith('target')]
        X_test = test_data[feature_cols]
        y_test = test_data[target_column].values
        
        # Get ensemble predictions
        predictions = []
        confidences = []
        
        for i in range(len(X_test)):
            try:
                pred_result = self.ensemble.predict(X_test.iloc[[i]])
                signal = pred_result.get('signal', 'HOLD')
                confidence = pred_result.get('confidence', 0)
                
                # Convert signal to numerical
                if signal == 'BUY':
                    predictions.append(1)
                elif signal == 'SELL':
                    predictions.append(-1)
                else:
                    predictions.append(0)
                
                confidences.append(confidence)
                
            except:
                predictions.append(0)
                confidences.append(0)
        
        predictions = np.array(predictions)
        confidences = np.array(confidences)
        
        # Calculate metrics
        from sklearn.metrics import accuracy_score, mean_squared_error
        
        # Directional accuracy
        y_direction = np.sign(y_test)
        pred_direction = np.sign(predictions)
        directional_accuracy = accuracy_score(y_direction, pred_direction)
        
        # MSE
        mse = mean_squared_error(y_test, predictions)
        
        # Sharpe ratio
        returns = predictions * y_test
        sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
        
        # Ensemble-specific metrics
        avg_confidence = np.mean(confidences)
        high_confidence_preds = np.sum(confidences > 0.7)
        
        return {
            'directional_accuracy': directional_accuracy,
            'mse': mse,
            'sharpe_ratio': sharpe_ratio,
            'avg_confidence': avg_confidence,
            'high_confidence_predictions': high_confidence_preds,
            'num_predictions': len(predictions)
        }
    
    def _log_model_to_mlflow(self, model_name: str, model, training_result: Dict[str, Any]):
        """Log model to MLflow."""
        try:
            with mlflow.start_run(nested=True, run_name=f"{model_name}_training"):
                # Log training metrics
                if 'training_history' in training_result:
                    history = training_result['training_history']
                    if isinstance(history, dict):
                        for key, value in history.items():
                            if isinstance(value, (int, float)):
                                mlflow.log_metric(f"{model_name}_{key}", value)
                            elif isinstance(value, list) and len(value) > 0:
                                for i, v in enumerate(value[-10:]):  # Log last 10 values
                                    if isinstance(v, (int, float)):
                                        mlflow.log_metric(f"{model_name}_{key}", v, step=i)
                
                # Log model artifact
                model_path = f"{model_name}_model"
                if hasattr(model, 'save_model'):
                    temp_path = f"/tmp/{model_name}_temp.pkl"
                    model.save_model(temp_path)
                    mlflow.log_artifact(temp_path, model_path)
                    os.remove(temp_path)
                
        except Exception as e:
            logger.warning(f"Failed to log {model_name} to MLflow: {e}")
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get summary of training results."""
        return {
            'config': self.config.__dict__,
            'trained_models': list(self.trained_models.keys()),
            'training_results': self.training_results,
            'ensemble_enabled': self.ensemble is not None
        }


# Utility context manager for MLflow
class nullcontext:
    """Null context manager for when MLflow is disabled."""
    def __enter__(self):
        return None
    def __exit__(self, *excinfo):
        pass