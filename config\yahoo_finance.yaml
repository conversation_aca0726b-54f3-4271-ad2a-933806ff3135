
# Yahoo Finance Data Source Configuration
data_sources:
  yahoo_finance:
    enabled: true
    rate_limit:
      requests_per_minute: 30
      burst_protection: true
      market_hours_only: true
    
    # IP Protection settings
    ip_protection:
      human_behavior: true
      random_delays: true
      weekend_enabled: false
      night_mode: true
      base_delay: 2.0
      max_delay: 10.0
    
    # Caching settings
    cache:
      quote_ttl: 5      # seconds
      bars_ttl: 300     # seconds
      info_ttl: 3600    # seconds
    
    # Supported intervals
    intervals:
      - "1m"
      - "5m" 
      - "15m"
      - "30m"
      - "1h"
      - "1d"
      - "1wk"
      - "1mo"
    
    # Supported periods
    periods:
      - "1d"
      - "5d"
      - "1mo"
      - "3mo"
      - "6mo"
      - "1y"
      - "2y"
      - "5y"
      - "10y"
      - "ytd"
      - "max"

# Update trading configuration to use Yahoo Finance
trading:
  data_source: "yahoo_finance"  # Switch from webull to yahoo_finance
  
  # Watchlist for Yahoo Finance (these symbols work well)
  watchlist:
    - "AAPL"   # Apple Inc.
    - "MSFT"   # Microsoft Corporation
    - "GOOGL"  # Alphabet Inc.
    - "TSLA"   # Tesla Inc.
    - "NVDA"   # NVIDIA Corporation
    - "AMZN"   # Amazon.com Inc.
    - "META"   # Meta Platforms Inc.
    - "NFLX"   # Netflix Inc.
    - "AMD"    # Advanced Micro Devices
    - "INTC"   # Intel Corporation
