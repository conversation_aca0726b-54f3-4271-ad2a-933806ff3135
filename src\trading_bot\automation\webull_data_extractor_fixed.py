# src/trading_bot/automation/webull_data_extractor_fixed.py
"""
Fixed Webull data extraction with proper Chrome options
"""

import time
import logging
from datetime import datetime
from typing import Dict, List, Optional
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebullDataExtractor:
    """Fixed data extraction for Webull"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def initialize_driver(self, headless=False):
        """Initialize Chrome driver with correct options"""
        try:
            # Create Chrome options
            options = uc.ChromeOptions()
            
            # Add arguments (these are correct)
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-setuid-sandbox')
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--start-maximized')
            
            if headless:
                options.add_argument('--headless')
            
            # Create driver with options
            self.driver = uc.Chrome(options=options, version_main=None)
            self.wait = WebDriverWait(self.driver, 10)
            
            # Additional stealth
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("✅ Driver initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize driver: {e}")
            return False
    
    def extract_stock_data(self, symbol: str) -> Dict:
        """Extract stock data from Webull"""
        try:
            # Navigate to stock page
            url = f"https://app.webull.com/stocks/{symbol}"
            logger.info(f"Navigating to {url}")
            self.driver.get(url)
            
            # Wait for page to load
            time.sleep(5)  # Give page time to load
            
            # Take screenshot for debugging
            screenshot_name = f"stock_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            self.driver.save_screenshot(screenshot_name)
            logger.info(f"Screenshot saved: {screenshot_name}")
            
            data = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'price': None,
                'change': None,
                'change_percent': None,
                'volume': None,
                'market_cap': None,
                'bid': None,
                'ask': None,
                'day_high': None,
                'day_low': None
            }
            
            # Get the entire page text
            page_text = self.driver.find_element(By.TAG_NAME, 'body').text
            
            # Extract price using regex patterns
            price_patterns = [
                r'\$(\d+\.\d{2})',  # Standard price format $123.45
                r'(\d+\.\d{2})\s*USD',  # Price with USD
                r'Last\s*\$?(\d+\.\d{2})',  # Last price
                r'Price\s*\$?(\d+\.\d{2})'  # Price label
            ]
            
            for pattern in price_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    # Find the most likely stock price (not too small, not too large)
                    for match in matches:
                        price = float(match)
                        if 0.01 < price < 10000:  # Reasonable stock price range
                            data['price'] = price
                            logger.info(f"Found price: ${price}")
                            break
                    if data['price']:
                        break
            
            # Extract volume
            volume_patterns = [
                r'Volume\s*([0-9,]+)',
                r'Vol\s*([0-9,]+)',
                r'Volume\s*([0-9.]+[MKB])',
                r'Vol\s*([0-9.]+[MKB])'
            ]
            
            for pattern in volume_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    volume_str = match.group(1)
                    data['volume'] = self._parse_volume(volume_str)
                    logger.info(f"Found volume: {data['volume']}")
                    break
            
            # Extract change percentage
            change_patterns = [
                r'([+-]?\d+\.\d{2}%)',
                r'([+-]?\d+\.\d{2})\s*\(',
                r'\(([+-]?\d+\.\d{2}%)\)'
            ]
            
            for pattern in change_patterns:
                match = re.search(pattern, page_text)
                if match:
                    change_str = match.group(1).replace('%', '')
                    data['change_percent'] = float(change_str)
                    logger.info(f"Found change: {data['change_percent']}%")
                    break
            
            # Extract market cap
            mcap_patterns = [
                r'Market\s*Cap\s*\$?([0-9.]+[TMKB])',
                r'Mkt\s*Cap\s*\$?([0-9.]+[TMKB])',
                r'Market\s*Capitalization\s*\$?([0-9.]+[TMKB])'
            ]
            
            for pattern in mcap_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    data['market_cap'] = match.group(1)
                    logger.info(f"Found market cap: {data['market_cap']}")
                    break
            
            # Try to extract bid/ask
            bid_ask_pattern = r'Bid\s*\$?(\d+\.\d{2}).*?Ask\s*\$?(\d+\.\d{2})'
            match = re.search(bid_ask_pattern, page_text, re.IGNORECASE | re.DOTALL)
            if match:
                data['bid'] = float(match.group(1))
                data['ask'] = float(match.group(2))
                logger.info(f"Found bid/ask: ${data['bid']}/${data['ask']}")
            
            # Extract day range
            range_pattern = r'(\d+\.\d{2})\s*-\s*(\d+\.\d{2})'
            matches = re.findall(range_pattern, page_text)
            if matches:
                # Usually the first range is the day range
                data['day_low'] = float(matches[0][0])
                data['day_high'] = float(matches[0][1])
                logger.info(f"Found day range: ${data['day_low']} - ${data['day_high']}")
            
            return data
            
        except Exception as e:
            logger.error(f"Failed to extract data for {symbol}: {e}")
            return {'symbol': symbol, 'error': str(e)}
    
    def _parse_volume(self, volume_str: str) -> int:
        """Parse volume string with K/M/B suffixes"""
        volume_str = volume_str.replace(',', '')
        
        if 'K' in volume_str.upper():
            return int(float(volume_str.upper().replace('K', '')) * 1_000)
        elif 'M' in volume_str.upper():
            return int(float(volume_str.upper().replace('M', '')) * 1_000_000)
        elif 'B' in volume_str.upper():
            return int(float(volume_str.upper().replace('B', '')) * 1_000_000_000)
        elif 'T' in volume_str.upper():
            return int(float(volume_str.upper().replace('T', '')) * 1_000_000_000_000)
        else:
            try:
                return int(float(volume_str))
            except:
                return 0
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            logger.info("✅ Driver closed")

def test_extraction():
    """Test the data extraction"""
    extractor = WebullDataExtractor()
    
    try:
        print("\n🔍 Testing Webull Data Extraction")
        print("=" * 50)
        
        if not extractor.initialize_driver(headless=False):
            print("❌ Failed to initialize driver")
            return
        
        print("✅ Driver initialized successfully\n")
        
        # Test with popular stocks
        symbols = ['AAPL', 'MSFT', 'TSLA']
        
        results = []
        
        for symbol in symbols:
            print(f"📊 Extracting data for {symbol}...")
            data = extractor.extract_stock_data(symbol)
            results.append(data)
            
            # Display results
            print(f"\n   Results for {symbol}:")
            print(f"   ├─ Price: ${data.get('price', 'Not found')}")
            print(f"   ├─ Change: {data.get('change_percent', 'Not found')}%")
            print(f"   ├─ Volume: {data.get('volume', 'Not found'):,}" if data.get('volume') else f"   ├─ Volume: Not found")
            print(f"   ├─ Market Cap: {data.get('market_cap', 'Not found')}")
            print(f"   ├─ Bid/Ask: ${data.get('bid', 'N/A')}/{data.get('ask', 'N/A')}")
            print(f"   └─ Day Range: ${data.get('day_low', 'N/A')} - ${data.get('day_high', 'N/A')}")
            
            if 'error' in data:
                print(f"   ⚠️ Error: {data['error']}")
            
            print()
            time.sleep(3)  # Delay between requests
        
        # Summary
        print("\n📈 EXTRACTION SUMMARY")
        print("=" * 50)
        
        successful = sum(1 for r in results if r.get('price') is not None)
        print(f"✅ Successfully extracted: {successful}/{len(symbols)} stocks")
        
        if successful > 0:
            print("\n✨ Data extraction is working!")
            print("You can now use this to feed data to your ML models.")
        else:
            print("\n⚠️ No price data extracted. Check the screenshots for debugging.")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Test stopped by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🧹 Cleaning up...")
        extractor.cleanup()
        print("✅ Test complete!\n")

if __name__ == "__main__":
    test_extraction()