"""Federal Reserve and economic indicators data fetcher."""

import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import pandas as pd
import numpy as np

from ...core.config import settings
from ...core.logger import get_logger
from ...utils.cache import cache_manager
from ...utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


@dataclass
class EconomicIndicator:
    """Economic indicator data structure."""
    series_id: str
    name: str
    value: float
    date: datetime
    frequency: str  # 'daily', 'weekly', 'monthly', 'quarterly'
    unit: str
    source: str
    category: str  # 'monetary', 'employment', 'inflation', 'growth', etc.


@dataclass
class EconomicSnapshot:
    """Economic snapshot with key indicators."""
    timestamp: datetime
    gdp_growth: Optional[float]
    inflation_rate: Optional[float]
    unemployment_rate: Optional[float]
    fed_funds_rate: Optional[float]
    yield_curve_10y2y: Optional[float]
    consumer_confidence: Optional[float]
    pmi_manufacturing: Optional[float]
    pmi_services: Optional[float]
    retail_sales_growth: Optional[float]
    housing_starts: Optional[float]


class IndicatorsFetcher:
    """Fetch economic indicators from Federal Reserve and other sources."""
    
    def __init__(self):
        self.rate_limiter = RateLimiter(max_requests=120, time_window=60)  # FRED API limit
        self.fred_base_url = "https://api.stlouisfed.org/fred"
        self.indicator_mappings = self._load_indicator_mappings()
        
    async def fetch_economic_snapshot(self) -> EconomicSnapshot:
        """
        Fetch current economic snapshot with key indicators.
        
        Returns:
            EconomicSnapshot with latest economic data
        """
        try:
            # Key indicators to fetch
            key_indicators = [
                'GDPC1',      # Real GDP
                'CPIAUCSL',   # CPI All Urban Consumers
                'UNRATE',     # Unemployment Rate
                'FEDFUNDS',   # Federal Funds Rate
                'GS10',       # 10-Year Treasury
                'GS2',        # 2-Year Treasury
                'UMCSENT',    # Consumer Sentiment
                'MANEMP',     # Manufacturing Employment
                'HOUST',      # Housing Starts
                'RSXFS'       # Retail Sales
            ]
            
            # Fetch all indicators concurrently
            tasks = []
            for series_id in key_indicators:
                tasks.append(self._fetch_fred_series(series_id, limit=1))
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            indicator_data = {}
            for i, result in enumerate(results):
                if isinstance(result, list) and result:
                    indicator_data[key_indicators[i]] = result[0]
                elif isinstance(result, Exception):
                    logger.warning(f"Failed to fetch {key_indicators[i]}: {result}")
            
            # Calculate derived indicators
            yield_curve_10y2y = None
            if 'GS10' in indicator_data and 'GS2' in indicator_data:
                yield_curve_10y2y = indicator_data['GS10'].value - indicator_data['GS2'].value
            
            # Calculate GDP growth (would need historical data)
            gdp_growth = None
            if 'GDPC1' in indicator_data:
                # Simplified - would need YoY calculation
                gdp_growth = 2.0  # Placeholder
            
            # Calculate inflation rate (would need MoM/YoY calculation)
            inflation_rate = None
            if 'CPIAUCSL' in indicator_data:
                # Simplified - would need YoY calculation
                inflation_rate = 3.2  # Placeholder
            
            return EconomicSnapshot(
                timestamp=datetime.utcnow(),
                gdp_growth=gdp_growth,
                inflation_rate=inflation_rate,
                unemployment_rate=indicator_data.get('UNRATE', {}).value if 'UNRATE' in indicator_data else None,
                fed_funds_rate=indicator_data.get('FEDFUNDS', {}).value if 'FEDFUNDS' in indicator_data else None,
                yield_curve_10y2y=yield_curve_10y2y,
                consumer_confidence=indicator_data.get('UMCSENT', {}).value if 'UMCSENT' in indicator_data else None,
                pmi_manufacturing=None,  # Would need ISM data
                pmi_services=None,       # Would need ISM data
                retail_sales_growth=None, # Would need MoM calculation
                housing_starts=indicator_data.get('HOUST', {}).value if 'HOUST' in indicator_data else None
            )
            
        except Exception as e:
            logger.error(f"Error fetching economic snapshot: {e}")
            return self._empty_snapshot()
    
    async def fetch_indicator_history(
        self,
        series_id: str,
        start_date: datetime = None,
        end_date: datetime = None,
        limit: int = 100
    ) -> List[EconomicIndicator]:
        """
        Fetch historical data for a specific economic indicator.
        
        Args:
            series_id: FRED series ID
            start_date: Start date for data
            end_date: End date for data
            limit: Maximum number of observations
            
        Returns:
            List of economic indicators
        """
        try:
            return await self._fetch_fred_series(
                series_id, start_date, end_date, limit
            )
        except Exception as e:
            logger.error(f"Error fetching indicator history for {series_id}: {e}")
            return []
    
    async def fetch_multiple_indicators(
        self,
        series_ids: List[str],
        start_date: datetime = None,
        limit: int = 50
    ) -> Dict[str, List[EconomicIndicator]]:
        """
        Fetch multiple economic indicators concurrently.
        
        Args:
            series_ids: List of FRED series IDs
            start_date: Start date for data
            limit: Maximum observations per series
            
        Returns:
            Dictionary mapping series IDs to indicator lists
        """
        try:
            tasks = []
            for series_id in series_ids:
                tasks.append(self._fetch_fred_series(series_id, start_date, limit=limit))
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            indicator_data = {}
            for i, result in enumerate(results):
                if isinstance(result, list):
                    indicator_data[series_ids[i]] = result
                elif isinstance(result, Exception):
                    logger.warning(f"Failed to fetch {series_ids[i]}: {result}")
                    indicator_data[series_ids[i]] = []
            
            return indicator_data
            
        except Exception as e:
            logger.error(f"Error fetching multiple indicators: {e}")
            return {}
    
    async def _fetch_fred_series(
        self,
        series_id: str,
        start_date: datetime = None,
        end_date: datetime = None,
        limit: int = 100
    ) -> List[EconomicIndicator]:
        """Fetch data from FRED API."""
        if not hasattr(settings, 'fred_api_key'):
            logger.warning("FRED API key not configured")
            return []
        
        await self.rate_limiter.acquire()
        
        try:
            # First get series info
            series_info = await self._get_series_info(series_id)
            if not series_info:
                return []
            
            # Then get observations
            url = f"{self.fred_base_url}/series/observations"
            params = {
                'series_id': series_id,
                'api_key': settings.fred_api_key,
                'file_type': 'json',
                'limit': limit,
                'sort_order': 'desc'  # Most recent first
            }
            
            if start_date:
                params['observation_start'] = start_date.strftime('%Y-%m-%d')
            if end_date:
                params['observation_end'] = end_date.strftime('%Y-%m-%d')
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    data = await response.json()
            
            if 'observations' not in data:
                logger.warning(f"No observations found for series {series_id}")
                return []
            
            indicators = []
            for obs in data['observations']:
                try:
                    # Skip missing values
                    if obs['value'] == '.':
                        continue
                    
                    indicator = EconomicIndicator(
                        series_id=series_id,
                        name=series_info['title'],
                        value=float(obs['value']),
                        date=datetime.strptime(obs['date'], '%Y-%m-%d'),
                        frequency=series_info['frequency'],
                        unit=series_info['units'],
                        source='FRED',
                        category=self._get_indicator_category(series_id)
                    )
                    indicators.append(indicator)
                except (ValueError, KeyError) as e:
                    logger.warning(f"Error parsing observation for {series_id}: {e}")
                    continue
            
            return indicators
            
        except Exception as e:
            logger.error(f"Error fetching FRED series {series_id}: {e}")
            return []
    
    async def _get_series_info(self, series_id: str) -> Optional[Dict[str, Any]]:
        """Get series metadata from FRED."""
        try:
            url = f"{self.fred_base_url}/series"
            params = {
                'series_id': series_id,
                'api_key': settings.fred_api_key,
                'file_type': 'json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    data = await response.json()
            
            if 'seriess' in data and data['seriess']:
                return data['seriess'][0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting series info for {series_id}: {e}")
            return None
    
    def _get_indicator_category(self, series_id: str) -> str:
        """Get category for an indicator series."""
        categories = {
            'monetary': ['FEDFUNDS', 'GS10', 'GS2', 'GS5', 'TB3MS', 'DGS30'],
            'employment': ['UNRATE', 'PAYEMS', 'CIVPART', 'EMRATIO', 'MANEMP'],
            'inflation': ['CPIAUCSL', 'CPILFESL', 'PCEPI', 'PCEPILFE'],
            'growth': ['GDPC1', 'GDPPOT', 'NYGDPMKTPCDWLD'],
            'consumer': ['UMCSENT', 'RSXFS', 'PCE', 'PSAVERT'],
            'housing': ['HOUST', 'HSNGMI', 'MORTGAGE30US'],
            'manufacturing': ['INDPRO', 'CAPUTLG2211A2SQ', 'NEWORDER'],
            'trade': ['BOPGSTB', 'IMPGS', 'EXPGS']
        }
        
        for category, series_list in categories.items():
            if series_id in series_list:
                return category
        
        return 'other'
    
    def _load_indicator_mappings(self) -> Dict[str, str]:
        """Load mappings of indicator names to FRED series IDs."""
        return {
            # Monetary Policy
            'fed_funds_rate': 'FEDFUNDS',
            'treasury_10y': 'GS10',
            'treasury_2y': 'GS2',
            'treasury_5y': 'GS5',
            'treasury_30y': 'DGS30',
            
            # Employment
            'unemployment_rate': 'UNRATE',
            'nonfarm_payrolls': 'PAYEMS',
            'labor_participation': 'CIVPART',
            'employment_ratio': 'EMRATIO',
            
            # Inflation
            'cpi_all_items': 'CPIAUCSL',
            'cpi_core': 'CPILFESL',
            'pce_price_index': 'PCEPI',
            'pce_core': 'PCEPILFE',
            
            # Growth
            'real_gdp': 'GDPC1',
            'potential_gdp': 'GDPPOT',
            'industrial_production': 'INDPRO',
            
            # Consumer
            'consumer_sentiment': 'UMCSENT',
            'retail_sales': 'RSXFS',
            'personal_consumption': 'PCE',
            'personal_saving_rate': 'PSAVERT',
            
            # Housing
            'housing_starts': 'HOUST',
            'housing_market_index': 'HSNGMI',
            'mortgage_30y': 'MORTGAGE30US',
            
            # Trade
            'trade_balance': 'BOPGSTB',
            'imports': 'IMPGS',
            'exports': 'EXPGS'
        }
    
    def get_series_id(self, indicator_name: str) -> Optional[str]:
        """Get FRED series ID for an indicator name."""
        return self.indicator_mappings.get(indicator_name)
    
    def _empty_snapshot(self) -> EconomicSnapshot:
        """Return empty economic snapshot."""
        return EconomicSnapshot(
            timestamp=datetime.utcnow(),
            gdp_growth=None,
            inflation_rate=None,
            unemployment_rate=None,
            fed_funds_rate=None,
            yield_curve_10y2y=None,
            consumer_confidence=None,
            pmi_manufacturing=None,
            pmi_services=None,
            retail_sales_growth=None,
            housing_starts=None
        )
    
    async def calculate_indicator_changes(
        self,
        series_id: str,
        periods: List[int] = [1, 3, 6, 12]
    ) -> Dict[str, float]:
        """
        Calculate period-over-period changes for an indicator.
        
        Args:
            series_id: FRED series ID
            periods: List of periods to calculate changes for
            
        Returns:
            Dictionary with period changes
        """
        try:
            # Fetch enough data for calculations
            max_periods = max(periods) + 1
            indicators = await self._fetch_fred_series(series_id, limit=max_periods)
            
            if len(indicators) < 2:
                return {}
            
            # Sort by date (most recent first)
            indicators.sort(key=lambda x: x.date, reverse=True)
            
            changes = {}
            current_value = indicators[0].value
            
            for period in periods:
                if period < len(indicators):
                    past_value = indicators[period].value
                    if past_value != 0:
                        change = ((current_value - past_value) / past_value) * 100
                        changes[f'{period}_period_change'] = change
            
            return changes
            
        except Exception as e:
            logger.error(f"Error calculating indicator changes for {series_id}: {e}")
            return {}
