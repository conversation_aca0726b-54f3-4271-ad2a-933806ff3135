<html translate="no"><head><meta http-equiv="origin-trial" content="A7vZI3v+Gz7JfuRolKNM4Aff6zaGuT7X0mf3wtoZTnKv6497cVMnhy03KDqX7kBz/q/iidW7srW31oQbBt4VhgoAAACUeyJvcmlnaW4iOiJodHRwczovL3d3dy5nb29nbGUuY29tOjQ0MyIsImZlYXR1cmUiOiJEaXNhYmxlVGhpcmRQYXJ0eVN0b3JhZ2VQYXJ0aXRpb25pbmczIiwiZXhwaXJ5IjoxNzU3OTgwODAwLCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ=="><meta http-equiv="Content-type" content="text/html; charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="google" content="notranslate"><link rel="shortcut icon" href="https://wbstatic.webullfintech.com/v0/web/favicon.ico"><link rel="manifest" href="/static/manifest.json"><script async="" src="https://www.google-analytics.com/analytics.js"></script><script type="text/javascript" async="" charset="utf-8" src="https://www.gstatic.com/recaptcha/releases/_cn5mBoBXIA0_T7xBjxkUqUA/recaptcha__en.js" crossorigin="anonymous" integrity="sha384-/ILBq7Rf7jA0CLWzfC5SIIkhImlHzZiEPgrCxq+PnZmvQGn/khw81itGKoHNsX5D"></script><script src="https://www.google.com/recaptcha/enterprise.js?render=6LdTw50kAAAAAK9fgRtNjcbLQTUc4Bb_O1pTDUJY" defer="defer"></script><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><meta name="keywords" content="web trading, web trading platform, web trade, stock trading online, stock trading platform, stock trading website"><meta name="description" content="Webull web trading, No need to download or install anything! Discover new opportunities while you track, manage, and trade from any internet-enabled browser."><title>Webull Web Trading, Trade directly on the web platform without any downloads - Webull</title><script>!function(){var o=window.location.search,e=o.indexOf("?");if(-1!==e){var a={};o.slice(e+1).split("&").map(o=>{var e=o.split("=");a[e[0]]=decodeURIComponent(e[1])});var i=a.source;i&&window.localStorage&&window.localStorage.setItem("source",i)}}();</script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-YVD276XF4X"></script><script>window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments)}gtag('js',new Date());gtag('config','G-YVD276XF4X');</script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/message.6db9748cd54629245eb0.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/sentry.550a165752bde9dca732.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/core-js.89207bc21fb56e1cc7d7.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/libraries.87ebb51db3596adba4fc.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/floating-ui.5cfea3f50f85cf0e043b.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/tanstack.9a786a45ed25eaacd95e.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/pixi.js.e43ffa66af9109a5072a.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/dexie.a63d840c5ed6ab601bc7.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/libraries1.87b2460b7b9acac4b867.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/moment.4336d32c307d1f3d4270.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/d3.ed8d1a723bb01e2270ba.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/swiper.003a6a532e32d36718cf.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/protobufjs.6d32f4f767a6b1b5976b.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/immutable.b5ab54ec01788bd686a7.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/webull-pine-ws.31fde9f845ee53ce43d8.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/webull-core.1d17c14abe1567b3c7ea.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/icons.ab446cae484658f6d1f0.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/wbui-pc.5536551bdc3ce00eb00c.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/webull-captcha.ef3f0a79e7ea240e4e26.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/account.84b23d0d547591ff98e8.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/webull-libs.c3abee0f446b19034d53.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/function.a17072c4876a5383398e.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/assets.891b242f23ac359250cf.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/libraries2.e15d2eab03defe26d62e.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/trade.0f91d1e93ddb133bb07e.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/ticker.aea849d9499beee529dc.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/webull-ui.a67e5da5950b4e2e90ee.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/market.b28ec0c33ce4f53c4b61.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/screener.b7b850c8af6f3c572edb.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/quote.6eba35d423463278ff72.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/watchlist.22ffe50872b4d9f55cb7.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/pineCode.6ae81a8bcca5ea7b2880.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/common.409840d592d0357d3d77.js"></script><script defer="defer" src="https://wbstatic.webullfintech.com/v1/webquotes-g/app.5691696b12e63a738abd.js"></script><style>.not-support-body {
  position: relative;
  height: 100vh;
  background: url(https://wbstatic.webullfintech.com/v1/webquotes-g/3f3b606457f5d5404c1a.jpg) no-repeat;
  background-size: cover;
  min-width: 1200px;
  background-position: center;
}
.not-support-body .logo {
  position: absolute;
  top: 25px;
  left: 35px;
  height: 45px;
  width: 232px;
  background-image: url(data:image/png;base64,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);
  background-position: 0 50%;
  background-repeat: no-repeat;
  background-size: 100% auto;
}
.not-support-body .logo.zh {
  background-image: url(data:image/png;base64,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);
}
.not-support-body .content {
  position: absolute;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  width: 1030px;
  color: #EEEEEE;
}
.not-support-body .content .txt {
  font-size: 30px;
  line-height: 45px;
  margin-top: 30px;
  letter-spacing: 1px;
}
.not-support-body .content .link {
  display: inline-block;
  color: #EEEEEE;
  border-bottom: 1px dashed #EEEEEE;
}
.not-support-body .content .link:hover {
  opacity: 0.7;
}

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL2Fzc2V0cy9zdHlsZS9ub1N1cHBvcnQubGVzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLDZEQUFBO0VBQ0Esc0JBQUE7RUFDQSxpQkFBQTtFQUNBLDJCQUFBO0FBQ0Y7QUFQQTtFQVNJLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtFQUNBLHlEQUFBO0VBQ0EsMEJBQUE7RUFDQSw0QkFBQTtFQUNBLDBCQUFBO0FBQ0o7QUFBSTtFQUNFLHlEQUFBO0FBRU47QUFyQkE7RUF3Qkksa0JBQUE7RUFDQSxTQUFBO0VBQ0EsUUFBQTtFQUNBLGdDQUFBO0VBQ0EsYUFBQTtFQUNBLGNBQUE7QUFBSjtBQTdCQTtFQWtDTSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FBRk47QUFuQ0E7RUF5Q00scUJBQUE7RUFDQSxjQUFBO0VBQ0EsaUNBQUE7QUFITjtBQUlNO0VBQ0UsWUFBQTtBQUZSIiwic291cmNlUm9vdCI6IiJ9 */</style><style>@font-face {
  font-family: "iconfont"; /* Project id 1684775 */
  src: url(https://wbstatic.webullfintech.com/v1/webquotes-g/62719812bdd1f49d08c6.woff2?t=1693881480584) format('woff2'),
       url(https://wbstatic.webullfintech.com/v1/webquotes-g/401dd93ee449c13bf1d9.woff?t=1693881480584) format('woff'),
       url(https://wbstatic.webullfintech.com/v1/webquotes-g/e870ee77cd094a65af50.ttf?t=1693881480584) format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.webull-Option-traders:before {
  content: "\e784";
}

.webull-watchlist_:before {
  content: "\e782";
}

.webull-feedback_:before {
  content: "\e783";
}

.webull-a-shanchu_16161:before {
  content: "\e781";
}

.webull-close1:before {
  content: "\e822";
}

.webull-a-zhenyan_2424:before {
  content: "\e780";
}

.webull-a-biyan_2424:before {
  content: "\e77f";
}

.webull-a-Refresh_2424:before {
  content: "\e77b";
}

.webull-a-Singlechoice1_2424:before {
  content: "\e77d";
}

.webull-a-shanchu_1616:before {
  content: "\e77e";
}

.webull-a-fanhui_2424:before {
  content: "\e77c";
}

.webull-xiazai_16_16:before {
  content: "\e77a";
}

.webull-a-computer_3232:before {
  content: "\e778";
}

.webull-a-QRcode_3232:before {
  content: "\e779";
}

.webull-fire_2424:before {
  content: "\e79d";
}

.webull-Alert-2020:before {
  content: "\e802";
}

.webull-xingzhuangjiehe1:before {
  content: "\e776";
}

.webull-xingzhuangjiehe:before {
  content: "\e775";
}

.webull-caps:before {
  content: "\e774";
}

.webull-Hot26_26:before {
  content: "\e773";
}

.webull-Exit-1-24_24:before {
  content: "\e772";
}

.webull-gengduo:before {
  content: "\e771";
}

.webull-huobiduihuan24_24:before {
  content: "\e770";
}

.webull-collapse_right:before {
  content: "\e76f";
}

.webull-collapse_left:before {
  content: "\e76e";
}

.webull-ticker_setting:before {
  content: "\e76d";
}

.webull-slider:before {
  content: "\e76c";
}

.webull-finance_text:before {
  content: "\e76a";
}

.webull-finance_table:before {
  content: "\e76b";
}

.webull-jiajian_16_16:before {
  content: "\e769";
}

.webull-line_break24_24:before {
  content: "\e767";
}

.webull-Aera_break24_24:before {
  content: "\e768";
}

.webull-code:before {
  content: "\e766";
}

.webull-qiepian5:before {
  content: "\e765";
}

.webull-qiepian4:before {
  content: "\e764";
}

.webull-chabeifen8:before {
  content: "\e763";
}

.webull-trade_16_16:before {
  content: "\e762";
}

.webull-Hollow_Circles_24_24:before {
  content: "\e75e";
}

.webull-Cross_24_24:before {
  content: "\e75f";
}

.webull-Histogram_24_24:before {
  content: "\e760";
}

.webull-StepLine_24_24:before {
  content: "\e761";
}

.webull-Circles_24_24:before {
  content: "\e777";
}

.webull-Quasi_14_14:before {
  content: "\e75d";
}

.webull-remind:before {
  content: "\e75c";
}

.webull-addfailed:before {
  content: "\e75b";
}

.webull-qiepian2:before {
  content: "\e759";
}

.webull-qiepian3:before {
  content: "\e75a";
}

.webull-qiepiandelete:before {
  content: "\e758";
}

.webull-list1:before {
  content: "\e757";
}

.webull-qiepian1:before {
  content: "\e756";
}

.webull-qiepian:before {
  content: "\e755";
}

.webull-question__:before {
  content: "\e754";
}

.webull-s1:before {
  content: "\e753";
}

.webull-s:before {
  content: "\e752";
}

.webull-editor-big__:before {
  content: "\e751";
}

.webull-paixu:before {
  content: "\e747";
}

.webull-setting__:before {
  content: "\e746";
}

.webull-chart_:before {
  content: "\e732";
}

.webull-chart_1:before {
  content: "\e733";
}

.webull-chart_2:before {
  content: "\e734";
}

.webull-chart-vertical_:before {
  content: "\e735";
}

.webull-chart_3:before {
  content: "\e736";
}

.webull-chart-vertical_1:before {
  content: "\e737";
}

.webull-chart-vertical_2:before {
  content: "\e738";
}

.webull-Amount__:before {
  content: "\e739";
}

.webull-alert-settings-small__:before {
  content: "\e73a";
}

.webull-chart_4:before {
  content: "\e73b";
}

.webull-edit-indicator__:before {
  content: "\e73c";
}

.webull-Follow-price__:before {
  content: "\e73d";
}

.webull-memo_:before {
  content: "\e73e";
}

.webull-Manual-input:before {
  content: "\e73f";
}

.webull-chart_5:before {
  content: "\e741";
}

.webull-Quantity__:before {
  content: "\e742";
}

.webull-cloning_:before {
  content: "\e743";
}

.webull-Percentage__:before {
  content: "\e744";
}

.webull-chicang_:before {
  content: "\e745";
}

.webull-Message__1:before {
  content: "\e730";
}

.webull-Alert-big__:before {
  content: "\e731";
}

.webull-stocks__:before {
  content: "\e72f";
}

.webull-Compare_:before {
  content: "\e72c";
}

.webull-Drawing_:before {
  content: "\e72d";
}

.webull-Indicator_:before {
  content: "\e72e";
}

.webull-chartsetting:before {
  content: "\e672";
}

.webull-Drawing:before {
  content: "\e673";
}

.webull-Indicator:before {
  content: "\e674";
}

.webull-Compare:before {
  content: "\e675";
}

.webull-Aera:before {
  content: "\e676";
}

.webull-ColoredBars:before {
  content: "\e67f";
}

.webull-Bars:before {
  content: "\e680";
}

.webull-Candles:before {
  content: "\e681";
}

.webull-line:before {
  content: "\e682";
}

.webull-HollowCandles:before {
  content: "\e683";
}

.webull-HeikinAshi:before {
  content: "\e684";
}

.webull-Ray:before {
  content: "\e677";
}

.webull-Horizentalline:before {
  content: "\e678";
}

.webull-VertivalLine:before {
  content: "\e679";
}

.webull-Parallel:before {
  content: "\e67a";
}

.webull-Polyline:before {
  content: "\e67b";
}

.webull-TrendLine:before {
  content: "\e67c";
}

.webull-Extended:before {
  content: "\e67d";
}

.webull-chart1x1:before {
  content: "\e67e";
}

.webull-chart1x2:before {
  content: "\e685";
}

.webull-chart3x1:before {
  content: "\e686";
}

.webull-chart1x3:before {
  content: "\e687";
}

.webull-chart2x2:before {
  content: "\e688";
}

.webull-chart3x3:before {
  content: "\e689";
}

.webull-chart2x1:before {
  content: "\e68a";
}

.webull-chart2x3:before {
  content: "\e68b";
}

.webull-chart3x2:before {
  content: "\e68c";
}

.webull-narrow:before {
  content: "\e68d";
}

.webull-amplification:before {
  content: "\e68e";
}

.webull-cloning:before {
  content: "\e68f";
}

.webull-Memo:before {
  content: "\e690";
}

.webull-Alert:before {
  content: "\e691";
}

.webull-Add_protfolio:before {
  content: "\e692";
}

.webull-menu:before {
  content: "\e693";
}

.webull-Trash:before {
  content: "\e694";
}

.webull-Lock:before {
  content: "\e695";
}

.webull-Setting:before {
  content: "\e696";
}

.webull-arrow_left_:before {
  content: "\e697";
}

.webull-arrow_on_:before {
  content: "\e698";
}

.webull-arrow_under_:before {
  content: "\e699";
}

.webull-arrow_right_:before {
  content: "\e69a";
}

.webull-Big_arrow_left_:before {
  content: "\e69b";
}

.webull-Big_arrow_right_:before {
  content: "\e69c";
}

.webull-add_:before {
  content: "\e69d";
}

.webull-cancel_:before {
  content: "\e69e";
}

.webull-check_:before {
  content: "\e69f";
}

.webull-Minus_:before {
  content: "\e6a0";
}

.webull-Plus_:before {
  content: "\e6a1";
}

.webull-Sorting_:before {
  content: "\e6a2";
}

.webull-Fallback_Copy:before {
  content: "\e6a3";
}

.webull-Account_:before {
  content: "\e6a4";
}

.webull-Stocks_:before {
  content: "\e6a5";
}

.webull-Watchlist_:before {
  content: "\e6a6";
}

.webull-Trade_:before {
  content: "\e6a7";
}

.webull-Screener_:before {
  content: "\e6a8";
}

.webull-Markets_:before {
  content: "\e6a9";
}

.webull-LOGO:before {
  content: "\e6aa";
}

.webull-email_:before {
  content: "\e6ab";
}

.webull-Message_:before {
  content: "\e6ac";
}

.webull-More_:before {
  content: "\e6ad";
}

.webull-Trademessages:before {
  content: "\e6b6";
}

.webull-Middle_arrow_under_:before {
  content: "\e6ae";
}

.webull-Middle_arrow_left_:before {
  content: "\e6af";
}

.webull-Middle_arrow_on_:before {
  content: "\e6b0";
}

.webull-Middle_arrow_right_:before {
  content: "\e6b1";
}

.webull-Tool-box_:before {
  content: "\e6b2";
}

.webull-Refresh_:before {
  content: "\e6b3";
}

.webull-Default_two_Co:before {
  content: "\e6b4";
}

.webull-Default_one_:before {
  content: "\e6b5";
}

.webull-Customer:before {
  content: "\e6ec";
}

.webull-chart4x1:before {
  content: "\e6c8";
}

.webull-chart1x4:before {
  content: "\e6c9";
}

.webull-W_1:before {
  content: "\e6b7";
}

.webull-D_1:before {
  content: "\e6b8";
}

.webull-H_4:before {
  content: "\e6b9";
}

.webull-MAX_:before {
  content: "\e6ba";
}

.webull-Y_5:before {
  content: "\e6bb";
}

.webull-Y_1:before {
  content: "\e6bc";
}

.webull-H_1:before {
  content: "\e6bd";
}

.webull-H_2:before {
  content: "\e6be";
}

.webull-M_3:before {
  content: "\e6bf";
}

.webull-M_1:before {
  content: "\e6c0";
}

.webull-m_5:before {
  content: "\e6c1";
}

.webull-m_15:before {
  content: "\e6c2";
}

.webull-M_6:before {
  content: "\e6c3";
}

.webull-m_30:before {
  content: "\e6c4";
}

.webull-m_1:before {
  content: "\e6c5";
}

.webull-Sorting_bottom_:before {
  content: "\e6c6";
}

.webull-Sorting_top_:before {
  content: "\e6c7";
}

.webull-Big_arrow_left_1:before {
  content: "\e6ca";
}

.webull-Big_arrow_right_1:before {
  content: "\e6cb";
}

.webull-Save_:before {
  content: "\e6cc";
}

.webull-hourglass_:before {
  content: "\e6cd";
}

.webull-lock_off_:before {
  content: "\e6ce";
}

.webull-lock_on_:before {
  content: "\e6cf";
}

.webull-little-Refresh_1:before {
  content: "\e6d0";
}

.webull-warning_:before {
  content: "\e6d1";
}

.webull-attention_:before {
  content: "\e6d2";
}

.webull-call_:before {
  content: "\e6d3";
}

.webull-after_hours_:before {
  content: "\e6d4";
}

.webull-upcoming_:before {
  content: "\e6d5";
}

.webull-action_menu_:before {
  content: "\e6d6";
}

.webull-earrings_:before {
  content: "\e6d7";
}

.webull-Widgets_Settings_:before {
  content: "\e6d8";
}

.webull-single_select_:before {
  content: "\e6d9";
}

.webull-multi_select_:before {
  content: "\e6da";
}

.webull-single_selected_:before {
  content: "\e6db";
}

.webull-multi_selected_:before {
  content: "\e6dc";
}

.webull-circle_add_:before {
  content: "\e6dd";
}

.webull-star_:before {
  content: "\e6de";
}

.webull-circle_added_:before {
  content: "\e6df";
}

.webull-sell_mkt_:before {
  content: "\e6e0";
}

.webull-buy_mkt_:before {
  content: "\e6e1";
}

.webull-More_1:before {
  content: "\e6e2";
}

.webull-Fib-Retracement:before {
  content: "\e6e3";
}

.webull-Fib-Time-Zone:before {
  content: "\e6e4";
}

.webull-Fib-Extensiont:before {
  content: "\e6e5";
}

.webull-nonetwork:before {
  content: "\e6e6";
}

.webull-Q_:before {
  content: "\e6e7";
}

.webull-hourglass_1:before {
  content: "\e6e8";
}

.webull-edit_:before {
  content: "\e6f3";
}

.webull-card_:before {
  content: "\e6f4";
}

.webull-done_:before {
  content: "\e6f6";
}

.webull-risk_:before {
  content: "\e6f5";
}

.webull-more_:before {
  content: "\e6e9";
}

.webull-paper_trading_:before {
  content: "\e6ea";
}

.webull-paper_account_:before {
  content: "\e6eb";
}

.webull-input_price_:before {
  content: "\e6ed";
}

.webull-follow_price_:before {
  content: "\e6ee";
}

.webull-percentage_:before {
  content: "\e6ef";
}

.webull-number_:before {
  content: "\e6f0";
}

.webull-arrow_under_trade_:before {
  content: "\e6f1";
}

.webull-arrow_on_trade_:before {
  content: "\e6f2";
}

.webull-flag_:before {
  content: "\e6f7";
}

.webull-filled_:before {
  content: "\e6f8";
}

.webull-news:before {
  content: "\e712";
}

.webull-search_:before {
  content: "\e6f9";
}

.webull-edit-indicator_:before {
  content: "\e6fa";
}

.webull-webull_:before {
  content: "\e6fb";
}

.webull-info_:before {
  content: "\e6fc";
}

.webull-webull-Add_protfolio_select:before {
  content: "\e6fd";
}

.webull-facebookicon:before {
  content: "\e66e";
}

.webull-rise__:before {
  content: "\e701";
}

.webull-ico-email:before {
  content: "\e60c";
}

.webull-fall__:before {
  content: "\e706";
}

.webull-icon-:before {
  content: "\e659";
}

.webull-Alert_:before {
  content: "\e6fe";
}

.webull-guge:before {
  content: "\e61d";
}

.webull-down_:before {
  content: "\e6ff";
}

.webull-shouji:before {
  content: "\e666";
}

.webull-up_:before {
  content: "\e700";
}

.webull-nav_setting:before {
  content: "\e601";
}

.webull-down_1:before {
  content: "\e702";
}

.webull-header_search:before {
  content: "\e603";
}

.webull-right_:before {
  content: "\e703";
}

.webull-edit1:before {
  content: "\e60f";
}

.webull-up_1:before {
  content: "\e704";
}

.webull-edit_profit:before {
  content: "\e610";
}

.webull-left_:before {
  content: "\e705";
}

.webull-ico_pwd:before {
  content: "\e611";
}

.webull-Trash_:before {
  content: "\e707";
}

.webull-ico_delete:before {
  content: "\e612";
}

.webull-BreakEven_:before {
  content: "\e708";
}

.webull-show_pwd:before {
  content: "\e613";
}

.webull-FullRecycle_:before {
  content: "\e709";
}

.webull-ico_close:before {
  content: "\e614";
}

.webull-FullExpansion_:before {
  content: "\e70a";
}

.webull-ico_setting:before {
  content: "\e615";
}

.webull-HalfExpansion_:before {
  content: "\e70b";
}

.webull-search_add_profit:before {
  content: "\e616";
}

.webull-money_:before {
  content: "\e716";
}

.webull-ico_tips:before {
  content: "\e617";
}

.webull-download__:before {
  content: "\e600";
}

.webull-add_profit:before {
  content: "\e618";
}

.webull-bottom__:before {
  content: "\e70c";
}

.webull-add_a_profit:before {
  content: "\e619";
}

.webull-Pitchfork:before {
  content: "\e70d";
}

.webull-ico_info:before {
  content: "\e61a";
}

.webull-Text:before {
  content: "\e710";
}

.webull-ico_email:before {
  content: "\e61b";
}

.webull-Rectangle:before {
  content: "\e711";
}

.webull-hide_pwd:before {
  content: "\e61c";
}

.webull-linepx_:before {
  content: "\e714";
}

.webull-ico_select:before {
  content: "\e620";
}

.webull-linepx_1:before {
  content: "\e715";
}

.webull-arrow_bottom:before {
  content: "\e625";
}

.webull-lock_on:before {
  content: "\e718";
}

.webull-arrow_right:before {
  content: "\e626";
}

.webull-Deposit_:before {
  content: "\e720";
}

.webull-arrow_top:before {
  content: "\e627";
}

.webull-Withdraw_:before {
  content: "\e721";
}

.webull-arrow_left:before {
  content: "\e628";
}

.webull-arrowlineleft_1:before {
  content: "\e722";
}

.webull-ico_stick:before {
  content: "\e62b";
}

.webull-Dottedline_2:before {
  content: "\e723";
}

.webull-added_profit1:before {
  content: "\e62e";
}

.webull-arrowlineright_1:before {
  content: "\e724";
}

.webull-ico_arrow_top:before {
  content: "\e62f";
}

.webull-linepx_5:before {
  content: "\e725";
}

.webull-ico_arrow_bot:before {
  content: "\e630";
}

.webull-linepx_12:before {
  content: "\e726";
}

.webull-ico-minus:before {
  content: "\e631";
}

.webull-linepx_22:before {
  content: "\e727";
}

.webull-ico-plus:before {
  content: "\e632";
}

.webull-linepx_32:before {
  content: "\e728";
}

.webull-ico-slice:before {
  content: "\e633";
}

.webull-Dottedline_11:before {
  content: "\e729";
}

.webull-ico_loading:before {
  content: "\e635";
}

.webull-RegressionTrend:before {
  content: "\e713";
}

.webull-chart_narrow:before {
  content: "\e636";
}

.webull-Transfer:before {
  content: "\e717";
}

.webull-chart_enlarge:before {
  content: "\e637";
}

.webull-exercise_:before {
  content: "\e740";
}

.webull-market_edit:before {
  content: "\e638";
}

.webull-Strike_right_:before {
  content: "\e748";
}

.webull-ico_arrow_right-copy:before {
  content: "\e66f";
}

.webull-Strike_left_:before {
  content: "\e749";
}

.webull-icon-message-setting:before {
  content: "\e602";
}

.webull-Width_up_:before {
  content: "\e74a";
}

.webull-icon-message-history:before {
  content: "\e61e";
}

.webull-Width_down_:before {
  content: "\e74b";
}

.webull-icon-message-delete:before {
  content: "\e61f";
}

.webull-Qty_up_:before {
  content: "\e74c";
}

.webull-icon-message-refesh:before {
  content: "\e621";
}

.webull-Qty_down_:before {
  content: "\e74d";
}

.webull-icon-message-alert:before {
  content: "\e622";
}

.webull-Exp_Up_:before {
  content: "\e74e";
}

.webull-icon-message-set:before {
  content: "\e623";
}

.webull-Exp_down_:before {
  content: "\e74f";
}

.webull-icon-rating:before {
  content: "\e624";
}

.webull-Swap_:before {
  content: "\e750";
}

.webull-show_ticker_detail:before {
  content: "\e62a";
}

.webull-Account__:before {
  content: "\e719";
}

.webull-profit_list:before {
  content: "\e62c";
}

.webull-Alert__:before {
  content: "\e71a";
}

.webull-profit_grid:before {
  content: "\e62d";
}

.webull-Papertrading__:before {
  content: "\e71c";
}

.webull-ticker_to_list:before {
  content: "\e629";
}

.webull-Message__:before {
  content: "\e71d";
}

.webull-icon_down_Arrow:before {
  content: "\e63a";
}

.webull-Stock__:before {
  content: "\e71e";
}

.webull-icon_feedback:before {
  content: "\e63b";
}

.webull-Screener__:before {
  content: "\e72b";
}

.webull-reduce:before {
  content: "\e639";
}

.webull-Quotes__:before {
  content: "\e71b";
}

.webull-enlarge:before {
  content: "\e63c";
}

.webull-Watchlists__:before {
  content: "\e71f";
}

.webull-real-time:before {
  content: "\e63d";
}

.webull-Trade__:before {
  content: "\e72a";
}

.webull-delayed-price:before {
  content: "\e63e";
}

.webull-icon_lock:before {
  content: "\e63f";
}

.webull-webull-ico_info_more:before {
  content: "\e640";
}

.webull-webull-ico_list_move:before {
  content: "\e641";
}

.webull-nav_screener:before {
  content: "\e605";
}

.webull-nav_calender:before {
  content: "\e606";
}

.webull-nav_news:before {
  content: "\e607";
}

.webull-nav_market:before {
  content: "\e608";
}

.webull-nav_qoute:before {
  content: "\e609";
}

.webull-nav_trade:before {
  content: "\e60a";
}

.webull-nav_profit:before {
  content: "\e60b";
}

.webull-graph_add:before {
  content: "\e634";
}

.webull-shape_average:before {
  content: "\e642";
}

.webull-shape_color_bar:before {
  content: "\e643";
}

.webull-shape_bar:before {
  content: "\e644";
}

.webull-shape_base:before {
  content: "\e645";
}

.webull-shape_area:before {
  content: "\e646";
}

.webull-graph_vs:before {
  content: "\e647";
}

.webull-shape_candle:before {
  content: "\e648";
}

.webull-shape_line:before {
  content: "\e649";
}

.webull-shape_hollow_candle:before {
  content: "\e64a";
}

.webull-graph_draw:before {
  content: "\e64b";
}

.webull-draw_horizontal:before {
  content: "\e64c";
}

.webull-draw_delete:before {
  content: "\e64d";
}

.webull-draw_magnet:before {
  content: "\e64e";
}

.webull-draw_vertical:before {
  content: "\e64f";
}

.webull-draw_trendline:before {
  content: "\e650";
}

.webull-draw_continue_line:before {
  content: "\e651";
}

.webull-graph_move:before {
  content: "\e65b";
}

.webull-line_style:before {
  content: "\e652";
}

.webull-line_style2:before {
  content: "\e653";
}

.webull-line_width:before {
  content: "\e654";
}

.webull-line_style4:before {
  content: "\e655";
}

.webull-line_style3:before {
  content: "\e656";
}

.webull-line_width3:before {
  content: "\e657";
}

.webull-line_width2:before {
  content: "\e658";
}

.webull-line_width4:before {
  content: "\e65a";
}

.webull-post_trade:before {
  content: "\e65c";
}

.webull-graph_setting:before {
  content: "\e65d";
}

.webull-simulation_holdings1:before {
  content: "\e70e";
}

.webull-withdrawal:before {
  content: "\e70f";
}

.webull-ticker_straight:before {
  content: "\e65e";
}

.webull-ticker_ray:before {
  content: "\e65f";
}

.webull-ticker_parallel:before {
  content: "\e660";
}

.webull-sort:before {
  content: "\e661";
}

.webull-safe:before {
  content: "\e604";
}

.webull-shang:before {
  content: "\e60d";
}

.webull-xia:before {
  content: "\e60e";
}

.webull-zuo:before {
  content: "\e662";
}

.webull-you:before {
  content: "\e663";
}

.webull-jingshi:before {
  content: "\e664";
}

.webull-i:before {
  content: "\e665";
}

.webull-nav_account:before {
  content: "\e667";
}

.webull-email:before {
  content: "\e668";
}

.webull-phone:before {
  content: "\e669";
}

.webull-search:before {
  content: "\e66a";
}

.webull-simulation_holdings:before {
  content: "\e66b";
}

.webull-Grid:before {
  content: "\e66d";
}

.webull-font:before {
  content: "\e670";
}

.webull-editor:before {
  content: "\e671";
}

.webull-list:before {
  content: "\e66c";
}


/*# sourceMappingURL=data:application/json;base64,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 */</style><style>@charset "utf-8";
/* ===== reset ===== */
html,
body {
  overflow-y: auto;
  font-size: 12px;
  -webkit-text-size-adjust: 100%;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
html,
body {
  overscroll-behavior-x: none;
}
html * {
  outline: 0;
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
body,
input,
textarea,
button {
  line-height: 1;
  font-family: "OpenSans-Regular", Arial, Helvetica, sans-serif;
  word-wrap: break-word;
  font-size: 12px;
  /*-webkit-overflow-scrolling: touch;*/
}
body {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
input::-ms-reveal,
input::-ms-clear {
  display: none;
}
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  margin: 0;
  padding: 0;
}
/* Mozilla Firefox hide scrollbar */
div,
p,
section {
  scrollbar-color: rgba(255, 255, 255, 0) rgba(255, 255, 255, 0);
  scrollbar-width: none;
}
input,
select,
textarea {
  font-size: 100%;
}
input,
textarea {
  border-radius: 0;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight: rgba(0, 0, 0, 0);
  -webkit-text-size-adjust: none;
}
*:not(input):not(textarea) {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
       user-select: none;
}
input {
  border: 0 none;
}
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 99999s;
  -webkit-transition: color 99999s ease-out, background-color 99999s ease-out;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
fieldset,
img {
  border: 0;
}
abbr,
acronym {
  border: 0;
  font-feature-settings: normal;
  font-variant: normal;
}
del {
  -webkit-text-decoration: line-through;
  text-decoration: line-through;
}
ins {
  -webkit-text-decoration: none;
  text-decoration: none;
}
address,
caption,
cite,
code,
dfn,
em,
strong,
th,
var,
i,
b {
  font-style: normal;
  font-weight: normal;
  cursor: default;
}
li,
ul {
  list-style: none;
}
caption,
th {
  text-align: left;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: normal;
  cursor: default;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
}
sup {
  vertical-align: text-top;
}
sub {
  vertical-align: text-bottom;
}
a {
  color: #3a3a3a;
  -webkit-text-decoration: none;
  text-decoration: none;
  cursor: pointer;
}
a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}
.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  line-height: 0;
  content: "";
}
.clearfix:after {
  clear: both;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #8a8d91;
}
input::-moz-placeholder,
textarea::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #8a8d91;
}
input:-moz-placeholder,
textarea::-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #8a8d91;
}
input:-ms-input-placeholder,
textarea::-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #8a8d91;
}
@keyframes anis {
  100% {
    transform: translateX(-100%);
  }
}
@font-face {
  font-family: "OpenSans-Bold";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Bold.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Bold.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-BoldItalic";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-BoldItalic.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-BoldItalic.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-ExtraBold";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-ExtraBold.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-ExtraBold.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-ExtraBoldItalic";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-ExtraBoldItalic.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-ExtraBoldItalic.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-Italic";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Italic.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Italic.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-Light";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Light.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Light.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-LightItalic";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-LightItalic.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-LightItalic.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-Regular";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Regular.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Regular.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-SemiBold";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-SemiBold.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-SemiBold.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "OpenSans-SemiBoldItalic";
  src: url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-SemiBoldItalic.woff2") format("woff2"), url("https://wbstatic.webullfintech.com/assets/fonts2/OpenSans-SemiBoldItalic.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
.hide {
  display: none !important;
}
::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}
::-webkit-scrollbar-button {
  width: 0;
  height: 0;
  display: none;
}
::-webkit-input-placeholder {
  color: #7b8288;
}
:-moz-placeholder {
  color: #7b8288;
}
::-moz-placeholder {
  color: #7b8288;
}
:-ms-input-placeholder {
  color: #7b8288;
}
.g-cursor-resize {
  cursor: col-resize !important;
}
.g-clickable {
  cursor: pointer !important;
}
.g-not-allowed {
  cursor: not-allowed !important;
}
.grecaptcha-badge {
  visibility: hidden;
}
.not-support-body {
  position: relative;
  height: 100vh;
  background: url(https://wbstatic.webullfintech.com/v1/webquotes-g/3f3b606457f5d5404c1a.jpg) no-repeat;
  background-size: cover;
  min-width: 1200px;
  background-position: center;
}
.not-support-body .logo {
  position: absolute;
  top: 25px;
  left: 35px;
  height: 45px;
  width: 232px;
  background-image: url(data:image/png;base64,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);
  background-position: 0 50%;
  background-repeat: no-repeat;
  background-size: 100% auto;
}
.not-support-body .logo.zh {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAATUAAAAwCAMAAABHex0xAAAArlBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8tivQqAAAAOXRSTlMArPsi2N89eAzyooy35xG9v8lEzQR9MRhJFeOThWJQOAjQ7vQew1cCNC7UKmiyn26n25uYcyZdQOsANfzpAAAH30lEQVRo3tzY6XaaQACG4c/BuK+4I1p3jGtM1OS7/xurM1NEKAa09JyePn+SnJCFl5lhANKPlyES5DTe8f9rrphFkvLs4r/XYsJnOedLDjGZmwpCLQY/on7J6JgDKnv8HZ1PXFQQzhCkgSTtyCLiyVkUqa4D6VRcnO2b9KQ1RJBTPK8d94dJq8z8/ar9GR7lLBomlK4obM79N0jL7Al+H0y62js5rSCWD0p9SG2SZbjKZNgcyJCc1m23Gnm/Wq7G8g88pHlOk18dSEdKR0gl8lDMwNObkjwhSQUy5kp5pmRV/NW8IgaCbEqFGNUqdRl4CK141xmeBaUqpB2lpb5U0ta/qiW9rtVliSaiLQUlE7tUquVWS6VSNvDKC+EgaExpEaNalZI4QhG85w03UpQGuFhRVm+6qwUnHXjSvLCRoJHQKSKdypRsNR1LbjWS819XfYWgmU6Ri1FtbVGxH6nW+1KBKoBBqeSNwMVv/8YKCRpSKiKKYVHaOmHVbD0Rg47uuURXg3Gg0n+gGjaU9sCeUkvfsYKHDaiYSE6V0hgRTi+UVjmEVSvppTgoTWkTqxo6aSp2rGojQzmQk9blY5XS+fJZXw2/tnHRg1agMkdiPqlVIg6bUCobCK2mvvuJAJNKo6rUKImq3xJXvdX1bvOW8vwa4yltD63LaCloVWobJGR0oLbGd5wVpa81QqsZlI4DbeEt1tG6wdFmGfBL+a/qE9XS1Kbt5La4sSZ9W6hxDzs7C6l25q0ytJl4sBpmloyWeLU8JT1VkmDTdcb3smS5jVdBUR1MyLq5kRlNU1VLhVbb8dFqaKcNJF9N0PWyTjQaB5EvDeQJbXkhBH3mzXxYtbZ4vBqaiFkt0/CkqawaATa0Mq/yJv7Q6I2eBSIsK8APSoVyoJrJsGpVSvmaa05J1PyWCIquFvSuozm4I02PKI7wJ5Yr3jARrTlWf9cIVuvzoj4cDlWobBfSkMoeQPTOo+d5vJpJZYh7aryV/sTTcrbgrTWitSj1Eag2nri7NVVtpjvogw7NWNV4JeJUG2VufFqUXjJhTrgo0q/0iqc091/0mTqItFadvyrodXzs6+b05fo06pTcAfBktf74Ynun2ppxFfQzVoDYmnhYJWsxYI5Io7Fbx69puTesprg+7n1QqeHZaqp6OplqM/5udZzhAY65m/I3RUR6o5LFa9bnncoBMLw3NGZVJTL+iWqoM8x8f0IsuW6/zDBLRNnTrTZgGDGCeds/UyPPSKzaWHt/qtqed1i7QbuJ7/S6rZpguAOiDEVENbax929hukXErWZKu7vVPDVIRl0bUxN1l6DScL9uQeoI3ifqu9Zi2QnGyxndTXFr8RstROgK3q0mqGz0HD7BE7ea1opXzdN2qw2CL4XmBnw+GG3ys1w7XU8TCMMw/DKIdbcSFEFFFOJu3GO+8z+xFmYwVBLBgfZP7z9Jrmxej4OzyOG13VD2e2Xb+DmxGaWqtvBYz6Pvq70d6BBOBzafjP9RtXmXEXdObne8nXH3HlXxhkjhlmLVrPGf1LEShGz6scn4qiVRSEuQq7bukLBDXJ047X2OT0cq3NRCmnL4BPJqgKXGuICxDi/z2BPQp+wyVTtvQg44/40Rx2aJcwZB+2Ei0mpS0TbZ3v87KVG1N4opA/xYMlw4X/9StRZiejVGkWrpHoutZjc+uBkV7CcyMKvb+YNqDeKqxj+otvYos2aUTaFCVS/IYubiQbUucVv87WpiRk+TOL6tlKhIDjJ6VG1xtwboKUkUYEpCololrZr40cGEvrX17g/ALtVC50/JagvLGvNqnyd/XgXIvfLQbTetmjEhaqzxSt9aW90mUS1lhMraQ6JaTZwIXXg1cxxN7W/IX23DaJRWDbrSA6AuAxoF9KVQo4AJzPVjK7G/KYRiSFTjn7RENWfobaNDyHL+agMimqRVi1tSwEaEVzMQKT7b1oBMtYZ4XGuxoZoaGFBgnLea4DxRrX53zbTDJSi+1K9SfkcDUtUOROQB0EnQxZ0znXkx1V6zVzOnonOkxH/za2uN8hABpKq5QaKX2Kb4w1kRt8tR7USRkpq5mtHgj8BAxOO3Z33DalMuUweS1crhOAVmTAxZSyGBLeSrtUn42ULWau7+fgOvpkxL8zMjeW0fstVOYlSN+cAoY0g3mi9bzWLE1QxkrbaeiP/qIvKeevTVW5Ek7zyHbDV3Gnxc8tHB6q54PT7sRTbJal3ihvAHg4EWXg6D3w58PA+EWaxZjSVHuJK+dDd2U5KhqIB0tfrt1i2Hmle4NX7BXyoaBeyrVLWLxwOMgAU9cAJ32XVIYO8AUJ7p40VZtPfxkHVk9KyVDshXK7Pby77x4mPJxzvTAf4dYo5ENasU3VGQqZp5oBvPQeBKnzSkUZ/s9uIAeaqFax7PQsCFsaLQ5nPpVJtLVKtsw3G6RLZqGFFktUjemFBHOr8+pYxYow/kq4ZxNb57VafBn90gYAQXa9uQukKNLVHJR9Zq2FOo2jUg1CkyNZGFO2swSqd1fSB3Nehs5eJGJ/q4QhiQbUnOBm6nZCKk1h4YgTODIW+fW7h5J8FeICtzs/XokclwDC5vNYxUxAxWsS+7fen1mqriGcPmSTcQsz5otl16Pc0qeIbbHzY+6Avs5TTykZva+81M5qggi3kvsERRXBRIdXZ1pVOyp4xVm4dJ4/hjtnDxP/sFK4CzSb/wlEgAAAAASUVORK5CYII=);
}
.not-support-body .content {
  position: absolute;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  width: 1030px;
  color: #EEEEEE;
}
.not-support-body .content .txt {
  font-size: 30px;
  line-height: 45px;
  margin-top: 30px;
  letter-spacing: 1px;
}
.not-support-body .content .link {
  display: inline-block;
  color: #EEEEEE;
  border-bottom: 1px dashed #EEEEEE;
}
.not-support-body .content .link:hover {
  opacity: 0.7;
}

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL2Fzc2V0cy9zdHlsZS9yZXNldC5sZXNzIiwid2VicGFjazovLy4vYXNzZXRzL3N0eWxlL2FwcC5sZXNzIiwid2VicGFjazovLy4vYXNzZXRzL3N0eWxlL3B1YmxpYy5sZXNzIiwid2VicGFjazovLy4vYXNzZXRzL3N0eWxlL25vU3VwcG9ydC5sZXNzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGdCQUFTO0FDQ1Qsc0JBQXNCO0FEQ3RCOztFQUVFLGdCQUFBO0VBRUEsZUFBQTtFQUNBLDhCQUFBO0VBRUEsV0FBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtBQ0FGO0FER0E7O0VBQ0UsMkJBQUE7QUNBRjtBREdBO0VBQ0UsVUFBQTtFQUNBLDhCQUFBO0VBQ0EsNkNBQUE7RUFDQSxtQ0FBQTtFQUNBLHNCQUFBO0FDREY7QURJQTs7OztFQUlFLGNBQUE7RUFDQSw2REFBQTtFQUNBLHFCQUFBO0VBQ0EsZUFBQTtFQ0ZBLHFDQUFxQztBQUN2QztBRElBO0VBQ0UseUJBQUE7S0FBQSxzQkFBQTtVQUFBLGlCQUFBO0FDRkY7QURLQTs7RUFFRSxhQUFBO0FDSEY7QURNQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7RUFzQ0UsU0FBQTtFQUNBLFVBQUE7QUNKRjtBQUNBLG1DQUFtQztBRE1uQzs7O0VBR0UsOERBQUE7RUFDQSxxQkFBQTtBQ0pGO0FET0E7OztFQUdFLGVBQUE7QUNMRjtBRE9BOztFQUVFLGdCQUFBO0VBQ0Esd0JBQUE7RUFDQSw2Q0FBQTtFQUNBLHVDQUFBO0VBQ0EsOEJBQUE7QUNMRjtBRE9BO0VBQ0UsMkJBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO09BQUEsaUJBQUE7QUNMRjtBRE9BO0VBQ0UsY0FBQTtBQ0xGO0FET0E7O0VBRUUsd0JBQUE7RUFDQSxxQkFBQTtBQ0xGO0FEUUE7RUFDRSwwQkFBQTtBQ05GO0FEb0JBOzs7O0VBSUUsZ0NBQUE7RUFDQSwyRUFBQTtBQ2xCRjtBRHFCQTtFQUNFLHlCQUFBO0VBQ0EsaUJBQUE7QUNuQkY7QURxQkE7O0VBRUUsU0FBQTtBQ25CRjtBRHFCQTs7RUFFRSxTQUFBO0VBQ0EsNkJBQUE7RUFBQSxvQkFBQTtBQ25CRjtBRHFCQTtFQUNFLHFDQUFBO0VBQUEsNkJBQUE7QUNuQkY7QURxQkE7RUFDRSw2QkFBQTtFQUFBLHFCQUFBO0FDbkJGO0FEcUJBOzs7Ozs7Ozs7OztFQVdFLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0FDbkJGO0FEcUJBOztFQUVFLGdCQUFBO0FDbkJGO0FEcUJBOztFQUVFLGdCQUFBO0FDbkJGO0FEcUJBOzs7Ozs7RUFNRSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0FDbkJGO0FEcUJBOzs7O0VBSUUsV0FBQTtBQ25CRjtBRHFCQTtFQUNFLHdCQUFBO0FDbkJGO0FEcUJBO0VBQ0UsMkJBQUE7QUNuQkY7QURxQkE7RUFDRSxjQUFBO0VBQ0EsNkJBQUE7RUFBQSxxQkFBQTtFQUNBLGVBQUE7QUNuQkY7QURxQkE7RUFDRSw2QkFBQTtFQUFBLHFCQUFBO0FDbkJGO0FEcUJBO0dBQ0UsT0FBQTtBQ25CRjtBRHFCQTs7RUFFRSxjQUFBO0VBQ0EsY0FBQTtFQUNBLFdBQUE7QUNuQkY7QURxQkE7RUFDRSxXQUFBO0FDbkJGO0FEcUJBOztFQUVFLGNBQUE7QUNuQkY7QURxQkE7O0VDbEJFLHdCQUF3QjtFRHFCeEIsY0FBQTtBQ25CRjtBRHFCQTs7RUNsQkUsNEJBQTRCO0VEcUI1QixjQUFBO0FDbkJGO0FEcUJBOztFQ2xCRSw0QkFBNEI7RURxQjVCLGNBQUE7QUNuQkY7QUNoT0E7RUFDRTtJQUNFLDRCQUFBO0VEa09GO0FBQ0Y7QUMvTkE7RUFDRSw0QkFBQTtFQUNBLDJMQUFBO0VBRUEsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0FEZ09GO0FDOU5BO0VBQ0Usa0NBQUE7RUFDQSx1TUFBQTtFQUVBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBRCtORjtBQzdOQTtFQUNFLGlDQUFBO0VBQ0EscU1BQUE7RUFFQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUQ4TkY7QUM1TkE7RUFDRSx1Q0FBQTtFQUNBLGlOQUFBO0VBRUEsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0FENk5GO0FDM05BO0VBQ0UsOEJBQUE7RUFDQSwrTEFBQTtFQUVBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBRDRORjtBQzFOQTtFQUNFLDZCQUFBO0VBQ0EsNkxBQUE7RUFFQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUQyTkY7QUN6TkE7RUFDRSxtQ0FBQTtFQUNBLHlNQUFBO0VBRUEsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0FEME5GO0FDeE5BO0VBQ0UsK0JBQUE7RUFDQSxpTUFBQTtFQUVBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBRHlORjtBQ3ZOQTtFQUNFLHVCQUFBO0VBQ0EsaU1BQUE7RUFFQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUR3TkY7QUN0TkE7RUFDRSxnQ0FBQTtFQUNBLG1NQUFBO0VBRUEsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0FEdU5GO0FDck5BO0VBQ0Usc0NBQUE7RUFDQSwrTUFBQTtFQUVBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBRHNORjtBQ2hOQTtFQUNFLHdCQUFBO0FEa05GO0FDOU1BO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QURnTkY7QUN6TUE7RUFDRSxRQUFBO0VBQ0EsU0FBQTtFQUNBLGFBQUE7QUQyTUY7QUN4TUE7RUFDRSxjQUFBO0FEME1GO0FDeE1BO0VBQ0UsY0FBQTtBRDBNRjtBQ3hNQTtFQUNFLGNBQUE7QUQwTUY7QUN4TUE7RUFDRSxjQUFBO0FEME1GO0FDdk1BO0VBQ0UsNkJBQUE7QUR5TUY7QUN0TUE7RUFDRSwwQkFBQTtBRHdNRjtBQ3JNQTtFQUNFLDhCQUFBO0FEdU1GO0FDcE1BO0VBQ0Usa0JBQUE7QURzTUY7QUV4VkE7RUFDRSxrQkFBQTtFQUNBLGFBQUE7RUFDQSw2REFBQTtFQUNBLHNCQUFBO0VBQ0EsaUJBQUE7RUFDQSwyQkFBQTtBRjBWRjtBRWhXQTtFQVNJLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtFQUNBLHlEQUFBO0VBQ0EsMEJBQUE7RUFDQSw0QkFBQTtFQUNBLDBCQUFBO0FGMFZKO0FFelZJO0VBQ0UseURBQUE7QUYyVk47QUU5V0E7RUF3Qkksa0JBQUE7RUFDQSxTQUFBO0VBQ0EsUUFBQTtFQUNBLGdDQUFBO0VBQ0EsYUFBQTtFQUNBLGNBQUE7QUZ5Vko7QUV0WEE7RUFrQ00sZUFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBRnVWTjtBRTVYQTtFQXlDTSxxQkFBQTtFQUNBLGNBQUE7RUFDQSxpQ0FBQTtBRnNWTjtBRXJWTTtFQUNFLFlBQUE7QUZ1VlIiLCJzb3VyY2VSb290IjoiIn0= */</style><style>[data-simplebar]{position:relative;flex-direction:column;flex-wrap:wrap;justify-content:flex-start;align-content:flex-start;align-items:flex-start}.simplebar-wrapper{overflow:hidden;width:inherit;height:inherit;max-width:inherit;max-height:inherit}.simplebar-mask{width:auto!important;height:auto!important}.simplebar-mask{direction:inherit;position:absolute;overflow:hidden;padding:0;margin:0;left:0;top:0;bottom:0;right:0;z-index:0}.simplebar-offset{direction:inherit!important;box-sizing:inherit!important;resize:none!important}.simplebar-offset{position:absolute;top:0;left:0;bottom:0;right:0;padding:0;margin:0;-webkit-overflow-scrolling:touch}.simplebar-content-wrapper{box-sizing:border-box!important}.simplebar-content-wrapper{direction:inherit;position:relative;display:block;height:100%;width:auto;max-width:100%;max-height:100%;overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.simplebar-content-wrapper::-webkit-scrollbar,.simplebar-hide-scrollbar::-webkit-scrollbar{display:none;width:0;height:0}.simplebar-content:after,.simplebar-content:before{content:' ';display:table}.simplebar-placeholder{max-height:100%;max-width:100%;width:100%;pointer-events:none}.simplebar-height-auto-observer-wrapper{box-sizing:inherit!important}.simplebar-height-auto-observer-wrapper{height:100%;width:100%;max-width:1px;position:relative;float:left;max-height:1px;overflow:hidden;z-index:-1;padding:0;margin:0;pointer-events:none;flex-grow:inherit;flex-shrink:0;flex-basis:0}.simplebar-height-auto-observer{box-sizing:inherit;display:block;opacity:0;position:absolute;top:0;left:0;height:1000%;width:1000%;min-height:1px;min-width:1px;overflow:hidden;pointer-events:none;z-index:-1}.simplebar-track{z-index:1;position:absolute;right:0;bottom:0;pointer-events:none;overflow:hidden}[data-simplebar].simplebar-dragging{pointer-events:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}[data-simplebar].simplebar-dragging .simplebar-content{pointer-events:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}[data-simplebar].simplebar-dragging .simplebar-track{pointer-events:all}.simplebar-scrollbar{position:absolute;left:0;right:0;min-height:10px}.simplebar-scrollbar:before{position:absolute;content:'';background:#000;border-radius:7px;left:2px;right:2px;opacity:0;transition:opacity .2s .5s linear}.simplebar-scrollbar.simplebar-visible:before{opacity:.5;transition-delay:0s;transition-duration:0s}.simplebar-track.simplebar-vertical{top:0;width:11px}.simplebar-scrollbar:before{top:2px;bottom:2px;left:2px;right:2px}.simplebar-track.simplebar-horizontal{left:0;height:11px}.simplebar-track.simplebar-horizontal .simplebar-scrollbar{right:auto;left:0;top:0;bottom:0;min-height:0;min-width:10px;width:auto}[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical{right:auto;left:0}.simplebar-dummy-scrollbar-size{-ms-overflow-style:scrollbar!important}.simplebar-dummy-scrollbar-size{direction:rtl;position:fixed;opacity:0;visibility:hidden;height:500px;width:500px;overflow-y:hidden;overflow-x:scroll}.simplebar-dummy-scrollbar-size>div{width:200%;height:200%;margin:10px 0}.simplebar-hide-scrollbar{position:fixed;left:0;visibility:hidden;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none}

/*# sourceMappingURL=data:application/json;base64,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 */</style><style>.driver-active .driver-overlay,.driver-active *{pointer-events:none}.driver-active .driver-active-element,.driver-active .driver-active-element *,.driver-popover,.driver-popover *{pointer-events:auto}@keyframes animate-fade-in{0%{opacity:0}to{opacity:1}}.driver-fade .driver-overlay{animation:animate-fade-in .2s ease-in-out}.driver-fade .driver-popover{animation:animate-fade-in .2s}.driver-popover{all:unset;box-sizing:border-box;color:#2d2d2d;margin:0;padding:15px;border-radius:5px;min-width:250px;max-width:300px;box-shadow:0 1px 10px rgba(0,0,0,0.4);z-index:1000000000;position:fixed;top:0;right:0;background-color:#fff}.driver-popover *{font-family:Helvetica Neue,Inter,ui-sans-serif,"Apple Color Emoji",Helvetica,Arial,sans-serif}.driver-popover-title{font:19px/normal sans-serif;font-weight:700;display:block;position:relative;line-height:1.5;zoom:1;margin:0}.driver-popover-close-btn{all:unset;position:absolute;top:0;right:0;width:32px;height:28px;cursor:pointer;font-size:18px;font-weight:500;color:#d2d2d2;z-index:1;text-align:center;transition:color;transition-duration:.2s}.driver-popover-close-btn:hover,.driver-popover-close-btn:focus{color:#2d2d2d}.driver-popover-title[style*=block]+.driver-popover-description{margin-top:5px}.driver-popover-description{margin-bottom:0;font:14px/normal sans-serif;line-height:1.5;font-weight:400;zoom:1}.driver-popover-footer{margin-top:15px;text-align:right;zoom:1;display:flex;align-items:center;justify-content:space-between}.driver-popover-progress-text{font-size:13px;font-weight:400;color:#727272;zoom:1}.driver-popover-footer button{all:unset;display:inline-block;box-sizing:border-box;padding:3px 7px;-webkit-text-decoration:none;text-decoration:none;text-shadow:1px 1px 0 #fff;background-color:#fff;color:#2d2d2d;font:12px/normal sans-serif;cursor:pointer;outline:0;zoom:1;line-height:1.3;border:1px solid #ccc;border-radius:3px}.driver-popover-footer .driver-popover-btn-disabled{opacity:.5;pointer-events:none}:not(body):has(>.driver-active-element){overflow:hidden!important}.driver-no-interaction,.driver-no-interaction *{pointer-events:none!important}.driver-popover-footer button:hover,.driver-popover-footer button:focus{background-color:#f7f7f7}.driver-popover-navigation-btns{display:flex;flex-grow:1;justify-content:flex-end}.driver-popover-navigation-btns button+button{margin-left:4px}.driver-popover-arrow{content:"";position:absolute;border:5px solid #fff}.driver-popover-arrow-side-over{display:none}.driver-popover-arrow-side-left{left:100%;border-right-color:transparent;border-bottom-color:transparent;border-top-color:transparent}.driver-popover-arrow-side-right{right:100%;border-left-color:transparent;border-bottom-color:transparent;border-top-color:transparent}.driver-popover-arrow-side-top{top:100%;border-right-color:transparent;border-bottom-color:transparent;border-left-color:transparent}.driver-popover-arrow-side-bottom{bottom:100%;border-left-color:transparent;border-top-color:transparent;border-right-color:transparent}.driver-popover-arrow-side-center{display:none}.driver-popover-arrow-side-left.driver-popover-arrow-align-start,.driver-popover-arrow-side-right.driver-popover-arrow-align-start{top:15px}.driver-popover-arrow-side-top.driver-popover-arrow-align-start,.driver-popover-arrow-side-bottom.driver-popover-arrow-align-start{left:15px}.driver-popover-arrow-align-end.driver-popover-arrow-side-left,.driver-popover-arrow-align-end.driver-popover-arrow-side-right{bottom:15px}.driver-popover-arrow-side-top.driver-popover-arrow-align-end,.driver-popover-arrow-side-bottom.driver-popover-arrow-align-end{right:15px}.driver-popover-arrow-side-left.driver-popover-arrow-align-center,.driver-popover-arrow-side-right.driver-popover-arrow-align-center{top:50%;margin-top:-5px}.driver-popover-arrow-side-top.driver-popover-arrow-align-center,.driver-popover-arrow-side-bottom.driver-popover-arrow-align-center{left:50%;margin-left:-5px}.driver-popover-arrow-none{display:none}

/*# sourceMappingURL=data:application/json;base64,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 */</style><style type="text/css">.message-root {
  position: fixed;
  top: 35%;
  left: 0;
  z-index: 9999;
  width: 100%;
  pointer-events: none;
}
.message-root-notice {
  text-align: center;
}
.message-root-notice-closable {
  padding-right: 20px;
}
.message-root-notice-close {
  position: absolute;
  right: 5px;
  top: 3px;
  color: #000;
  cursor: pointer;
  outline: none;
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
  text-shadow: 0 1px 0 #fff;
  filter: alpha(opacity=20);
  opacity: 0.2;
  text-decoration: none;
}
.message-root-notice-close-x:after {
  content: "×";
}
.message-root-notice-close:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  text-decoration: none;
}
.message-root-notice-content {
  display: inline-block;
  padding: 16px 30px;
  pointer-events: all;
  background: rgba(36, 41, 54, 0.8);
  border-radius: 12px;
  color: #fff;
  text-align: center;
}
.message-root-custom-content-icon {
  color: #fff !important;
  font-size: 36px !important;
  margin: 0 0 6px;
}
.message-root-custom-content > span {
  display: block;
  font-size: 14px;
  line-height: 20px;
}
.message-root-info > i {
  display: none;
}
.message-root-loading,
.message-root-success,
.message-root-warning,
.message-root-error {
  width: 140px;
  padding: 14px 0;
}
</style><style>/**
 * Swiper 11.1.15
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2024 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: November 18, 2024
 */

/* FONT_START */
@font-face {
  font-family: 'swiper-icons';
  src: url("data:application/font-woff;charset=utf-8;base64, 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");
  font-weight: 400;
  font-style: normal;
}
/* FONT_END */
:root {
  --swiper-theme-color: #007aff;
  /*
  --swiper-preloader-color: var(--swiper-theme-color);
  --swiper-wrapper-transition-timing-function: initial;
  */
}
:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
  display: block;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: ease;
  transition-timing-function: initial;
  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}
.swiper-horizontal {
  touch-action: pan-y;
}
.swiper-vertical {
  touch-action: pan-x;
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  backface-visibility: hidden;
}
/* 3D Effects */
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}
.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d;
}
.swiper-3d {
  perspective: 1200px;
}
.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}
/* CSS Mode */
.swiper-css-mode > .swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
}
.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
  display: none;
}
.swiper-css-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: start start;
}
.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
  scroll-snap-type: x mandatory;
}
.swiper-css-mode.swiper-vertical > .swiper-wrapper {
  scroll-snap-type: y mandatory;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
  scroll-snap-type: none;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: none;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper::before {
  content: '';
  flex-shrink: 0;
  order: 9999;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always;
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
  margin-left: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
  margin-top: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}
/* Slide styles start */
/* 3D Shadows */
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}
.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid #007aff;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}
.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear;
}
.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff;
}
.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000;
}
@keyframes swiper-preloader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Slide styles end */

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL25vZGVfbW9kdWxlcy9zd2lwZXIvc3dpcGVyLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Ozs7OztFQVVFOztBQUVGLGVBQWU7QUFDZjtFQUNFLDJCQUEyQjtFQUMzQiw0Q0FBNnJFO0VBQzdyRSxnQkFBZ0I7RUFDaEIsa0JBQWtCO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0VBQ0UsNkJBQTZCO0VBQzdCOzs7R0FHQztBQUNIO0FBQ0E7RUFDRSxrQkFBa0I7RUFDbEIsY0FBYztFQUNkLGlCQUFpQjtFQUNqQixrQkFBa0I7RUFDbEIsVUFBVTtBQUNaO0FBQ0E7RUFDRSxpQkFBaUI7RUFDakIsa0JBQWtCO0VBQ2xCLGtCQUFrQjtFQUNsQixnQkFBZ0I7RUFDaEIsZ0JBQWdCO0VBQ2hCLFVBQVU7RUFDViw2QkFBNkI7RUFDN0IsVUFBVTtFQUNWLGNBQWM7QUFDaEI7QUFDQTtFQUNFLHNCQUFzQjtBQUN4QjtBQUNBO0VBQ0Usa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxZQUFZO0VBQ1osVUFBVTtFQUNWLGFBQWE7RUFDYiw4QkFBOEI7RUFDOUIsZ0NBQXFGO0VBQXJGLG1DQUFxRjtFQUFyRixxRkFBcUY7RUFDckYsdUJBQXVCO0FBQ3pCO0FBQ0E7OztFQUdFLGlDQUFpQztBQUNuQztBQUNBO0VBQ0UsbUJBQW1CO0FBQ3JCO0FBQ0E7RUFDRSxtQkFBbUI7QUFDckI7QUFDQTtFQUNFLGNBQWM7RUFDZCxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQiw4QkFBOEI7RUFDOUIsY0FBYztBQUNoQjtBQUNBO0VBQ0Usa0JBQWtCO0FBQ3BCO0FBQ0EsZ0JBQWdCO0FBQ2hCOztFQUVFLFlBQVk7QUFDZDtBQUNBO0VBQ0UsdUJBQXVCO0VBQ3ZCLHNDQUFzQztBQUN4QztBQUNBO0VBQ0Usd0JBQXdCO0VBRWhCLDJCQUEyQjtBQUNyQztBQUNBLGVBQWU7QUFDZjtFQUNFLG1CQUFtQjtBQUNyQjtBQUNBO0VBQ0UsNEJBQTRCO0FBQzlCO0FBQ0E7RUFDRSxtQkFBbUI7QUFDckI7QUFDQTs7RUFFRSw0QkFBNEI7QUFDOUI7QUFDQSxhQUFhO0FBQ2I7RUFDRSxjQUFjO0VBQ2QscUJBQXFCO0VBQ3JCLGdCQUFnQjtFQUNoQix3QkFBd0I7RUFDeEIsbUNBQW1DO0FBQ3JDO0FBQ0E7RUFDRSxhQUFhO0FBQ2Y7QUFDQTtFQUNFLDhCQUE4QjtBQUNoQztBQUNBO0VBQ0UsNkJBQTZCO0FBQy9CO0FBQ0E7RUFDRSw2QkFBNkI7QUFDL0I7QUFDQTtFQUNFLHNCQUFzQjtBQUN4QjtBQUNBO0VBQ0UsdUJBQXVCO0FBQ3pCO0FBQ0E7RUFDRSxXQUFXO0VBQ1gsY0FBYztFQUNkLFdBQVc7QUFDYjtBQUNBO0VBQ0UsZ0NBQWdDO0VBQ2hDLHdCQUF3QjtBQUMxQjtBQUNBO0VBQ0UsaURBQXlEO0FBQzNEO0FBQ0E7RUFDRSxZQUFZO0VBQ1osZUFBZTtFQUNmLDBDQUEwQztBQUM1QztBQUNBO0VBQ0UsZ0RBQXdEO0FBQzFEO0FBQ0E7RUFDRSxXQUFXO0VBQ1gsY0FBYztFQUNkLDJDQUEyQztBQUM3QztBQUNBLHVCQUF1QjtBQUN2QixlQUFlO0FBQ2Y7Ozs7Ozs7Ozs7RUFVRSxrQkFBa0I7RUFDbEIsT0FBTztFQUNQLE1BQU07RUFDTixXQUFXO0VBQ1gsWUFBWTtFQUNaLG9CQUFvQjtFQUNwQixXQUFXO0FBQ2I7QUFDQTtFQUNFLCtCQUErQjtBQUNqQztBQUNBO0VBQ0UsZ0ZBQWdGO0FBQ2xGO0FBQ0E7RUFDRSxpRkFBaUY7QUFDbkY7QUFDQTtFQUNFLCtFQUErRTtBQUNqRjtBQUNBO0VBQ0Usa0ZBQWtGO0FBQ3BGO0FBQ0E7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQixTQUFTO0VBQ1QsUUFBUTtFQUNSLGtCQUFrQjtFQUNsQixpQkFBaUI7RUFDakIsV0FBVztFQUNYLHFCQUFxQjtFQUNyQixzQkFBc0I7RUFDdEIseUJBQTBFO0VBQTFFLDBFQUEwRTtFQUMxRSxrQkFBa0I7RUFDbEIsNkJBQTZCO0FBQy9CO0FBQ0E7O0VBRUUsbURBQW1EO0FBQ3JEO0FBQ0E7RUFDRSw4QkFBOEI7QUFDaEM7QUFDQTtFQUNFLDhCQUE4QjtBQUNoQztBQUNBO0VBQ0U7SUFDRSx1QkFBdUI7RUFDekI7RUFDQTtJQUNFLHlCQUF5QjtFQUMzQjtBQUNGO0FBQ0EscUJBQXFCIiwic291cmNlUm9vdCI6IiJ9 */</style><style>.swiper-fade.swiper-free-mode .swiper-slide {
  transition-timing-function: ease-out;
}
.swiper-fade .swiper-slide {
  pointer-events: none;
  transition-property: opacity;
}
.swiper-fade .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-fade .swiper-slide-active {
  pointer-events: auto;
}
.swiper-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL25vZGVfbW9kdWxlcy9zd2lwZXIvbW9kdWxlcy9lZmZlY3QtZmFkZS5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxvQ0FBb0M7QUFDdEM7QUFDQTtFQUNFLG9CQUFvQjtFQUNwQiw0QkFBNEI7QUFDOUI7QUFDQTtFQUNFLG9CQUFvQjtBQUN0QjtBQUNBO0VBQ0Usb0JBQW9CO0FBQ3RCO0FBQ0E7RUFDRSxvQkFBb0I7QUFDdEIiLCJzb3VyY2VSb290IjoiIn0= */</style><style>.hljs {
  display: block;
  overflow-x: auto;
  padding: 12px;
  white-space: pre-wrap;
  -webkit-user-select: text;
     -moz-user-select: text;
          user-select: text;
  font-family: "OpenSans-Regular", Arial, Helvetica, sans-serif;
}
.hljs span {
  -webkit-user-select: text;
     -moz-user-select: text;
          user-select: text;
}
.dark {
  color: #EEEEEE;
  background: #303336;
}
.dark-comment,
.dark-quote {
  color: #5c6370;
}
.dark-doctag,
.dark-formula,
.dark-keyword {
  color: #c678dd;
}
.dark-deletion,
.dark-name,
.dark-section,
.dark-selector-tag,
.dark-subst {
  color: #e06c75;
}
.dark-literal {
  color: #56b6c2;
}
.dark-addition,
.dark-attribute,
.dark-meta-string,
.dark-regexp,
.dark-string {
  color: #98c379;
}
.dark-built_in,
.dark-class .dark-title {
  color: #e6c07b;
}
.dark-attr,
.dark-number,
.dark-selector-attr,
.dark-selector-class,
.dark-selector-pseudo,
.dark-template-variable,
.dark-type,
.dark-variable {
  color: #d19a66;
}
.dark-bullet,
.dark-link,
.dark-meta,
.dark-selector-id,
.dark-symbol,
.dark-title {
  color: #61aeee;
}
.dark-emphasis {
  font-style: italic;
}
.dark-strong {
  font-weight: 700;
}
.dark-link {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}
.light {
  color: #383a42;
  background: #E5F1FF;
}
.light-comment,
.light-quote {
  color: #a0a1a7;
}
.light-doctag,
.light-formula,
.light-keyword {
  color: #a626a4;
}
.light-deletion,
.light-name,
.light-section,
.light-selector-tag,
.light-subst {
  color: #e45649;
}
.light-literal {
  color: #0184bb;
}
.light-addition,
.light-attribute,
.light-meta-string,
.light-regexp,
.light-string {
  color: #50a14f;
}
.light-built_in,
.light-class .light-title {
  color: #c18401;
}
.light-attr,
.light-number,
.light-selector-attr,
.light-selector-class,
.light-selector-pseudo,
.light-template-variable,
.light-type,
.light-variable {
  color: #986801;
}
.light-bullet,
.light-link,
.light-meta,
.light-selector-id,
.light-symbol,
.light-title {
  color: #4078f2;
}
.light-emphasis {
  font-style: italic;
}
.light-strong {
  font-weight: 700;
}
.light-link {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9tb2R1bGUvcGluZUNvZGUvaGlnaGxpZ2h0Lmxlc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EscUJBQUE7RUFDQSx5QkFBQTtLQUFBLHNCQUFBO1VBQUEsaUJBQUE7RUFDQSw2REFBQTtBQUNGO0FBRUE7RUFDRSx5QkFBQTtLQUFBLHNCQUFBO1VBQUEsaUJBQUE7QUFBRjtBQUdBO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0FBREY7QUFHRTs7RUFDRSxjQUFBO0FBQUo7QUFJRTs7O0VBQ0UsY0FBQTtBQUFKO0FBR0U7Ozs7O0VBQ0UsY0FBQTtBQUdKO0FBQUU7RUFDRSxjQUFBO0FBRUo7QUFDRTs7Ozs7RUFDRSxjQUFBO0FBS0o7QUFGRTs7RUFDRSxjQUFBO0FBS0o7QUFGRTs7Ozs7Ozs7RUFDRSxjQUFBO0FBV0o7QUFSRTs7Ozs7O0VBQ0UsY0FBQTtBQWVKO0FBWkU7RUFDRSxrQkFBQTtBQWNKO0FBWEU7RUFDRSxnQkFBQTtBQWFKO0FBVkU7RUFDRSxrQ0FBQTtFQUFBLDBCQUFBO0FBWUo7QUFSQTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtBQVVGO0FBUkU7O0VBQ0UsY0FBQTtBQVdKO0FBUEU7OztFQUNFLGNBQUE7QUFXSjtBQVJFOzs7OztFQUNFLGNBQUE7QUFjSjtBQVhFO0VBQ0UsY0FBQTtBQWFKO0FBVkU7Ozs7O0VBQ0UsY0FBQTtBQWdCSjtBQWJFOztFQUNFLGNBQUE7QUFnQko7QUFiRTs7Ozs7Ozs7RUFDRSxjQUFBO0FBc0JKO0FBbkJFOzs7Ozs7RUFDRSxjQUFBO0FBMEJKO0FBdkJFO0VBQ0Usa0JBQUE7QUF5Qko7QUF0QkU7RUFDRSxnQkFBQTtBQXdCSjtBQXJCRTtFQUNFLGtDQUFBO0VBQUEsMEJBQUE7QUF1QkoiLCJzb3VyY2VSb290IjoiIn0= */</style><style>.splitter-layout {
  position: absolute;
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.splitter-layout .layout-pane {
  position: relative;
  flex: 0 0 auto;
}
.splitter-layout .layout-pane.layout-pane-primary {
  flex: 1 1 auto;
}
.splitter-layout > .layout-splitter {
  flex: 0 0 auto;
  background: linear-gradient(to left, transparent, transparent 1px, #4F4F4F 1px, transparent);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 10px;
  height: 100%;
  border-top: none;
  border-bottom: none;
  padding-left: 1px;
  cursor: col-resize;
}
.splitter-layout > .layout-splitter .layout-splitter-btn {
  width: 7px;
  height: 64px;
}
.splitter-layout.layout-changing {
  cursor: col-resize;
}
.splitter-layout.splitter-layout-vertical {
  flex-direction: column;
}
.splitter-layout.splitter-layout-vertical.layout-changing {
  cursor: row-resize;
}
.splitter-layout.splitter-layout-vertical > .layout-splitter {
  background: linear-gradient(to bottom, transparent, transparent 1px, #4F4F4F 1px, transparent);
  width: 100%;
  height: 10px;
  border-left: none;
  border-right: none;
  cursor: row-resize;
}
.splitter-layout.splitter-layout-vertical > .layout-splitter .layout-splitter-btn {
  width: 64px;
  height: 7px;
  margin: 1px 0;
}
.splitter-layout .layout-splitter-btn {
  cursor: pointer;
  background: #36393c;
  border-radius: 2px;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}
.splitter-layout .layout-splitter-btn:hover span {
  border-right-color: #EEEEEE;
}
.splitter-layout .layout-splitter-btn span {
  display: inline-block;
  border: 5px solid;
  border-color: transparent;
  border-right-color: #AAAEB2;
}
.splitter-layout .layout-splitter-btn span.up {
  transform: rotate(90deg);
  margin-bottom: 5px;
}
.splitter-layout .layout-splitter-btn span.down {
  transform: rotate(270deg);
  margin-top: 5px;
}
.splitter-layout .layout-splitter-btn span.left {
  transform: rotate(0deg);
  margin-right: 5px;
}
.splitter-layout .layout-splitter-btn span.right {
  transform: rotate(180deg);
  margin-left: 5px;
}

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9tb2R1bGUvd2VidWxsLXVpL1NwbGl0UGFuZWwvaW5kZXgubGVzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtBQUNGO0FBQ0U7RUFDRSxrQkFBQTtFQUNBLGNBQUE7QUFDSjtBQUNJO0VBQ0UsY0FBQTtBQUNOO0FBR0U7RUFDRSxjQUFBO0VBQ0EsNEZBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7QUFESjtBQVZFO0VBY0ksVUFBQTtFQUNBLFlBQUE7QUFETjtBQUtFO0VBQ0Usa0JBQUE7QUFISjtBQU1FO0VBQ0Usc0JBQUE7QUFKSjtBQU1JO0VBQ0Usa0JBQUE7QUFKTjtBQU9JO0VBQ0UsOEZBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQUxOO0FBREk7RUFTSSxXQUFBO0VBQ0EsV0FBQTtFQUNBLGFBQUE7QUFMUjtBQXJEQTtFQWdFSSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQVJKO0FBVUk7RUFFSSwyQkFBQTtBQVRSO0FBakVBO0VBK0VNLHFCQUFBO0VBQ0EsaUJBQUE7RUFDQSx5QkFBQTtFQUNBLDJCQUFBO0FBWE47QUF2RUE7RUFzRk0sd0JBQUE7RUFDQSxrQkFBQTtBQVpOO0FBM0VBO0VBMkZNLHlCQUFBO0VBQ0EsZUFBQTtBQWJOO0FBL0VBO0VBZ0dNLHVCQUFBO0VBQ0EsaUJBQUE7QUFkTjtBQW5GQTtFQXFHTSx5QkFBQTtFQUNBLGdCQUFBO0FBZk4iLCJzb3VyY2VSb290IjoiIn0= */</style><style>/* BASICS */

.CodeMirror {
  /* Set height, width, borders, and global font properties here */
  font-family: monospace;
  height: 300px;
  color: black;
  direction: ltr;
}

/* PADDING */

.CodeMirror-lines {
  padding: 4px 0; /* Vertical padding around content */
}
.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
  padding: 0 4px; /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  background-color: white; /* The little square between H and V scrollbars */
}

/* GUTTER */

.CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f7f7f7;
  white-space: nowrap;
}
.CodeMirror-linenumbers {}
.CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #999;
  white-space: nowrap;
}

.CodeMirror-guttermarker { color: black; }
.CodeMirror-guttermarker-subtle { color: #999; }

/* CURSOR */

.CodeMirror-cursor {
  border-left: 1px solid black;
  border-right: none;
  width: 0;
}
/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}
.cm-fat-cursor .CodeMirror-cursor {
  border: 0 !important;
}
.cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  background: #7e7;
}
.cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}
.cm-fat-cursor-mark {
  background-color: rgba(20, 255, 20, 0.5);
  animation: blink 1.06s steps(1) infinite;
}
.cm-animate-fat-cursor {
  width: auto;
  border: 0;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}
@keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}

/* Can style cursor different in overwrite (non-insert) mode */
.CodeMirror-overwrite .CodeMirror-cursor {}

.cm-tab { display: inline-block; text-decoration: inherit; }

.CodeMirror-rulers {
  position: absolute;
  left: 0; right: 0; top: -50px; bottom: 0;
  overflow: hidden;
}
.CodeMirror-ruler {
  border-left: 1px solid #ccc;
  top: 0; bottom: 0;
  position: absolute;
}

/* DEFAULT THEME */

.cm-s-default .cm-header {color: blue;}
.cm-s-default .cm-quote {color: #090;}
.cm-negative {color: #d44;}
.cm-positive {color: #292;}
.cm-header, .cm-strong {font-weight: bold;}
.cm-em {font-style: italic;}
.cm-link {-webkit-text-decoration: underline;text-decoration: underline;}
.cm-strikethrough {-webkit-text-decoration: line-through;text-decoration: line-through;}

.cm-s-default .cm-keyword {color: #708;}
.cm-s-default .cm-atom {color: #219;}
.cm-s-default .cm-number {color: #164;}
.cm-s-default .cm-def {color: #00f;}
.cm-s-default .cm-variable,
.cm-s-default .cm-punctuation,
.cm-s-default .cm-property,
.cm-s-default .cm-operator {}
.cm-s-default .cm-variable-2 {color: #05a;}
.cm-s-default .cm-variable-3, .cm-s-default .cm-type {color: #085;}
.cm-s-default .cm-comment {color: #a50;}
.cm-s-default .cm-string {color: #a11;}
.cm-s-default .cm-string-2 {color: #f50;}
.cm-s-default .cm-meta {color: #555;}
.cm-s-default .cm-qualifier {color: #555;}
.cm-s-default .cm-builtin {color: #30a;}
.cm-s-default .cm-bracket {color: #997;}
.cm-s-default .cm-tag {color: #170;}
.cm-s-default .cm-attribute {color: #00c;}
.cm-s-default .cm-hr {color: #999;}
.cm-s-default .cm-link {color: #00c;}

.cm-s-default .cm-error {color: #f00;}
.cm-invalidchar {color: #f00;}

.CodeMirror-composing { border-bottom: 2px solid; }

/* Default styles for common addons */

div.CodeMirror span.CodeMirror-matchingbracket {color: #0b0;}
div.CodeMirror span.CodeMirror-nonmatchingbracket {color: #a22;}
.CodeMirror-matchingtag { background: rgba(255, 150, 0, .3); }
.CodeMirror-activeline-background {background: #e8f2ff;}

/* STOP */

/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */

.CodeMirror {
  position: relative;
  overflow: hidden;
  background: white;
}

.CodeMirror-scroll {
  overflow: scroll !important;
}

.CodeMirror-scroll { /* Things will break if this is overridden */
  /* 30px is the magic margin used to hide the element's real scrollbars */
  /* See overflow: hidden in .CodeMirror */
  margin-bottom: -30px; margin-right: -30px;
  padding-bottom: 30px;
  height: 100%;
  outline: none; /* Prevent dragging from highlighting the element */
  position: relative;
}
.CodeMirror-sizer {
  position: relative;
  border-right: 30px solid transparent;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actual scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  position: absolute;
  z-index: 6;
  display: none;
}
.CodeMirror-vscrollbar {
  right: 0; top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}
.CodeMirror-hscrollbar {
  bottom: 0; left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}
.CodeMirror-scrollbar-filler {
  right: 0; bottom: 0;
}
.CodeMirror-gutter-filler {
  left: 0; bottom: 0;
}

.CodeMirror-gutters {
  position: absolute; left: 0; top: 0;
  min-height: 100%;
  z-index: 3;
}
.CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -30px;
}
.CodeMirror-gutter-wrapper {
  background: none !important;
  border: none !important;
}
.CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
}
.CodeMirror-gutter-background {
  position: absolute;
  top: 0; bottom: 0;
  z-index: 4;
}
.CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}
.CodeMirror-gutter-wrapper ::-moz-selection { background-color: transparent }
.CodeMirror-gutter-wrapper ::selection { background-color: transparent }
.CodeMirror-gutter-wrapper ::-moz-selection { background-color: transparent }

.CodeMirror-lines {
  cursor: text;
  min-height: 1px; /* prevents collapsing before first draw */
}
.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
  /* Reset some styles that the rest of the page might have set */ border-radius: 0;
  border-width: 0;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-variant-ligatures: contextual;
  font-feature-settings: "calt";
  font-variant-ligatures: contextual;
}
.CodeMirror-wrap pre.CodeMirror-line,
.CodeMirror-wrap pre.CodeMirror-line-like {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}

.CodeMirror-linebackground {
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  z-index: 0;
}

.CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  padding: 0.1px; /* Force widget margins to stay inside of the container */
}

.CodeMirror-widget {}

.CodeMirror-rtl pre { direction: rtl; }

.CodeMirror-code {
  outline: none;
}

/* Force content-box sizing for the elements where we expect it */
.CodeMirror-scroll,
.CodeMirror-sizer,
.CodeMirror-gutter,
.CodeMirror-gutters,
.CodeMirror-linenumber {
  box-sizing: content-box;
}

.CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.CodeMirror-cursor {
  position: absolute;
  pointer-events: none;
}
.CodeMirror-measure pre { position: static; }

div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}
div.CodeMirror-dragcursors {
  visibility: visible;
}

.CodeMirror-focused div.CodeMirror-cursors {
  visibility: visible;
}

.CodeMirror-selected { background: #d9d9d9; }
.CodeMirror-focused .CodeMirror-selected { background: #d7d4f0; }
.CodeMirror-crosshair { cursor: crosshair; }
.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection { background: #d7d4f0; }
.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection { background: #d7d4f0; }
.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection { background: #d7d4f0; }

.cm-searching {
  background-color: #ffa;
  background-color: rgba(255, 255, 0, .4);
}

/* Used to force a border model for a node */
.cm-force-border { padding-right: .1px; }

@media print {
  /* Hide the cursor when printing */
  .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}

/* See issue #2901 */
.cm-tab-wrap-hack:after { content: ''; }

/* Help users use markselection to safely style text background */
span.CodeMirror-selectedtext { background: none; }

/* Based on Sublime Text's dark theme */

.cm-s-dark.CodeMirror { background: #151718; color: #f8f8f2;  }
.cm-s-dark div.CodeMirror-selected { background: #49483E; }
.cm-s-dark .CodeMirror-line::-moz-selection, .cm-s-dark .CodeMirror-line > span::-moz-selection, .cm-s-dark .CodeMirror-line > span > span::-moz-selection { background: rgba(73, 72, 62, .99); }
.cm-s-dark .CodeMirror-line::selection, .cm-s-dark .CodeMirror-line > span::selection, .cm-s-dark .CodeMirror-line > span > span::selection { background: rgba(73, 72, 62, .99); }
.cm-s-dark .CodeMirror-line::-moz-selection, .cm-s-dark .CodeMirror-line > span::-moz-selection, .cm-s-dark .CodeMirror-line > span > span::-moz-selection { background: rgba(73, 72, 62, .99); }
.cm-s-dark .CodeMirror-gutters { background: #212426; border-right: 0px; }
.cm-s-dark .CodeMirror-guttermarker { color: white; }
.cm-s-dark .CodeMirror-guttermarker-subtle { color: #d0d0d0; }
.cm-s-dark .CodeMirror-linenumber { color: #d0d0d0; text-align: center; font-size: 12px;}
.cm-s-dark .CodeMirror-cursor { border-left: 1px solid #f8f8f0; }

.cm-s-dark span.cm-comment { color: #75715e; }
.cm-s-dark span.cm-atom { color: #ae81ff; }
.cm-s-dark span.cm-number { color: #ae81ff; }

.cm-s-dark span.cm-comment.cm-attribute { color: #97b757; }
.cm-s-dark span.cm-comment.cm-def { color: #bc9262; }
.cm-s-dark span.cm-comment.cm-tag { color: #bc6283; }
.cm-s-dark span.cm-comment.cm-type { color: #5998a6; }

.cm-s-dark span.cm-property, .cm-s-dark span.cm-attribute { color: #a6e22e}
.cm-s-dark span.cm-keyword { color: #f92672; }
.cm-s-dark span.cm-builtin { color: #66d9ef; cursor: pointer}
.cm-s-dark span.cm-string { color: #e6db74; }

.cm-s-dark span.cm-variable { color: #f8f8f2; }
.cm-s-dark span.cm-variable-2 { color: #9effff }
.cm-s-dark span.cm-variable-3, .cm-s-dark span.cm-type { color: #66d9ef; }
.cm-s-dark span.cm-def { color: #fd971f; }
.cm-s-dark span.cm-bracket { color: #f8f8f2; }
.cm-s-dark span.cm-tag { color: #f92672; }
.cm-s-dark span.cm-header { color: #ae81ff; }
.cm-s-dark span.cm-link { color: #ae81ff; }
.cm-s-dark span.cm-error { background: #f92672; color: #f8f8f0; }
.cm-s-dark .CodeMirror-lint-tooltip {
  background-color: #151616;
  border: 1px solid #4B4F52;
  border-radius: 3px;
  color: #EEEEEE;
  z-index: 9999;
  font-family: "OpenSans-Regular", Arial, Helvetica, sans-serif;
  display: flex;
  justify-content: center;
  align-content: center;
}
.cm-s-dark .CodeMirror-activeline-background { background: #373831; }
.cm-s-dark .CodeMirror-matchingbracket {
  color: white !important;
}
.cm-s-dark .CodeMirror-matchingbracket {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}
.cm-s-dark .syntax-error {
  background-color: #222324 !important;
}
.cm-s-dark .syntax-error {
  /* transition: background-color 1s ease; */
}

/* Based on Sublime Text's light theme */

.cm-s-light.CodeMirror { background: #FFFFFF; color: #09132C;  }
.cm-s-light div.CodeMirror-selected { background: rgb(179, 188, 201); }
.cm-s-light .CodeMirror-line::-moz-selection, .cm-s-light .CodeMirror-line > span::-moz-selection, .cm-s-light .CodeMirror-line > span > span::-moz-selection { background: rgba(73, 72, 62, .99); }
.cm-s-light .CodeMirror-line::selection, .cm-s-light .CodeMirror-line > span::selection, .cm-s-light .CodeMirror-line > span > span::selection { background: rgba(73, 72, 62, .99); }
.cm-s-light .CodeMirror-line::-moz-selection, .cm-s-light .CodeMirror-line > span::-moz-selection, .cm-s-light .CodeMirror-line > span > span::-moz-selection { background: rgba(73, 72, 62, .99); }
.cm-s-light .CodeMirror-gutters { background: #F1F2F6; border-right: 0px; }
.cm-s-light .CodeMirror-guttermarker { color: #09132C; }
.cm-s-light .CodeMirror-guttermarker-subtle { color: #09132C; }
.cm-s-light .CodeMirror-linenumber { color: #09132C; text-align: center; font-size: 12px; }
.cm-s-light .CodeMirror-cursor { border-left: 1px solid black; }

.cm-s-light span.cm-comment { color: #a86; }
.cm-s-light span.cm-keyword { line-height: 1em; color: blue; }
.cm-s-light span.cm-string { color: #1c258a; }
.cm-s-light span.cm-builtin { line-height: 1em; color: #077; cursor: pointer }
.cm-s-light span.cm-special { line-height: 1em; color: #0aa; }
.cm-s-light span.cm-variable { color: black; }
.cm-s-light span.cm-variable-2 { color: #e63534 }
.cm-s-light span.cm-variable-3, .cm-s-light span.cm-type { color: #077; }
.cm-s-light span.cm-property, .cm-s-light span.cm-attribute { color: #8a38b9}
.cm-s-light span.cm-number, .cm-s-neat span.cm-atom { color: #3a3; }
.cm-s-light span.cm-meta { color: #555; }
.cm-s-light span.cm-link { color: #3a3; }
.cm-s-light .CodeMirror-lint-tooltip {
  background-color: #FFFFFF;
  border: 1px solid #D9DAE0;
  border-radius: 2px;
  color: #09132C;
  z-index: 9999;
  font-family: "OpenSans-Regular", Arial, Helvetica, sans-serif;
  display: flex;
  justify-content: center;
  align-content: center;
}
.cm-s-neat .CodeMirror-activeline-background { background: #e8f2ff; }
.cm-s-neat .CodeMirror-matchingbracket { color:black !important; }
.cm-s-neat .CodeMirror-matchingbracket { outline:1px solid grey; }
.cm-s-light .syntax-error {
  background-color: #F2F6FA !important;
}
.cm-s-light .syntax-error {
  /* transition: background-color 1s ease; */
}




/* The lint marker gutter */
.CodeMirror-lint-markers {
  width: 16px;
}

.CodeMirror-lint-tooltip {
  background-color: #ffd;
  border: 1px solid black;
  border-radius: 4px 4px 4px 4px;
  color: black;
  font-family: monospace;
  font-size: 10pt;
  overflow: hidden;
  padding: 2px 5px;
  position: fixed;
  white-space: pre;
  white-space: pre-wrap;
  z-index: 100;
  max-width: 600px;
  opacity: 0;
  transition: opacity .4s;
  -moz-transition: opacity .4s;
  -webkit-transition: opacity .4s;
  -o-transition: opacity .4s;
  -ms-transition: opacity .4s;
}

.CodeMirror-lint-mark-error, .CodeMirror-lint-mark-warning {
  background-position: left bottom;
  background-repeat: repeat-x;
}

.CodeMirror-lint-mark-error {
  background-image:
  url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAADCAYAAAC09K7GAAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9sJDw4cOCW1/KIAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAHElEQVQI12NggIL/DAz/GdA5/xkY/qPKMDAwAADLZwf5rvm+LQAAAABJRU5ErkJggg==);
}

.CodeMirror-lint-mark-warning {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAADCAYAAAC09K7GAAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9sJFhQXEbhTg7YAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAMklEQVQI12NkgIIvJ3QXMjAwdDN+OaEbysDA4MPAwNDNwMCwiOHLCd1zX07o6kBVGQEAKBANtobskNMAAAAASUVORK5CYII=);
}

.CodeMirror-lint-marker-error, .CodeMirror-lint-marker-warning {
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  display: inline-block;
  height: 16px;
  width: 16px;
  vertical-align: middle;
  position: relative;
}

.CodeMirror-lint-message-error, .CodeMirror-lint-message-warning {
  padding-left: 18px;
  background-position: top left;
  background-repeat: no-repeat;
}

.CodeMirror-lint-marker-error, .CodeMirror-lint-message-error {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAHlBMVEW7AAC7AACxAAC7AAC7AAAAAAC4AAC5AAD///+7AAAUdclpAAAABnRSTlMXnORSiwCK0ZKSAAAATUlEQVR42mWPOQ7AQAgDuQLx/z8csYRmPRIFIwRGnosRrpamvkKi0FTIiMASR3hhKW+hAN6/tIWhu9PDWiTGNEkTtIOucA5Oyr9ckPgAWm0GPBog6v4AAAAASUVORK5CYII=);
  background-position-y: center;
}

.CodeMirror-lint-marker-warning, .CodeMirror-lint-message-warning {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAANlBMVEX/uwDvrwD/uwD/uwD/uwD/uwD/uwD/uwD/uwD6twD/uwAAAADurwD2tQD7uAD+ugAAAAD/uwDhmeTRAAAADHRSTlMJ8mN1EYcbmiixgACm7WbuAAAAVklEQVR42n3PUQqAIBBFUU1LLc3u/jdbOJoW1P08DA9Gba8+YWJ6gNJoNYIBzAA2chBth5kLmG9YUoG0NHAUwFXwO9LuBQL1giCQb8gC9Oro2vp5rncCIY8L8uEx5ZkAAAAASUVORK5CYII=);
  background-position-y: center;
}

.CodeMirror-lint-marker-multiple {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAHCAMAAADzjKfhAAAACVBMVEUAAAAAAAC/v7914kyHAAAAAXRSTlMAQObYZgAAACNJREFUeNo1ioEJAAAIwmz/H90iFFSGJgFMe3gaLZ0od+9/AQZ0ADosbYraAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
  background-position: right bottom;
  width: 100%; height: 100%;
}

.react-codemirror2 {
  width: 100%;
  height: 100%;
}
.CodeMirror {
  height: 323px;
  line-height: 20px;
  letter-spacing: 0;
  font-size: 14px;
  font-family: "OpenSans-Regular", Arial, Helvetica, sans-serif;
}

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL2Fzc2V0cy9zdHlsZS9jb2RlbWlycm9yLmNzcyIsIndlYnBhY2s6Ly8uL2Fzc2V0cy9zdHlsZS9kYXJrLmNzcyIsIndlYnBhY2s6Ly8uL2Fzc2V0cy9zdHlsZS9saWdodC5jc3MiLCJ3ZWJwYWNrOi8vLi9hc3NldHMvc3R5bGUvbGludC5jc3MiLCJ3ZWJwYWNrOi8vLi9hc3NldHMvc3R5bGUvY29kZW1pcnJvci5sZXNzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLFdBQUE7O0FBRUE7RUFDQSxnRUFBQTtFQUNBLHNCQUFBO0VBQ0EsYUFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FBQ0E7O0FBRUEsWUFBQTs7QUFFQTtFQUNBLGNBQUEsRUFBQSxvQ0FBQTtBQUNBO0FBQ0E7O0VBRUEsY0FBQSxFQUFBLGtDQUFBO0FBQ0E7O0FBRUE7RUFDQSx1QkFBQSxFQUFBLGlEQUFBO0FBQ0E7O0FBRUEsV0FBQTs7QUFFQTtFQUNBLDRCQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtBQUNBO0FBQ0EseUJBQUE7QUFDQTtFQUNBLG9CQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0FBQ0E7O0FBRUEsMkJBQUEsWUFBQSxFQUFBO0FBQ0Esa0NBQUEsV0FBQSxFQUFBOztBQUVBLFdBQUE7O0FBRUE7RUFDQSw0QkFBQTtFQUNBLGtCQUFBO0VBQ0EsUUFBQTtBQUNBO0FBQ0EsNkNBQUE7QUFDQTtFQUNBLDZCQUFBO0FBQ0E7QUFDQTtFQUVBLG9CQUFBO0FBRUE7QUFKQTtFQUNBLFdBQUE7RUFFQSxnQkFBQTtBQUNBO0FBQ0E7RUFDQSxVQUFBO0FBQ0E7QUFDQTtFQUNBLHdDQUFBO0VBR0Esd0NBQUE7QUFDQTtBQUNBO0VBQ0EsV0FBQTtFQUNBLFNBQUE7RUFHQSx3Q0FBQTtFQUNBLHNCQUFBO0FBQ0E7QUFXQTtFQUNBLElBQUE7RUFDQSxNQUFBLDZCQUFBLEVBQUE7RUFDQSxNQUFBO0FBQ0E7O0FBRUEsOERBQUE7QUFDQSwwQ0FBQTs7QUFFQSxVQUFBLHFCQUFBLEVBQUEsd0JBQUEsRUFBQTs7QUFFQTtFQUNBLGtCQUFBO0VBQ0EsT0FBQSxFQUFBLFFBQUEsRUFBQSxVQUFBLEVBQUEsU0FBQTtFQUNBLGdCQUFBO0FBQ0E7QUFDQTtFQUNBLDJCQUFBO0VBQ0EsTUFBQSxFQUFBLFNBQUE7RUFDQSxrQkFBQTtBQUNBOztBQUVBLGtCQUFBOztBQUVBLDBCQUFBLFdBQUEsQ0FBQTtBQUNBLHlCQUFBLFdBQUEsQ0FBQTtBQUNBLGNBQUEsV0FBQSxDQUFBO0FBQ0EsY0FBQSxXQUFBLENBQUE7QUFDQSx3QkFBQSxpQkFBQSxDQUFBO0FBQ0EsUUFBQSxrQkFBQSxDQUFBO0FBQ0EsVUFBQSxrQ0FBQSxDQUFBLDBCQUFBLENBQUE7QUFDQSxtQkFBQSxxQ0FBQSxDQUFBLDZCQUFBLENBQUE7O0FBRUEsMkJBQUEsV0FBQSxDQUFBO0FBQ0Esd0JBQUEsV0FBQSxDQUFBO0FBQ0EsMEJBQUEsV0FBQSxDQUFBO0FBQ0EsdUJBQUEsV0FBQSxDQUFBO0FBQ0E7Ozs0QkFHQTtBQUNBLDhCQUFBLFdBQUEsQ0FBQTtBQUNBLHNEQUFBLFdBQUEsQ0FBQTtBQUNBLDJCQUFBLFdBQUEsQ0FBQTtBQUNBLDBCQUFBLFdBQUEsQ0FBQTtBQUNBLDRCQUFBLFdBQUEsQ0FBQTtBQUNBLHdCQUFBLFdBQUEsQ0FBQTtBQUNBLDZCQUFBLFdBQUEsQ0FBQTtBQUNBLDJCQUFBLFdBQUEsQ0FBQTtBQUNBLDJCQUFBLFdBQUEsQ0FBQTtBQUNBLHVCQUFBLFdBQUEsQ0FBQTtBQUNBLDZCQUFBLFdBQUEsQ0FBQTtBQUNBLHNCQUFBLFdBQUEsQ0FBQTtBQUNBLHdCQUFBLFdBQUEsQ0FBQTs7QUFFQSx5QkFBQSxXQUFBLENBQUE7QUFDQSxpQkFBQSxXQUFBLENBQUE7O0FBRUEsd0JBQUEsd0JBQUEsRUFBQTs7QUFFQSxxQ0FBQTs7QUFFQSxnREFBQSxXQUFBLENBQUE7QUFDQSxtREFBQSxXQUFBLENBQUE7QUFDQSwwQkFBQSxpQ0FBQSxFQUFBO0FBQ0EsbUNBQUEsbUJBQUEsQ0FBQTs7QUFFQSxTQUFBOztBQUVBO21EQUNBOztBQUVBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0FBQ0E7O0FBRUE7RUFDQSwyQkFBQTtBQVFBOztBQVRBLHFCQUNBLDRDQUFBO0VBQ0Esd0VBQUE7RUFDQSx3Q0FBQTtFQUNBLG9CQUFBLEVBQUEsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBLEVBQUEsbURBQUE7RUFDQSxrQkFBQTtBQUNBO0FBQ0E7RUFDQSxrQkFBQTtFQUNBLG9DQUFBO0FBQ0E7O0FBRUE7OzBCQUVBO0FBQ0E7RUFDQSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxhQUFBO0FBQ0E7QUFDQTtFQUNBLFFBQUEsRUFBQSxNQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQUNBO0FBQ0E7RUFDQSxTQUFBLEVBQUEsT0FBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUFDQTtBQUNBO0VBQ0EsUUFBQSxFQUFBLFNBQUE7QUFDQTtBQUNBO0VBQ0EsT0FBQSxFQUFBLFNBQUE7QUFDQTs7QUFFQTtFQUNBLGtCQUFBLEVBQUEsT0FBQSxFQUFBLE1BQUE7RUFDQSxnQkFBQTtFQUNBLFVBQUE7QUFDQTtBQUNBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EscUJBQUE7RUFDQSxtQkFBQTtFQUNBLG9CQUFBO0FBQ0E7QUFDQTtFQUdBLDJCQUFBO0VBQ0EsdUJBQUE7QUFDQTtBQUxBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBR0E7QUFDQTtFQUNBLGtCQUFBO0VBQ0EsTUFBQSxFQUFBLFNBQUE7RUFDQSxVQUFBO0FBQ0E7QUFDQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLFVBQUE7QUFDQTtBQUNBLDhDQUFBLDhCQUFBO0FBQUEseUNBQUEsOEJBQUE7QUFDQSw4Q0FBQSw4QkFBQTs7QUFFQTtFQUNBLFlBQUE7RUFDQSxlQUFBLEVBQUEsMENBQUE7QUFDQTtBQUNBOztFQUVBLCtEQUFBLEVBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EsdUJBQUE7RUFDQSxvQkFBQTtFQUNBLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxvQkFBQTtFQUNBLGNBQUE7RUFDQSxVQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLHdDQUFBO0VBQ0EsMENBQUE7RUFDQSw2QkFBQTtFQUFBLGtDQUFBO0FBQ0E7QUFDQTs7RUFFQSxxQkFBQTtFQUNBLHFCQUFBO0VBQ0Esa0JBQUE7QUFDQTs7QUFFQTtFQUNBLGtCQUFBO0VBQ0EsT0FBQSxFQUFBLFFBQUEsRUFBQSxNQUFBLEVBQUEsU0FBQTtFQUNBLFVBQUE7QUFDQTs7QUFFQTtFQUNBLGtCQUFBO0VBQ0EsVUFBQTtFQUNBLGNBQUEsRUFBQSx5REFBQTtBQUNBOztBQUVBLG9CQUFBOztBQUVBLHNCQUFBLGNBQUEsRUFBQTs7QUFFQTtFQUNBLGFBQUE7QUFDQTs7QUFFQSxpRUFBQTtBQUNBOzs7OztFQU1BLHVCQUFBO0FBQ0E7O0FBRUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxTQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQUNBOztBQUVBO0VBQ0Esa0JBQUE7RUFDQSxvQkFBQTtBQUNBO0FBQ0EsMEJBQUEsZ0JBQUEsRUFBQTs7QUFFQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBQ0E7QUFDQTtFQUNBLG1CQUFBO0FBQ0E7O0FBRUE7RUFDQSxtQkFBQTtBQUNBOztBQUVBLHVCQUFBLG1CQUFBLEVBQUE7QUFDQSwyQ0FBQSxtQkFBQSxFQUFBO0FBQ0Esd0JBQUEsaUJBQUEsRUFBQTtBQUNBLDRIQUFBLG1CQUFBLEVBQUE7QUFBQSw2R0FBQSxtQkFBQSxFQUFBO0FBQ0EsNEhBQUEsbUJBQUEsRUFBQTs7QUFFQTtFQUNBLHNCQUFBO0VBQ0EsdUNBQUE7QUFDQTs7QUFFQSw0Q0FBQTtBQUNBLG1CQUFBLG1CQUFBLEVBQUE7O0FBRUE7RUFDQSxrQ0FBQTtFQUNBO0lBQ0Esa0JBQUE7RUFDQTtBQUNBOztBQUVBLG9CQUFBO0FBQ0EsMEJBQUEsV0FBQSxFQUFBOztBQUVBLGlFQUFBO0FBQ0EsK0JBQUEsZ0JBQUEsRUFBQTs7QUM1VkEsdUNBQUE7O0FBRUEsd0JBQUEsbUJBQUEsRUFBQSxjQUFBLEdBQUE7QUFDQSxxQ0FBQSxtQkFBQSxFQUFBO0FBQ0EsNkpBQUEsaUNBQUEsRUFBQTtBQUFBLDhJQUFBLGlDQUFBLEVBQUE7QUFDQSw2SkFBQSxpQ0FBQSxFQUFBO0FBQ0EsaUNBQUEsbUJBQUEsRUFBQSxpQkFBQSxFQUFBO0FBQ0Esc0NBQUEsWUFBQSxFQUFBO0FBQ0EsNkNBQUEsY0FBQSxFQUFBO0FBQ0Esb0NBQUEsY0FBQSxFQUFBLGtCQUFBLEVBQUEsZUFBQSxDQUFBO0FBQ0EsZ0NBQUEsOEJBQUEsRUFBQTs7QUFFQSw2QkFBQSxjQUFBLEVBQUE7QUFDQSwwQkFBQSxjQUFBLEVBQUE7QUFDQSw0QkFBQSxjQUFBLEVBQUE7O0FBRUEsMENBQUEsY0FBQSxFQUFBO0FBQ0Esb0NBQUEsY0FBQSxFQUFBO0FBQ0Esb0NBQUEsY0FBQSxFQUFBO0FBQ0EscUNBQUEsY0FBQSxFQUFBOztBQUVBLDREQUFBLGNBQUE7QUFDQSw2QkFBQSxjQUFBLEVBQUE7QUFDQSw2QkFBQSxjQUFBLEVBQUEsZUFBQTtBQUNBLDRCQUFBLGNBQUEsRUFBQTs7QUFFQSw4QkFBQSxjQUFBLEVBQUE7QUFDQSxnQ0FBQSxlQUFBO0FBQ0EseURBQUEsY0FBQSxFQUFBO0FBQ0EseUJBQUEsY0FBQSxFQUFBO0FBQ0EsNkJBQUEsY0FBQSxFQUFBO0FBQ0EseUJBQUEsY0FBQSxFQUFBO0FBQ0EsNEJBQUEsY0FBQSxFQUFBO0FBQ0EsMEJBQUEsY0FBQSxFQUFBO0FBQ0EsMkJBQUEsbUJBQUEsRUFBQSxjQUFBLEVBQUE7QUFDQTtFQUNBLHlCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsNkRBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxxQkFBQTtBQUNBO0FBQ0EsK0NBQUEsbUJBQUEsRUFBQTtBQUNBO0VBRUEsdUJBQUE7QUFDQTtBQUhBO0VBQ0Esa0NBQUE7RUFBQSwwQkFBQTtBQUVBO0FBQ0E7RUFFQSxvQ0FBQTtBQUNBO0FBSEE7RUFDQSwwQ0FBQTtBQUVBOztBQ3REQSx3Q0FBQTs7QUFFQSx5QkFBQSxtQkFBQSxFQUFBLGNBQUEsR0FBQTtBQUNBLHNDQUFBLDhCQUFBLEVBQUE7QUFDQSxnS0FBQSxpQ0FBQSxFQUFBO0FBQUEsaUpBQUEsaUNBQUEsRUFBQTtBQUNBLGdLQUFBLGlDQUFBLEVBQUE7QUFDQSxrQ0FBQSxtQkFBQSxFQUFBLGlCQUFBLEVBQUE7QUFDQSx1Q0FBQSxjQUFBLEVBQUE7QUFDQSw4Q0FBQSxjQUFBLEVBQUE7QUFDQSxxQ0FBQSxjQUFBLEVBQUEsa0JBQUEsRUFBQSxlQUFBLEVBQUE7QUFDQSxpQ0FBQSw0QkFBQSxFQUFBOztBQUVBLDhCQUFBLFdBQUEsRUFBQTtBQUNBLDhCQUFBLGdCQUFBLEVBQUEsV0FBQSxFQUFBO0FBQ0EsNkJBQUEsY0FBQSxFQUFBO0FBQ0EsOEJBQUEsZ0JBQUEsRUFBQSxXQUFBLEVBQUEsZ0JBQUE7QUFDQSw4QkFBQSxnQkFBQSxFQUFBLFdBQUEsRUFBQTtBQUNBLCtCQUFBLFlBQUEsRUFBQTtBQUNBLGlDQUFBLGVBQUE7QUFDQSwyREFBQSxXQUFBLEVBQUE7QUFDQSw4REFBQSxjQUFBO0FBQ0Esc0RBQUEsV0FBQSxFQUFBO0FBQ0EsMkJBQUEsV0FBQSxFQUFBO0FBQ0EsMkJBQUEsV0FBQSxFQUFBO0FBQ0E7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsYUFBQTtFQUNBLDZEQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EscUJBQUE7QUFDQTtBQUNBLCtDQUFBLG1CQUFBLEVBQUE7QUFDQSx5Q0FBQSxzQkFBQSxFQUFBO0FBQUEseUNBQUEsc0JBQUEsRUFBQTtBQUNBO0VBRUEsb0NBQUE7QUFDQTtBQUhBO0VBQ0EsMENBQUE7QUFFQTs7Ozs7QUN4Q0EsMkJBQUE7QUFDQTtFQUNBLFdBQUE7QUFDQTs7QUFFQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSw4QkFBQTtFQUNBLFlBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxVQUFBO0VBQ0EsdUJBQUE7RUFDQSw0QkFBQTtFQUNBLCtCQUFBO0VBQ0EsMEJBQUE7RUFDQSwyQkFBQTtBQUNBOztBQUVBO0VBQ0EsZ0NBQUE7RUFDQSwyQkFBQTtBQUNBOztBQUVBO0VBQ0E7eUNBQ0E7QUFDQTs7QUFFQTtFQUNBLHlEQUFBO0FBQ0E7O0FBRUE7RUFDQSxrQ0FBQTtFQUNBLDRCQUFBO0VBQ0EsZUFBQTtFQUNBLHFCQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0FBQ0E7O0FBRUE7RUFDQSxrQkFBQTtFQUNBLDZCQUFBO0VBQ0EsNEJBQUE7QUFDQTs7QUFFQTtFQUNBLHlEQUFBO0VBQ0EsNkJBQUE7QUFDQTs7QUFFQTtFQUNBLHlEQUFBO0VBQ0EsNkJBQUE7QUFDQTs7QUFFQTtFQUNBLHlEQUFBO0VBQ0EsNEJBQUE7RUFDQSxpQ0FBQTtFQUNBLFdBQUEsRUFBQSxZQUFBO0FBQ0E7O0FDcEVBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUEwZ0JGO0FBdmdCQTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZUFBQTtFQUNBLDZEQUFBO0FBeWdCRiIsInNvdXJjZVJvb3QiOiIifQ== */</style><style>:root {
  --swiper-navigation-size: 44px;
  /*
  --swiper-navigation-top-offset: 50%;
  --swiper-navigation-sides-offset: 10px;
  --swiper-navigation-color: var(--swiper-theme-color);
  */
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: 50%;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(44px / 44 * 27);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: 44px;
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (44px / 2));
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}
.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}
.swiper-button-prev.swiper-button-hidden,
.swiper-button-next.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}
.swiper-navigation-disabled .swiper-button-prev,
.swiper-navigation-disabled .swiper-button-next {
  display: none !important;
}
.swiper-button-prev svg,
.swiper-button-next svg {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  transform-origin: center;
}
.swiper-rtl .swiper-button-prev svg,
.swiper-rtl .swiper-button-next svg {
  transform: rotate(180deg);
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  left: 10px;
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: 10px;
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-lock {
  display: none;
}
/* Navigation font start */
.swiper-button-prev:after,
.swiper-button-next:after {
  text-transform: none !important;
}
.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: 44px;
  font-size: var(--swiper-navigation-size);
  letter-spacing: 0;
  font-feature-settings: ;
  font-variant: normal;
  font-variant: initial;
  line-height: 1;
}
.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: 'prev';
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: 10px;
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: 'next';
}
/* Navigation font end */

/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL25vZGVfbW9kdWxlcy9zd2lwZXIvbW9kdWxlcy9uYXZpZ2F0aW9uLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLDhCQUE4QjtFQUM5Qjs7OztHQUlDO0FBQ0g7QUFDQTs7RUFFRSxrQkFBa0I7RUFDbEIsUUFBNkM7RUFBN0MsNkNBQTZDO0VBQzdDLDJCQUFvRDtFQUFwRCxvREFBb0Q7RUFDcEQsWUFBcUM7RUFBckMscUNBQXFDO0VBQ3JDLGtDQUEyRDtFQUEzRCwyREFBMkQ7RUFDM0QsV0FBVztFQUNYLGVBQWU7RUFDZixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixnRUFBZ0U7QUFDbEU7QUFDQTs7RUFFRSxhQUFhO0VBQ2IsWUFBWTtFQUNaLG9CQUFvQjtBQUN0QjtBQUNBOztFQUVFLFVBQVU7RUFDVixZQUFZO0VBQ1osb0JBQW9CO0FBQ3RCO0FBQ0E7O0VBRUUsd0JBQXdCO0FBQzFCO0FBQ0E7O0VBRUUsV0FBVztFQUNYLFlBQVk7RUFDWixzQkFBbUI7S0FBbkIsbUJBQW1CO0VBQ25CLHdCQUF3QjtBQUMxQjtBQUNBOztFQUVFLHlCQUF5QjtBQUMzQjtBQUNBOztFQUVFLFVBQWlEO0VBQWpELGlEQUFpRDtFQUNqRCxXQUFXO0FBQ2I7QUFDQTs7RUFFRSxXQUFrRDtFQUFsRCxrREFBa0Q7RUFDbEQsVUFBVTtBQUNaO0FBQ0E7RUFDRSxhQUFhO0FBQ2Y7QUFDQSwwQkFBMEI7QUFDMUI7O0VBSUUsK0JBQStCO0FBSWpDO0FBUkE7O0VBRUUseUJBQXlCO0VBQ3pCLGVBQXdDO0VBQXhDLHdDQUF3QztFQUV4QyxpQkFBaUI7RUFDakIsdUJBQXFCO0VBQXJCLG9CQUFxQjtFQUFyQixxQkFBcUI7RUFDckIsY0FBYztBQUNoQjtBQUNBOztFQUVFLGVBQWU7QUFDakI7QUFDQTs7RUFFRSxXQUFrRDtFQUFsRCxrREFBa0Q7RUFDbEQsVUFBVTtBQUNaO0FBQ0E7O0VBRUUsZUFBZTtBQUNqQjtBQUNBLHdCQUF3QiIsInNvdXJjZVJvb3QiOiIifQ== */</style><style>:root {
  /*
  --swiper-pagination-color: var(--swiper-theme-color);
  --swiper-pagination-left: auto;
  --swiper-pagination-right: 8px;
  --swiper-pagination-bottom: 8px;
  --swiper-pagination-top: auto;
  --swiper-pagination-fraction-color: inherit;
  --swiper-pagination-progressbar-bg-color: rgba(0,0,0,0.25);
  --swiper-pagination-progressbar-size: 4px;
  --swiper-pagination-bullet-size: 8px;
  --swiper-pagination-bullet-width: 8px;
  --swiper-pagination-bullet-height: 8px;
  --swiper-pagination-bullet-border-radius: 50%;
  --swiper-pagination-bullet-inactive-color: #000;
  --swiper-pagination-bullet-inactive-opacity: 0.2;
  --swiper-pagination-bullet-opacity: 1;
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bullet-vertical-gap: 6px;
  */
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}
.swiper-pagination-disabled > .swiper-pagination,
.swiper-pagination.swiper-pagination-disabled {
  display: none !important;
}
/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 8px;
  bottom: var(--swiper-pagination-bottom, 8px);
  top: auto;
  top: var(--swiper-pagination-top, auto);
  left: 0;
  width: 100%;
}
/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transform: scale(0.33);
  position: relative;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  transform: scale(0.33);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  transform: scale(0.33);
}
.swiper-pagination-bullet {
  width: 8px;
  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
  height: 8px;
  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
  display: inline-block;
  border-radius: 50%;
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  background: #000;
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  opacity: 0.2;
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
}
button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
  -webkit-appearance: none;
          -moz-appearance: none;
       appearance: none;
}
.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}
.swiper-pagination-bullet:only-child {
  display: none !important;
}
.swiper-pagination-bullet-active {
  opacity: 1;
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
}
.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: 8px;
  right: var(--swiper-pagination-right, 8px);
  left: auto;
  left: var(--swiper-pagination-left, auto);
  top: 50%;
  transform: translate3d(0px, -50%, 0);
}
.swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 6px 0;
  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  display: block;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  transition: 200ms transform, 200ms top;
}
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 4px;
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform, 200ms left;
}
.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform, 200ms right;
}
/* Fraction */
.swiper-pagination-fraction {
  color: inherit;
  color: var(--swiper-pagination-fraction-color, inherit);
}
/* Progress */
.swiper-pagination-progressbar {
  background: rgba(0, 0, 0, 0.25);
  background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, 0.25));
  position: absolute;
}
.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: scale(0);
  transform-origin: left top;
}
.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  transform-origin: right top;
}
.swiper-horizontal > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-horizontal,
.swiper-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: 4px;
  height: var(--swiper-pagination-progressbar-size, 4px);
  left: 0;
  top: 0;
}
.swiper-vertical > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-vertical,
.swiper-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
  width: 4px;
  width: var(--swiper-pagination-progressbar-size, 4px);
  height: 100%;
  left: 0;
  top: 0;
}
.swiper-pagination-lock {
  display: none;
}

/*# sourceMappingURL=data:application/json;base64,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 */</style><style data-jss="" data-meta="makeStyles">
.jss43 {
  width: 1em;
  height: 1em;
  display: inline-block;
  overflow: hidden;
  vertical-align: -0.15em;
}
@-webkit-keyframes jss45 {
  from {
    transform: rotate(0);
    transform-origin: 50% 50%;
  }
  to {
    transform: rotate(1turn);
    transform-origin: 50% 50%;
  }
}
.jss46 {
  display: inline-block;
  animation: jss45 1s linear forwards;
}
.jss47 {
  display: inline-block;
  animation: jss45 1s linear infinite forwards;
}
.jss48 {
  cursor: not-allowed !important;
  opacity: 0.5;
}
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
.jss37 {
  z-index: 99999;
}
.jss38 {
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 4px;
}
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="Icon">
.jss339 {
  color: #5D606D;
  cursor: inherit;
  display: inline-block;
  font-size: 24px;
  font-style: normal;
  font-family: iconfont!important;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
}
.jss340 {
  cursor: pointer;
}
.jss340:hover {
  color: #181C2F;
}
@-webkit-keyframes jss341 {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
.jss342 {
  animation: jss341 1s linear forwards;
}
.jss343 {
  animation: jss341 1s linear infinite forwards;
}
.jss344 {
  color: rgba(9,19,44,0.5);
  width: 24px;
  cursor: pointer;
  height: 24px;
  display: block;
  font-size: 24px;
  background: #F1F3F6;
  text-align: center;
  line-height: 24px;
  border-radius:  2px;
}
.jss344:after {
  border-color: #D9DAE0;
}
.jss344:hover {
  color: rgba(9,19,44,0.7);
}
.jss345 {
  color: rgba(9,19,44,0.5);
  background-color: #F1F3F6;
}
.jss345:after {
  border-color: #D9DAE0;
}
.jss346 {
  color: #0057FF;
  background-color: rgba(0,87,255,0.15);
}
.jss346:after {
  border-color: #0057FF;
}
.jss346:hover {
  color: #0057FF;
}
.jss347 {
  position: relative;
}
.jss347:after {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 0.5px;
  border-radius: 2px;
  pointer-events: none;
}
</style><style data-jss="" data-meta="Select">
.jss136 {
  display: none;
}
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="Select">
</style><style data-jss="" data-meta="makeStyles">

</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">

</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
.jss711 {
  color: #181C2F;
  cursor: pointer;
  display: flex;
  font-size: 12px;
  min-height: 12px;
  align-items: center;
}
.jss711:hover {
  color: #181C2F;
}
.jss712 {
  cursor: not-allowed;
}
.jss712, .jss712:hover {
  color: #8C8E97;
}
.jss713 {
  font-size: 6px;
  transition: 200ms transform linear;
  margin-left: 4px;
}
.jss714 {
  transform: rotate(-180deg);
}
</style><style data-jss="" data-meta="makeStyles">
.jss238 {
  color: #181C2F;
  border: 1px solid;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-color: #E2E7ED;
  border-radius: 4px;
  justify-content: space-between;
}
.jss238:hover {
  border-color: #DBE1E8;
}
.jss239 {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss240 {
  cursor: not-allowed;
}
.jss240, .jss240:hover {
  color: #8C8E97;
  border-color: #E2E7ED;
  background-color: #F8FAFD;
}
.jss241, .jss241:hover {
  border-color: #0057FF;
}
.jss242 {
  font-size: 16px;
  transition: 200ms transform linear;
}
.jss243 {
  transform: rotate(-180deg);
}
.jss244 {
  color: #0057FF;
}
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
.jss721 {
  display: flex;
  z-index: 99999;
  align-items: center;
  justify-content: center;
  background-color: rgba(15,21,39,0.6);
}
.jss722 {
  width: 600px;
  height: 600px;
  display: flex;
  overflow: hidden;
  border-radius: 8px;
  flex-direction: column;
}
.jss724 {
  width: 100%;
  position: relative;
}
.jss725 {
  top: 12px;
  right: 12px;
  width: 20px;
  border: 0.5px solid rgba(0, 0, 0, .4);
  cursor: pointer;
  height: 20px;
  outline: none;
  z-index: 1;
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, .4);
}
.jss725:hover {
  background-color: rgba(255, 255, 255, .5);
}
.jss727 {
  flex: 1;
  color: #181C2F;
  display: flex;
  padding: 20px;
  flex-direction: column;
  background-color: #FFFFFF;
}
.jss728 {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}
.jss729 {
  flex: 1;
}
.jss730 {
  text-align: right;
}
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
.jss338 {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3000;
  position: fixed;
}
</style><style data-jss="" data-meta="Container">
.jss194 {
  flex: 1;
  height: 100%;
  display: flex;
  overflow: hidden;
  position: relative;
  min-width: 1000px;
  background: #DBE1E8;
}
</style><style data-jss="" data-meta="makeStyles">
.jss12 {
  flex: 1;
  width: 600px;
  height: 100%;
  padding: 10px 20px 0;
  overflow: hidden;
  min-height: 448px;
  line-height: 20px;
}
.jss13 {
  color: #0057FF;
  cursor: pointer;
}
.jss14 {
  top: 0;
  left: 0;
  width: 100%;
  border: 0;
  height: 100%;
  position: absolute;
  scrolling: no;
  frame-border: 0;
  margin-width: 0;
  margin-height: 0;
  allow-transparency: yes;
}
.jss14 .webview-page {
  width: 100%;
  height: 100%;
}
.jss15 {
  width: 100%;
  border: 1px solid #F3F6FA;
  height: 300px;
  padding: 16px 0px 0px 16px;
  overflow: auto;
  position: relative;
  background: #F8FAFD;
  border-radius: 4px;
  margin-bottom: 20px;
  padding-right: 0px;
}
.jss16 {
  color: #181C2F;
  font-size: 14px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  font-weight: 600;
  line-height: 20px;
  margin-bottom: 4px;
}
.jss17 {
  color: #5D606D;
}
.jss17 p {
  color: #5D606D;
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 16px;
}
.jss17 li {
  color: #5D606D;
  font-size: 12px;
  line-height: 18px;
}
.jss17 h2 {
  color: #5D606D;
  font-size: 20px;
  line-height: 30px;
  margin-bottom: 10px;
}
.jss17 strong {
  font-family: SourceHanSansCN-Regular,OpenSans-Regular,Arial,sans-serif!important;
}
.jss17 em {
  font-style: italic;
}
.jss17 h1 {
  font-size: 22px;
  line-height: 28px;
  margin-bottom: 10px;
}
.jss17 h3 {
  font-size: 18px;
  line-height: 27px;
  margin-bottom: 10px;
}
.jss17 h4 {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 10px;
}
.jss17 h5 {
  font-size: 14px;
  line-height: 21px;
  margin-bottom: 10px;
}
.jss17 ul {
  display: table;
  margin-bottom: 28px;
}
.jss17 ol {
  counter-reset: ol;
  margin-bottom: 28px;
}
.jss17 a {
  color: #0057FF;
  cursor: pointer;
}
.jss17 ol li {
  display: table-row;
  font-size: 16px;
  line-height: 28px;
  list-style-type: none;
}
.jss17 ol li::before {
  width: 24px;
  content: counter(ol);
  display: table-cell;
  font-size: 14px;
  counter-increment: ol;
}
.jss17 ul li {
  display: table-row;
  font-size: 16px;
  line-height: 28px;
  list-style-type: none;
}
.jss17 ul li::before {
  width: 24px;
  content: • ;
  display: table-cell;
  white-space: pre;
}
.jss17 em strong {
  font-family: SourceHanSansCN-Regular,OpenSans-Regular,Arial,sans-serif!important;
}
.jss17 strong em {
  font-family: SourceHanSansCN-Regular,OpenSans-Regular,Arial,sans-serif!important;
}
.jss18 {
  margin-right: 16px;
  margin-bottom: 20px;
}
.jss19 {
  width: 100%;
  height: 250px;
  border-radius: 4px;
}
.jss20 {
  color: #5D606D;
  font-size: 14px;
  line-height: 20px;
  white-space: pre-line;
  padding-bottom: 20px;
}
.jss20 i {
  cursor: pointer;
  line-height: 16px;
  text-decoration: underline;
}
.jss20>p {
  color: #0057FF;
  font-size: 12px;
  text-align: left;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0;
}
.jss20 i:not(:first-child) {
  margin-left: 12px;
}
.jss20 i:hover {
  color: #3379FF;
}
.jss21 {
  color: #5D606D;
  font-size: 12px;
}
.jss22 {
  margin: 16px 20px 16px 0px;
  line-height: 16px;
}
.jss23 {
  left: 47%;
  color: #fff !important;
  width: 200px;
  padding: 9px 12px;
  position: fixed;
  font-size: 12px;
  transform: translateX(-50%);
  line-height: 16px;
  border-radius: 4px;
  background-color: #0D86FF;
}
.jss23 i {
  left: 50%;
  width: 14px;
  bottom: -5px;
  height: 8px;
  display: block;
  position: absolute;
  transform: translateX(-50%);
  background-size: 100% 100%;
}
.jss24 {
  width: 250px;
  padding: 6px 0;
  font-size: 14px;
  line-height: 20px;
  border-bottom: 1px solid rgba(199,203,209,0.30);
  margin-bottom: 12px;
  letter-spacing: 0;
}
.jss24 > input {
  background: #FFFFFF;
}
.jss24 > input::placeholder {
  color: #AFB4BA;
}
.jss25 {
  color: #5D606D;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0;
  padding-bottom: 10px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss32 {
  color: #FFFFFF;
  display: inline-block;
  padding: 4px 10px;
  position: relative;
  font-size: 12px;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.10);
  line-height: 15px;
}
.jss32:before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: '';
  z-index: -1;
  position: absolute;
  border-radius: 2px;
  background-color: #0057FF;
}
.jss32:after {
  width: 3px;
  height: 3px;
  content: '';
  position: absolute;
  border-radius: 2px;
  background-color: #0057FF;
}
.jss33 {
  padding-right: 18px;
}
.jss33:before {
  right: 8px;
}
.jss33:after {
  top: 50%;
  right: 7px;
  transform: rotate(45deg) translateY(-50%);
  border-top: 1px solid #0057FF;
  border-right: 1px solid #0057FF;
}
.jss34 {
  padding-left: 18px;
}
.jss34:before {
  left: 8px;
}
.jss34:after {
  top: 50%;
  left: 5px;
  transform: rotate(45deg) translateY(-50%);
  border-left: 1px solid #0057FF;
  border-bottom: 1px solid #0057FF;
}
.jss35 {
  padding-top: 12px;
}
.jss35:before {
  top: 8px;
}
.jss35:after {
  top: 7px;
  left: 50%;
  transform: rotate(45deg) translateX(-50%);
  border-top: 1px solid #0057FF;
  border-left: 1px solid #0057FF;
}
.jss36 {
  padding-bottom: 12px;
}
.jss36:before {
  bottom: 8px;
}
.jss36:after {
  left: 50%;
  bottom: 4px;
  transform: rotate(45deg) translateX(-50%);
  border-right: 1px solid #0057FF;
  border-bottom: 1px solid #0057FF;
}
</style><style data-jss="" data-meta="Global">
.jss57 {
  display: flex;
}
.jss58 {
  display: flex;
  align-items: center;
}
.jss59 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.jss60 {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
.jss61 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.jss62 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.jss63 {
  cursor: pointer;
}
.jss64 {
  position: relative;
}
.jss64:after {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  position: absolute;
  border-color: #F3F6FA;
  border-style: solid;
  border-width: 0;
  pointer-events: none;
}
.jss65:after {
  border-left-width: 1px;
}
.jss66:after {
  border-right-width: 1px;
}
.jss67:after {
  border-top-width: 1px;
}
.jss68:after {
  border-bottom-width: 1px;
}
.jss69:after {
  border-width: 1px;
}
.jss70:after {
  border-color: #E9EDF2;
}
.jss71:after {
  border-color: #C6CBD1;
}
.jss72:after {
  border-color: rgba(243,246,250,0.7);
}
</style><style data-jss="" data-meta="makeStyles">
.jss1 {
  color: #FFFFFF;
  height: 600px;
  display: flex;
  font-size: 12px;
  background: #FFFFFF;
  border-radius: 6px;
}
.jss2 {
  top: 10px;
  color: #8A8D91;
  right: 10px;
  cursor: pointer;
  position: absolute;
  font-size: 18px;
}
.jss3 {
  width: 334px;
  height: 100%;
  display: flex;
  padding: 30px;
  align-items: flex-start;
  flex-shrink: 0;
  flex-direction: column;
  background-size: cover;
  justify-content: space-between;
  background-image: url(https://wbstatic.webullfintech.com/v1/webquotes-g/788950ad4dddd61462a8.png);
}
.jss4 {
  width: 680px;
  height: 100%;
}
.jss5 {
  width: 100%;
  height: 411px;
}
.jss6 {
  color: #181C2F;
  font-size: 16px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  font-weight: 600;
  letter-spacing: 0;
}
.jss7 {
  color: #5D606D;
  font-size: 13px;
  margin-top: 10px;
  text-align: left;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0;
}
.jss8 {
  margin: 30px;
}
.jss9 {
  color: #181C2F;
  font-size: 24px;
  text-align: left;
  font-family: var(--webull-font-family-bold, OpenSans-Bold,Arial,sans-serif) !important;
  font-weight: 700;
  line-height: 30px;
  letter-spacing: 0;
}
.jss10 {
  font-size: 20px;
}
.jss11 {
  color: #AFB4BA;
  font-size: 12px;
  margin-top: 10px;
  text-align: left;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0;
}
</style><style data-jss="" data-meta="makeStyles">
.jss28 {
  display: inline-block;
  position: relative;
}
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
.jss50 {
  height: 100%;
  display: flex;
}
.jss51 {
  width: 48px;
  padding-top: 32px;
  background-color: #0F1527;
}
.jss52 {
  fill: #8C8E97;
  color: #8C8E97;
  width: 100%;
  cursor: pointer;
  position: relative;
  text-align: center;
}
.jss52:hover {
  fill: #C6CBD1;
  color: #C6CBD1;
}
.jss52 + .jss52 {
  margin-top: 28px;
}
.jss53 {
  color: inherit;
  font-size: 32px;
  line-height: 32;
}
.jss54 {
  fill: #1BC2FF;
  color: #0057FF;
  position: relative;
}
.jss54:hover {
  fill: #1BC2FF;
  color: #0057FF;
}
.jss55 {
  width: calc( 100% - 48px );
  min-width: 700px;
}
.jss56 {
  line-height: 30px;
  margin-left: 5px;
}
</style><style data-jss="" data-meta="WithPushMessage(WithTrade(WithGlobalCommand(e(WithMessage(WithStores(MessageCenterIcon))))))">
.jss26 {
  width: 24px;
  height: 24px;
  position: relative;
  text-align: center;
  -webkit-app-region: no-drag;
}
.jss27 {
  color: #5D606D;
  width: 24px;
  cursor: pointer;
  height: 24px;
  display: inline-block;
  font-size: 24px;
  line-height: 24px;
}
.jss27:active {
  color: rgba(9,19,44,0.7);
}
.jss27:hover {
  color: #181C2F;
}
</style><style data-jss="" data-meta="makeStyles">
.jss78 {
  width: 20px;
  cursor: pointer;
  height: 20px;
  margin-left: 2px;
  border-radius: 50%;
  background-size: cover;
  background-color: rgba(251,144,29,0.15);
  background-image: url(data:image/gif;base64,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);
  background-repeat: no-repeat;
  background-position: center;
}
.jss79 {
  color: #181C2F;
  width: 400px;
  height: 200px;
  display: inline-block;
  padding: 0px 10px;
  position: relative;
  font-size: 12px;
  line-height: 15px;
  border-radius: 2px;
  background-color: #FFFFFF;
}
.jss80 {
  height: 40px;
  display: flex;
  align-items: center;
  flex-direction: row;
}
.jss81 {
  padding: 2px 0px;
  overflow: hidden;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss82 {
  width: 100%;
  height: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.jss83 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.jss84 {
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style><style data-jss="" data-meta="makeStyles">
.jss94 {
  height: 460px;
}
.jss95 {
  width: 100%;
  height: 350px;
  background-size: cover !important;
}
.jss96 {
  color: #181C2F;
  font-size: 14px;
  line-height: 20px;
}
.jss96 a {
  color: #0057FF;
  text-decoration: underline;
}
.jss97 {
  color: #5D606D;
}
.jss97:active {
  color: #181C2F;
}
.jss97:hover {
  color: #181C2F;
}
.jss98 {
  padding: 0px !important;
}
.jss99 {
  border-top: unset !important;
}
.jss100 {
  width: 100%;
  height: 400px;
  padding: 30px;
  overflow: auto;
  border-radius: 4px;
}
.jss100 strong {
  color: #181C2F;
  font-size: 14px;
  font-family: var(--webull-font-family-bold, OpenSans-Bold,Arial,sans-serif) !important;
  line-height: 24px;
  letter-spacing: 0;
}
.jss100 li, .jss100 p {
  color: #5D606D;
  width: 100%;
  font-size: 14px;
  text-align: left;
  word-break: break-word;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  line-height: 24px;
  white-space: pre-wrap;
  letter-spacing: 0;
  text-orientation: mixed;
  -webkit-writing-mode: horizontal-tb;
}
</style><style data-jss="" data-meta="makeStyles">
.jss154 {
  top: 48px;
  color: #5D606D;
  height: 30px;
  display: flex;
  padding: 0px 20px;
  z-index: 9999;
  position: absolute;
  background: #FFFFFF;
  box-shadow: 0px 2px 6px 1px rgba(1,1,2,0.18);
  align-items: center;
  border-radius: 20px;
}
.jss155 {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.jss156 {
  display: flex;
  padding-left: 10px;
}
.jss157 {
  width: 100px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss158 {
  margin: auto;
  overflow: hidden;
  position: relative;
  max-width: 400px;
  white-space: nowrap;
}
.jss159 {
  width: fit-content;
}
.jss159:hover {
  animation-play-state: paused;
}
.jss160 {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABBNJREFUaAXVmj1MFEEUx2fmjruTRBMaItJSGPAjBowYC4UEYqNoIY1ojEbpLaypLSiNDcYINlgosUMTbYwYIcYvQkGrpzYkkuDesbfj+8/d7n3t7t3ezh1709zsfLz5/e/ezs6+d5xpKHLx9L5M5u8Yk2yEcXaYTPZJKbs45/thnurbVN+i6iaN2aAxb5LJA6/45Pt/6A9TeKOT5dOjXUbOmuBcTjDJxyWTnUFsccZ3GJfLUvKlVEws8atfITBwCSxAvhzszG4Zdwn4nmRMfcOBV62YQBDbJOh+ois1yy+s7VR0+17WLUAuXollMuu3CHyG3KDH12qjnZylSchMMtk/xyef5eoxU5cAuXi81zDMF+TNQ/UYDT+Gr6ZS8Ut88vOPWrZqCsg+OXYqx83nTfvWvQjp14jJ+OXE9S8fvIagXfh1ZhaOTFnMfNtyeECRm2JtMPgxev4CCt6y5v0mt6pPCHEtOfVtwW09VwFwG6inXSblNqnVbQRpCBY/5+ZOVQLUDZvZ/RjabXiciUNnlFbr5ztyCTOcbronUsmOk5U3dpkAbJWGsb4Serch+MT4Yya6T+QF/PnEsss3wotg2J36h0u32LKbGPt8aHhCxjdvw0MB6vavgevGixzKMxYtOALwhFUPqWJfwzXe2V01162talAdDWAEqz3UEYDjQWi/t60285O2V8VaWEMJwMEMZ5tmrqvTNljBDJtKAE6Vug5mOkG9bIEVzI4AdST2Gh3RdptZ4GUE5/mIcnpj4R2E2AXepMinnLvae0a0esAMdkE7z0i00ALQELsovMMGmBWhofT+jV2oL0JIQVH6BKIHQWdFZTzYhR36iApUEA6wO0eJIBOjNBYutB0loCAsYIcLNRRQ8ltImkZVt1tb1aCADWCHC20GnFdzuPWbXujMYtQQdbQ1oWzG6UG2QYbPazW+84tlX99h8YGbyqz5/RFj1Ka9EDs35gcuki8taTfeAoPkQhNCRYkRaG2zguAw2IUKcVOUuM34GSLbYFfPAYS4202AzawEqPg8hbi1ikBcqPcsEweH6b2vQ69pYgUzjCoBSC4gPq9tlUJcKDH6gCXG5ljH6EOtIsBqJ0SUAIAjuUBH67QOEZVxoVjPMMWGBnWYJt9nacVasOYIQGYEyQU9qzTPChhLsziOACyJzAhJXA27PGKhFoUT7ZJLr9D1mn0Z4pOv5hmLJspio2jWHtzNZfLw1m5x1UZq9QR3bbvtFF4vcyFbAOLwXIjb9vVef4LFLTcALlcB6EBGBJkR8rHqszEGtKBgbb/sDBCq7oFKrqgn+WoKgKAop1k9Xaj0l0BaR2VGOJ/W9bArte/UaaehI/J0PgtTO0eMeXX9As4CVGnbvxqUikC9bf/sUSlEidnDv9v8ByretuvS99zaAAAAAElFTkSuQmCC);
}
.jss161 {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABQ9JREFUaAXVWk2MFUUQrp6FdVnc1Q1eYEUOLpGDRA4S0HBQCIaLWSTBoFEvZPUgHPSAFyXLz4mDHhYvRi/+AIEEIR4kSkQjagwaNRzYhL1gBC4aI6vrivLa76t+M8ybNz89+354r5KX6fmpqq+6q6urq5+RJpDd+tICuXJ1o1TsoyJmhYgdwXUI1wEn3kyj/TueTeE6KYE5I0sWf2qOvfF3o+rNXAXYdU8PSaUyCkCjYuUxK9JfRhYUz4iRT2DUSQmCk+bsIRhYnkobYB9/vl9+u/YygO+yVqo9XF5xnMMYwQiZA7Jo8HXz0Vsz8XdFbW8D7NajPXL5+HYIHLfWLi4SPJf3xpirlC/DW94xx5684SPDywC7/rlhmb1+AsAf9BHa6Dcw5Dvp691sPnv3cpGsQgPsum1rpCIftqrXswDqaATyhDl75Nusb/g8yHtpH37qGYD/vN3giUl1Ujcx5FDmCJDR2sp7Obxte2VM8Kz5+vD7aQpTDai6DXu+L42p3c/gTrPwlUfS3KnOhXTCOp/vCPDsLO1IYmIwSVCNARoqXbRpSZhM6C51q3OC2BjOY1RjAOM8PmxLqIxh8G4qNrcWRTzRHKiusFNqafS68xoaXhcNjoQr9rwIItIDb/D9C0R2Iro9cJ/I+YsiBxEgpv+KRHk1BhaK7ICMlctFfpyEjA+QHRXndsRoNJWR/dSjI+ASsxuXvHObV8bEjG6IcNqpSzAI8v5ASuNDdyCFmnhVzMiy6Gt74rTIgbej+7yG5k5BzzImgG4OIKv0Bk/J7PkYKRAAEgIrohTwyrIKWbgnKVbNhKOVGClxGaLbJMjLiCzwlJUiM6EiceswG92M/HLl11L5PP33zd01LhBKz3SnHPDK8+LeUvMIvj8jdy+5K+BOqhR4IuWEhc+r4hB59Zo6EkXgd+wrBZ6qFDOwB24bmEDhc8sJ62NEHviLmPwEf+1PH43132ALa+xD2z62YjfVv/V8kgeQ0Qkhw9x7T50wS/A7GwAPiZB8ClGIG/AGqGAkWgXeIbYjMIDVgwYpx4ik5Gb0/E2ZZogj4BG8b7JktjyMaC54IrEDMKCNhNjHOdFMogt5rv8FanMmc8gZhdjB28NHDV7NNF1oTgWlGs0e4MPv1YiDr4k0xQjLXIjlvgYoBzx9Xv0+Ib55RpgpjgBy2TkSezGRVYaSXEqBOI9Yn7liNzwSdjLQQmuotcyV4AFAezPBp4DDFZarLNotMQJF4kCrxEyMyhCTuTzw3BvE04MiIyYwJyizBCGWzRB7oCVurRKX4MZOKrPnszY2eUYsx8aGu7MyBMzEjjlAQom7DN2PbWCCnM8X7MpoREYCqFvLhMz8W4fZGcD6vJa481mitz/Vznsv8CFz1orNfbEnuS1loJ3eQ549P5+fHV+6ku31XjJ+uODieF+vyJffoyA+UevzRUL+uS5y+huROwdFbpsv8sU5t6n/978iTn2PysR+89VhHI7Ad0KObi2rVOcALHEnI+OhQR18HQ9rQsQYGaCAeTLCw4UOJcUGjHF4kQuFD6unMee8i1whY4uvWpHr612dPLWpM4A4urq8TgNcHd6Msd0ZZMbSzgaIrXYOxNDyRERPRni4cIsIbjObdzpDWKkuFMfb1Yd8aiFPCTl52hidVBd1FpxQEl+mC8VHQWf+8Ja1EPwCfjyMbglRNnXgoHttMtpkKSx0oSRj1/7VoM6Qbv2zR9IQ3t/Kv9v8DwKnVuOuKxNeAAAAAElFTkSuQmCC);
}
.jss162 {
  background-image: url(data:image/png;base64,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);
}
.jss163 {
  background-image: url(data:image/png;base64,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);
}
</style><style data-jss="" data-meta="makeStyles">
.jss86 {
  color: #5D606D;
  width: 24px;
  cursor: pointer;
  height: 24px;
  font-size: 24px;
}
.jss86:hover {
  color: #181C2F;
}
.jss87 {
  top: 30px;
  left: 50%;
  color: #FFFFFF;
  cursor: pointer;
  padding: 10px;
  z-index: 7000;
  position: absolute;
  font-size: 13px;
  transform: translateX(-50%);
  background: #0057FF;
  text-align: center;
  border-radius: 4px;
}
.jss87 > span {
  white-space: nowrap;
}
.jss87:after {
  top: 0px;
  left: 50%;
  content: "";
  position: absolute;
  transform: rotate(45deg) translateX(-50%);
  border-top: 8px solid #0057FF;
  border-left: 8px solid #0057FF;
}
.jss88 {
  color: #FFFFFF;
  width: 260px;
  height: 136px;
  padding: 16px 20px 20px 20px;
  position: relative;
  font-size: 13px;
  background: #0057FF;
  box-shadow: 0 2px 9px 2px rgba(0,0,0,0.10);
  font-family: OpenSans-Regular;
  font-weight: 400;
  line-height: 18px;
  border-radius: 6px;
}
.jss88:before {
  top: -10px;
  left: 50%;
  content: '';
  position: absolute;
  transform: translateX(-50%);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 10px solid #0057FF;
}
.jss88:after {
  top: -10px;
  left: 0;
  width: 100%;
  height: 10px;
  content: '';
  position: absolute;
  background: transparent;
}
.jss89 {
  right: 20px;
  bottom: 20px;
  position: absolute;
  border-radius: 4px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss90 {
  color: #5D606D;
  cursor: pointer;
  font-size: 24px;
}
.jss90:active {
  color: #181C2F;
}
.jss90:hover {
  color: #181C2F;
}
</style><style data-jss="" data-meta="TradeAccountSwitchBar">
.jss660 {
  color: #181C2F;
  width: fit-content;
  font-size: 12px;
  text-align: left;
}
.jss661 {
  height: 30px;
  position: relative;
  line-height: 30px;
  white-space: nowrap;
}
.jss662 {
  color: #0057FF;
  cursor: pointer;
}
</style><style data-jss="" data-meta="makeStyles">
.jss152 {
  cursor: pointer;
  display: flex;
  align-items: center;
}
.jss152 > span {
  color: #0057FF;
}
.jss152 > i {
  color: #0057FF;
  font-size: 21px;
}
.jss153 {
  background: url(https://wbstatic.webullfintech.com/v1/webquotes-g/483f66e0fab29f20e9e1.jpeg) 0 0 no-repeat;
  background-size: cover;
}
</style><style data-jss="" data-meta="makeStyles">
.jss556 {
  position: relative;
  border-top: 1px solid #D9DFE8;
}
.jss557 {
  top: 5px;
  right: 5px;
  width: 14px;
  height: 14px;
  position: absolute;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(0,0,0,0.2);
}
.jss558 {
  width: 14px;
  cursor: pointer;
  font-size: 10px;
  line-height: 14px;
}
.jss559 {
  color: #FFFFFF;
}
.jss560 {
  height: 80px;
  display: flex;
  padding-left: 100px;
  flex-direction: column;
  background-size: cover;
  justify-content: center;
  background-image: url(https://wbstatic.webullfintech.com/v1/webquotes-g/8c5d380f49100e96166f.png);
}
.jss561 {
  flex-direction: row;
  justify-content: flex-start;
}
.jss562 {
  color: #171B6B;
  font-size: 14px;
  line-height: 20px;
  white-space: pre-line;
  padding-right: 6px;
}
.jss563 {
  color: #0057FF;
  cursor: pointer;
  display: flex;
  font-size: 14px;
  line-height: 20px;
}
.jss563 > i {
  color: #0057FF;
  font-size: 20px;
}
.jss564 {
  display: flex;
  align-self: center;
  align-items: flex-end;
}
.jss564 .jss563 {
  margin-left: 8px;
}
</style><style data-jss="" data-meta="makeStyles">

</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="UserView">
.jss148 {
  color: #181C2F;
  width: 24px;
  cursor: pointer;
  height: 24px;
  display: inline-block;
  font-size: 12px;
  text-align: center;
  margin-right: 10px;
  -webkit-app-region: no-drag;
}
.jss149 {
  width: 24px;
  border: 0.5px solid #DBE1E8;
  height: 24px;
  border-radius: 50%;
}
.jss150 {
  width: 858px;
  border-radius: 12px;
}
.jss151 {
  display: none;
}
</style><style data-jss="" data-meta="makeStyles">
.jss73 {
  color: #5D606D;
  cursor: pointer;
  font-size: 24px;
}
.jss73:active {
  color: rgba(9,19,44,0.7);
}
.jss73:hover {
  color: #181C2F;
}
</style><style data-jss="" data-meta="makeStyles">
.jss101 {
  right: 16px;
  width: 480px;
  border: 1px solid #DBE1E8;
  bottom: 46px;
  cursor: pointer;
  height: 138px;
  z-index: 5000;
  position: fixed;
  box-shadow: 0 2px 9px 2px rgba(0,0,0,0.10);
  border-radius: 8px;
}
.jss102 {
  top: 10px;
  color: #5D606D;
  right: 10px;
  position: absolute;
  font-size: 16px;
}
.jss102:hover {
  color: #181C2F;
}
.jss103 {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
</style><style data-jss="" data-meta="makeStyles">

</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
.jss862 {
  padding: 1px 4px;
  font-size: 10px;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  font-weight: 400;
  line-height: 14px;
  margin-right: 4px;
  border-radius: 3px;
}
.jss863 {
  color: #0CAF82;
  background: rgba(12,175,130,0.1);
}
.jss864 {
  color: #FE3957;
  background: rgba(254,57,87,0.1);
}
.jss865 {
  color: #0057FF;
  background: rgba(0,87,255,0.1);
}
.jss866 {
  color: #FD6946;
  background: rgba(253,105,70,0.1);
}
</style><style data-jss="" data-meta="makeStyles">
.jss104 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.jss105 {
  display: flex;
  align-items: center;
}
.jss105:after {
  width: 1px;
  height: 16px;
  margin: 0 10px;
  content: '';
  display: block;
  background: #DBE1E8;
}
.jss106 {
  height: 600px;
  display: flex;
  max-height: 100%;
}
.jss107 {
  width: 336px;
  height: 100%;
  border-right: 1px solid #F3F6FA;
}
.jss108 {
  width: 356px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.jss109 {
  align-self: end;
  padding-right: 20px;
  padding-bottom: 12px;
}
.jss110 {
  color: #5D606D;
  cursor: pointer;
  font-size: 23px;
}
.jss110:active {
  color: rgba(9,19,44,0.7);
}
.jss110:hover {
  color: #181C2F;
}
.jss111 {
  font-size: 14px;
}
</style><style data-jss="" data-meta="TickerSearch">
.jss129 {
  margin-right: 10px;
  border-radius: 4px;
  background-color: #FFFFFF;
}
.jss130 {
  color: #5D606D;
  font-size: 16px;
  margin-right: 4px;
}
.jss131 {
  color: #5D606D;
  cursor: pointer;
  font-size: 16px;
}
.jss131:hover {
  color: #181C2F;
}
.jss132 {
  width: 300px;
}
.jss133 {
  height: 200px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss663 {
  gap: 8px;
  display: inline-flex;
}
.jss663 img {
  width: 16px;
  height: 16px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss664 {
  cursor: pointer;
}
</style><style data-jss="" data-meta="makeStyles">
.jss852 {
  width: fit-content;
  border: none;
  height: fit-content;
  display: inline-block;
  outline: none;
  font-size: 16px;
  line-height: 0;
  border-radius: 2px;
  background-color: transparent;
}
.jss853 {
  cursor: pointer;
}
</style><style data-jss="" data-meta="makeStyles">
.jss531 {
  color: #8C8E97;
  flex-basis: 100%;
  margin-top: 4px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  line-height: 16px;
  margin-right: 4px;
}
.jss532 {
  gap: 4px;
  display: inline-flex;
}
</style><style data-jss="" data-meta="makeStyles">
.driver-popover {
  color: #FFFFFF;
  padding: 16px 20px 20px;
  min-width: 260px;
  box-shadow: none;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  border-radius: 6px;
  background-color: #0057FF;
}
.driver-popover .driver-popover-close-btn {
  top: 6px;
  color: rgba(255,255,255,0.7);
  right: 6px;
  width: 24px;
  height: 24px;
  display: block;
  font-size: 24px;
}
.driver-popover .driver-popover-arrow {
  border-width: 7px;
}
.driver-popover .driver-popover-arrow-side-left.driver-popover-arrow {
  border-left-color: #0057FF;
}
.driver-popover .driver-popover-arrow-side-right.driver-popover-arrow {
  border-right-color: #0057FF;
}
.driver-popover .driver-popover-arrow-side-top.driver-popover-arrow {
  border-top-color: #0057FF;
}
.driver-popover .driver-popover-arrow-side-bottom.driver-popover-arrow {
  border-bottom-color: #0057FF;
}
.driver-popover .driver-popover-title {
  font-size: 14px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  font-weight: 600;
  line-height: 20px;
}
.driver-popover .driver-popover-description {
  font-size: 13px;
  margin-top: 8px !important;
  line-height: 18px;
}
.driver-popover .driver-popover-progress-text {
  color: #fff;
}
.driver-popover .driver-popover-footer {
  margin-top: 18px;
}
.driver-popover .driver-popover-footer button {
  color: #FFFFFF;
  border: 1px solid rgba(255,255,255,0.3);
  padding: 4px 24px;
  background: rgba(255,255,255,0.1);
  line-height: 20px;
  text-shadow: none;
  border-radius: 4px;
}
.driver-popover .driver-popover-footer button:hover {
  border: 1px solid rgba(255,255,255,0.3);
  background: rgba(255,255,255,0.3);
}
.driver-popover .driver-popover-description h2 {
  font-size: 14px;
  margin-top: 20px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  font-weight: 600;
  line-height: 20px;
}
.driver-popover .driver-popover-description p {
  font-size: 13px;
  margin-top: 8px;
}
.driver-popover .driver-popover-close-btn:hover {
  color: rgba(255,255,255,1);
}
.driver-overlay path {
  opacity: 0.3;
}
.driver-active-no-overlay .driver-overlay {
  display: none;
}
.driver-active.driver-active-no-overlay * {
  pointer-events: auto;
}
.driver-active.driver-active-no-overlay-not-allow-close * {
  pointer-events: none;
}
.driver-active.driver-active-no-overlay-not-allow-close .driver-active-element, .driver-active.driver-active-no-overlay-not-allow-close .driver-active-element *, .driver-active.driver-active-no-overlay-not-allow-close .driver-popover, .driver-active.driver-active-no-overlay-not-allow-close .driver-popover * {
  pointer-events: auto;
}
:not(body):has(>.driver-active-element) {
  overflow: visible !important;
}
.driver-popover-bottom-center .driver-popover-arrow {
  left: 50%;
  margin-left: -5px;
}
.driver-active .driver-active-element[data-guideId='module-guide1'], .driver-active .driver-active-element[data-guideId='module-guide3'] {
  position: relative;
}
.driver-active .driver-active-element[data-guideId='market-guide1'], .driver-active .driver-active-element[data-guideId='market-guide2'], .driver-active .driver-active-element[data-guideId='market-guide3'], .driver-active .driver-active-element[data-guideId='market-guide4'] {
  border: none;
  z-index: 20;
  transition: border;
  border-radius: 4px;
  transition-delay: 0.2s;
}
.driver-active .driver-active-element[data-guideId='module-guide1']:before, .driver-active .driver-active-element[data-guideId='module-guide3']:before {
  top: -2px;
  left: -2px;
  right: -2px;
  border: 1px dashed #7FABFF;
  bottom: -2px;
  content: "";
  position: absolute;
  border-radius: 4px;
}
.driver-popover-combo-strategy {
  width: 290px !important;
}
.driver-popover-combo-strategy1 .driver-popover-arrow-side-left.driver-popover-arrow-align-start {
  top: 5px;
}
.driver-popover-daily-report .driver-popover-arrow-side-left.driver-popover-arrow-align-start, .driver-popover-daily-report .driver-popover-arrow-side-right.driver-popover-arrow-align-start {
  top: 22px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss278 {
  color: #5D606D;
  cursor: pointer;
  font-family: iconfont!important;
}
.jss278:hover {
  color: #181C2F;
}
@-webkit-keyframes jss279 {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
.jss280 {
  display: inline-block;
  animation: jss279 1s linear forwards;
}
.jss281 {
  display: inline-block;
  animation: jss279 1s linear infinite forwards;
}
.jss283 {
  border-radius: 0;
}
.jss284 {
  color: #0057FF;
}
.jss284:hover {
  color: #0057FF;
}
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
.jss266 {
  gap: 8px;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: flex-start;
}
.jss267 {
  color: #181C2F;
  display: flex;
  padding: 4px 0;
  box-shadow: 0 2px 9px 2px rgba(0,0,0,0.10);
  align-items: center;
  border-radius: 4px;
  flex-direction: column;
  justify-content: flex-start;
  background-color: #FFFFFF;
}
.jss267 > :not(:first-child) {
  margin-top: 5px;
}
</style><style data-jss="" data-meta="WithI18n(Unknown)">
.jss828 {
  color: rgba(9,19,44,0.5);
  cursor: pointer;
  display: flex;
  position: relative;
  border-radius: 2px;
  justify-content: space-between;
}
.jss829 * {
  color: #0057FF!important;
}
.jss829 *:hover {
  color: #0057FF!important;
}
.jss831 {
  line-height: 24px;
  padding-left: 5px;
}
.jss832 {
  color: #0B90FF !important;
  background: rgba(11,144,255,0.15);
}
.jss834 {
  width: 24px;
  height: 24px;
  font-size: 24px;
  line-height: 24px;
}
.jss836 {
  line-height: 20px;
  margin-left: 5px;
}
.jss837 {
  padding: 4px 0;
}
.jss838 {
  cursor: pointer;
  display: flex;
  padding: 8px 16px;
  align-items: flex-start;
  justify-content: flex-start;
}
.jss838:hover {
  color: #181C2F;
  background-color: #E5EEFF;
}
.jss839 {
  width: 52px;
  height: 52px;
  border-radius: 6px;
}
.jss840 {
  margin-left: 8px;
}
.jss841 {
  color: #181C2F;
  font-size: 14px;
  text-align: left;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0;
}
.jss842 {
  color: #8C8E97;
  width: 290px;
  display: -webkit-box;
  overflow: hidden;
  font-size: 12px;
  text-align: left;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  font-weight: 400;
  line-height: 16px;
  white-space: normal;
  overflow-wrap: break-word;
  text-overflow: ellipsis;
  letter-spacing: 0;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.jss843 {
  width: 376px;
  padding: 8px 0px;
  position: relative;
  box-shadow: 0 2px 9px 2px rgba(0,0,0,0.10);
  max-height: 152px;
  border-radius: 6px;
  background-color: #FFFFFF;
}
.jss844 {
  width: 100%;
}
.jss847 {
  display: none;
}
</style><style data-jss="" data-meta="DrawIcon">
.jss776 {
  width: 24px;
  height: 24px;
  font-size: 18px;
  line-height: 24px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss802 {
  width: 100%;
  position: relative;
}
.jss802 .swiper-slide {
  border-radius: 6px;
}
.jss802 img {
  width: 100%;
  height: 350px;
  display: block;
  border-radius: 6px 6px 0 0;
  background-color: #7FABFF;
}
.jss802 .info {
  height: 166px;
  padding: 20px;
  border-radius: 0 0 6px 6px;
  background-color: #FFFFFF;
}
.jss802 h1 {
  color: #181C2F;
  font-size: 16px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  line-height: 24px;
  margin-bottom: 12px;
}
.jss802 p {
  color: #181C2F;
  font-size: 14px;
  line-height: 20px;
}
.jss802 .button {
  right: 20px;
  bottom: 20px;
  z-index: 1;
  position: absolute;
}
</style><style data-jss="" data-meta="makeStyles">
.jss783 {
  cursor: pointer;
  display: flex;
  position: relative;
  border-radius: 2px;
  justify-content: space-between;
}
.jss784 * {
  color: #0057FF!important;
}
.jss784 *:hover {
  color: #0057FF!important;
}
.jss786 {
  line-height: 24px;
  padding-left: 5px;
}
.jss789 {
  width: 24px;
  height: 24px;
  font-size: 24px;
  line-height: 24px;
}
.jss791 {
  line-height: 20px;
  margin-left: 5px;
}
.jss792 {
  width: 24px;
  height: 24px;
  font-size: 2px;
  line-height: 24px;
}
.jss793 {
  top: 0px;
  color: #FFFFFF;
  right: 200px;
  cursor: pointer;
  padding: 10px;
  z-index: 1000;
  position: absolute;
  font-size: 12px;
  white-space: nowrap;
  border-radius: 2px;
  background-color: #0057FF;
}
.jss793:after {
  top: 50%;
  right: -6px;
  content: "";
  position: absolute;
  transform: rotate(45deg) translateX(-50%);
  border-top: 8px solid #0057FF;
  border-left: 8px solid #0057FF;
}
.jss794 {
  color: rgba(255,255,255,0.8);
  font-size: 16px;
  margin-left: 10px;
  vertical-align: text-bottom;
}
.jss794:hover {
  color: #FFFFFF;
}
.jss795 {
  top: 3px;
  right: 2px;
  width: 6px;
  border: 1px solid #F3F6FA;
  height: 6px;
  position: absolute;
  border-radius: 50%;
  background-color: #E9425C;
}
</style><style data-jss="" data-meta="WithI18n(n)">
.jss289 {
  top: -1px;
  right: 0;
  width: 6px;
  border: 1px solid #FFFFFF;
  height: 6px;
  position: absolute;
  border-radius: 50%;
  background-color: #E9425C;
}
</style><style data-jss="" data-meta="WithI18n(WithGlobalCommand(e(n)))">
.jss811 {
  cursor: pointer;
  display: flex;
  position: relative;
  border-radius: 2px;
  justify-content: space-between;
}
.jss812 * {
  color: #0057FF!important;
}
.jss812 *:hover {
  color: #0057FF!important;
}
.jss814 {
  line-height: 24px;
  padding-left: 5px;
}
.jss817 {
  color: currentColor;
}
.jss817:hover {
  color: currentColor!important;
}
.jss818 {
  width: 24px;
  height: 24px;
  font-size: 24px;
  line-height: 24px;
}
.jss820 {
  line-height: 20px;
  margin-left: 5px;
}
.jss821 {
  width: 24px;
  height: 24px;
  font-size: 2px;
  line-height: 24px;
}
</style><style data-jss="" data-meta="WithI18n(WithTickerConfig(e(n)))">
.jss268 {
  padding: 20px;
  border-bottom: 1px solid #E9EDF2;
}
.jss269 {
  display: flex;
  justify-content: space-between;
}
.jss269:not(:first-child) {
  margin-top: 10px;
}
.jss270 {
  width: 39px;
  cursor: pointer;
  height: 24px;
  border-radius: 2px;
  background-color: #E9EDF2;
}
.jss270:not(:first-child) {
  margin-left: 8px;
}
.jss271 {
  background-color: #DBE1E8;
}
.jss272 {
  height: 36px;
  display: flex;
  padding: 0 16px;
  font-size: 14px;
  align-items: center;
  line-height: 36px;
}
.jss273 {
  color: #5D606D;
}
.jss274 {
  margin-left: 10px;
}
.jss275 {
  cursor: pointer;
  display: flex;
  border-radius: 2px;
  justify-content: space-between;
}
</style><style data-jss="" data-meta="makeStyles">
.jss805 {
  color: #5D606D;
  cursor: pointer;
  font-size: 20px;
}
.jss805:hover {
  color: #181C2F;
}
.jss806 {
  font-size: 24px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss408 {
  width: 16px;
}
</style><style data-jss="" data-meta="e(e(Unknown))">
.jss404 {
  color: #FA9933;
  margin: 0 -2px;
  display: inline-block;
  font-size: 16px;
  vertical-align: bottom;
}
.jss404:hover {
  color: #FA9933;
}
.jss405 {
  color: #FA9933;
  height: 16px;
  display: inline-block;
  font-size: 16px;
  line-height: 16px;
  margin-left: 2px;
  vertical-align: bottom;
}
.jss405:hover {
  color: #FA9933;
}
.jss406 {
  gap: 4px;
  display: flex;
  align-items: center;
}
.jss407 {
  overflow: hidden;
  text-overflow: ellipsis;
}
</style><style data-jss="" data-meta="WithI18n(Unknown)">
.jss733 {
  top: -32px;
  right: 10px;
  display: none;
  z-index: 1;
  position: absolute;
}
.jss735 {
  top: -24px;
  right: 10px;
}
.jss735 > .jss738 > i {
  width: 20px;
  height: 20px;
  font-size: 20px;
  line-height: 20px;
  margin-left: 5px;
  border-radius: 0;
}
.jss735 > .jss738 > i:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.jss735 > .jss738 > i:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.jss736 {
  top: -23px;
  right: 52px;
}
.jss737 {
  display: block;
}
.jss740 {
  width: 16px;
  cursor: pointer;
  height: 16px;
  font-size: 16px;
  line-height: 16px;
}
.jss741 {
  color: #5D606D;
}
.jss741:hover {
  color: #181C2F;
}
.jss742 {
  width: 80px;
}
.jss743 {
  top: -30px;
  right: 0;
  position: absolute;
}
</style><style data-jss="" data-meta="WithI18n(e(n))">
.jss765 {
  height: 30px;
  display: inline-block;
  padding: 0 5px;
  line-height: 30px;
  margin-right: 10px;
}
.jss766 {
  color: #5D606D;
  cursor: pointer;
  display: inline-block;
  margin-left: 10px;
}
.jss767 {
  color: #5D606D;
}
.jss768 {
  color: #0057FF;
}
</style><style data-jss="" data-meta="makeStyles">
.jss716 {
  padding-top: 12px;
}
.jss717 {
  width: 100%;
}
.jss718 {
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 12px;
}
.jss719 {
  gap: 12px;
  display: flex;
}
.jss720 {
  flex: 1;
  border: 1px solid #E9EDF2;
  padding: 12px 0;
  font-size: 13px;
  background: #F8FAFD;
  text-align: center;
  line-height: 20px;
  border-radius: 4px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss683 {
  font-size: 16px;
}
.jss684 {
  color: #5D606D;
}
.jss685 {
  color: #FFCF00;
}
.jss686 {
  cursor: pointer;
}
.jss687 {
  cursor: not-allowed;
}
.jss688 {
  color: #0057FF;
  right: 15px;
  position: absolute;
  font-size: 16px;
}
.jss689 {
  color: #5D606D;
  background-color: #FFFFFF;
}
.jss689:after {
  border-color: #D9DAE0;
}
.jss689:hover {
  color: #181C2F;
}
.jss690 {
  width: 70%;
  height: 100%;
  display: flex;
  white-space: nowrap;
}
.jss691 {
  height: 28px;
  display: inline-block;
  padding: 0 10px;
  position: relative;
  text-align: center;
  line-height: 28px;
}
.jss692 {
  margin-right: 5px;
}
.jss693 {
  color: #0057FF;
  font-weight: bold;
}
.jss693:after {
  left: 50%;
  width: 18px;
  bottom: 0;
  height: 2px;
  content: '';
  display: block;
  position: absolute;
  transform: translate(-50%, 0);
  background: #0057FF;
  border-radius: 1px;
}
.jss694 {
  color: #5D606D;
  border-bottom: transparent;
}
.jss695 {
  margin-left: 6px;
}
.jss696 {
  margin-left: 6px;
}
.jss697 {
  color: #5D606D;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.jss698 {
  cursor: pointer;
  display: inline-block;
  padding: 0 5px;
  position: relative;
  margin-top: 5px;
  text-align: center;
  line-height: 22px;
  margin-left: 5px;
  margin-right: 5px;
}
.jss699 {
  cursor: pointer;
  height: 22px;
  display: inline-block;
  padding: 0 5px;
  margin-top: 5px;
  text-align: center;
  line-height: 22px;
  margin-right: 5px;
}
.jss700 {
  cursor: default;
}
.jss701 {
  display: flex;
}
.jss702 {
  border-right: 1px solid rgba(216, 216, 216, 0.2);
}
.jss703 {
  border-right: none;
}
.jss704 {
  display: flex;
  white-space: nowrap;
  justify-content: space-between;
}
.jss705 {
  color: #181C2F;
  position: relative;
}
.jss705::before {
  left: 0;
  width: 100%;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  background: #FFFFFF;
}
.jss707 {
  color: #0057FF;
  font-weight: bold;
}
.jss707:after {
  left: 50%;
  width: 18px;
  bottom: 0;
  height: 2px;
  content: '';
  display: block;
  position: absolute;
  transform: translate(-80%, 0);
  background: #0057FF;
  border-radius: 1px;
}
.jss708 {
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 4px;
}
</style><style data-jss="" data-meta="WithPine(e(WithStores(n)))">
.jss753 {
  color: #181C2F;
  display: flex;
  font-size: 12px;
  box-sizing: content-box;
}
.jss754 {
  width: 200px;
  height: 100%;
  background: #FFFFFF;
  border-left: 1px solid #DBE1E8;
  border-bottom: 1px solid #DBE1E8;
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
}
.jss755 {
  width: 500px;
  border: 1px solid #DBE1E8;
  height: 100%;
  background: #FFFFFF;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
}
.jss756 {
  width: 100%;
}
.jss757 {
  width: 100%;
  height: 34px;
  display: flex;
  padding: 0 20px;
  line-height: 34px;
  justify-content: space-between;
}
.jss757.active {
  background: #D9ECFF;
}
.jss757:hover {
  background: #E5EEFF;
}
.jss758 {
  top: 0;
  width: 100%;
  height: 33px;
  padding: 0 20px;
  z-index: 1;
  position: sticky;
  background: #FFFFFF;
  border-top: 1px solid #DBE1E8;
  line-height: 33px;
  border-bottom: 1px solid #DBE1E8;
}
.jss758 > span {
  color: #5D606D;
  margin-left: 3px;
}
.jss759 {
  border-top: 1px solid #DBE1E8;
}
.jss760 {
  padding: 0;
}
.jss761 {
  font-size: 22px;
}
.jss762 {
  top: 45%;
}
.jss763 {
  width: 24px;
  height: 24px;
  font-size: 20px;
  text-align: center;
  line-height: 23px;
}
.jss764 {
  display: flex;
  align-items: center;
}
</style><style data-jss="" data-meta="WithI18n(e(r))">
.jss867 {
  color: #5D606D;
  z-index: 10;
  position: absolute;
  font-size: 14px;
  text-align: right;
  pointer-events: none;
}
.jss868 {
  font-size: 14px;
  line-height: 20px;
}
.jss869 {
  font-size: 18px;
  margin-top: 3px;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
}
.jss870 {
  margin-left: 5px;
}
.jss871 {
  color: #5D606D;
  display: inline-block;
  margin-top: -1px;
  margin-left: 4px;
  vertical-align: middle;
}
</style><style data-jss="" data-meta="WithI18n(e(r))">
</style><style data-jss="" data-meta="WebullIcon">
.jss892 {
  font-size: 16px;
  font-style: normal;
  font-family: iconfont!important;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
}
</style><style data-jss="" data-meta="makeStyles">
.jss891 {
  color: #D9DAE0;
  width: 18px;
  cursor: move;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.jss891 > i {
  cursor: move;
  font-size: 13px!important;
}
</style><style data-jss="" data-meta="makeStyles">
.jss874 {
  position: relative;
}
.jss875 {
  height: 36px;
  display: flex;
  background: #FFFFFF;
  box-shadow: 0 7px 20px 0 rgba(0,0,0,0.20);
  align-items: center;
  border-radius: 2px;
}
.jss876 {
  color: #D9DAE0;
  width: 18px;
  cursor: move;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.jss876 > i {
  cursor: move;
  font-size: 13px!important;
}
.jss877 {
  color: rgba(9,19,44,0.5);
  width: 36px;
  height: 36px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
}
.jss877:hover {
  color: rgba(9,19,44,0.7);
  background: #E5EEFF;
}
.jss878 {
  background: #FFFFFF;
}
.jss879 {
  font-size: 24px;
}
.jss880 i {
  font-size: 20px;
}
.jss881 {
  color: #181C2F;
  cursor: pointer;
  padding: 10px 5px 10px 20px;
}
.jss881:hover {
  color: #181C2F;
  background: #E5EEFF;
}
.jss882 {
  left: 9px;
  width: 18px;
  bottom: 6px;
  height: 2px;
  position: absolute;
  background: #3F89FF;
}
.jss883 {
  font-size: 20px;
  padding-right: 10px;
}
.jss884 {
  left: 20px;
  width: 10px;
  position: absolute;
  transform: scale(.3);
}
.jss885 {
  font-size: 12px;
}
.jss886 {
  width: 198px;
}
.jss887 {
  top: 100%;
  left: 0;
  position: absolute;
  background: #FFFFFF;
  box-shadow: 0 7px 20px 0 rgba(0,0,0,0.20);
  margin-top: 1px;
}
.jss888 {
  left: -54px;
}
.jss889 {
  left: -90px;
}
.jss890 {
  color: rgba(9,19,44,0.7);
  width: 157px;
  height: 24px;
  margin: 10px 10px 20px 20px;
  background: rgba(242,242,245,0.8);
  text-align: center;
  line-height: 24px;
  border-radius: 2px;
}
.jss890:hover {
  cursor: pointer;
  background: #F2F2F5;
}
</style><style data-jss="" data-meta="makeStyles">
.jss873 {
  z-index: 10;
  position: absolute;
}
.jss873 > div {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style><style data-jss="" data-meta="WithI18n(e(WithCurrentPage(r)))">
.jss620 {
  color: #0057FF;
  cursor: pointer;
  display: inline-block;
  font-size: 12px;
  font-family: OpenSans-Regular;
  font-weight: 400;
  line-height: 16px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss618 {
  top: 50%;
  left: 50%;
  color: #8C8E97;
  display: flex;
  position: absolute;
  font-size: 12px;
  min-width: 200px;
  transform: translate(-50%, -50%);
  align-items: center;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  font-weight: 400;
  line-height: 16px;
  flex-direction: column;
}
.jss619 {
  width: 60px;
  height: 60px;
  margin-bottom: 4px;
  background-size: 100% 100%;
  background-image: url(data:image/png;base64,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);
}
</style><style data-jss="" data-meta="WithI18n(WithTickerConfig(Unknown))">
.jss674 {
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 0;
  position: absolute;
  flex-direction: column;
}
.jss675 {
  top: 0;
  left: 0;
  color: #8C8E97;
  right: 0;
  width: 130px;
  bottom: 0;
  height: 100px;
  margin: auto;
  position: absolute;
  text-align: center;
}
.jss676 {
  line-height: 20px;
}
.jss677 {
  width: 130px;
  height: 24px;
  font-size: 12px;
  text-align: center;
  line-height: 20px;
}
.jss678 {
  width: 200px;
}
.jss679 {
  width: 200px;
}
.jss680 {
  color: #EFF2F4;
  cursor: auto;
  font-size: 52px;
}
.jss681 {
  height: 29px;
  display: flex;
  border-bottom: 1px solid #E9EDF2;
  padding-right: 12px;
  justify-content: space-between;
}
.jss682 {
  width: 100%;
  height: 31px;
  border-top: 1px solid #E2E7ED;
}
</style><style data-jss="" data-meta="makeStyles">
.jss665 {
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 0;
  position: absolute;
  flex-direction: column;
}
.jss666 {
  top: 0;
  left: 0;
  color: #8C8E97;
  right: 0;
  width: 130px;
  bottom: 0;
  height: 100px;
  margin: auto;
  position: absolute;
  text-align: center;
}
.jss667 {
  line-height: 20px;
}
.jss668 {
  width: 130px;
  height: 24px;
  font-size: 12px;
  text-align: center;
  line-height: 20px;
}
.jss669 {
  width: 200px;
}
.jss670 {
  width: 200px;
}
.jss671 {
  color: #EFF2F4;
  cursor: auto;
  font-size: 52px;
}
.jss672 {
  height: 29px;
  display: flex;
  border-bottom: 1px solid #E9EDF2;
  padding-right: 12px;
  justify-content: space-between;
}
.jss673 {
  width: 100%;
  height: 31px;
  border-top: 1px solid #E2E7ED;
}
</style><style data-jss="" data-meta="makeStyles">
.jss115 {
  color: #5D606D;
  display: flex;
  align-items: center;
}
.jss115 * {
  cursor: pointer;
}
.jss115 button {
  border: none;
  outline: none;
  background-color: transparent;
}
.jss115 svg {
  color: #5D606D;
  font-size: 16px;
}
.jss115 svg:hover {
  color: #181C2F;
}
.jss115 button:disabled svg, .jss115 button:disabled svg:hover {
  color: #AFB4BA;
  cursor: not-allowed;
}
.jss116 {
  margin-left: 6px;
}
.jss117 {
  margin: 0 6px;
}
.jss117 > span + span {
  margin-left: 4px;
}
.jss118 > svg {
  font-size: 24px;
}
.jss119 {
  gap: 2px;
  display: flex;
  position: relative;
  align-items: center;
  margin-right: 2px;
}
.jss120 {
  left: -64px;
  width: 64px;
  position: absolute;
  overflow-x: hidden;
}
.jss120:hover .jss121 {
  transform: translateX(0);
}
.jss121 {
  gap: 2px;
  width: 100%;
  display: flex;
  transform: translateX(64px);
  transition: transform 500ms;
  padding-right: 2px;
  justify-content: flex-end;
  background-image: linear-gradient(269deg, #E9EDF2 65%, transparent 94%);
}
.jss122 {
  transform: rotateY(.5turn);
}
</style><style data-jss="" data-meta="makeStyles">
.jss547 {
  color: #5D606D;
  border: none;
  cursor: pointer;
  outline: none;
  font-size: 16px;
  background-color: transparent;
}
.jss547:hover {
  color: #181C2F;
}
</style><style data-jss="" data-meta="WithRefPrice(Unknown)">
.jss297 {
  color: #181C2F;
  font-size: 12px;
  white-space: nowrap;
}
.jss297 > span {
  margin: 0 3px;
}
.jss298 {
  color: #0CAF82;
}
.jss299 {
  color: #FE3957;
}
.jss300 {
  color: #181C2F;
}
.jss301 {
  top: 1px;
  cursor: default;
  display: inline-block;
  position: relative;
  font-size: 16px;
  background: none;
  margin-left: 5px;
}
.jss301:hover {
  color: #AAAEB2;
}
.jss302 > span {
  margin-right: 4px;
}
.jss303 {
  color: #5D606D;
  background-color: #FFFFFF;
}
</style><style data-jss="" data-meta="makeStyles">
.jss461 {
  width: 100%;
  padding: 7px 0;
  position: absolute;
  text-align: center;
}
.jss462 {
  border: 1px solid #E2E7ED;
  height: 402px;
  margin: 0px 20px;
  display: flex;
  border-radius: 2px;
}
.jss463 {
  color: #5D606D;
  margin: 10px 20px 0px;
  font-size: 12px;
  line-height: 24px;
}
.jss464 {
  top: 10px;
  right: 12px;
  position: absolute;
}
.jss465 {
  color: #5D606D;
  font-size: 16px;
}
.jss466 {
  width: 200px;
  height: 100%;
}
.jss466:after {
  border-color: #E2E7ED;
}
.jss467 {
  color: #181C2F;
  width: 100%;
  height: 34px;
  padding: 0 10px;
  overflow: hidden;
  font-size: 12px;
  line-height: 34px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss467.active {
  background: #D9ECFF;
}
.jss467:hover {
  background: #E5EEFF;
}
.jss468 {
  margin-left: 5px;
}
.jss469 {
  width: 100%;
  height: 100%;
}
.jss470 {
  width: 100%;
  cursor: pointer;
  height: 100px;
  display: flex;
  padding: 0 20px;
  align-items: center;
}
.jss472 {
  flex-shrink: 0;
}
.jss473 {
  width: 80px;
  height: 60px;
  margin: 20px;
  flex-grow: 0;
  flex-shrink: 0;
  border-radius: 2px;
  background-size: cover;
  background-position: center center;
}
.jss474 {
  color: #181C2F;
  font-size: 14px;
  line-height: 16px;
}
.jss475 {
  color: #5D606D;
  font-size: 12px;
  margin-top: 3px;
  line-height: 16px;
}
.jss476 {
  top: 35%;
}
.jss477 {
  top: 50%;
  left: auto;
  color: #FFFFFF;
  right: 28px;
  cursor: pointer;
  padding: 10px;
  z-index: 1000;
  position: absolute;
  font-size: 13px;
  transform: translateY(-50%);
  background: #0057FF;
  line-height: 16px;
  white-space: nowrap;
  border-radius: 4px;
}
.jss477:after {
  top: 50%;
  left: auto;
  right: -8px;
  content: "";
  position: absolute;
  transform: rotate(-45deg) translateY(-50%);
  border-top: 10px solid #0057FF;
  border-left: 10px solid #0057FF;
  border-radius: 1px;
}
.jss478 {
  color: rgba(255,255,255,0.8);
  font-size: 16px;
  margin-left: 10px;
  vertical-align: text-bottom;
}
.jss478:hover {
  color: #FFFFFF;
}
</style><style data-jss="" data-meta="WithMultiWinStock(n)">
.jss577 {
  width: 100%;
  cursor: move;
  display: flex;
  border-top: 1px solid #E9EDF2;
  align-items: center;
  background-color: #F3F6FA;
}
.jss578 {
  cursor: auto;
}
.jss579 {
  flex: 1 0 0;
  color: #181C2F;
  height: 100%;
  line-height: 30px;
}
.jss580 {
  width: 26px;
  height: 30px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #E9EDF2;
  padding-right: 10px;
}
.jss581 {
  color: #5D606D;
  cursor: pointer;
  font-size: 16px;
  text-align: center;
  line-height: 16px;
}
.jss582 {
  color: #0057FF;
  cursor: pointer;
  font-size: 12px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss368 > i {
  color: #0057FF;
}
.jss368 > i:hover {
  color: #0057FF;
}
.jss369 {
  color: #8C8E97;
  width: 100%;
  cursor: pointer;
  height: 26px;
  font-size: 16px;
  line-height: 26px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss376 {
  cursor: pointer;
  padding: 0 10px;
  position: relative;
  transition: margin 0.2s;
}
.jss376:hover {
  background: #E5EEFF;
}
.jss376.drag-active-top:before {
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  content: "";
  z-index: 1;
  position: absolute;
  background: #0057FF;
}
.jss376.drag-active-bottom:before {
  left: 0;
  width: 100%;
  bottom: 0;
  height: 1px;
  content: "";
  z-index: 1;
  position: absolute;
  background: #0057FF;
}
.jss377 {
  background: #D6E4FF;
}
.jss377:hover {
  background: #D6E4FF;
}
.jss378 {
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  bottom: 0;
  height: 100%;
  opacity: 0;
  position: absolute;
}
.jss379 {
  animation: jss381 .5s ease-in-out;
  background: linear-gradient(89deg,#1A242D 0%, #FF526D 100%);
}
.jss380 {
  animation: jss381 .5s ease-in-out;
  background: linear-gradient(89deg,#1A242D 0%, #00C288 100%);
}
@-webkit-keyframes jss381 {
  0% {
    opacity: 0.04;
  }
  50% {
    opacity: 0.12;
  }
  100% {
    opacity: 0.04;
  }
}
.jss382 {
  color: #181C2F;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  padding-top: 7px;
}
.jss383 {
  flex: 1 1 128px;
  overflow: hidden;
  min-width: 0px;
  text-overflow: ellipsis;
}
.jss384 {
  color: #5D606D;
  width: 100%;
  overflow: hidden;
  position: relative;
  font-size: 12px;
  line-height: 16px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss385 {
  top: 2px;
  left: 0;
  color: #181C2F;
  width: 16px;
  padding: 1px 1px;
  position: absolute;
  font-size: 8px;
  background: #F5A623;
  text-align: center;
  line-height: 1;
  margin-right: 5px;
  border-radius: 1px;
}
.jss386 {
  color: #181C2F;
  width: 100%;
  overflow: hidden;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss387 {
  height: 20px;
  display: flex;
  position: relative;
  align-items: center;
  line-height: 20px;
}
.jss388 {
  height: 20px;
  display: flex;
  position: relative;
  align-items: center;
  line-height: 20px;
}
.jss389 {
  gap: 2px;
  display: flex;
  position: relative;
  align-items: center;
}
.jss390 {
  color: #5D606D;
  float: left;
  overflow: hidden;
  position: relative;
  font-size: 12px;
  line-height: 16px;
  white-space: nowrap;
  padding-right: 3px;
  text-overflow: ellipsis;
}
.jss391 {
  color: #181C2F;
  float: left;
  overflow: hidden;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  padding-right: 3px;
  text-overflow: ellipsis;
}
.jss392 {
  color: #5D606D;
  float: left;
  overflow: hidden;
  font-size: 11px;
  line-height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss393 {
  color: #181C2F;
  float: left;
  overflow: hidden;
  font-size: 11px;
  line-height: 16px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss394 {
  flex: 1;
  padding: 0 0 0 4px;
}
.jss395 {
  float: left;
  font-size: 10px;
  text-align: right;
  white-space: nowrap;
}
.jss396 {
  font-size: 14px;
  line-height: 20px;
  justify-content: flex-end;
}
.jss396.small {
  font-size: 12px;
}
.jss397 {
  display: flex;
  justify-content: flex-end;
}
.jss398 {
  font-size: 12px;
  line-height: 16px;
}
.jss399 {
  font-size: 12px;
  line-height: 16px;
  margin-right: 5px;
}
.jss400 {
  font-size: 12px;
  line-height: 16px;
  white-space: nowrap;
  margin-right: 5px;
}
.jss401 {
  color: #0CAF82;
}
.jss402 {
  color: #FE3957;
}
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
</style><style data-jss="" data-meta="makeStyles">
.jss358 {
  height: 100%;
  position: relative;
  min-height: 0;
}
.jss359 {
  white-space: nowrap;
}
.jss360 {
  display: flex;
  padding: 0 6px 0 10px;
  align-items: center;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  justify-content: space-between;
}
.jss360:after {
  border-color: #F3F6FA;
}
.jss361 {
  flex: 1;
  color: #5D606D;
  display: inline-block;
  position: relative;
  font-size: 12px;
  line-height: 16px;
  padding-top: 3px;
  user-select: none;
}
.jss362 {
  text-align: left;
}
.jss363 {
  text-align: right;
}
.jss364 {
  flex: 0 0 20px;
  text-align: center;
}
.jss365 {
  height: calc(100% - 30px);
  display: flex;
  position: relative;
  flex-direction: column;
}
.jss366 {
  flex: 1;
  height: 100%;
  overflow-y: auto;
}
.jss367 {
  color: #5D606D;
  padding: 4px 4px 0px 10px;
  font-size: 10px;
  border-top: 1px solid #DBE1E8;
  text-align: left;
  line-height: 16px;
}
</style><style data-jss="" data-meta="WithI18n(WithGlobalCommand(WithPortfolio(e(e(WithStores(WithAction(r)))))))">
.jss231 {
  height: 100%;
  display: flex;
  position: relative;
  flex-direction: column;
}
.jss232 {
  width: 100%;
  padding: 5px 10px;
}
.jss233 {
  width: 100%;
  height: calc(100% - 50px);
}
.jss234 {
  color: #5D606D;
  cursor: pointer;
  height: 36px;
  display: flex;
  padding: 0px 16px;
  border-top: 1px solid #F3F6FA;
  align-items: center;
}
.jss235 {
  border-radius: 4px;
  background-color: #E2E7ED;
}
.jss235:hover {
  background-color: #DBE1E8;
}
</style><style data-jss="" data-meta="makeStyles">
.jss638 {
  width: 100%;
  position: relative;
}
.jss639 {
  min-height: 166px;
}
.jss640 {
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
}
.jss641 {
  top: 50%;
  left: 50%;
  color: #8A8D91;
  display: flex;
  position: absolute;
  min-width: 230px;
  transform: translate(-50%, -50%);
  text-align: center;
  flex-direction: column;
  justify-content: center;
}
.jss642 {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 20px;
}
.jss642 > span {
  color: #0057FF;
}
.jss642 > i {
  color: #0057FF;
  font-size: 21px;
}
.jss643 {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
</style><style data-jss="" data-meta="e(WithRouter(n))">
.jss315 {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}
.jss316 {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
.jss317 {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
.jss318 {
  top: 50%;
  left: 0;
  right: 0;
  width: 100%;
  height: 150px;
  position: absolute;
  transform: translateY(-50%);
  background: url(data:image/png;base64,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) 50% 0 no-repeat;
  background-size: auto 65px;
}
.jss318 .cont-nodata-tips {
  color: #7B8288;
  font-size: 14px;
  text-align: center;
  padding-top: 80px;
}
.jss319 {
  color: #181C2F;
  width: calc(100% - 20px);
  height: 30px;
  margin: 10px;
  padding: 0 10px;
  font-size: 12px;
  margin-top: 0;
  text-align: left;
  line-height: 30px;
  margin-bottom: 0;
  background-color: #FFFFFF;
}
.jss319:after {
  border-color: #E2E7ED;
}
.jss320 {
  cursor: pointer;
  display: flex;
  padding: 8px 10px;
  line-height: 20px;
}
.jss320:after {
  border-color: #E9EDF2;
}
.jss320:hover {
  background: #E5EEFF;
}
.jss320:active {
  background: #D6E4FF;
}
.jss321 {
  flex: 1;
  width: calc(100% - 50px);
  display: flex;
  justify-content: space-between;
}
.jss322 {
  color: #181C2F;
  display: flex;
  overflow: hidden;
  font-size: 14px;
  align-items: center;
  font-family: OpenSans-Regular;
  font-weight: 400;
  line-height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss323 {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.jss324 {
  color: #5D606D;
  font-size: 14px;
  flex-shrink: 0;
  font-family: OpenSans-Regular;
  font-weight: 400;
  line-height: 20px;
  padding-left: 48px;
}
.jss325 {
  position: relative;
}
.jss325:after {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  position: absolute;
  border-color: #F3F6FA;
  border-style: solid;
  border-width: 0;
  pointer-events: none;
}
.jss326:after {
  border-left-width: 1px;
}
.jss327:after {
  border-right-width: 1px;
}
.jss328:after {
  border-top-width: 1px;
}
.jss329:after {
  border-bottom-width: 1px;
}
.jss330:after {
  border-width: 1px;
}
.jss331:after {
  border-color: #E9EDF2;
}
</style><style data-jss="" data-meta="e(WithDataLevelByExchange(n))">
.jss548 {
  width: 100%;
  display: flex;
  margin-top: 10px;
  padding-top: 10px;
}
.jss548:after {
  border-color: #E2E7ED;
  border-top-style: dashed;
}
.jss549 {
  color: #5D606D;
  width: 276px;
  font-size: 11px;
  line-height: 14px;
}
.jss550 {
  width: 24px;
  cursor: pointer;
  height: 24px;
  font-size: 16px;
  text-align: center;
  line-height: 24px;
}
.jss552 {
  position: relative;
}
.jss552:after {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  position: absolute;
  border-color: #F3F6FA;
  border-style: solid;
  border-width: 0;
  pointer-events: none;
}
.jss553:after {
  border-top-width: 1px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss534 {
  gap: 6px;
  display: flex;
  align-items: center;
}
</style><style data-jss="" data-meta="makeStyles">
.jss535 {
  color: #5D606D;
  width: 16px;
  cursor: pointer;
  height: 16px;
  font-size: 16px;
  line-height: 16px;
}
.jss535:hover {
  color: #181C2F;
}
.jss536 {
  margin-right: 4px;
}
.jss537 {
  color: #FFCF00;
}
.jss537:hover {
  color: #FFCF00;
}
</style><style data-jss="" data-meta="makeStyles">
.jss525 {
  flex: 1;
  color: #181C2F;
  height: 100%;
  font-size: 14px;
}
.jss525 .tit {
  overflow: hidden;
  text-wrap: nowrap;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  line-height: 19px;
  text-overflow: ellipsis;
}
.jss525 .tit-name {
  color: #5D606D;
  padding: 0px;
  overflow: hidden;
  font-size: 12px;
  text-wrap: nowrap;
  text-overflow: ellipsis;
}
.jss525 .narrowSymbol {
  padding-left: 8px;
}
.jss526 {
  display: flex;
  align-items: center;
}
.jss527 {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.jss528 {
  color: #5D606D;
  font-size: 12px;
  margin-top: 4px;
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
  line-height: 16px;
  letter-spacing: 0;
}
</style><style data-jss="" data-meta="WithI18n(WithRefPrice(e(n)))">
.jss484 {
  height: 100%;
  padding: 10px;
  position: relative;
  background: #FFFFFF;
}
.jss485 {
  padding: 12px 10px;
  position: relative;
  background: #FFFFFF;
}
.jss486 {
  display: flex;
  justify-content: flex-start;
}
.jss487 {
  width: 100%;
  display: flex;
  padding-bottom: 12px;
  justify-content: space-between;
}
.jss488 {
  height: 20px;
  display: flex;
  padding-right: 4px;
  justify-content: space-between;
}
.jss489 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.jss490 {
  top: 84px;
  right: 10px;
  height: 22px;
  display: flex;
  position: absolute;
  align-items: center;
}
.jss491 {
  top: 82px;
  right: 10px;
  height: 22px;
  display: flex;
  position: absolute;
  align-items: center;
}
.jss492 {
  top: 41.5px;
  right: 10px;
  height: 22px;
  display: flex;
  position: absolute;
  align-items: center;
}
.jss493 {
  top: 9.5px;
  right: 10px;
  height: 22px;
  display: flex;
  position: absolute;
  align-items: center;
}
.jss494 {
  top: 84px;
  right: 10px;
  height: 22px;
  display: flex;
  position: absolute;
  align-items: center;
}
.jss495 {
  top: 84px;
  right: 10px;
  height: 22px;
  display: flex;
  position: absolute;
  align-items: center;
}
.jss496 {
  top: 41.5px;
  right: 10px;
  height: 22px;
  display: flex;
  position: absolute;
  align-items: center;
}
.jss497 {
  top: 9.5px;
  right: 10px;
  height: 22px;
  display: flex;
  position: absolute;
  align-items: center;
}
.jss498 {
  display: flex;
  align-items: flex-end;
  line-height: 30px;
}
.jss499 {
  margin: 12px 0 12px 0;;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.jss500 {
  height: 20px;
  display: flex;
  overflow: hidden;
  align-items: flex-end;
}
.jss501 {
  height: 16px;
  line-height: 16px;
  padding-right: 5px;
}
.jss502 {
  color: #181C2F;
  display: flex;
  align-items: flex-end;
}
.jss502 .price {
  font-size: 40px;
  text-align: left;
  font-family: var(--webull-font-family-bold, OpenSans-Bold,Arial,sans-serif) !important;
  margin-right: 4px;
}
.jss502 .widePrice {
  font-size: 12px;
  text-align: left;
  margin-right: 4px;
}
.jss502 .changePrice {
  display: flex;
  flex-direction: column;
}
.jss502 .change, .jss502 .changeRatio {
  font-size: 14px;
  margin-right: 4px;
  margin-bottom: 1px;
}
.jss502.other {
  height: 16px;
  font-size: 12px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  line-height: 16px;
}
.jss502.other > *  {
  font-size: 12px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  margin-bottom: 0;
}
.jss502 .changePrice .ratio {
  font-size: 12px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  line-height: 14px;
}
.jss503 {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-top: 8px;
  justify-content: flex-end;
}
.jss504 {
  color: #181C2F;
  display: inline-block;
  padding: 5px;
  position: relative;
  font-size: 12px;
  line-height: 15px;
  border-radius: 2px;
  background-color: #FFFFFF;
}
.jss504:before {
  top: 0;
  left: 0;
  right: 0;
  border: 1px solid rgba(73, 73, 73, .4);
  bottom: 0;
  content: '';
  z-index: -1;
  position: absolute;
  border-radius: 2px;
  background-color: #0F0F0F;
}
.jss504:after {
  width: 3px;
  height: 3px;
  content: '';
  position: absolute;
  border-radius: 2px;
  background-color: #0F0F0F;
}
.jss505 {
  width: 22px;
  cursor: pointer;
  height: 22px;
  background-size: auto 16px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAMKADAAQAAAABAAAAMAAAAADbN2wMAAADMUlEQVRoBe2av4vUQBTHvzPJnrC4uIKKHIiIImdlYS0eCCIqiIWdoBa6d5VWeiBY+qPT6rC6xv/A3v/gCsHDyvYQFFwQBDebjN+3u9m7zWW8jEk2WdgUO8n8eO/zZl7eTGZHYXQZY1T7Fdrw0Y4CHDAeVFxWh1SFMLqBP+ij211DVyllhGsAeeGdaXz9hcV+iGYdYPdj8D38Pt3C9mZHBVp6fpbgxTjpaGEWdnXopTkcahzfz+o6lnsRvmnx+TrCZWIiu5YXNlPlGlYSdr+0aKNwTEe4KHabBj6aPn4W3QfC7hcuFFhkyHuoFG4Zg4bIVz08MRr3ePtFnou8CjPARDihQXCDmwT0MYjSQ1Qa0lIR1mjE3SLhRVZuAwh+Uit0CH7jn/IMzhcNn8sAQp8yIVY4E15DBC8D3EKGOs5VnEeAPX2GwCv0kKvURq+p9spsgNI4awKscvFxhci1WSftb0CIcwReZa9fJrXa/XJW2/dD7XYDNP06wHNC3x5U3RVV6gAeM9h9OMCzMXxcu8g0wiXOFUfyirSOAKPMdcbv0i4Gg3Wu7eVl+s6fLaafIzD1sEW9P7Iqthogk09WITnrHeV7tcy+WqYRXCtPGPWJK+UPzN+26bAaYGvAaPQm9LDBmTVI1uFM2/BC3Ofk9jhZ5vg8NoryHjBYdyKFzTQZ9ncgrTbz+gbv0+CluuRLuaXpf2VzZJrskKe2xs4G+Ap3pKfTBEq+lKeV5cxbsrV3diFxDy6TU12Eq9Cypgkrp/MI2HqiqvyZN8A6NLYenVIUsqnfk+88AtOOQnuIExnOBlQUhRLYO4/OLlRRFNohTtw5j0CifeWPM2+AswvNo1DBTufsQvMoVPUIFKw/tzhnF8qtsWABcwNcOjT+EIpTl7a2ulMdAflejiI0JbUBuearg6+N7LwNrxCPuAMgwkvZiI3V5Eh7/Ebe4Jbb21jGxAhwL0i+Z+sKL8wLI8aYf3J3ORruKPTGpfW76Y0Yx2Sq9cIslfY/2VhNOTeDf+8Hf9+XI790qcKu5exB6ZrKUkB2LQcn5OxBWTrKkivMwq7l1IccnJglI+LDHsLOsD+8ZvW4zV/lkxHfSJwyEAAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
  background-position: 100% 50%;
}
.jss506 {
  width: 22px;
  height: 22px;
  background-size: auto 16px;
  background-repeat: no-repeat;
  background-position: 100% 50%;
}
.jss506.Ls {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAkBJREFUWAnFV79rFUEQ/mbvPQmSSAiIEuwCFiYgFnYpxFoIWFj4B4iChUUEsRQkXSCV2PjjT9BC/RsEwSaBVCpJHSKmMfdux28v5uXi7t273XuJA4+3O3Mz3/dmZvfdCCi6Pn8GO+YS+jqJfNB3uhOTfi9HLnuYsdsyv74vJfgursAW2YmBhgKbrMA0Nkz5y08b3BFymMy6KdMeYhirE70Jsfej3FhyM5aaiz0L1ceAnIsiwH4zUQ51Dyse0HS+ztykHweBywS40wTSZBsDgeIpAZJPUDcCYm+z7gtNv3CULZ1AZqaheDgKYJQ9nUCRP2LwuK4PsEkjIHKNqb8ViBet6kV7oKCPPIn3C3skZMDcZe3nwuHiT0NcBlQu8rq9VwPOv1UsQYrFoV3lPWBeDveBRRwBY5cJMhGI81elPahcKDeCfYh+4vON0r4EmS7yvr/RGK1qVLyBZj+qqtC6fQaszjDAx0qQWa6vVvZHS5HvTP1rEj7S1azaE1DDesJ9DkSK60z3i8PtsW/VFTZEfkxXs2lfAi+AmfJUTnHQeF+CtoCyAwG+P/qyy4O45qvrNR0IwM+AyCqs/KyH8y3pBBT/ZEA+M/0ffIhmTRcC1Qz8RuYaL17SCUilB0ReoTBb8fA8rClOpY+YwxJ8Q2bfpsZpfw/4CJMQd9FmzzHQgW9up+lAQKfYdO948L+2gwo/xcGEs1qKWNnke0HUmfdgiM3BhINiiog8o9uvFNehD7GNm1LhBsXTFofpJmSH+z/H8z/jvJSY8JowfQAAAABJRU5ErkJggg==);
}
.jss506.L1 {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAAE/klEQVRYCaVXz2skVRCuej0zWV3Cru6iaERPWdhNAotCwIMHQfDg2bsnYe/qTQge9eBB8ORxD4IgCB5kxYt/Q+J60b0Yw0ISxLCrM93vlV/V6+p+3TPZVexJ+tX76tdX9ao7EyZcsrc1o4tXNmgxXa9Dmk4V1KsT8nYFMIKWHNyxX6WqqZIzOrlwyHtfLfgAyTfDU9eRbDJtM9atuYZTOTN6TPCedp/sURJzQ6dP3J3cqFD5JE00UW3ptKg+mUnGoqS1KvLj9CMfkQld+XsjLKKs1/D1lLoqEQ/naweM4vyvbeT1wEFykS0JT1gSsiQOnMfEHc/Tr2LKcRpKXGN0eSB3MVXWTQd0AsDiMlgjnKMvTF0cEFA/dc0h6nbNpiUxd165GtOVmpUgCIzYFts8CRlw+D/GX5m0BIcdaDWepKzaZVuVzfBMyphtTU55qBrvWgJDY3ukAZWoyr73NQcb7roEBuvtHH1rOOkcCkGLY5GnaV6/QSFQU8l3FGZ/DrqgG43tYOHfiaaHQUgXKNGbiLpGsfqBqnTiNgUBZ5ojhqZ5VVg+oCRRZtU3U+lrMQs3t0iPYJLoNRF6H7/PMwtxJXc8ua4FgRLG3wdKWwIv4fQLSZoTStA3pOftUnbC0B8tfFaY3xOKrzPpRy8+ROQ/SsvJchtzxJTiNjPjBKr9Ch45sT6aU4qx+SiQXE1cfRlD+NE6khY7QtUt9Js48CdI/inkF5D4FFUcI/E1EjkgxCyvlU8B2j5lkk01Rif2/alwR07xOah2UdZmPjCimOQWOrarncJ1TyJ9joTfMqW3kXxNY+Gtu+8xfM0EcnmOIdr8Gro/RespVdWBKVobfTeguiNJOB6RS6qLUW4iPJILVPSZYoHpDgZ5D0O3BsuXoAM3zrHUoL36DhQkODVb1ijhhziEe26sq1YMWkeGiVy2NaZ3dRWm74nTXcPaW6JmxyYAw0Ap/OyH6TY9AUewppi2lTE+CBasp6YGST2OSuQIxQKSy1HiTQi72DcVo+1u6OPKvGPVi2CY67nD2U4npkS0C5qAw5Z2DOc2aJm9oLQFHH7XoGjxk5TSO1j1tL6mhn4bzwvMtiWhBg5L568klh5DruN6kvSiHiJGcF+MYB41D47zP0K7tUN4tukqqDysJvSFzp8PpQZHjArkrtswJzno5l8DmSG+hplh14UpBYnXE3LrOwCj/THPF2APB8bjVa29lQLflzC7T2kuHMIzbadu43xPNZY2cWo3CGm+iVleUxytHnTT61qaAZyptczq1+j6g4nH/ViTazDM0wJ8jtF+1Z1WzLc1p15amMm4pRgwgLhE/sLg/Kpid1kHauLFhy+/0oEmFE0sxFFzO5f+u2j/luzcloXOz4WlDrhiefUas8Zj63GqxrWOm5WDA4th5BUEOq9sOdj2G5M0W5tR61dR8d4KcrnJEQf3MLAeqEaRxjrdF9mymEk4ETVp+amIa5nNig6sNjR/uxVZW7BM4l/oS6zPO0DNOxNYJgZlARZim3OwdI81UE2hJEoXk7tbqcHjKX4IQ3yQwDZL+iEwPmuvdWg1CiuhDrPQnI3gYjtyH20HXVIv6J2Id0GJOJm+LR4onYWfJvFQ/5BYVsdt829vI6c2W5l4ZJEDc93Qg0uH9qLS/5BvNBc3FqlZ56on3FPoasjQaFvUaP9JWxcKG/8qZ1AKNc3qM3pwcsh7B4t/AMLiDwIc8VifAAAAAElFTkSuQmCC);
}
.jss506.L2 {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAAFPElEQVRYCZ1Xz4scRRSuqq6enp2dyc7uJgYdiJgQjDu5hBgwHjzk4MF4EUEQ7yKeBP0DIogoSBQSD0Y9ePEQclDw4j+Qgwe97CaHwELAXUfZdXenZ3p6+keV71V1TVdP9/SqlWSq6r3vfd+rVz/oUAJt626/sUGWe5GXdKggLtqqG7hqvLMYdzFICho3GPUfHO3v9N/YiiiKn+eN50hCuSJYHDvjJzUCOQhGChfjoGBWE0mSR8ODh2yDOL2ZOHoQj//q2nH+WewCcfRTwjdW1nssYqJTqVgrAs5af5YBYmL1g4PMmHeRkB1GBc3qYwCmz2KsaR6KI3AgOfoXYrII5S9vA2XSZcVgRCJwjhGnc6aMOncs9GdIlawBmZ4Qptz5PCesUizgMmKbYKG/ApuZ2CwGBmq7bOx/TgJJCgTlCfotjK5ABlO7NAcooA2dRWBMeQ/O8kpytxkpTJxtARjrdY3XUraGhjPvYSlqzzPi3FEcAUepAgXewgRj504y+ksYxBmj6dFW3eD1ixXcBXKEmzsw9Y9enkbRO5SRwdrqqXe1tiGsSMQ2MUkPD/deE2lyXVBynkgZMM5/PtHt3eZz946bZGPXyOskxlH6vBTiDBTpUTn3ikTycLK/9+enQqbX4LXDFkF/Mk3jt/zhTrDa7X1l8+n3Hy0ZgalHKsVFCpGOw7dUQKY5EcGpJIwvONwZt5bbv2oylwwn+1fpmHKXe9su5z9Eiei2ljoftxpLv+/5ezelTF9MRXLZLNjsZp6ASgJUoJQg3iRCnMMVOIxtWosjaZg+M51OP2cRGUACr2LYyP/7ShSFtyiVcdNrvn7CO3kfeO7r5PDZpwNCgUw6A2MzfTEBtIJaOhk+C/vmEEmFu9x5aM4F9hxJJCGCyK4hmUbh25gsc/g9l7d2lT2rWBAPz8JZeAXOEmly96e81HpZhVtgCKMouajypmTbcdjE2LHnXmuAa4FVNZNUeiPfvyKlvASu8fJS+1sbO56MzgSBfwvwTcqcu+3W2i/an2UHk8oESCr6uEpY1SbeZ/xjqiA5Hiq6D6JEBtHKdAqrByzn/LtGwzs0AqNgeCEIh9/A/LTD+L31zhOfaZ/9iw9RnszMIwSBAwjZ0fwAmiQQBF9Nu3hAx9HoJSnkJRjvtZvd7zVBTIbB8IUw8u9Q2CaXNW6utk9/QlImtL/4qytgJRGHURdW9BQQw6pcfQMwBjDx7ImlA/SnSfomJupy5w6nLESMf3hwPZqEXxBBRYN77610TGKaA6nsph4iZYihyPA3CaM+kuIOhJPw9nQyEXiCKaObndWVDzATGO/SFFCSPA2ux61m90fkOBodXIvT+EM88EQKL46nH+0f/IVbidfpt/XOk+8XKg561i3AJbqw/bKPqwMRJFnT1weqR+kfKIIEkI4+6ZAlZe6XnLMUzEQk4qrSRhyFDx0pQQI2AqYg9BjN842Ovz53ed6o30KwQvhxzYUPTxRHqOlnMZXxRWP1LTAMyHhM0+dCv58lKMaXOIqGylugozJgEV/SMAZcl6mCHaLGtkEFoEEb9RnAcbEyCmZAeDZUq8SAB+JNDXBLsJUotV5JJ98CA1Dh8z+1zgIYt8SIm74IsGfWF5Ey1+qAE/21mIw8ez2NlB2ixhYPk0TY/n8hAnCLwIgs6s3WVPklkzFzXOFXOatFTK5ZX5eI8mkcngoDNWPUbATMZ9s77R2XiSSHVKbz/42gnD/hFo0jkgfNdAcfLrJ1o9842xv10jjtUOKVz3rZYjFlzlpMBgeMhI+W5cTzURz/e/4PMQE3nmtCuqIAAAAASUVORK5CYII=);
}
.jss506.L1USA {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA81JREFUWAntVz2MG0UUfm9mZ73x+S53l0MIKQ1BQiiXjpIrSHNJ+EkkChAlokGUSBRQ0FNCR58mScEdChJCKUKJUESaoAQJKVJOgqCE47DPa+/OvMd7Y3yE2Guzih0anrQ7O/Pevu97PzP2Iois3+D0p27/KJlGi6F0ujYvQXCloX7nmWZj58Y6FqjgPxblcSCw8wId69dAeDZ1P2D2Xe9YMGZlrNGcFy3RrtG0zxmn0r1im3nXvBJdFIptJhk8Dt3/BJJZpnl1zb9vE3qDEaW+Iih1Dnjpt7vJx1U4sytBBkli/CkmgdZL8YVI3k+3q8B1fWYEDjf8BhMsa+gSOCCzRA+38t/p5mMh4Ex4JQIrAwFXCR4/nwSuuqk9kNnyqUUszqEBG93GnCGQxfxet3leIi3SJCwbDhtAQsFo/RmQsNfeS77S3T5JphJY5u67BvmMJja6EiYqGuRatr9/P29eWGiUp6TeSdSLwoiNJ7wSSu5MAlfdxB6wGFoW6SSqZxrU9qDBZMkZelN4YWr55bguzae22ohlYbYUYJpMJLDC+5sCnEUnGrISkTE2mM4Bjh7J8rcF8bhOtAdUz8y323/Y6zqdJhMJZBzOKphGN2gw7e6IoYFLnVmyEN4ZZEgVAqfm/t9Fr+Qqe2ARu08D0QnZzBFcolIiPUDTFlJPyJYDFlYot9h0eviIjczLwObmymLvdbQmlAFvt9vuWlUmKjPQ8v1zf0c2qC2xueLZnB+Wwig1JRYvjZ6FM3yTUnE6RXrLQdg85PxnS0vFZj0CFqwjOqMOY10l1Rpd17jt+9TclklX12NWhjY6Cpd+sFuWYJ08X76313zPeC5sgFCLwFp/b0Ma68iwpgomqd3Z883vA9tOALOlYDFDMg4zIhTudPvZdRPCMUf82pPN9tc+8MXdXnpVrMbK2B7Igj+r1lLWuKVYClUgfjH0cBeWPm1B/qX1FsFaCNIPKoWHX1o2fw58sDm4DxvOv+iQT8tO+mRgMXofIdCkYjUheoE0PN37crLJcUFts3A5Rqo+PPgOHLoV3f0FHp/ltlx0ThiLuwXjz41ALXnfyXuap7EyQuBwv/MSMyX6SzaosRy7zN/m7H4d6+GhxTSEddklK6smvxQ83+kljY8eMvnHdIRASv7V2GC69/Vcl+YqbDbxJ/VBjztu9QOZ6zUQP3wYP6K7Vjz/oKoRwoLMlcOB9K2deqYfGNd8GMmAgO3X9PFI5pUH0SN5rfGyVNmVNexnahq/E/VDcaZeazhTbKNfqfKvoPKorOGvnqlgKnbs9v/y8/xPDSvKmlmg/yIAAAAASUVORK5CYII=);
}
.jss506.L2USA {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABNZJREFUWAmdV89vG0UU/nZ27djxUtImjSikoCJxqSr+AXqBA6VAi8QBxBUuqDcO3H2jXEBCQog7F8SBBIHEoUgggVQu0AOilAoJRJCiOASndRrb693le7M7k9n1xmt1JO+8efPe+96vGe964Oh20+beHtbaCcIkRUN4hVHiRNy0LEsUNIoLR0b5iBJg0AI2u11v7An4uI+zSQLf1TIgEZUdfVcko2du5uIVMnQkbgK/KoncBRdgGVqHjwrdTGCepygfYSCJ4Q+BNSVpd21Z+ZwwDomMS2vDVti1MD+tGgiV1Nw1rGnHsEMeBkOmqzMFKUqu4pRAxmAWGkpIkTXADUfRBbF0vu+IZdbM88gNI5DPuVxg2C6w5RmCs5af17ijN0WWbAQSmeG5dEFRBPIUHCXTjPG25+FVRQGPeVX8pR4+2wfetbYMkGVQTvPyDbOfY2kxS5dkHBvwGgjiGBfYT+CJQspZRuJhI6P4NMYtIyOUnPPycFkuXZYza2+E8wRdSgWcTJnpyK2DFL9pmRlGsgwYS+VZFGcoG+PJCC9K1PqXO8FsfG72y2bdtW1CYRbqS+CDPk6N7uEl1tIPWFPWWNfWD3DQOo5P0gBjFWMpinFeas5tEF+I4UTh63mcLzigg3UiHvRxhem86POSjgggM+sKnl+gj/2FFXw6uosLKkUg0UsPyH3O6VraxkB8qRvStMVBhvBG+wgnEzxNY7axTIMJLx7jNa69JMILmi+p54bQsYf1otGKVR5o4ARs6y287R6eZfQt7YCkN68ty6FBOK2NdvAG+WdThm3AWaY/ebnfqIDMWAVA4LAEpY1ohMtiVIZPNJNiKbKsJd1ehDel9hK19Ifkns6sC1k5Shgio3SjlDbu7ODMJMI544AAEHDIdU87ItmQHwF1+s2cIPJb+LIS/AjmYQYcgb0ddj7X+jwzHN3hCtf8Bm4nE7wlwAZc33iypkPswO+SFvq9m/iQiVn0WBruT/jf/0ejjevt0/h2+A+exwRPth7DVYGkXHEwIp/pv+hGJnS7g43OCjYIdM91QNOSDTHW1M2n2sv4WAXo+E3cWFzBe6SPUe+0J6clwhW/je8N6pQD23/pW23ZgIhl3u2bDzyMnxdCDLwA6xKt2ZdZ+oFyf/sncJ1UEq7iFxo+1XoQ3zRP4iYzsOsrhNEWLvE+2Wms5g6w9FMlGO7jsvZODEtaOZoBvsgoYOlRfDDs4ys2nSf3ggyZGe1WtuIF9i8eZ9SN1kn8Lr4RfJeX0ypT/5wK8Y6Rk7ngwN3/cCKe4CkjINFxSES2sYIAk3AFt2bdcskQ53hb3uaRHYuBYAG76RgvM5NbeZbskS84cKfHBik5Rc9/DI9jWwzpUToxhu3OPKZn6OBPhsdm7LEZF9MOPio7XnBgPMQlUZLAzVluhvV/qQZIz3SQHf6+y1PL+IHrZ7RNuWadIDIHcsZDT+D1RFlsbaPDxtOEo6TX5cd97hcy0D4GvsBUjPs0bi3N0M9uQitZQcxQrpCeZtXoFzIwpV2j7NZySndOhpJvtUrZOvBKJYcp+jU21CJfM3jXzPXi4JjOyBrjU/JlBvX5KjdQ8pUqH4p2v87zun0xVOcc9xl53HoEm/poyhey/lAEP89ntWWd4TnAeUVH/O8YCHj3FW/8P5LKpQeiO9rRAAAAAElFTkSuQmCC);
}
.jss506.BMP {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAArhJREFUWAnFV72LE0EUf29mNzlj7kwicocIgoKIp4iInYVYW1lY+BcoWFgoiL1cJ1iJjR+9jSnU3k4QbU70GpWchaK5O4xekt2Z53ubD/OxG3fnvDhhs7Pvzb7f7/3mze4OAjdaXsxBXe0Dn4oQhL7Ytq35XgABNqBiV3FxuY0R+DocAWv0toHGBVbaQAneqijzaYMLIcFk1VUkexzDjDb7rnXWvP51KdNtPOXqn8z5T1ugengdCOYyEeB6U5luSBhsVlqXScEewIQBE8xbJ1BrH4LQXojAHaJ5E8ilcoXfgpsMrvkHLgpsiYBd2TzP8340Ahb5HRayO4GGLdEmXYFB2Qf7qfQDcCZgas2rhDQ3JLtDEboR+ByeIEPnhrKXjKeiQGA886N9IwKTjGlA62koYFeDiwR0sJ/tIKjCzGWI9OLwyYEcJnc3zIL52nxMBDNjA5kIEoRcE9/7vhxW9f7Cvf51TCdTDZi19jVCBpes5RD5e2e5RC5qhHnu8gnbuuQ9l/6klpqA/dI8TWTPDM29gEsbPPdqIgcPYVfuU2dA8n9qAhyigj48A9sJxtnuBQPH++A9YF4JiPhRL+QeJMP+8aQmoOZnqnybHFGz9eYp2zB3e9cRke504KxeAo+/fFK01ATGY+Hs0KO3Ww+osKrK+Vfj4+Mt7gSQvx9H5h4R1vXuHXfioeKt7gQsdRToZi7h0fduQw434qHirc4ESLMCRlA7BxfeS1XJP42HSbY6EwArNdApfQZv6Z3+UjJMssedgGIFZEmyAuip+5D3a8kwyR6H91c3mIdFWQWo8YMq+4+SISZ73BUAKvKrh7Svb0Gow8kwyV5nAvwVPIugnkA+/yY5/N89vDFJ98QaDYUK3uuCn2nNj8YQbH4dHzsAEJTHnFMx+GtKdqkgG8VpN8GUHbLg/s/t+W/iIsabVuPUJwAAAABJRU5ErkJggg==);
}
.jss506.Delay {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAjdJREFUWAntV7FuE0EQnZk9G+fihERCQIFEi6gpKBD/gKIEReITEKKhQVDT0SHRgUQXhESB4A8QH4CgS5UiTUic85lzfLvDzDoXpTAmnrOdhjntnb1ez3vzdnb2FkFsfYubvS5c8x7aAaChfbMyAhg4B920DTvvN/AIFTw/gJslgpsV6Ci/CYNfXIEfpJHPG1wJKaZik8o+iuE8+hSbZj3n4wJRbMmJ8zUTgawHt/c64dNexp+zAu7UCcFEoF+Gp0B8FTBcHpTh2dwJEMEVQAa9AvhLnsEUiBJPTOwFXA2FgNxqmYl5YBbYClzCd/rFZiYFMNI+JlBTARMBScAhLJsDP5HLRiAqrgoMVcAw5ymIOa/BRx3qqWBTgAPG6FXImjlgWgUxCRU4NqkGzr6hmQho+cE4//oE2B/kHzv+933Ltm4ioJFrFTxRAHjFk3/S5XzrEPt3dWbOaiYCDUyeI+KuRh+JxLu84rC/XnL/5T5mrws+unEWEiYCy675ZZnSNUf4ColzzYmqqSoBw61eo3jXwezxv0iYCKjTBKF/EdI3i8nCPVHjg6wK2ZPkktyIyshK8c4/KKgYq4SZQBXZhTL5tcrtFy1ubRLR12Fyan4Mm9RM0eTvVptA5Trl5vaqX3rU4OZDwf4uKuQO3NsF3/pZjRn1tBWiUZ6O+5ZC+g1Amh8z6NRPU1PglM+JPv4nIK93MJhIsykOVmzSg+IUfU7kSrFJT6l6UJzon1MYrJiKHYvEeR7P/wACR8mQwkPO5gAAAABJRU5ErkJggg==);
}
.jss506.Nbbo {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABDJJREFUWAntVk1MZEUQ7n4jM+PgsmaTVTAxJuqSePKAOLtx8efmVS/qEYIcjJtwIN48efCyUU8edg3ucWO8afSy6gAxMBFJDAcTMV4IUZCLEGD4mdfWVz/vvZnx7QB7INnYMK+qvqquqtdd1f2cO+PhW+Jfm73oGr7qgnusBe8mfDZyw0yOXrz8etOEgjFCC96H2Lm1nri05Gu1I6BpAuOzky4OHxJWFvMTPKdfSvwcjjy/6CJH76DDNN4Y0kX+9/1K+d2+r2qbZErj7bnXKPjHxJ08ODtIHyEEF+IkPCkoMAdXDHmE8HRprzGFWQ/g4eL4Pab6iCJPKwU4SIJdcFUL8dFKiwwBsSNZAbDIh9K8CJUkEPyzYgXIufGR/p9Ab8z8WQW1kYebHrQ4V38rK3fjJQEXHjTD6dHB2ujV/iHIl588Vxv7/LeXwefh0N3LkHUZm0k2rVIq7O58+gLXQu87PzZ295sVBMjDOXimCE+ajK5AOq1SjBoTt1aWgRA/mCSQg6czhTu4OjzP+y2vJn3GvALWISSW7tSvdCSwuX144eZc697DdR7enoD3vocaQUoK5wBXHAGWEJU3N0WaT7uLe5PRhp7eEj+ufs4m9Wl7jRMJo2MFAF56tLIKurK++ziojTzc9Ex9YYKd97SgiWAL4VXfkcCb1Ud+/uiNp+gICG7qiz82btc3uCPy8MSzMsW5haV27G5yRwLr/xyUB873PINJxP9qk/Nw05+WdiSwvLYz8PfW4S/YQOKfMMd5uOmNHoxUJ53T66igt1HmPMXWQwQtf1//RLYkcw7gGLYj+Dg8B86cA9SGi9QJdOGQhr3TwzaejelBeaEYqQ2fy+QmWgsO6Ti8zGp7mlcueXoknUA8JYMitZx0C/yXbS5OLdLbf4f15TbkRCwbuJTVQPCWC/PU0e6HibwVYXi4v+vL9Dq309XovwxoIs8E1cHXm3MPfVP7S2qg2fxa6lL2iM2wdSRyHSHNPe8qJuu2opXk3AWjAx2AWfgA4UN/V+meGMBXg/yKj2wXQKODlBzYZK5ZcotqzigiqjTNhSwRWIbXr5/UOOPbjDh7OROSMBwHkrYJbGWqcB4S/6dhYzIWiS4h+pNvQnXJwWhC0oaKY4K6kC3w/lVVCTknRIJbEgC3KYQoTWepGG3xY4L6M/F/ml0BXskwNPQBgfJhml1L4/lYkwsEENcPGPppLYkAKdJJSkhBmG0Y8crG3u2V7iy8LzUQwsOkusJKXGR6WbB3nhBThUuR0cR0IB6SIwzFCCbEMXUfUfhBErDnYtSSJhIIC97Pk8YKOFwnfpMNNQAItzGsODgz7E+4iENKcEHYDhMRmIOzF4I5AzEiiBLYDMUIMVN/oVrtc83mK4TJZxgc2NBrHWKy5Kw3o8zSk41+DfBKsosodRBCvFosux/8t/Ut1p3141+DFn3LLlKSiwAAAABJRU5ErkJggg==);
}
.jss506.webull-long {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA+BJREFUWAnFVz1sXEUQ/mb33U98Dj6fscFKDESOIBCEFIgo+BFpqEBCoFBFcgUVUkSXBgnJHRIFBRIFlClCgQSioIAiCIqEIkKAMZYT2WCbHI7x+YzP53s/u8zsXeyTfT97juKMdO/tm3k737czs/NuCSx26mQaq+ooUrYfUZwS3V2TVBAhog0UzCKdnArJga/hCZhE3zXQVo6VTpDH78qt/KDBhZBgctSVC3srhgeh45SrO815sjj7UbI0+/G++HK9Bfua2DTJmuQFUJOix+H+CFTXh0x59UXO4zgUQEzA/DM3oQ71XcV9hRnAfyP1TMAsz79pw613LZDZXqwQSGrnbaUGbK3/pHMj7yM3cGvb3mHA/P3F/D33lqltXeAZDlxWTg0PMnapsMmzplL8DKa6Q7ADhH8Eqv/lYWsTDoTBlKYZ6PR30JpXajJk4sesiV+xFhkLe8SUixNq8NinHbCdyZuA2Vw7zWHvkxVbomk9emwCNs2qHbHlv36mcHNSlGTC5/nWlYB/Cmw8ahthpiC4shtcaKj+ocscIetIWjsuum7iT4D9u3w35X2Pc53bJEW/ip7vS3vsLRTeKVBBcCNJuMpFTPwyovLnSA2s1BU7Vzp85DyFpdPI5q/taNuPyP5w4pn25iZLWMnZ8sIXxuJ+KUSu+pAv14nUCt9LpINp6Ow0ckNTvfQBfwLCZW3hJZtUPmASgWw7V4FCRmyNZ6XoGvUVJpEaXhR1N+mlBoD82PeUK5xTgb7KeJHrA4IuXvhe3yH2aVT/vYTo1sPdwMXuXQPcAzTWb77Bc0LKP/IOkARUWx9j/VFEScEifpwQv2qArPxUuPoeUvm3u6XDm4Ap33wddusCNxrojeIm+se+RSY7x4TmkHUB+ArR8iVVW70oBLgZnaKoNI7UyA1ZaTvxToHSlmHqYTYIT7R0mBqZt6Qvu/qQ1KDWtRd4E0Aq+HM71zDHWxIQpaLq7S8kbJJr+17D4E9AH57lRRlXbEieQ3XhzB7n0fI4qfiM2xYSAZWRFHUU7xrggipSsPI1f3Be4xwTksqHqjJzHUot8VMa1gxz3o/fBieieWQHf0PcEb+HXSB+0sOfqKh4ylr7kAA5QCT1dMiK5Sc3wgbSA5OIs13g6zu4PsvnKq03N3aOP0Zf8usVB9joATJm4DJp/Q0OjZ4FHvzFx2VvnbDZowoVwtKjNildFDWlC2dBg/Pd9n2zCxn7F+HumSZtEDzwh1u5i8LIfK/g4tK/CHcTaDyT0j+2MXmpyV558qk7PRt4IbV6ic+JfDDhg+K9EsZWckrlf5jJgXMQTDkhC/C9PJ7/D8N9WQCkFPVVAAAAAElFTkSuQmCC);
}
.jss506.webull-short {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAwJJREFUWAntV71rU1EU/92PtEk/ralV0w8VRbAgCCqCuOkgHRwc6uBqwdFN6BRdrKLYIlWjq4JoBwdHEQf/AgcXB0GlWi00tikmeR/Xc17zmhfzkjwlaZceeLn3nnvu+f3uOefdlytAkn5u2hZyGIKDLhrGWNdCsaCQ29mFr+lxURQM/iOLUSNIvYEiDJyBbfigvZ1vMDjvkzfM2LIU9g3cewCKUi5p2OqcBxCrujEmsKmyRaBmBBZzGOWn1fnRYQALWRzL5fFQEr2ihaepPtwNs2uGLjQCxkUPgwsB2AYXv61g0pWgUbX8BvYsK2QKGoerZxtrQgn09+GtVnjtk3ANzv/M4RqRqbDPAwcKEo9Ie7SoMGPHsL8xZKVFhUN/Sgu4u7djUmm8EmxB5yaRGFuycMNR8NK2ChzKS2QoSkmOlBToKcYw62qkfD9R2lACvJBJDPYirSXmJHlnEGNwOpvH7VWDExbwgDS9rOeHE0QR67c6cF8kkIwCzjY1CfgOdnVjSgk84Uh4QBKnCsAsAXZ5xCSy9BWbIvACkyCbISuBe2j3vqy+m5ptQwK8cqAD01rjsb9Tbj1CEotxBxOJAuaUg6tKwV5LGQ66PZgWimg0kEgE2EdSI0MH98xaYVI1CnzvdHAp5uATz7fl8U7auE4ETIngEbEDN2WpZtgmTNTxc+nIRROXeG8LLAkphuMuLmsX80GnsoiPbjtWCPSkR1RihBIxZFbwJmgX7IceREGDv/vdBi8o1y9hQHVYFjuBESq+McWvqsA8RSLF6SAiZ9Uwlq0vuFW2Lvf+mYC31K4EZ51pxx0C3MdlTRHgevTqhDtSYzw+gl/5z8h46wM/kWsgsCa0y2+BX5wMyrtn4daLRBwTnXtxYU1b/v2/CJTXr/fMKq6IDpwh8ASdW/wnJyZcagUdXDQ2ksYKg+sLSp2mEdAFLNJJ8Iz9uiXnTqmt1zQtBfVA6s1tEdiKAEeg4kSrVzAtmLMkXxRb4DiaS8KWfEvli2K0Fc2zYkwPm11u5vX8D1gYvFRo1uLnAAAAAElFTkSuQmCC);
}
.jss506.webull-short-HTB {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAA6xJREFUaAXtWl1IFFEU/u6qq/mT5g9mYWQulQqV0B++BLJQvVQE1Us9pUWh0FOvRdBD0ENUL/09JVGRBUEEUYa9RAQF4aKV+UORkv/pJq1u0zl7Z3ZmdmdbadNmYA7MzLnnnJl7zrnnnHtn5gqooCiKwCVfA4RyGBA1UJRcjWeLqxBTgBKAIm6gufu6EEJhvQSflCtVZQiFWkjpem7bHoRog9d7UBztHPBEPO8k5dm77GjSmXUXysXKRqJctb3XLRUURzwy5i259idSvnoiCWt/VRNoKGo8tqs2CVS1JFOlpBFwNrgG/O/xc0fAHYEUPeCGUIoOTPl2x49AuqULltYCSyqBmSDQ/dhSBFmFQEW95PU9B6ZHgOIqoKTGWj6WyvJ8H8PqXUCaV+KRMy31p8fkM8d6gNCkgWdGrQ1YsxtYdwiY+JzYgPwVgP+cfFrrAdlZhR/YesLcQ6LWwBvdgG2ngKwCa8nwT6C3DXhxBgh+i5OxNiBObI6EiX6gv10X9mQA5XWyPfyeFBjUeaOfdFzDmDZCcoLeszIX04iuBRYVAb6dQP5K4O4e4NesJh25/lsDPjwE+NCAw6zxtWy9vQZ0PdA41teeJ8DL8zpPpAH1Z4HqfRSaFJ5FZNBQh84nzN5JrISBjtu6why2MWBvA1jZ/HJd5XFK6BhIEEKRd30gPQtYviXmFrVZ6LOmp0LNLqa4r5ZP8NJHkRLCNx2X7Y+PgOGuuKcnMIDKGENOCbD3lsQX4syxzocRZn7ICvTuppEaxRMYoI5AeAb4/iUqbELSM4G8ZSZSyo3gkF6pMnKA3DIgIxuoO0lllgrCqwvUhepctbMEBqhCU4NAi99ar9INwP5Wa97fUjvvmatQGjlp4zFgczMdTeRMmpdYxgD2TmKexNjrk1+lyr4dBtUlam8DNHU5Dxi8efJqONvfAA7Vggqp8tyrkJrEBksXBC1dD9Q2yK44eVlx33aabmlGnqVwChgmNVWhPyfxgmht6ITXTdrayUAGF5P20zQPdBqpEdzaAF4p8vJ2ejTuhiiBl8OBO7IZHI6STQgnoSYz1mtimRpd96lcUtk0Ai/apgaA8T5ajT4DwiEjN4rTx91V5sIaZTkDsX8SJ/Gja0ASB8072x2BeXdxkg7cEUjioHlnuyMw7y5O0gH9peQ/4A4F0p1CiH7fOxaUAP2lpL0HTgXSXUS2Glz2PXXMPgnN2bxfoqnb74ns+qCNE5QL9AXVIaBt9qCdEtFXL6dut/kNyZ/9M8PslrYAAAAASUVORK5CYII=);
}
.jss507 {
  color: #8C8E97;
  padding: 0 2px 0 1px;
  font-size: 12px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
  line-height: 16px;
  white-space: nowrap;
}
.jss507 .iconfont {
  font-size: 12px;
  margin-right: 3px;
}
.jss507 > .tips-time {
  font-family: var(--webull-font-family-normal, OpenSans-Regular,Arial,sans-serif) !important;
}
.jss508 {
  top: 150px;
  left: 20px;
}
.jss509 {
  color: #0CAF82;
}
.jss510 {
  color: #FE3957;
}
.jss511 {
  color: #181C2F;
}
.jss512 {
  padding: 20px;
  background: #FFFFFF;
  margin-top: 10px;
  border-radius: 2px;
}
.jss513 {
  color: #FFFFFF;
  font-size: 14px;
  line-height: 19px;
  margin-bottom: 10px;
}
.jss514 {
  width: 100%;
  height: 1px;
  margin: 10px 0;
  background: rgba(255,255,255,0.3);
}
.jss515 {
  color: #FFFFFF;
  display: flex;
  font-size: 12px;
  margin-top: 5px;
  line-height: 17px;
  justify-content: space-between;
}
.jss516 {
  color: rgba(255,255,255,0.7);
  padding-right: 20px;
}
.jss518 {
  color: #FA9933;
}
.jss519 {
  width: 22px;
  height: 22px;
  background-size: auto 16px;
}
.jss520 {
  gap: 6px;
  display: flex;
  align-items: center;
}
.jss521 {
  width: 100%;
  height: 1px;
  background-color: #F3F6FA;
}
.jss522 {
  margin-left: 8px;
}
.jss523 {
  display: flex;
  flex-direction: column;
}
.jss524 {
  color: #181C2F;
  height: 22px;
  margin: 12px 0;
  display: flex;
  overflow: hidden;
  align-items: center;
}
</style><style data-jss="" data-meta="makeStyles">
.jss248 {
  border: 1px solid #DBE1E8;
  cursor: pointer;
  display: flex;
  z-index: 10;
  position: absolute;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
}
.jss248:hover {
  border-color: #0057FF !important;
  background-color: #0057FF;
}
.jss248:hover svg {
  color: #FFFFFF;
  border: none;
}
.jss249 {
  top: calc((100% - 60px) / 2);
  right: -11px;
  width: 10px;
  height: 60px;
  border-left: none;
  padding-left: 1px;
  border-radius: 0px 4px 4px 0px;
}
.jss250 {
  top: -12px;
  right: calc((100% - 60px) / 2);
  width: 60px;
  height: 10px;
  border-bottom: none;
  border-radius: 4px 4px 0px 0px;
  padding-bottom: 1px;
}
.jss251 {
  top: calc((100% - 60px) / 2);
  left: -3px;
  width: 10px;
  height: 60px;
  border-left: none;
  padding-left: 1px;
  border-radius: 0px 4px 4px 0px;
}
.jss251.left {
  left: unset;
  right: 0px;
  border-left: 1px solid #DBE1E8;
  border-right: none;
  border-radius: 4px 0px 0px 4px;
}
.jss252 {
  left: calc((100% - 60px) / 2);
  width: 60px;
  bottom: 0px;
  height: 10px;
  border-bottom: none;
  border-radius: 4px 4px 0px 0px;
}
.jss253 {
  color: #5D606D;
  display: inline-block;
  font-size: 8px;
}
.jss253:hover svg {
  color: #FFFFFF;
}
.jss254 {
  transform: rotate(270deg);
  -webkit-transform: rotate(270deg);
}
.jss255 {
  transform: rotate(90deg);
  margin-right: 2px;
  -webkit-transform: rotate(90deg);
}
.jss256 {
  transform: rotate(180deg);
  margin-bottom: 1px;
  -webkit-transform: rotate(180deg);
}
.jss257 {
  margin-top: 1px;
}
</style><style data-jss="" data-meta="e(e(WithStores(e(WithAction(n)))))">
.jss566 {
  color: #181C2F;
  display: flex;
  padding: 10px 10px 10px 13px;
  background: #FFFFFF;
  border-top: 1px solid #F3F6FA;
}
.jss567 {
  width: calc( 100% - 32px);
}
.jss568 {
  padding: 0 10px;
  line-height: 20px;
}
.jss568 .cont-external-tips {
  color: #181C2F;
}
.jss569 {
  display: -webkit-box;
  overflow: hidden;
  font-size: 14px;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.jss570 {
  width: 16px;
  cursor: auto;
  font-size: 16px;
  line-height: 20px;
}
.jss571 {
  color: #0057FF;
}
.jss571:hover {
  color: #0057FF;
}
.jss572 {
  color: #FA9933;
}
.jss572:hover {
  color: #FA9933;
}
.jss573 {
  color: #4F4F4F;
}
.jss573:hover {
  color: #181C2F;
  cursor: pointer;
}
</style><style data-jss="" data-meta="e(q(WithTradeInfo(n)))">
.jss615 {
  width: 100%;
  height: 310px;
  overflow: auto;
}
.jss615 tr td {
  background-color: #FFFFFF;
}
.jss616 {
  width: 100%;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.jss617 {
  height: 250px;
  position: relative;
}
</style><style data-jss="" data-meta="WithStores(WithDataLevel(WithTableCache(e(WithTradeInfo(e(WithMultiWinStock(WithCurrentPage(n))))))))">
.jss601 {
  min-height: 310px;
}
.jss602 {
  top: -38px;
  left: 16px;
  color: #FFFFFF;
  cursor: pointer;
  padding: 10px;
  z-index: 1000;
  position: absolute;
  font-size: 12px;
  background: #0057FF;
  white-space: nowrap;
  border-radius: 2px;
}
.jss602:after {
  left: 16px;
  bottom: -6px;
  content: "";
  position: absolute;
  transform: rotate(45deg) translateX(-50%);
  border-top: 8px solid #0057FF;
  border-left: 8px solid #0057FF;
}
.jss603 {
  color: rgba(255,255,255,0.8);
  font-size: 16px;
  margin-left: 10px;
  vertical-align: text-bottom;
}
.jss603:hover {
  color: #FFFFFF;
}
.jss604 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.jss605 {
  width: 150px;
  max-width: 300px !important;
}
</style><style data-jss="" data-meta="makeStyles">
.jss651 {
  width: 100%;
  position: relative;
}
.jss652 {
  min-height: 166px;
}
.jss653 {
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
}
.jss654 {
  top: 50%;
  left: 50%;
  color: #8A8D91;
  display: flex;
  position: absolute;
  min-width: 230px;
  transform: translate(-50%, -50%);
  text-align: center;
  flex-direction: column;
  justify-content: center;
}
.jss655 {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding-left: 20px;
}
.jss655 > span {
  color: #0057FF;
}
.jss655 > i {
  color: #0057FF;
  font-size: 21px;
}
.jss656 {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
.jss657 {
  padding: 0px 20px;
  text-align: center;
}
</style><style data-jss="" data-meta="WithRefPrice(WithPortfolio(n))">
.jss445 {
  border: 0;
  height: 40px;
  margin: 10px 0;
  display: flex;
  padding: 0 20px 0 0;
  line-height: 40px;
  border-radius: 4px;
  justify-content: space-between;
  background-color: #FFFFFF;
}
.jss445 span {
  margin-right: 8px;
}
.jss447 {
  display: flex;
  justify-content: space-between;
}
.jss448 {
  color: #181C2F;
  font-size: 13px;
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
}
.jss449 {
  color: #0CAF82;
}
.jss450 {
  color: #FE3957;
}
.jss451 {
  color: #181C2F;
}
.jss452 {
  color: #5D606D;
  width: 16px;
  cursor: pointer;
  font-size: 16px;
}
.jss453 {
  color: #FFCF00;
}
.jss454 {
  top: 50px;
  right: 0;
}
.jss455 {
  margin-right: 10px;
}
.jss456 {
  margin-right: 4px !important;
}
.jss457 {
  font-family: var(--webull-font-family-semi-bold, OpenSans-SemiBold,Arial,sans-serif) !important;
}
</style><style data-jss="" data-meta="WithTickerConfig(WithRefPrice(Unknown))">
.jss590 {
  padding: 10px;
  background: #FFFFFF;
  min-height: 150px;
}
.jss591 {
  overflow: hidden;
  max-height: 72px;
  transition: all .5s ease;
  margin-bottom: 0px;
  -webkit-transition: all .5s ease;
}
.jss591.expand {
  max-height: 600px;
  margin-bottom: 8px;
}
.jss592 {
  display: flex;
  line-height: 18px;
}
.jss592:not(:last-child) {
  margin-bottom: 8px;
}
.jss592 .tr {
  flex: 1;
  display: flex;
  justify-content: space-between;
}
.jss592 .tr:nth-child(1) {
  margin: 0 5px 0 0;
}
.jss592 .tr:nth-child(2) {
  margin: 0 0 0 5px;
}
.jss592 .tr .td {
  font-size: 13px;
}
.jss592 .tr .td:first-child {
  color: #5D606D;
}
.jss592 .tr .td:last-child {
  color: #181C2F;
}
.jss593 {
  left: 0;
  color: #5D606D;
  width: 100%;
  cursor: pointer;
  text-align: center;
}
.jss593:hover {
  color: #181C2F;
}
.jss593.expand {
  transform: rotate(180deg);
  transition: all .5s ease;
  -webkit-transform: rotate(180deg);
  -webkit-transition: all .5s ease;
}
.jss593 .iconfont {
  cursor: pointer;
}
.jss594 {
  color: #0CAF82 !important;
}
.jss595 {
  color: #FE3957 !important;
}
.jss596 {
  left: 10px;
  font-size: 14px;
  margin-top: 1px;
}
.jss597 {
  cursor: pointer;
  display: flex;
  align-items: center;
}
.jss598 {
  cursor: pointer;
  display: flex;
  align-items: center;
}
.jss599 {
  color: #0057FF;
}
.jss600 {
  white-space: pre;
}
</style><style data-jss="" data-meta="WithI18n(n)">
.jss574 {
  min-height: 120px;
}
.jss575 {
  color: #EEEEEE;
  width: 100%;
  height: 30px;
  padding: 0 10px;
  font-size: 12px;
  text-align: left;
  line-height: 30px;
}
.jss575:after {
  border-color: #E2E7ED;
}
.jss576 {
  padding: 10px 10px 8px;
  position: relative;
}
</style><style data-jss="" data-meta="makeStyles">
.jss622 {
  color: #EEEEEE;
  width: 100%;
  height: 30px;
  padding: 0 10px;
  font-size: 12px;
  text-align: left;
  line-height: 30px;
}
.jss622:after {
  border-color: #303030;
}
.jss623 {
  height: 180px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss432 {
  height: 100%;
  position: relative;
}
.jss433 {
  width: 100%;
  height: 100%;
}
.jss434 {
  width: 100%;
  height: 100%;
}
.jss435 {
  animation: jss437 0.3s ease-in-out forwards;
}
.jss436 {
  top: -60px;
  left: 0;
  width: calc( 100% - 2px );
  height: 60px;
  margin: 0 1px;
  padding: 0 9px;
  z-index: 100;
  position: absolute;
  background: #FFFFFF;
  border-bottom: 1px solid #D9DFE8;
}
@-webkit-keyframes jss437 {
  0% {
    top: -60px;
  }
  50% {
    top: -30px;
  }
  100% {
    top: 0px;
  }
}
.jss438 > div:last-child {
  overflow: hidden;
  border-radius: 0px 0px 4px 4px;
}
.jss439 {
  display: flex;
  flex-direction: column;
}
.jss440 {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  position: absolute;
}
.jss441 {
  height: fit-content;
  position: relative;
  background-color: #FFFFFF;
}
.jss441.drag-active-top::before {
  top: -1px;
  width: 100%;
  height: 1px;
  content: "";
  position: absolute;
  background: #0B90FF;
}
.jss441.drag-active-bottom::before {
  left: 0;
  width: 100%;
  bottom: 0;
  height: 1px;
  content: "";
  position: absolute;
  background: #0B90FF;
}
.jss442 {
  color: #181C2F;
  width: 100%;
  height: 30px;
  padding: 0 10px;
  font-size: 12px;
  background: #FFFFFF;
  text-align: left;
  line-height: 30px;
}
.jss442:after {
  border-color: #E2E7ED;
}
.jss443 {
  border: 0;
  background-color: #FFFFFF;
}
.jss444 {
  width: 100%;
  bottom: -2px;
  cursor: ns-resize;
  height: 10px;
  position: absolute;
}
</style><style data-jss="" data-meta="WithTrade(WithDataLevel(e(Unknown)))">
.jss424 {
  height: 100%;
  display: flex;
  position: relative;
  background: #DBE1E8;
}
.jss425 {
  width: 340px;
  height: 100%;
  overflow: hidden;
  transition: width 0.2s;
  border-radius: 4px;
}
.jss426 {
  width: 0;
  height: 100%;
}
.jss427 {
  width: 24px;
  height: 100%;
  background: #FFFFFF;
}
.jss427:after {
  border-color: #D9DFE8;
}
.jss429 {
  color: #5D606D;
  width: 100%;
  cursor: pointer;
  padding: 11px 0;
  word-wrap: break-word;
  text-align: center;
  line-height: 24px;
  -webkit-writing-mode: vertical-rl;
}
.jss429:hover {
  background: #E5EEFF;
}
.jss430 {
  color: #181C2F;
  background: #D6E4FF;
}
.jss431 {
  padding: 10px;
}
</style><style data-jss="" data-meta="e(StockInfoView)">
.jss294 {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  box-sizing: border-box;
  align-items: center;
  padding-left: 10px;
  justify-content: space-between;
}
.jss295 {
  line-height: 28px;
}
</style><style data-jss="" data-meta="makeStyles">
.jss311 {
  color: #5D606D;
  cursor: pointer;
  font-size: 16px;
}
</style><style data-jss="" data-meta="TickerOperationPanel">
.jss304 {
  color: #5D606D;
  display: flex;
  position: relative;
  border-radius: 4px;
  flex-direction: column;
  background-color: #FFFFFF;
}
.jss305 {
  margin-top: 4px;
}
.jss306 {
  gap: 4px;
  display: flex;
  padding-right: 10px;
}
.jss307 {
  flex: 1;
  display: flex;
  overflow: hidden;
  border-radius: 0px 0px 4px 4px;
  flex-direction: column;
}
</style><style data-jss="" data-meta="WithTickerConfig(WithMultiWinStock(e(FullAndMultiIcons)))">
.jss261 {
  gap: 5px;
  display: flex;
  align-item: center;
  padding-right: 10px;
}
.jss262 {
  display: flex;
  align-items: center;
  border-radius: 2px;
  justify-content: flex-end;
}
.jss263 {
  color: #D9DAE0;
  width: 17px;
  cursor: move;
  height: 34px;
  font-size: 14px;
  text-align: center;
  line-height: 34px;
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
}
.jss263:hover {
  cursor: move;
  font-size: 13px!important;
}
</style><style data-jss="" data-meta="WithRecentVisit(WithAction(WithPortfolio(StocksView)))">
.jss195 {
  width: 100%;
  height: 100%;
  background-color: #DBE1E8;
}
.jss196 {
  top: 0;
  left: 0;
  right: 0;
  width: 240px;
  bottom: 0;
  margin: 4px;
  position: absolute;
  background: #FFFFFF;
  border-radius: 4px;
}
.jss196.cont-l {
  width: 0;
}
.jss197 {
  left: 248px;
  width: calc( 100% - 248px);
  height: calc(100% - 8px);
  margin: 4px 0px;
  display: flex;
  position: absolute;
}
.jss198 {
  top: 4px;
  left: 4px;
  width: 100%;
  height: calc(100% - 8px);
  display: flex;
  position: absolute;
}
.jss199 {
  width: 100%;
  height: 40px;
  background: #FFFFFF;
  flex-shrink: 0;
}
.jss200 {
  width: calc( 100% - 348px);
  height: 100%;
  display: flex;
  position: relative;
  border-bottom: none;
  flex-direction: column;
}
.jss201 {
  border-left: none;
}
.jss202 {
  width: 10px;
}
.jss203 {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: #FFFFFF;
}
.jss204 {
  width: calc(100% - 4px);
  height: 100%;
  display: flex;
  border-right: none;
  border-bottom: none;
  flex-direction: column;
}
.jss205 {
  flex: 1;
  width: 100%;
  position: relative;
  background: #FFFFFF;
  border-radius: 0px 0px 4px 4px;
}
.jss206 {
  right: 4px;
  height: 100%;
  z-index: 6;
  position: absolute;
  border-radius: 4px;
}
.jss207:after {
  border-width: 1px;
}
.jss208:after {
  border: none;
}
.jss209 {
  width: 100%;
  height: 44px;
}
.jss210 {
  width: 100%;
  height: calc(100% - 49px);
}
.jss211 {
  top: 10px;
  left: 10px;
  z-index: 1;
  position: absolute;
}
.jss211.isMulti {
  top: 36px;
}
.jss213 {
  cursor: pointer;
  font-size: 14px;
}
.jss214 {
  height: 100%;
}
.jss215 {
  width: 100%;
  padding: 5px 10px;
}
.jss216 {
  height: 100%;
}
.jss217 {
  height: calc( 100% - 36px );
}
.jss218:hover {
  background: #E5EEFF;
}
.jss219 {
  color: #181C2F;
  padding: 4px 0px;
  z-index: 9999;
  position: relative;
  min-width: 60px;
  background: #FFFFFF;
  box-shadow: 0 7px 20px 0 rgba(0,0,0,0.20);
  line-height: 24px;
  border-radius: 2px;
}
.jss220 {
  width: 150px;
  cursor: pointer;
  line-height: 34px;
}
.jss220:hover {
  background: #E5EEFF;
}
.jss221 {
  padding: 0 25px 0 20px;
}
.jss222 {
  right: 10px;
  position: absolute;
  font-size: 14px;
}
.jss223 {
  position: relative;
}
.jss223:after {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  position: absolute;
  border-color: #F3F6FA;
  border-style: solid;
  border-width: 0;
  pointer-events: none;
}
.jss224:after {
  border-bottom-width: 1px;
}
.jss225:after {
  border-width: 1px;
}
.jss226 {
  border-radius: 4px;
  background-color: #E2E7ED;
}
.jss226:hover {
  background-color: #DBE1E8;
}
</style><style data-styled="active" data-styled-version="6.1.17"></style></head><body><div id="app" style="height: 100%;"><section class="Portal__PortalContainer-sc-bbmzs7-0 fnJqwh"><header class="TitleBar__TitleBarContainer-sc-1ggkkou-1 hIGEdh"><div class="TitleBar__TitleBarLeft-sc-1ggkkou-2 kZPdwZ"><div class="TitleBar__TitleBarLogoWrap-sc-1ggkkou-4 dePdta"><div class="TitleBar__TitleBarLogo-sc-1ggkkou-5 exnzKu"></div></div><span class="TitleBar__TitleBarDivider-sc-1ggkkou-0 eOWkQw"></span><div class="TitleBar__TitleBarOperators-sc-1ggkkou-6 gfHdNV"><div class="jss26"><span class="jss28"><div><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss49 jss27" role="img"><path fill="currentColor" d="m611.755 533.76 218.453 145.792 4.096 3.243c16.768 14.933 21.333 42.069 9.899 63.146-6.4 11.734-16.854 19.712-28.63 22.187l-607.36-.085c-19.797-4.182-34.773-23.851-34.773-47.446 0-16.725 7.68-32.298 20.31-41.088L412.16 533.76l55.125 34.39a77.013 77.013 0 0 0 89.43 0l55.04-34.39zm-440.32-150.315 197.333 123.222-197.333 131.84a84.821 84.821 0 0 0-.811.512V382.89l.81.554zm681.856-.597v256.213l-.726-.597-197.461-131.755 197.461-123.264.726-.597zm-9.088-106.837c11.434 21.077 6.869 48.213-9.899 63.146l-4.096 3.2-295.85 184.747a38.4 38.4 0 0 1-40.022 2.773l-4.693-2.816-295.851-184.704c-12.63-8.746-20.31-24.32-20.31-41.088 0-20.778 11.649-38.528 27.947-45.269h621.099c8.875 3.67 16.555 10.667 21.675 20.01z"></path></svg></span></div></span></div><span class="jss28"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss77 jss73" role="img"><path fill="currentColor" d="M490.667 213.333a362.667 362.667 0 0 1 276.48 597.376h-552.96a362.667 362.667 0 0 1 276.48-597.376zm0 256a23.85 23.85 0 0 0-23.68 21.206L453.29 613.675a64 64 0 1 0 74.794 0l-13.738-123.136a23.85 23.85 0 0 0-23.68-21.206zm-1.024-127.445c-107.051 0-197.462 64.725-231.254 163.541a21.333 21.333 0 0 0 40.363 13.824c27.904-81.578 102.101-134.698 190.89-134.698a205.099 205.099 0 0 1 193.025 135.509 21.333 21.333 0 1 0 40.106-14.421 247.765 247.765 0 0 0-233.088-163.755z"></path></svg></span></span><div><div class="jss85"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss355 jss86" role="img"><path fill="currentColor" d="M725.333 768a42.667 42.667 0 0 1 0 85.333H298.667a42.667 42.667 0 0 1 0-85.333h426.666zm0-554.667a128 128 0 0 1 128 128v256a128 128 0 0 1-128 128H298.667a128 128 0 0 1-128-128v-256a128 128 0 0 1 128-128h426.666zm-212.608 87.04a21.333 21.333 0 0 0-21.333 21.334v200.362l-81.664-81.706a21.333 21.333 0 0 0-30.208 30.165l120.704 120.661a21.248 21.248 0 0 0 13.227 6.187h5.376a21.248 21.248 0 0 0 13.226-6.187L652.8 470.528a21.333 21.333 0 0 0-30.208-30.165l-88.533 88.49V321.707a21.333 21.333 0 0 0-21.334-21.334z"></path></svg></div></div><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss93 jss90" role="img"><path fill="currentColor" d="M701.739 610.56a37.93 37.93 0 0 1-14.934 62.848l-465.194 152.96a37.93 37.93 0 0 1-47.872-47.872l152.96-465.195a37.93 37.93 0 0 1 62.848-14.933zm11.392-109.312a21.333 21.333 0 1 1-30.166 30.165 21.333 21.333 0 0 1 30.166-30.165zm-105.6-346.965a149.333 149.333 0 0 1 5.632 205.226l-5.632 5.974-30.166 30.165a21.333 21.333 0 0 1-32.64-27.221l2.475-2.987 30.165-30.123a106.667 106.667 0 0 0 0-150.869 21.333 21.333 0 1 1 30.166-30.165zm331.861 211.2a21.333 21.333 0 1 1-30.165 30.165c-56.32-56.277-149.206-55.893-211.158.256l-6.058 5.76-54.315 54.315-2.987 2.474a21.333 21.333 0 0 1-29.653-29.696l2.475-2.944 54.314-54.314 6.614-6.315c78.378-71.936 197.888-72.79 270.933.299zM501.931 229.717a21.333 21.333 0 1 1-30.166 30.166 21.333 21.333 0 0 1 30.166-30.166zm331.861 271.531a21.333 21.333 0 1 1-30.165 30.165 21.333 21.333 0 0 1 30.165-30.165zm-90.496-392.235a21.333 21.333 0 1 1-30.165 30.208 21.333 21.333 0 0 1 30.165-30.208z"></path></svg></span><div><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss114 jss110" role="img"><path fill="currentColor" d="M501.632 192c34.048 0 66.773 12.373 91.392 34.517l103.125 92.715a121.6 121.6 0 0 1 41.046 90.453v328.448A93.867 93.867 0 0 1 643.328 832H257.195c-54.87 0-99.328-41.941-99.328-93.653V285.653c0-51.712 44.458-93.653 99.328-93.653h244.48zm91.136 233.045a27.776 27.776 0 0 0-36.693 2.688l-81.494 81.878-38.57-38.614-2.39-2.133a27.776 27.776 0 0 0-36.693 2.56l-96.768 96.17-2.219 2.433a27.605 27.605 0 0 0 1.622 36.693l2.389 2.133c10.752 8.534 26.581 7.68 36.779-2.474l76.928-76.502L454.4 568.62l2.39 2.133a27.776 27.776 0 0 0 36.82-2.645l101.206-101.803 2.176-2.432a27.605 27.605 0 0 0-1.792-36.693zm256.299 16.726c11.776 0 21.333 13.952 21.333 31.189v265.387c0 51.712-28.672 93.653-64 93.653a21.333 21.333 0 0 1-21.333-21.333v-345.43a23.467 23.467 0 0 1 23.466-23.466h40.534z"></path></svg></span></div></div><span class="TitleBar__TitleBarDivider-sc-1ggkkou-0 eOWkQw"></span><div class="jss115"><span class="jss28"><span><button class="jss118"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss128" role="img"><path fill="currentColor" d="M682.667 213.333a128 128 0 0 1 128 128v341.334a128 128 0 0 1-128 128H341.333a128 128 0 0 1-128-128V341.333a128 128 0 0 1 128-128h341.334zM512 351.83a21.333 21.333 0 0 0-21.333 21.334v277.674a21.333 21.333 0 1 0 42.666 0V373.163A21.333 21.333 0 0 0 512 351.829zm-128 74.838A21.333 21.333 0 0 0 362.667 448v128a21.333 21.333 0 1 0 42.666 0V448A21.333 21.333 0 0 0 384 426.667zm256 0A21.333 21.333 0 0 0 618.667 448v128a21.333 21.333 0 1 0 42.666 0V448A21.333 21.333 0 0 0 640 426.667z"></path></svg></button></span><span class="jss29 jss125"></span></span></div></div><div class="TitleBar__TitleBarRight-sc-1ggkkou-3 ceLpeK"><form class="jss129"><div aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><div class="jss139 jss143 jss132"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss147 jss130" role="img"><path fill="currentColor" d="M304.448 272.448A288 288 0 0 1 733.056 656l1.28 1.152 135.808 135.744a32 32 0 0 1-45.248 45.248L689.152 702.4l-1.088-1.408a288 288 0 0 1-383.616-428.544zm45.248 45.248a224 224 0 1 0 316.8 316.8 224 224 0 0 0-316.8-316.8z"></path></svg><div class="jss141 jss145"><input autocomplete="off" placeholder="Symbol/Name" value=""></div><label class="jss140 jss144"></label></div></div></form><div class="jss660"><span class="jss662">Login</span></div></div></header><section class="Portal__PortalBody-sc-bbmzs7-1 ligyfR"><nav class="Navigation__NavigationContainer-sc-12fqvc-0 dIwuek"><div class="Navigation__NavigationItemsArea-sc-12fqvc-2 dkrNa-d"><ul class="Navigation__NavigationItems-sc-12fqvc-1 iXxFRu"><li data-id="watch" class="NavItem__NavItemContainer-sc-1mfh24j-0 bxBDTs"><div><span class="NavItem__NavItemContent-sc-1mfh24j-1 hWBaSF"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss166" role="img"><path fill="currentColor" d="m760.896 344.672 5.632 5.408a156.288 156.288 0 0 1-.8 221.024l-33.6 33.376a219.616 219.616 0 0 0-57.984-150.976l-7.104-7.36-108.896-108.16a156.32 156.32 0 0 1 202.752 6.688z"></path><path d="m258.272 571.104 208.896 207.36a64 64 0 0 0 86.432 3.456l4.064-3.744 65.024-65.504a156.288 156.288 0 0 0-.8-221.024L478.496 349.28a156.288 156.288 0 0 0-215.36-4.608l-5.664 5.408a156.288 156.288 0 0 0 .8 221.024z"></path></svg></span></div></li><li data-id="options" class="NavItem__NavItemContainer-sc-1mfh24j-0 bxBDTs"><div><span class="NavItem__NavItemContent-sc-1mfh24j-1 hWBaSF"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss169" role="img"><path fill="currentColor" d="M542.592 192A96 96 0 0 1 576 197.984v74.432c0 59.456 49.024 111.776 112.32 111.776h79.488c.128 2.08.192 4.16.192 6.24V704a96 96 0 0 1-96 96H352a96 96 0 0 1-96-96V288a96 96 0 0 1 96-96h190.592zm136.064 255.968H569.792l-4.288.32A32.896 32.896 0 0 0 536.8 480.8l.32 4.256.768 4c3.776 14.496 16.864 24.608 31.904 24.64h22.72l-74.144 66.336-44.448-44.736-3.712-3.232a33.184 33.184 0 0 0-41.824 1.92l-89.888 80.736-3.2 3.36a32.79 32.79 0 0 0 .832 43.04l3.328 3.2 3.968 2.816c12.48 7.584 28.48 6.112 39.328-3.616l66.336-59.808 44.48 44.8 3.712 3.296c12.704 9.28 30.144 8.512 41.824-1.92l161.6-144.64 3.072-3.136c8.32-9.76 10.24-23.424 4.8-35.072a33.024 33.024 0 0 0-29.92-19.072z"></path><path d="M640.512 192a77.56 77.56 0 0 1 55.072 22.944l49.056 49.472A80.633 80.633 0 0 1 768 321.184h-63.488a64 64 0 0 1-64-64V192z"></path></svg></span></div></li><li data-id="stocks" class="NavItem__NavItemContainer-sc-1mfh24j-0 bxBDTs"><div><span class="NavItem__NavItemContent-sc-1mfh24j-1 fxrxxa"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss172" role="img"><path fill="currentColor" d="M768 448v192a128 128 0 0 1-128 128H320a64 64 0 0 1-64-64v-33.76l218.496-192.192 45.28 45.28a31.968 31.968 0 0 0 42.432 2.496l2.784-2.528L677.6 410.592A127.584 127.584 0 0 0 768 448zM640 256c5.632 0 11.168.352 16.576 1.056a128.16 128.16 0 0 0-12.224 96.16L542.336 455.392l-22.56-22.592a64 64 0 0 0-87.52-2.784L256 584.96V384a128 128 0 0 1 128-128h256zm116.704 75.36c6.24 13.76 10.08 28.8 11.04 44.672L768 384c-17.632 0-33.6-7.136-45.184-18.688l33.92-33.92z"></path><path d="M677.568 410.56a127.584 127.584 0 0 0 90.4 37.44L768 640a128 128 0 0 1-128 128H320a64 64 0 0 1-64-64v-33.76l218.496-192.192 45.28 45.28a31.968 31.968 0 0 0 45.216 0L677.568 410.56zM768 256a64 64 0 1 1-10.24 127.2l5.44.64a64.064 64.064 0 0 1-32.768-12.032A64 64 0 0 1 768 256z"></path></svg></span></div></li><li data-id="market" class="NavItem__NavItemContainer-sc-1mfh24j-0 bxBDTs"><div><span class="NavItem__NavItemContent-sc-1mfh24j-1 hWBaSF"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss175" role="img"><path fill="currentColor" d="M429.92 270.464a256.128 256.128 0 0 1 292.672 87.296l1.568 2.112 3.328 4.672-4.896-6.784a253.056 253.056 0 0 1 24.096 39.2 32 32 0 0 0 57.344-28.416 321.28 321.28 0 0 0-8.992-16.832c45.984-.224 76.32 8.832 83.264 28 18.144 49.824-128.704 148.992-328 221.536-199.296 72.544-375.552 90.944-393.664 41.12-6.976-19.136 10.4-45.568 45.76-74.976a382.98 382.98 0 0 0 4.096 19.264 32 32 0 0 0 62.24-14.976 251.36 251.36 0 0 1-6.944-46.208A256.16 256.16 0 0 1 429.92 270.464z"></path><path d="M765.6 574.112a255.744 255.744 0 0 1-160.576 177.472 255.744 255.744 0 0 1-237.088-32.736c60.448-11.328 130.784-30.72 204.256-57.472 71.168-25.92 135.68-55.2 188.32-84.448z"></path></svg></span></div></li><li data-id="screener" class="NavItem__NavItemContainer-sc-1mfh24j-0 bxBDTs"><div><span class="NavItem__NavItemContent-sc-1mfh24j-1 hWBaSF"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss178" role="img"><path fill="currentColor" d="m768.896 692.064 60.32 60.736a32 32 0 0 1-42.368 47.776l-3.008-2.656-57.28-57.536a338.034 338.034 0 0 0 42.336-48.32zm-89.664-89.76 3.008 2.688 40.736 40.96a273.408 273.408 0 0 1-41.536 49.056l-44.576-44.896a32 32 0 0 1 42.368-47.808z"></path><path d="M496 224a272 272 0 1 1 0 544 272 272 0 0 1 0-544zm170.976 178.144a32 32 0 0 0-42.464 2.592l-93.056 93.568-45.184-45.184-2.784-2.496a32 32 0 0 0-42.4 2.432L330.464 563.008 328 565.792a32 32 0 0 0 2.336 42.464l2.784 2.496a32 32 0 0 0 42.496-2.368l87.936-87.456 45.344 45.344 2.784 2.464a32 32 0 0 0 42.528-2.56l115.68-116.32 2.464-2.784a32 32 0 0 0-2.592-42.464z"></path></svg></span></div></li><li data-id="trade" class="NavItem__NavItemContainer-sc-1mfh24j-0 bxBDTs"><div><span class="NavItem__NavItemContent-sc-1mfh24j-1 hWBaSF"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss181" role="img"><path fill="currentColor" d="M429.984 467.136 179.2 708.128a62.144 62.144 0 0 0 27.232 104.96 61.952 61.952 0 0 0 60.416-17.152l231.776-261.76 58.944 60.672a52.192 52.192 0 0 0 15.552 66.016 52.032 52.032 0 0 0 67.52-5.216l161.6-161.024a52.16 52.16 0 0 0 5.216-67.648 52.032 52.032 0 0 0-65.952-15.552L566.208 235.936a52.192 52.192 0 0 0-15.616-65.888 52.032 52.032 0 0 0-67.424 5.12l-161.6 160.992a52.16 52.16 0 0 0-5.312 67.744c15.168 20.8 43.232 27.36 66.048 15.456l47.712 47.776z"></path><path d="M636 736h103.872a64 64 0 0 1 62.528 50.464l9.824 45.344H563.648l9.792-45.344A64 64 0 0 1 636 736zm-108 95.808h319.936a16.096 16.096 0 0 1 0 32.192H528a16.096 16.096 0 0 1 0-32.192z"></path></svg></span></div></li><li data-id="account" class="NavItem__NavItemContainer-sc-1mfh24j-0 bxBDTs"><div><span class="NavItem__NavItemContent-sc-1mfh24j-1 hWBaSF"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss184" role="img"><path fill="currentColor" d="M704 288a96 96 0 0 1 96 96v32h-80a112 112 0 0 0-111.84 105.856L608 528a112 112 0 0 0 112 112h80v32a96 96 0 0 1-96 96H320a96 96 0 0 1-96-96V384a96 96 0 0 1 96-96h384zm96 192v96h-80l-4.608-.224A48 48 0 0 1 720 480h80z"></path><path d="M720 480h80a32 32 0 0 1 32 32v32a32 32 0 0 1-32 32h-80a48 48 0 0 1 0-96z"></path></svg></span></div></li><li data-id="paper" class="NavItem__NavItemContainer-sc-1mfh24j-0 bxBDTs"><div><span class="NavItem__NavItemContent-sc-1mfh24j-1 hWBaSF"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss187" role="img"><path fill="currentColor" d="M704 256c53.024 0 96 46.272 96 103.36v241.28C800 657.728 757.024 704 704 704H320c-53.024 0-96-46.272-96-103.36V359.36C224 302.304 266.976 256 320 256h384zm-192 64a32 32 0 0 0-32 32v7.808c-19.104 3.328-34.784 10.24-47.04 20.8-17.92 15.424-26.848 33.856-26.848 55.328 0 18.176 5.76 32.096 17.216 41.76 11.488 9.792 31.168 17.952 59.104 24.48 38.816 8.672 61.248 14.784 67.264 18.336a17.024 17.024 0 0 1 9.088 15.552c0 7.104-3.744 12.416-11.2 16-7.488 3.52-17.6 5.312-30.272 5.312-21.568 0-31.904 0-46.88-11.072-14.944-11.072-19.552-38.08-39.744-40.544-20.192-2.528-32.64 11.936-30.432 31.328 2.24 19.36 7.296 29.92 25.792 45.312 12.928 10.784 31.008 17.184 53.952 20.032V608a32 32 0 0 0 64 0v-5.76c22.4-3.04 40-9.664 52.704-19.84C614.912 567.84 624 549.056 624 526.08c0-17.504-6.336-32.064-19.04-43.712-12.8-11.52-32.064-20.096-57.728-25.792-38.528-7.936-60.928-13.536-67.296-16.832a15.744 15.744 0 0 1-9.504-15.104c0-5.536 2.976-10.368 8.96-14.496 5.952-4.128 15.968-6.176 30.016-6.176 17.056 0 32.768 1.92 47.04 15.552 14.272 13.632 11.072 33.312 30.528 33.312 19.488 0 28.16-14.56 26.944-30.56a62.272 62.272 0 0 0-30.112-47.392 119.392 119.392 0 0 0-39.744-14.336L544 352a32 32 0 0 0-32-32z"></path><path d="M288 736h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32z"></path></svg></span></div></li></ul></div><div class="Feedback__FeedbackContainer-sc-920uvv-0 fNJiOz"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss190 Feedback__FeedbackMenuItem-sc-920uvv-1 jifbUj" role="img"><path fill="currentColor" d="M672 288a96 96 0 0 1 96 96v208a80 80 0 0 1-80 80h-40.224a96 96 0 0 0-67.904 28.128l-45.248 45.248a32 32 0 0 1-45.248 0l-45.248-45.248A96 96 0 0 0 376.224 672H336a80 80 0 0 1-80-80V384a96 96 0 0 1 96-96h320zM448.448 519.968a32 32 0 1 0-42.368 47.968A159.488 159.488 0 0 0 512 608c39.52 0 76.864-14.4 105.888-40.032a32 32 0 1 0-42.368-48A95.488 95.488 0 0 1 512 544c-23.776 0-46.08-8.64-63.552-24.032z"></path></svg></div><div class="Setting__SettingContainer-sc-19i4sbk-0 dWtihS"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss193 Setting__SettingIcon-sc-19i4sbk-1 fFJzN" role="img"><path fill="currentColor" d="m562.88 223.808 160 100A96 96 0 0 1 768 405.216v181.568a96 96 0 0 1-45.12 81.408l-160 100a96 96 0 0 1-101.76 0l-160-100A96 96 0 0 1 256 586.784V405.216a96 96 0 0 1 45.12-81.408l160-100a96 96 0 0 1 101.76 0zM512 416a96 96 0 1 0 0 192 96 96 0 0 0 0-192z"></path></svg></div></nav><section class="Portal__PortalContent-sc-bbmzs7-2 juzuHp"><main class="jss194"><div class="jss195"><div class="jss196"><div class="jss209"><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Watchlists</span></li><li class="Tabs__TabsTab-sc-14jya01-3 cqjTsZ"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Recently</span></li></ul></div></div><div class="jss210"><div class="jss231"><div class="jss232"><div class="jss235"><div aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none" class="jss238 jss245"><span class="jss239 jss246">My Watchlist</span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss247 jss242" role="img"><path fill="currentColor" d="M240.192 330.048a32 32 0 0 0-45.184 45.248l294.4 294.208a32 32 0 0 0 45.248 0l293.76-294.208a32 32 0 1 0-45.312-45.248L512 601.6 240.192 330.048z"></path></svg></div></div></div><div class="jss233"><div class="jss358" tabindex="-1"><div class="jss359"><div class="jss360 jss64 jss68"><div class="jss361 jss362">Name</div><div class="jss361 jss363">Price/Change</div><div class="jss361 jss364"><span aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none" class=""><i class="jss339 webull-paixu jss340 jss369"></i></span></div></div></div><div class="jss365"><div style="flex: 1 1 0%; overflow: hidden;"><div data-simplebar="init" class="jss374 jss375" style="max-height: 100%; max-width: 100%;"><div class="simplebar-wrapper" style="margin: 0px;"><div class="simplebar-height-auto-observer-wrapper"><div class="simplebar-height-auto-observer"></div></div><div class="simplebar-mask"><div class="simplebar-offset" style="right: 0px; bottom: 0px;"><div class="simplebar-content-wrapper" tabindex="0" role="region" aria-label="scrollable content" style="height: auto; overflow: hidden;"><div class="simplebar-content" style="padding: 0px;"><ul style="height: 336px; width: 100%; position: relative;"><li data-ticker-list-key="913354090" class="jss377 jss376 jss403 jss64 jss68" draggable="true" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 56px; transform: translateY(0px);"><div class="jss378"></div><div class="jss382"><div class="jss383"><div class="jss387 jss389"><span class="jss391">IXIC</span><span class="jss393"> IDXNASDAQ</span></div><div class="jss384">NASDAQ</div></div><div class="jss394"><div class="jss387 jss396 jss402">20,585.53</div><div class="jss388 jss397"><span class="jss399 jss402">-45.14</span><div class="jss398 jss402">-0.22%</div></div></div></div></li><li data-ticker-list-key="913243250" class="jss376 jss409 jss64 jss68" draggable="true" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 56px; transform: translateY(56px);"><div class="jss378"></div><div class="jss382"><div class="jss383"><div class="jss387 jss389"><span class="jss391">DIA</span><span class="jss393"> NYSEARCA</span><button class="jss852"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAAEMklEQVRYCeVXS4gcRRj+qrp7ZnadXYwmPlEUsooYAl5ED6IYcvEiGIIX8S66Fw0kQSETibckXhREEE+KFz2KoB6CMZc8yEEQ8RFfeSAhyGY38+iuqnw1ma6qqenuTXJNwW7/1f/j++rrv2q6gVt9iFCA3k7TWr0b9yPBgjHIQt+UXeepu89kIZFD4nI3xdleT4zKeo6ABV+5B48JgbR0Vl4bQBooXyvFXAMUiwl+LknIEsSuvBHcAt8seJDLFaerBVWeDEfAyl7enLk2Advg9fxxwdRjOblrn/mNFg/BanKN8pQdgTDvuu0agHF+ky8AaCbQVKTJFwDMmFFePYEgsJ1ioyywnY9pMwQ6vJ4fFfjKpDjXACBbV/ASm+5xJfFt0cExL7zPmiUQADNMpgpPCY3d2uB2gv/KbSSkwfa2wrNDYJkkLrhyPldmfbyQaLzF+IwkvnYxkeF3gXX4AmWYFhnOscjpIsGrwxaWeX2d8y8Y8HBqsK0MDK/pGp4k8V0a+J3gq2oOf1TUHqdMEwirTOxc48+BwAGV4S8q0NcJBnwE31h3AtzhUibkRR9LlPUdI3GCp99FRRI2z8VFhicwu3oXKlo8RoOhUjxopwXw23hlk1yu+q6Wxj66Lg1THIbGoyRxhqrVEpjtgQBobEbEKPttUmMnH8M/aONkGc6VdLMB9lL2RZXgzTTBJvo2aYFfpLQncDCCml6BwF9nSoFuOsIuNuUWdvYHXNl/NtZotLMreION+gQB38sXqEyOrWNfCz/N1Av0rFcgYGkLsKMXswJ7+fyfp+wfqQ6O2PtssrTTx8u8vphLHCjmcVxriERhie6L3CXnbZwbUd1qAlGQKLAhU3ibOj7NFR7MO/iSBak2ZHuAbVTgNZPhE/79KAwWMo2E0j5UCJyxZBg5x5/iftVOmCYQAVvWaYH72Fy2sZZ0hv16Ht9DjcGR2sIKr/BXNGFH7qBCO2yOHWR3J0kM22v4XEt8N9qIQ9c80/+nCUz7wFVvlgrvUvYut+GeUYbjnLuG4uoUFTnF3jgdphL8Xjbqc9yKRxl8qWjhh9Af2p5AtHpup608fvcTvM8Ce/iaciEz2MAVjwfPgxWdYjCaw/thQWt3VvhIDJ4ZdfCh6eLf2B/OPYHwLm2ZY5mXB9hceSvHx+FJwG4XVGQ3z/ejURpUjpZWeIT7/2/Rxv9OrjhwMq8lQPmOUeyTts1nivC1TS/gVE1NiBQnmHOkkFir3OeB2rUEhvP4tA6g6X6SYcTcz2xMJXiUXEsgivPTgL2/GVhN/grfLIGKIFe+yWeD1vO7Qt5wBOx7u0lupsR1AEfEeFi5w9g/Jn40eF6RZQtERaKIG506LEfAfrGwc/kLG431gJv8FcSNQNFdw9kShZvMj16Pn2b2o4Hv7e7VeT0Anz5tRXkT2S9b8PKraDrhVp1dBYmmMyoyn35HAAAAAElFTkSuQmCC" class="jss408"></button></div><div class="jss384">SPDR® Dow Jones® Industrial Average℠ ETF Trust</div></div><div class="jss394"><div class="jss387 jss396 jss402">443.57</div><div class="jss388 jss397"><span class="jss400 jss402">Pre: </span><div class="jss398 jss402">-0.37%</div></div></div></div></li><li data-ticker-list-key="913354362" class="jss376 jss410 jss64 jss68" draggable="true" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 56px; transform: translateY(112px);"><div class="jss378"></div><div class="jss382"><div class="jss383"><div class="jss387 jss389"><span class="jss391">SPX</span><span class="jss393"> IDXSP</span></div><div class="jss384">S&amp;P 500 Index</div></div><div class="jss394"><div class="jss387 jss396 jss402">6,259.75</div><div class="jss388 jss397"><span class="jss399 jss402">-20.71</span><div class="jss398 jss402">-0.33%</div></div></div></div></li><li data-ticker-list-key="913256135" class="jss376 jss411 jss64 jss68" draggable="true" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 56px; transform: translateY(168px);"><div class="jss378"></div><div class="jss382"><div class="jss383"><div class="jss387 jss389"><span class="jss391">AAPL</span><span class="jss393"> NASDAQ</span><button class="jss852"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAAEMklEQVRYCeVXS4gcRRj+qrp7ZnadXYwmPlEUsooYAl5ED6IYcvEiGIIX8S66Fw0kQSETibckXhREEE+KFz2KoB6CMZc8yEEQ8RFfeSAhyGY38+iuqnw1ma6qqenuTXJNwW7/1f/j++rrv2q6gVt9iFCA3k7TWr0b9yPBgjHIQt+UXeepu89kIZFD4nI3xdleT4zKeo6ABV+5B48JgbR0Vl4bQBooXyvFXAMUiwl+LknIEsSuvBHcAt8seJDLFaerBVWeDEfAyl7enLk2Advg9fxxwdRjOblrn/mNFg/BanKN8pQdgTDvuu0agHF+ky8AaCbQVKTJFwDMmFFePYEgsJ1ioyywnY9pMwQ6vJ4fFfjKpDjXACBbV/ASm+5xJfFt0cExL7zPmiUQADNMpgpPCY3d2uB2gv/KbSSkwfa2wrNDYJkkLrhyPldmfbyQaLzF+IwkvnYxkeF3gXX4AmWYFhnOscjpIsGrwxaWeX2d8y8Y8HBqsK0MDK/pGp4k8V0a+J3gq2oOf1TUHqdMEwirTOxc48+BwAGV4S8q0NcJBnwE31h3AtzhUibkRR9LlPUdI3GCp99FRRI2z8VFhicwu3oXKlo8RoOhUjxopwXw23hlk1yu+q6Wxj66Lg1THIbGoyRxhqrVEpjtgQBobEbEKPttUmMnH8M/aONkGc6VdLMB9lL2RZXgzTTBJvo2aYFfpLQncDCCml6BwF9nSoFuOsIuNuUWdvYHXNl/NtZotLMreION+gQB38sXqEyOrWNfCz/N1Av0rFcgYGkLsKMXswJ7+fyfp+wfqQ6O2PtssrTTx8u8vphLHCjmcVxriERhie6L3CXnbZwbUd1qAlGQKLAhU3ibOj7NFR7MO/iSBak2ZHuAbVTgNZPhE/79KAwWMo2E0j5UCJyxZBg5x5/iftVOmCYQAVvWaYH72Fy2sZZ0hv16Ht9DjcGR2sIKr/BXNGFH7qBCO2yOHWR3J0kM22v4XEt8N9qIQ9c80/+nCUz7wFVvlgrvUvYut+GeUYbjnLuG4uoUFTnF3jgdphL8Xjbqc9yKRxl8qWjhh9Af2p5AtHpup608fvcTvM8Ce/iaciEz2MAVjwfPgxWdYjCaw/thQWt3VvhIDJ4ZdfCh6eLf2B/OPYHwLm2ZY5mXB9hceSvHx+FJwG4XVGQ3z/ejURpUjpZWeIT7/2/Rxv9OrjhwMq8lQPmOUeyTts1nivC1TS/gVE1NiBQnmHOkkFir3OeB2rUEhvP4tA6g6X6SYcTcz2xMJXiUXEsgivPTgL2/GVhN/grfLIGKIFe+yWeD1vO7Qt5wBOx7u0lupsR1AEfEeFi5w9g/Jn40eF6RZQtERaKIG506LEfAfrGwc/kLG431gJv8FcSNQNFdw9kShZvMj16Pn2b2o4Hv7e7VeT0Anz5tRXkT2S9b8PKraDrhVp1dBYmmMyoyn35HAAAAAElFTkSuQmCC" class="jss408"></button></div><div class="jss384">Apple Inc</div></div><div class="jss394"><div class="jss387 jss396 jss402">211.16</div><div class="jss388 jss397"><span class="jss400 jss402">Pre: </span><div class="jss398 jss402">-0.74%</div></div></div></div></li><li data-ticker-list-key="913303964" class="jss376 jss412 jss64 jss68" draggable="true" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 56px; transform: translateY(224px);"><div class="jss378"></div><div class="jss382"><div class="jss383"><div class="jss387 jss389"><span class="jss391">GOOG</span><span class="jss393"> NASDAQ</span><button class="jss852"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAAEMklEQVRYCeVXS4gcRRj+qrp7ZnadXYwmPlEUsooYAl5ED6IYcvEiGIIX8S66Fw0kQSETibckXhREEE+KFz2KoB6CMZc8yEEQ8RFfeSAhyGY38+iuqnw1ma6qqenuTXJNwW7/1f/j++rrv2q6gVt9iFCA3k7TWr0b9yPBgjHIQt+UXeepu89kIZFD4nI3xdleT4zKeo6ABV+5B48JgbR0Vl4bQBooXyvFXAMUiwl+LknIEsSuvBHcAt8seJDLFaerBVWeDEfAyl7enLk2Advg9fxxwdRjOblrn/mNFg/BanKN8pQdgTDvuu0agHF+ky8AaCbQVKTJFwDMmFFePYEgsJ1ioyywnY9pMwQ6vJ4fFfjKpDjXACBbV/ASm+5xJfFt0cExL7zPmiUQADNMpgpPCY3d2uB2gv/KbSSkwfa2wrNDYJkkLrhyPldmfbyQaLzF+IwkvnYxkeF3gXX4AmWYFhnOscjpIsGrwxaWeX2d8y8Y8HBqsK0MDK/pGp4k8V0a+J3gq2oOf1TUHqdMEwirTOxc48+BwAGV4S8q0NcJBnwE31h3AtzhUibkRR9LlPUdI3GCp99FRRI2z8VFhicwu3oXKlo8RoOhUjxopwXw23hlk1yu+q6Wxj66Lg1THIbGoyRxhqrVEpjtgQBobEbEKPttUmMnH8M/aONkGc6VdLMB9lL2RZXgzTTBJvo2aYFfpLQncDCCml6BwF9nSoFuOsIuNuUWdvYHXNl/NtZotLMreION+gQB38sXqEyOrWNfCz/N1Av0rFcgYGkLsKMXswJ7+fyfp+wfqQ6O2PtssrTTx8u8vphLHCjmcVxriERhie6L3CXnbZwbUd1qAlGQKLAhU3ibOj7NFR7MO/iSBak2ZHuAbVTgNZPhE/79KAwWMo2E0j5UCJyxZBg5x5/iftVOmCYQAVvWaYH72Fy2sZZ0hv16Ht9DjcGR2sIKr/BXNGFH7qBCO2yOHWR3J0kM22v4XEt8N9qIQ9c80/+nCUz7wFVvlgrvUvYut+GeUYbjnLuG4uoUFTnF3jgdphL8Xjbqc9yKRxl8qWjhh9Af2p5AtHpup608fvcTvM8Ce/iaciEz2MAVjwfPgxWdYjCaw/thQWt3VvhIDJ4ZdfCh6eLf2B/OPYHwLm2ZY5mXB9hceSvHx+FJwG4XVGQ3z/ejURpUjpZWeIT7/2/Rxv9OrjhwMq8lQPmOUeyTts1nivC1TS/gVE1NiBQnmHOkkFir3OeB2rUEhvP4tA6g6X6SYcTcz2xMJXiUXEsgivPTgL2/GVhN/grfLIGKIFe+yWeD1vO7Qt5wBOx7u0lupsR1AEfEeFi5w9g/Jn40eF6RZQtERaKIG506LEfAfrGwc/kLG431gJv8FcSNQNFdw9kShZvMj16Pn2b2o4Hv7e7VeT0Anz5tRXkT2S9b8PKraDrhVp1dBYmmMyoyn35HAAAAAElFTkSuQmCC" class="jss408"></button></div><div class="jss384">Alphabet Inc</div></div><div class="jss394"><div class="jss387 jss396 jss401">181.31</div><div class="jss388 jss397"><span class="jss400 jss402">Pre: </span><div class="jss398 jss402">-0.58%</div></div></div></div></li><div index="5" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 56px; transform: translateY(280px);"><p class="WatchlistTickerList__AddWatchListRow-sc-rcok03-0 dRRrJd"><span aria-expanded="false" aria-haspopup="dialog"><button class="Text__TextButtonStyled-sc-1tdg0ne-0 ioHFvP"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss414 SearchTickerAddWatchListDialog__SearchTickerAddWatchListDialogTriggerIcon-sc-orwhqs-0 fWGKQh" role="img"><path fill="currentColor" d="M847.04 0C909.44 0 960 50.56 960 112.96v734.08C960 909.44 909.44 960 847.04 960H112.96C50.56 960 0 909.44 0 847.04V112.96C0 50.56 50.56 0 112.96 0h734.08zm-6.528 64H119.488A55.488 55.488 0 0 0 64 119.488v721.024C64 871.168 88.832 896 119.488 896h721.024A55.488 55.488 0 0 0 896 840.512V119.488A55.488 55.488 0 0 0 840.512 64zM480 192a32 32 0 0 1 32 32v224h224a32 32 0 1 1 0 64H512v224a32 32 0 1 1-64 0V512H224a32 32 0 0 1 0-64h224V224a32 32 0 0 1 32-32z"></path></svg>Add Symbol</button></span></p></div></ul></div></div></div></div><div class="simplebar-placeholder" style="width: 240px; height: 336px;"></div></div><div class="simplebar-track simplebar-horizontal" style="visibility: hidden;"><div class="simplebar-scrollbar" style="width: 0px; display: none;"></div></div><div class="simplebar-track simplebar-vertical" style="visibility: hidden;"><div class="simplebar-scrollbar" style="height: 0px; display: none;"></div></div></div></div></div></div></div></div></div><div class="jss248 jss249 left"><span class="jss253"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss258 jss255" role="img"><path fill="currentColor" d="M561.92 722.688 963.2 321.536A38.4 38.4 0 0 0 936.064 256H97.28a38.4 38.4 0 0 0-27.136 65.536l401.28 401.152a64 64 0 0 0 90.496 0z"></path></svg></span></div></div><div class="jss197"><div class="jss200"><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Chart</span></li></ul><div class="jss262"><div class="jss261 jss266"><div class="jss275" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span><span class="jss278 jss282 jss285 undefined" iconsize="16"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss288" role="img"><path fill="currentColor" d="M768 64a128 128 0 0 1 128 128v576a128 128 0 0 1-128 128H192A128 128 0 0 1 64 768V192A128 128 0 0 1 192 64h576zm0 64H192a64 64 0 0 0-64 64v576a64 64 0 0 0 64 64h576a64 64 0 0 0 64-64V192a64 64 0 0 0-64-64zm-56.704 199.424a32 32 0 0 1 4.096 45.056l-142.08 170.432a32 32 0 0 1-42.048 6.4l-110.976-72.128-124.352 151.168a32 32 0 0 1-40.32 7.616l-4.736-3.2a32 32 0 0 1-4.352-45.12l142.528-173.184a32 32 0 0 1 42.112-6.528l111.104 72.192L666.176 331.52a32 32 0 0 1 45.12-4.096z"></path></svg></span></span></div><span><span class="jss278 jss282 jss290 undefined jss264" iconsize="16"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss293" role="img"><path fill="currentColor" d="M668.992 64c47.872 0 92.16 26.176 115.968 68.608l157.12 278.848c23.872 42.432 23.872 94.72 0 137.088L784.96 827.392A133.376 133.376 0 0 1 668.992 896H355.008a133.376 133.376 0 0 1-115.968-68.608L81.92 548.544a140.032 140.032 0 0 1 0-137.088l157.12-278.848A133.376 133.376 0 0 1 355.008 64h313.984zm0 64H355.008c-22.656 0-43.84 11.008-57.216 29.376l-4.672 7.232-157.056 278.848c-11.264 19.968-12.608 44.16-3.776 65.344l3.776 7.744L293.12 795.392c11.328 20.16 31.296 33.408 53.44 36.096l8.448.512h313.984c22.656 0 43.84-11.008 57.216-29.376l4.672-7.232 157.056-278.848c11.328-20.096 12.608-44.352 3.776-65.344l-3.776-7.744L730.88 164.608a71.36 71.36 0 0 0-53.44-36.096l-8.448-.512zM512 352a128 128 0 1 1 0 256 128 128 0 0 1 0-256zm0 64a64 64 0 1 0 0 128 64 64 0 0 0 0-128z"></path></svg></span></span></div></div></div><div class="jss199 jss223 jss224"><div class="jss294"><div class="jss297 jss295 jss296"><span><span>IXIC</span></span><span class="jss299 g-clickable">20,585.53</span><span class="jss299">-45.14</span><span class="jss299">-0.22%</span></div></div></div><div class="jss205"><div class="jss674"><div class="jss681"><ul class="jss690"><li class="jss691 jss694 jss686"><span>30s</span></li><li class="jss691 jss693 jss686"><span>1m</span></li><li class="jss691 jss694 jss686"><span>2m</span></li><li class="jss691 jss694 jss686"><span>3m</span></li><li class="jss691 jss694 jss686"><span>5m</span></li><li class="jss691 jss694 jss686"><span>10m</span></li><li class="jss691 jss694 jss686"><span>15m</span></li><li class="jss691 jss694 jss686"><span>30m</span></li><li class="jss691 jss694 jss686"><span>1h</span></li><li class="jss691 jss694 jss686"><span>2h</span></li><li class="jss691 jss694"><div class="jss697"><span>4h</span><div class="jss711" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span></span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss715 jss713" role="img"><path fill="currentColor" d="M566.613 780.459 942.08 279.893a68.267 68.267 0 0 0-54.613-109.226H136.533A68.267 68.267 0 0 0 81.92 279.893L457.387 780.46a68.267 68.267 0 0 0 109.226 0z"></path></svg></div></div></li></ul></div><div class="clearfix jss733 jss734 jss737"><div class="jss738 jss266"><div aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none" class="DrawIcon__DrawIconTrigger-sc-1g61xbn-1 csrmGF"><span><span class="jss278 jss282 jss779 undefined jss776" iconsize="16"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss782" role="img"><path fill="currentColor" d="M327.936 822.144A16 16 0 0 1 313.152 832h-67.904A53.248 53.248 0 0 1 192 778.752v-67.904a16 16 0 0 1 27.264-11.328l105.152 105.152a16 16 0 0 1 3.52 17.472zm81.472-49.024a44.736 44.736 0 0 1-32 13.184l.192-108.224 107.328-107.392a22.4 22.4 0 0 0 2.56-28.608l-2.56-3.072a22.4 22.4 0 0 0-28.608-2.56l-3.072 2.56L345.856 646.4l-108.16-.128a44.8 44.8 0 0 1 13.184-31.68l285.44-285.44L694.848 487.68l-285.44 285.44zM817.92 307.84a48 48 0 0 1 0 67.84l-90.752 90.688-169.472-169.6 90.688-90.688a47.936 47.936 0 0 1 67.776 0l101.76 101.76z"></path></svg></span></span></div><div class="jss783 jss785" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span><span class="jss278 jss282 jss798 undefined jss789"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss801" role="img"><path fill="currentColor" d="M725.333 213.333a85.333 85.333 0 0 1 85.334 85.334v426.666a85.333 85.333 0 0 1-85.334 85.334H298.667a85.333 85.333 0 0 1-85.334-85.334v-70.656l118.998-119.296 2.901-2.432a21.333 21.333 0 0 1 27.05 2.219l61.227 59.477 4.95 4.31a64 64 0 0 0 85.589-5.632l84.992-90.24 29.056 29.013a21.333 21.333 0 0 0 36.139-11.435l18.858-109.098a21.333 21.333 0 0 0-24.746-24.619l-108.8 19.2a21.333 21.333 0 0 0-11.35 36.053l30.72 30.72-85.29 90.454-3.115 2.73a21.333 21.333 0 0 1-27.264-2.09l-61.227-59.435-4.864-4.267a64 64 0 0 0-85.034 4.95l-88.79 89.002V298.667a85.333 85.333 0 0 1 85.334-85.334h426.666z"></path></svg></span></span></div><span><span class="jss28"><div style="height: 100%;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss810 jss805 jss806" role="img"><path fill="currentColor" d="M512 213.333a256 256 0 0 1 256 256c0 97.28-42.112 148.395-86.485 193.067l-17.92 17.707c-8.064 7.936-16 15.786-23.595 23.893v64a42.667 42.667 0 0 1-42.667 42.667H426.667A42.667 42.667 0 0 1 384 768v-64c-55.296-58.624-128-105.557-128-234.667a256 256 0 0 1 256-256zm121.899 174.635a16.853 16.853 0 0 0-23.894 2.133l-65.578 79.318-58.795-38.486a16.853 16.853 0 0 0-22.315 3.414l-75.477 92.458a17.152 17.152 0 0 0 2.347 24.022l2.517 1.706a16.853 16.853 0 0 0 21.333-4.053l65.792-80.64 58.795 38.485c7.21 4.694 16.768 3.243 22.23-3.413l75.22-90.88a17.152 17.152 0 0 0-2.133-24.021zM261.12 183.467l44.33 25.6a25.6 25.6 0 0 1-25.6 44.373l-44.373-25.6a25.6 25.6 0 0 1 25.6-44.373zm565.675 9.386a25.6 25.6 0 0 1-9.387 34.987l-44.288 25.6a25.6 25.6 0 1 1-25.6-44.373l44.33-25.6a25.6 25.6 0 0 1 34.987 9.386zM512 85.333c11.776 0 21.333 11.776 21.333 26.283v32.768c0 14.507-9.557 26.283-21.333 26.283s-21.333-11.776-21.333-26.283v-32.768c0-14.507 9.557-26.283 21.333-26.283z"></path></svg></div><span class="jss29 jss809"></span></span></span><div class="jss711" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span><div class="jss811 jss813"><span><span class="jss278 jss282 jss824 undefined jss818"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss827" role="img"><path fill="currentColor" d="M320 133.44c14.08 0 25.6 12.544 25.6 27.968v96.512h51.2c28.288 0 51.2 25.088 51.2 56v396.544c0 30.912-22.912 56-51.2 56h-51.2v96.832c0 15.424-11.52 27.968-25.6 27.968-14.08 0-25.6-12.544-25.6-27.968v-96.832h-51.2c-28.288 0-51.2-25.088-51.2-56V313.92c0-30.912 22.912-56 51.2-56h51.2v-96.512c0-15.424 11.52-27.968 25.6-27.968zm384 64c6.784 0 13.312 2.88 18.112 8 4.8 5.12 7.488 12.16 7.488 19.392v94.784h51.2c28.288 0 51.2 24.576 51.2 54.848V649.92c0 30.272-22.912 54.848-51.2 54.848h-51.2v95.104c0 15.104-11.52 27.392-25.6 27.392-14.08 0-25.6-12.288-25.6-27.392v-95.104h-51.2c-28.288 0-51.2-24.576-51.2-54.848V374.4c0-30.272 22.912-54.848 51.2-54.848h51.2v-94.72c0-7.296 2.688-14.272 7.488-19.456A24.768 24.768 0 0 1 704 197.44z"></path></svg></span></span></div></span></div><div class="jss828 jss830"><span><span class="jss278 jss282 jss848 undefined jss834"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss851" role="img"><path fill="currentColor" d="M448.512 256h91.435a17.067 17.067 0 0 1 15.573 24.021L342.187 757.888A17.067 17.067 0 0 1 326.613 768H217.6a17.067 17.067 0 0 1-17.067-16.043l-28.501-477.866A17.067 17.067 0 0 1 189.099 256h84.65a17.067 17.067 0 0 1 17.067 16.555l7.68 261.632v15.957c0 29.995-1.707 56.661-5.035 80h2.518c2.901-13.312 7.552-29.739 13.909-49.237l6.144-18.006c4.01-11.093 7.723-20.224 11.179-27.434l105.429-268.63A17.067 17.067 0 0 1 448.512 256zm375.595 361.003c0 51.712-15.318 91.904-45.952 120.661-30.592 28.757-73.302 43.093-128.128 43.093a240.64 240.64 0 0 1-64.982-8.533c-11.52-3.03-32.896-12.928-64.17-29.739A17.152 17.152 0 0 1 512 727.552v-83.328a17.067 17.067 0 0 1 25.344-14.848c25.173 13.568 43.52 22.485 55.04 26.795 22.613 8.362 43.093 12.544 61.44 12.544 15.36 0 27.05-3.584 34.987-10.838 7.893-7.253 11.861-17.493 11.861-30.72a41.899 41.899 0 0 0-3.755-17.664 73.003 73.003 0 0 0-10.624-16.341l-4.181-4.693c-5.376-5.547-14.123-14.08-26.325-25.515l-6.4-5.973c-24.32-21.888-42.07-44.246-53.078-67.03a170.027 170.027 0 0 1-16.597-75.093c0-30.464 6.827-57.899 20.48-82.219 13.653-24.32 32.939-43.136 57.813-56.533C682.923 262.699 710.784 256 741.632 256c48.64 0 89.301 9.77 122.027 29.27 7.253 4.18 10.282 13.055 7.125 20.863l-30.293 74.539a17.067 17.067 0 0 1-22.656 9.13c-35.414-15.658-60.416-23.466-74.966-23.466-13.354 0-23.594 3.243-30.805 9.813-7.168 6.486-10.752 16.043-10.752 28.587 0 11.435 3.456 22.059 10.453 31.957 6.102 8.619 17.067 19.84 32.811 33.579l7.04 6.059c24.576 20.266 42.795 41.514 54.656 63.872 11.904 22.357 17.835 47.957 17.835 76.8z"></path></svg></span></span></div></div></div><div style="display: flex; flex: 1 1 0%; height: 100%; overflow: hidden;"><div style="position: relative; width: 100%; flex: 1 1 0%; z-index: 0; overflow: hidden;"><div role="presentation" style="position: relative; user-select: none; width: 1260px; height: 487px;"><canvas width="1260" height="487" style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 1260px; height: 487px;"></canvas><canvas width="1260" height="487" style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 1260px; height: 487px;"></canvas></div><div class="jss873" style="right: 76px; top: 18px; display: none;"><div class="jss874"><div class="jss875"><div class="jss891 jss64 jss66"><i class="jss892 webull-graph_move"></i></div><span><div class="jss877"><div style="background: rgb(25, 118, 211); width: 16px; height: 16px; border-radius: 2px; margin-left: -10px;"></div><i class="jss892 webull-icon_down_Arrow jss884"></i></div></span><span><div class="jss877"><i class="jss892 webull-line_width jss883"></i><i class="jss892 webull-icon_down_Arrow jss884"></i></div></span><span><div><div class="jss877"><i class="jss892 webull-line_width jss883"></i><i class="jss892 webull-icon_down_Arrow jss884"></i></div></div></span><span><div class="jss877"><i class="jss892 webull-lock_on jss879"></i></div></span><span><div class="jss877"><i class="jss892 webull-draw_delete jss879"></i></div></span><span><div class="jss877"><i class="jss892 webull-Exit-1-24_24 jss879"></i></div></span></div></div></div></div></div><div class="jss682"><div class="jss704"><ul class="jss701 jss702"><li class="jss698 jss694"><span>1D</span></li><li class="jss698 jss694"><span>5D</span></li><li class="jss698 jss694"><span>1M</span></li><li class="jss698 jss694"><span>3M</span></li><li class="jss698 jss694"><span>6M</span></li><li class="jss698 jss694"><span>YTD</span></li><li class="jss698 jss694"><span>1Y</span></li><li class="jss698 jss694"><span>5Y</span></li><li class="jss698 jss694"><span>Max</span></li></ul><ul class="jss765"><li class="jss766"><span><div class="jss711" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span>Linear</span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss773 jss713" role="img"><path fill="currentColor" d="M566.613 780.459 942.08 279.893a68.267 68.267 0 0 0-54.613-109.226H136.533A68.267 68.267 0 0 0 81.92 279.893L457.387 780.46a68.267 68.267 0 0 0 109.226 0z"></path></svg></div></span></li><li class="jss766 jss768"><span><span>Auto</span></span></li></ul></div></div></div></div><div class="jss304 jss305" style="height: 284px;"><div class="jss248 jss250"><span class="jss253"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss308 jss257" role="img"><path fill="currentColor" d="M561.92 722.688 963.2 321.536A38.4 38.4 0 0 0 936.064 256H97.28a38.4 38.4 0 0 0-27.136 65.536l401.28 401.152a64 64 0 0 0 90.496 0z"></path></svg></span></div><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">News</span></li><li class="Tabs__TabsTab-sc-14jya01-3 cqjTsZ"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Options</span></li></ul><div class="jss306"><span><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss314 jss311" role="img"><path fill="currentColor" d="M470.592 553.344a32 32 0 0 1 3.712 40.832l-3.712 4.48-231.168 231.168a63.744 63.744 0 0 0 9.088 1.728L256 832h160a32 32 0 1 1 0 64H256a128 128 0 0 1-128-128V608a32 32 0 0 1 64 0v160c0 5.696.768 11.2 2.112 16.448l231.232-231.04a32 32 0 0 1 45.248 0zM864 576a32 32 0 0 1 32 32v160a128 128 0 0 1-128 128H608a32 32 0 1 1 0-64h160a64 64 0 0 0 63.552-56.512L832 768V608a32 32 0 0 1 32-32zm-96-448a128 128 0 0 1 128 128v160a32 32 0 1 1-64 0V256a64 64 0 0 0-2.432-17.536L598.656 469.248a32 32 0 0 1-48.96-40.832l3.648-4.416 230.208-230.08a63.744 63.744 0 0 0-8.064-1.472L768 192H608a32 32 0 0 1 0-64h160zm-352 0a32 32 0 0 1 0 64H256a64 64 0 0 0-63.552 56.512L192 256v160a32 32 0 0 1-64 0V256a128 128 0 0 1 128-128h160z"></path></svg></span></span></div></div><div class="jss307"><div class="jss315"><div class="jss317"><div data-simplebar="init" class="jss374 jss861 simplebar-scrollable-y" style="max-height: 100%; max-width: 100%;"><div class="simplebar-wrapper" style="margin: 0px;"><div class="simplebar-height-auto-observer-wrapper"><div class="simplebar-height-auto-observer"></div></div><div class="simplebar-mask"><div class="simplebar-offset" style="right: 0px; bottom: 0px;"><div class="simplebar-content-wrapper" tabindex="0" role="region" aria-label="scrollable content" style="height: auto; overflow: hidden scroll;"><div class="simplebar-content" style="padding: 0px;"><ul style="height: 1836px; width: 100%; position: relative;"><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(0px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">U.S. S&amp;P 500 E-MINIS DOWN 0.39%, NASDAQ 100 E-MINIS DOWN 0.36%, DOW E-MINIS DOWN 0.38%</span></div><div class="jss324">Reuters&nbsp;&nbsp;·&nbsp;&nbsp;8m ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(36px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">S&amp;P 500 EMINIS FALL 0.5%, NASDAQ FUTURES DOWN 0.5%, </span></div><div class="jss324">Reuters&nbsp;&nbsp;·&nbsp;&nbsp;10h ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(72px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">Is Arbe Robotics (NASDAQ:ARBE) Weighed On By Its Debt Load?</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;17h ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(108px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">In the wake of Mereo BioPharma Group plc's (NASDAQ:MREO) latest US$200m market cap drop, institutional owners may be forced to take severe actions</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;18h ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(144px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">Should You Be Impressed By 3 E Network Technology Group Limited's (NASDAQ:MASK) ROE?</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;18h ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(180px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">Insiders own 66% of Priority Technology Holdings, Inc. (NASDAQ:PRTH) in spite of selling recently and the recent dip may have hurt them</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;18h ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(216px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">Individual investors are ZenaTech, Inc.'s (NASDAQ:ZENA) biggest owners and were rewarded after market cap rose by US$40m last week</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;18h ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(252px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">The Market Lifts Optex Systems Holdings, Inc (NASDAQ:OPXS) Shares 27% But It Can Do More</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;19h ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(288px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">What Village Farms International, Inc.'s (NASDAQ:VFF) 28% Share Price Gain Is Not Telling You</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;19h ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(324px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">Further Upside For Precipio, Inc. (NASDAQ:PRPO) Shares Could Introduce Price Risks After 41% Bounce</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;1d ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(360px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">Returns on Capital Paint A Bright Future For Lantheus Holdings (NASDAQ:LNTH)</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;1d ago</div></div></li></div><div style="position: absolute; top: 0px; left: 0px; width: 100%; height: 36px; transform: translateY(396px);"><li class="jss320 jss325 jss329 jss331"><div class="jss321"><div class="jss322"><span class="jss323">Oncology Institute (NASDAQ:TOI) pops 24% this week, taking one-year gains to 566%</span></div><div class="jss324">Simply Wall St&nbsp;&nbsp;·&nbsp;&nbsp;1d ago</div></div></li></div></ul></div></div></div></div><div class="simplebar-placeholder" style="width: 1260px; height: 1836px;"></div></div><div class="simplebar-track simplebar-horizontal" style="visibility: hidden;"><div class="simplebar-scrollbar" style="width: 0px; display: none;"></div></div><div class="simplebar-track simplebar-vertical" style="visibility: visible;"><div class="simplebar-scrollbar" style="height: 34px; transform: translate3d(0px, 0px, 0px); display: block;"></div></div></div></div></div></div></div></div><div class="jss206"><div class="jss248 jss251 right"><span class="jss253"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss337 jss254" role="img"><path fill="currentColor" d="M561.92 722.688 963.2 321.536A38.4 38.4 0 0 0 936.064 256H97.28a38.4 38.4 0 0 0-27.136 65.536l401.28 401.152a64 64 0 0 0 90.496 0z"></path></svg></span></div><div class="jss424"><div class="jss425 jss208"><div class="jss432" id="rightTabWrap"><div class="jss433"><div class="jss436"><div class="jss445"><div class="jss446"><span class="jss448">IXIC</span><span class="jss450 g-clickable">20,585.53</span><span class="jss450">-45.14</span><span class="jss450">-0.22%</span></div><div class="jss447"><div class="jss455"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss460 jss452 jss453" role="img"><path fill="currentColor" d="M292.864 907.904a39.232 39.232 0 0 1-56.96-41.344L276.8 628.608 103.744 460.032a39.232 39.232 0 0 1 21.824-66.88l239.04-34.688 106.816-216.64a39.232 39.232 0 0 1 70.336 0L648.64 358.4l238.976 34.688a39.232 39.232 0 0 1 21.76 66.944L736.384 628.608l40.832 238.016a39.232 39.232 0 0 1-56.896 41.28L506.56 795.52 292.8 907.904h.064z"></path></svg></span></div><div class="jss464"><span><i class="jss339 webull-ticker_setting jss340 jss465"></i></span></div></div></div></div><div style="overflow: visible; height: 0px; width: 0px;"><div data-simplebar="init" class="jss372 jss481 simplebar-scrollable-y" style="max-height: 907px; width: 340px;"><div class="simplebar-wrapper" style="margin: 0px;"><div class="simplebar-height-auto-observer-wrapper"><div class="simplebar-height-auto-observer"></div></div><div class="simplebar-mask"><div class="simplebar-offset" style="right: 0px; bottom: 0px;"><div class="simplebar-content-wrapper" tabindex="0" role="region" aria-label="scrollable content" style="height: auto; overflow: hidden scroll;"><div class="simplebar-content" style="padding: 0px;"><div class="jss438" id="DomWrap"><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Quote</span></li></ul></div><div class="jss485"><div class="jss487 clearfix"><div class="jss525 jss526"><div class="tit">IXIC <span>IDXNASDAQ</span></div><span><div class="tit tit-name  narrowSymbol" style="max-width: 192px;">NASDAQ</div></span></div></div><div class="jss498"><div class="jss502 jss510 false"><div class="price  g-clickable">20,585.53</div><div class="changePrice"><span class="ratio">-45.14</span><span class="ratio">-0.22%</span></div></div></div><div class="jss499"><div class="jss507"><span>Closed</span></div><div class="jss507"><div class="tips-time"><span>07/11 17:16 EDT</span></div></div></div><div class="jss521"></div><div class="jss489"><div style="padding-top: 8px;"><div class="jss534"><span><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss540 jss535 jss537 jss536" role="img"><path fill="currentColor" d="M292.864 907.904a39.232 39.232 0 0 1-56.96-41.344L276.8 628.608 103.744 460.032a39.232 39.232 0 0 1 21.824-66.88l239.04-34.688 106.816-216.64a39.232 39.232 0 0 1 70.336 0L648.64 358.4l238.976 34.688a39.232 39.232 0 0 1 21.76 66.944L736.384 628.608l40.832 238.016a39.232 39.232 0 0 1-56.896 41.28L506.56 795.52 292.8 907.904h.064z"></path></svg></span></span><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss543 jss535 jss536" role="img"><path fill="currentColor" d="M800 832a32 32 0 1 1 0 64H544a32 32 0 0 1 0-64h256zm0-128a32 32 0 1 1 0 64H608a32 32 0 0 1 0-64h192zM653.376 529.472 409.088 823.808a64 64 0 0 1-34.752 21.248L192 886.912l6.4-189.12a64 64 0 0 1 14.976-39.04l243.904-293.76 196.096 164.48zM774.08 289.28a64 64 0 0 1 7.936 90.112l-92.16 98.048L493.76 312.96l92.16-98.112a64 64 0 0 1 90.112-7.872l98.048 82.304z"></path></svg></span><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss546 jss535 jss536" role="img"><path fill="currentColor" d="M512 128c24.96 0 46.08 24.64 52.736 63.104 104.576 23.808 187.904 117.12 213.44 237.12l2.816 14.784a375.322 375.322 0 0 1 5.312 62.912v62.272c0 67.584 44.736 130.432 101.504 135.296l8.192.384C896 739.264 871.424 768 841.152 768H182.848c-28.16 0-51.328-24.768-54.464-56.64l-.384-7.488c57.856 0 105.216-59.712 109.44-126.08l.256-9.6V505.92c0-156.032 95.36-286.08 221.568-314.816C465.92 152.64 487.04 128 512 128zM402.304 832h219.392c0 70.72-49.088 128-109.696 128-57.856 0-105.216-52.224-109.44-118.4l-.256-9.6z"></path></svg></span></div></div><div class="jss503"><div class="jss663"></div></div></div><div class="jss464"><span><i class="jss339 webull-ticker_setting jss340 jss465"></i></span></div></div><div class="jss556"><div class="jss560"><div class="jss562">Sign up and Get Free Level 2 Market Data for 1-Month!</div><div class="jss563"><span>Know More</span><i class="jss339 webull-Middle_arrow_right_"></i></div></div><div class="jss557"><i class="jss339 webull-cancel_ jss558 jss559"></i></div></div><div class="jss565"><div class="jss566"><div class="g-clickable"><i class="jss339 webull-news jss340 jss570 jss571 jss572 g-clickable"></i></div><div class="jss567 g-clickable"><div class="jss568 jss569">U.S. S&amp;P 500 E-MINIS DOWN 0.39%, NASDAQ 100 E-MINIS DOWN 0.36%, DOW E-MINIS DOWN 0.38%</div></div><div><i class="jss339 webull-cancel_ jss340 jss570 jss573"></i></div></div></div><div><div class="jss441 jss443" id="KeyStatistics" draggable="true"><div class="jss574"><div class="jss577"><div class="jss579"><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Key Statistics</span></li></ul></div></div><div class="jss580" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss589 jss581" role="img"><path fill="currentColor" d="M160 320h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 512h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 704h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 1 1 0-64z"></path></svg></span></div></div><div class="jss576" draggable="true"><div class="jss591 expand"><div class="jss592"><div class="tr"><div class="td">Open</div><div class="td value open jss595 g-clickable">20,562.88</div></div><div class="tr"><div class="td">Open Int</div><div class="td value openInterest jss600">--</div></div></div><div class="jss592"><div class="tr"><div class="td">Prev Close</div><div class="td value preClose jss600 g-clickable">20,630.67</div></div><div class="tr"><div class="td">High</div><div class="td value high jss594 g-clickable">20,647.98</div></div></div><div class="jss592"><div class="tr"><div class="td">Low</div><div class="td value low jss595 g-clickable">20,509.75</div></div><div class="tr"><div class="td">Volume</div><div class="td value volume jss600">1.31B</div></div></div><div class="jss592"><div class="tr"><div class="td">Avg Vol(3M)</div><div class="td value avgVol3M jss600">1.44B</div></div><div class="tr"><div class="td">52 Wk High</div><div class="td value fiftyTwoWkHigh jss600">20,655.39</div></div></div><div class="jss592"><div class="tr"><div class="td">52 Wk Low</div><div class="td value fiftyTwoWkLow jss600">14,784.03</div></div><div class="tr"><div class="td">Turnover</div><div class="td value dealAmount jss600">--</div></div></div><div class="jss592"><div class="tr"><div class="td">% Range</div><div class="td value vibrateRatio jss600">0.67%</div></div><div class="tr empty"></div></div></div></div></div></div><div class="jss441 jss443" id="Trade" draggable="true"></div><div class="jss441 jss443" id="OrderPosition" draggable="true"><div class="jss601"><div class="jss577"><div class="jss579"><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Positions</span></li><li class="Tabs__TabsTab-sc-14jya01-3 cqjTsZ"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Orders</span></li></ul></div></div><div class="jss580" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss614 jss581" role="img"><path fill="currentColor" d="M160 320h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 512h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 704h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 1 1 0-64z"></path></svg></span></div></div><div draggable="true"><div class="jss618"><div class="jss619"></div><div><span>Please </span><div class="jss620">Login</div><span> to view Positions</span></div></div></div></div></div><div class="jss441 jss443" id="PriceLadderTrade" draggable="true"></div><div class="jss441 jss443" id="QuickChart" draggable="true"><div class="jss621"><div class="jss577"><div class="jss579"><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Quick Chart</span></li></ul></div></div><div class="jss580" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss630 jss581" role="img"><path fill="currentColor" d="M160 320h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 512h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 704h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 1 1 0-64z"></path></svg></span></div></div><div class="jss623" draggable="true"><div style="position: relative; width: 100%; height: 100%; z-index: 0;"><div role="presentation" style="position: relative; user-select: none; width: 340px; height: 180px;"><canvas width="340" height="180" style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 340px; height: 180px;"></canvas><canvas width="340" height="180" style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 340px; height: 180px;"></canvas></div></div></div></div></div><div class="jss441 jss443" id="Totalview" draggable="true"><div class="jss577"><div class="jss579"><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Level 1</span></li></ul></div></div><div class="jss580" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss637 jss581" role="img"><path fill="currentColor" d="M160 320h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 512h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 704h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 1 1 0-64z"></path></svg></span></div></div><div class="jss638 jss639" draggable="true"><div class="jss618"><div class="jss619"></div><div><span>Please </span><div class="jss620">Login</div><span> to view Details.</span></div></div></div></div><div class="jss441 jss443" id="OvernightOrderBook" draggable="true"><div class="jss577"><div class="jss579"><div class="Tabs__TabsContainer-sc-14jya01-0 dogVCK"><ul class="Tabs__TabsContent-sc-14jya01-1 gcXiGn"><li class="Tabs__TabsTab-sc-14jya01-3 gCrvYe"><span class="Tabs__TabsTabContent-sc-14jya01-2 dpxrXY">Blue Ocean Lv1</span></li></ul></div></div><div class="jss580" aria-expanded="false" aria-haspopup="listbox" role="combobox" aria-autocomplete="none"><span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" class="jss43 jss44 jss650 jss581" role="img"><path fill="currentColor" d="M160 320h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 512h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64zM160 704h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64zm192 0h512a32 32 0 1 1 0 64H352a32 32 0 1 1 0-64z"></path></svg></span></div></div><div class="jss651 jss652" draggable="true"><div class="jss618"><div class="jss619"></div><div><span>Please </span><div class="jss620">Login</div><span> to view Details.</span></div></div></div></div></div></div></div></div></div></div><div class="simplebar-placeholder" style="width: 340px; height: 1452px;"></div></div><div class="simplebar-track simplebar-horizontal" style="visibility: hidden;"><div class="simplebar-scrollbar" style="width: 0px; display: none;"></div></div><div class="simplebar-track simplebar-vertical" style="visibility: visible;"><div class="simplebar-scrollbar" style="height: 566px; transform: translate3d(0px, 0px, 0px); display: block;"></div></div></div></div></div></div></div></div></div></div></div></main><footer class="Footer__FooterContainer-sc-107yvra-0 gcsHap"><span><i class="jss339 webull-little-Refresh_1 jss340 Footer__FooterIcon-sc-107yvra-1 iyiquP"></i></span><span status="online" class="NetworkStatusView__NetworkStatusIcon-sc-eg1189-0 CFkbD"></span><label class="Datetime__FooterDatetimeLabel-sc-6cxuql-0 fJBjtz">Eastern Time <span>07/14/02025 04:09:08</span></label></footer></section></section></section><div id="DndDescribedBy-0" style="display: none;">
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  </div><div id="DndLiveRegion-0" role="status" aria-live="assertive" aria-atomic="true" style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(100%); white-space: nowrap;"></div></div><div><div class="grecaptcha-badge" data-style="bottomright" style="width: 256px; height: 60px; display: block; transition: right 0.3s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;"><div class="grecaptcha-logo"><iframe title="reCAPTCHA" width="256" height="60" role="presentation" name="a-am1axdmml0ae" frameborder="0" scrolling="no" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation" src="https://www.google.com/recaptcha/enterprise/anchor?ar=1&amp;k=6LdTw50kAAAAAK9fgRtNjcbLQTUc4Bb_O1pTDUJY&amp;co=aHR0cHM6Ly9hcHAud2VidWxsLmNvbTo0NDM.&amp;hl=en&amp;v=_cn5mBoBXIA0_T7xBjxkUqUA&amp;size=invisible&amp;cb=j152sphgbqy7"></iframe></div><div class="grecaptcha-error"></div><textarea id="g-recaptcha-response-100000" name="g-recaptcha-response" class="g-recaptcha-response" style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea></div><iframe style="display: none;"></iframe></div></body></html>