"""Trading API client for Webull."""

import time
from typing import Dict, List, Optional

from ..core.config import settings
from ..models.account import Account, AccountBalance, AccountSummary, Portfolio, TradingPermissions
from ..models.enums import APIEndpointType, OrderStatus
from ..models.orders import Order, OrderRequest, Position
from ..utils.exceptions import OrderValidationError
from ..utils.logger import get_structured_logger
from .market_data import MarketDataClient

logger = get_structured_logger(__name__)


class TradingClient(MarketDataClient):
    """Trading API client with order management and portfolio tracking."""
    
    def __init__(self):
        super().__init__()
        self.is_paper_trading = settings.webull.paper_trading
        
    async def get_account_summary(self, use_cache: bool = True) -> AccountSummary:
        """
        Get complete account summary.
        
        Args:
            use_cache: Whether to use cached data
            
        Returns:
            AccountSummary object with account, balance, and permissions
        """
        await self.ensure_authenticated()
        
        try:
            endpoint_type = APIEndpointType.PAPER if self.is_paper_trading else APIEndpointType.TRADE
            
            response = await self._make_request(
                "GET",
                endpoint_type,
                "get_account",
                auth_required=True,
                use_cache=use_cache,
                cache_key=f"account_summary:{'paper' if self.is_paper_trading else 'live'}",
            )
            
            account_summary = AccountSummary.from_dict(response)
            
            # Update account ID
            self.account_id = account_summary.account.id
            
            logger.info(
                "Retrieved account summary",
                extra={
                    "account_id": account_summary.account.id,
                    "total_value": float(account_summary.balance.total_value),
                    "cash": float(account_summary.balance.cash),
                    "is_paper": self.is_paper_trading,
                }
            )
            
            return account_summary
            
        except Exception as e:
            logger.error(f"Failed to get account summary: {e}")
            raise
    
    async def get_positions(self, use_cache: bool = True) -> List[Position]:
        """
        Get current positions.
        
        Args:
            use_cache: Whether to use cached data
            
        Returns:
            List of Position objects
        """
        await self.ensure_authenticated()
        
        try:
            endpoint_type = APIEndpointType.PAPER if self.is_paper_trading else APIEndpointType.TRADE
            
            response = await self._make_request(
                "GET",
                endpoint_type,
                "get_positions",
                auth_required=True,
                use_cache=use_cache,
                cache_key=f"positions:{'paper' if self.is_paper_trading else 'live'}",
            )
            
            positions = []
            for position_data in response.get("data", []):
                position = Position.from_dict(position_data)
                positions.append(position)
            
            logger.info(
                f"Retrieved {len(positions)} positions",
                extra={
                    "position_count": len(positions),
                    "is_paper": self.is_paper_trading,
                }
            )
            
            return positions
            
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
            raise
    
    async def get_orders(
        self,
        status: Optional[OrderStatus] = None,
        use_cache: bool = True,
    ) -> List[Order]:
        """
        Get orders.
        
        Args:
            status: Filter by order status (None for all)
            use_cache: Whether to use cached data
            
        Returns:
            List of Order objects
        """
        await self.ensure_authenticated()
        
        try:
            endpoint_type = APIEndpointType.PAPER if self.is_paper_trading else APIEndpointType.TRADE
            
            params = {}
            if status:
                params["status"] = status.value
            
            cache_key = f"orders:{'paper' if self.is_paper_trading else 'live'}:{status.value if status else 'all'}"
            
            response = await self._make_request(
                "GET",
                endpoint_type,
                "get_orders",
                params=params,
                auth_required=True,
                use_cache=use_cache,
                cache_key=cache_key,
            )
            
            orders = []
            for order_data in response.get("data", []):
                order = Order.from_dict(order_data)
                orders.append(order)
            
            logger.info(
                f"Retrieved {len(orders)} orders",
                extra={
                    "order_count": len(orders),
                    "status_filter": status.value if status else "all",
                    "is_paper": self.is_paper_trading,
                }
            )
            
            return orders
            
        except Exception as e:
            logger.error(f"Failed to get orders: {e}")
            raise
    
    async def place_order(self, order_request: OrderRequest) -> Order:
        """
        Place a new order.
        
        Args:
            order_request: Order request details
            
        Returns:
            Order object with order details
            
        Raises:
            OrderValidationError: If order validation fails
        """
        await self.ensure_authenticated()
        
        # Validate order request
        if not order_request.validate():
            raise OrderValidationError(
                "Invalid order request",
                validation_type="order_structure",
                order_data=order_request.to_dict(),
            )
        
        try:
            endpoint_type = APIEndpointType.PAPER if self.is_paper_trading else APIEndpointType.TRADE
            
            response = await self._make_request(
                "POST",
                endpoint_type,
                "place_order",
                data=order_request.to_dict(),
                auth_required=True,
                use_cache=False,
            )
            
            # Create order object from response
            order_data = response.get("data", {})
            order = Order.from_dict(order_data)
            
            # If no order ID in response, use the one from request
            if not order.id and response.get("orderId"):
                order.id = response["orderId"]
            
            logger.order_event(
                "placed",
                order_id=order.id,
                symbol=order.symbol,
                side=order.side.value,
                quantity=order.quantity,
                price=float(order.price) if order.price else None,
                status=order.status.value,
            )
            
            return order
            
        except Exception as e:
            logger.order_event(
                "place_failed",
                symbol=order_request.symbol,
                side=order_request.side.value,
                quantity=order_request.quantity,
                price=float(order_request.price) if order_request.price else None,
                error=str(e),
            )
            raise
    
    async def cancel_order(self, order_id: str) -> bool:
        """
        Cancel an existing order.
        
        Args:
            order_id: Order ID to cancel
            
        Returns:
            True if cancellation successful
        """
        await self.ensure_authenticated()
        
        if not order_id:
            raise OrderValidationError(
                "Order ID is required",
                validation_type="order_id",
                order_data={"order_id": order_id},
            )
        
        try:
            endpoint_type = APIEndpointType.PAPER if self.is_paper_trading else APIEndpointType.TRADE
            
            data = {"orderId": order_id}
            
            response = await self._make_request(
                "POST",
                endpoint_type,
                "cancel_order",
                data=data,
                auth_required=True,
                use_cache=False,
            )
            
            success = response.get("success", False)
            
            if success:
                logger.order_event(
                    "cancelled",
                    order_id=order_id,
                    status="CANCELLED",
                )
            else:
                logger.order_event(
                    "cancel_failed",
                    order_id=order_id,
                    error=response.get("msg", "Unknown error"),
                )
            
            return success
            
        except Exception as e:
            logger.order_event(
                "cancel_failed",
                order_id=order_id,
                error=str(e),
            )
            raise
    
    async def modify_order(
        self,
        order_id: str,
        quantity: Optional[int] = None,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
    ) -> Order:
        """
        Modify an existing order.
        
        Args:
            order_id: Order ID to modify
            quantity: New quantity (optional)
            price: New price (optional)
            stop_price: New stop price (optional)
            
        Returns:
            Updated Order object
        """
        await self.ensure_authenticated()
        
        if not order_id:
            raise OrderValidationError(
                "Order ID is required",
                validation_type="order_id",
                order_data={"order_id": order_id},
            )
        
        if not any([quantity, price, stop_price]):
            raise OrderValidationError(
                "At least one modification parameter is required",
                validation_type="modification_params",
                order_data={"order_id": order_id},
            )
        
        try:
            endpoint_type = APIEndpointType.PAPER if self.is_paper_trading else APIEndpointType.TRADE
            
            data = {"orderId": order_id}
            if quantity is not None:
                data["quantity"] = quantity
            if price is not None:
                data["price"] = price
            if stop_price is not None:
                data["stopPrice"] = stop_price
            
            response = await self._make_request(
                "POST",
                endpoint_type,
                "modify_order",
                data=data,
                auth_required=True,
                use_cache=False,
            )
            
            order_data = response.get("data", {})
            order = Order.from_dict(order_data)
            
            logger.order_event(
                "modified",
                order_id=order.id,
                symbol=order.symbol,
                side=order.side.value,
                quantity=order.quantity,
                price=float(order.price) if order.price else None,
                status=order.status.value,
            )
            
            return order
            
        except Exception as e:
            logger.order_event(
                "modify_failed",
                order_id=order_id,
                error=str(e),
            )
            raise
    
    async def get_portfolio(self, use_cache: bool = True) -> Portfolio:
        """
        Get complete portfolio information.
        
        Args:
            use_cache: Whether to use cached data
            
        Returns:
            Portfolio object with account, balance, and positions
        """
        await self.ensure_authenticated()
        
        try:
            # Get account summary and positions
            account_summary = await self.get_account_summary(use_cache)
            positions = await self.get_positions(use_cache)
            
            portfolio = Portfolio(
                account=account_summary.account,
                balance=account_summary.balance,
                positions=positions,
            )
            
            logger.info(
                "Retrieved portfolio",
                extra={
                    "total_value": float(portfolio.balance.total_value),
                    "position_count": portfolio.position_count,
                    "cash_percentage": portfolio.balance.cash_percentage,
                    "day_pnl": float(portfolio.balance.day_pnl),
                }
            )
            
            return portfolio
            
        except Exception as e:
            logger.error(f"Failed to get portfolio: {e}")
            raise
    
    async def get_buying_power(self) -> Dict[str, float]:
        """
        Get current buying power.
        
        Returns:
            Dictionary with buying power information
        """
        await self.ensure_authenticated()
        
        try:
            endpoint_type = APIEndpointType.PAPER if self.is_paper_trading else APIEndpointType.TRADE
            
            response = await self._make_request(
                "GET",
                endpoint_type,
                "get_buying_power",
                auth_required=True,
                use_cache=True,
                cache_key=f"buying_power:{'paper' if self.is_paper_trading else 'live'}",
            )
            
            buying_power = {
                "cash": response.get("cash", 0.0),
                "buying_power": response.get("buyingPower", 0.0),
                "day_trading_buying_power": response.get("dayTradingBuyingPower", 0.0),
                "margin_buying_power": response.get("marginBuyingPower", 0.0),
            }
            
            logger.info(
                "Retrieved buying power",
                extra=buying_power
            )
            
            return buying_power
            
        except Exception as e:
            logger.error(f"Failed to get buying power: {e}")
            raise

    async def close_position(
        self,
        symbol: str,
        quantity: Optional[int] = None,
        order_type: str = "MARKET",
        limit_price: Optional[float] = None,
        dry_run: bool = False
    ) -> Order:
        """
        Close a position by placing a sell order.

        Args:
            symbol: Stock symbol to close
            quantity: Number of shares to close (None for full position)
            order_type: Order type (MARKET, LIMIT)
            limit_price: Limit price for LIMIT orders
            dry_run: Simulate order without placing

        Returns:
            Order object representing the close order

        Raises:
            OrderValidationError: If position doesn't exist or invalid parameters
        """
        await self.ensure_authenticated()

        try:
            # Get current position
            positions = await self.get_positions()
            position = next((p for p in positions if p.symbol == symbol), None)

            if not position:
                raise OrderValidationError(f"No position found for {symbol}")

            if position.quantity <= 0:
                raise OrderValidationError(f"No long position to close for {symbol}")

            # Determine quantity to close
            close_quantity = quantity if quantity is not None else abs(position.quantity)

            if close_quantity > abs(position.quantity):
                raise OrderValidationError(
                    f"Cannot close {close_quantity} shares, only {abs(position.quantity)} available"
                )

            # Create sell order to close position
            order_request = OrderRequest(
                symbol=symbol,
                side="SELL",
                quantity=close_quantity,
                order_type=order_type,
                limit_price=limit_price,
                time_in_force="DAY",
                extended_hours=False,
            )

            if dry_run:
                logger.info(
                    f"DRY RUN: Would close {close_quantity} shares of {symbol}",
                    extra={
                        "symbol": symbol,
                        "quantity": close_quantity,
                        "order_type": order_type,
                        "limit_price": limit_price,
                    }
                )

                # Return mock order for dry run
                return Order(
                    id=f"dry_run_{symbol}_{int(time.time())}",
                    symbol=symbol,
                    side="SELL",
                    quantity=close_quantity,
                    order_type=order_type,
                    status=OrderStatus.FILLED,
                    limit_price=limit_price,
                    filled_price=position.current_price,
                    filled_quantity=close_quantity,
                    created_at=int(time.time() * 1000),
                    updated_at=int(time.time() * 1000),
                )

            # Place actual close order
            order = await self.place_order(order_request)

            logger.info(
                f"Position close order placed for {symbol}",
                extra={
                    "order_id": order.id,
                    "symbol": symbol,
                    "quantity": close_quantity,
                    "order_type": order_type,
                }
            )

            return order

        except Exception as e:
            logger.error(f"Failed to close position for {symbol}: {e}")
            raise

    async def close_all_positions(
        self,
        exclude_symbols: Optional[List[str]] = None,
        order_type: str = "MARKET",
        dry_run: bool = False
    ) -> List[Order]:
        """
        Close all open positions.

        Args:
            exclude_symbols: Symbols to exclude from closing
            order_type: Order type for close orders
            dry_run: Simulate orders without placing

        Returns:
            List of Order objects for close orders
        """
        await self.ensure_authenticated()

        try:
            positions = await self.get_positions()
            exclude_symbols = exclude_symbols or []

            close_orders = []

            for position in positions:
                if position.symbol in exclude_symbols:
                    continue

                if position.quantity <= 0:
                    continue  # Skip short positions for now

                try:
                    order = await self.close_position(
                        symbol=position.symbol,
                        order_type=order_type,
                        dry_run=dry_run
                    )
                    close_orders.append(order)

                except Exception as e:
                    logger.error(f"Failed to close position {position.symbol}: {e}")
                    continue

            logger.info(
                f"Initiated close orders for {len(close_orders)} positions",
                extra={
                    "order_count": len(close_orders),
                    "symbols": [order.symbol for order in close_orders],
                    "dry_run": dry_run,
                }
            )

            return close_orders

        except Exception as e:
            logger.error(f"Failed to close all positions: {e}")
            raise

    async def get_order_history(
        self,
        symbol: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 100
    ) -> List[Order]:
        """
        Get order history with optional filtering.

        Args:
            symbol: Filter by symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            status: Filter by order status
            limit: Maximum number of orders to return

        Returns:
            List of Order objects
        """
        await self.ensure_authenticated()

        try:
            endpoint_type = APIEndpointType.PAPER if self.is_paper_trading else APIEndpointType.TRADE

            params = {"limit": limit}
            if symbol:
                params["symbol"] = symbol
            if start_date:
                params["start_date"] = start_date
            if end_date:
                params["end_date"] = end_date
            if status:
                params["status"] = status

            response = await self._make_request(
                "GET",
                endpoint_type,
                "get_order_history",
                params=params,
                auth_required=True,
                use_cache=False,
            )

            orders = [Order.from_dict(order_data) for order_data in response.get("orders", [])]

            logger.info(
                f"Retrieved {len(orders)} orders from history",
                extra={
                    "symbol": symbol,
                    "start_date": start_date,
                    "end_date": end_date,
                    "status": status,
                    "count": len(orders),
                }
            )

            return orders

        except Exception as e:
            logger.error(f"Failed to get order history: {e}")
            raise
