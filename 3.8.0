Requirement already satisfied: aiohttp in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (3.8.5)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp) (25.3.0)
Requirement already satisfied: charset-normalizer<4.0,>=2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp) (3.4.2)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp) (6.6.3)
Requirement already satisfied: async-timeout<5.0,>=4.0.0a3 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp) (4.0.3)
Requirement already satisfied: yarl<2.0,>=1.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp) (1.20.1)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp) (1.7.0)
Requirement already satisfied: aiosignal>=1.1.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp) (1.4.0)
Requirement already satisfied: idna>=2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from yarl<2.0,>=1.0->aiohttp) (3.10)
Requirement already satisfied: propcache>=0.2.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from yarl<2.0,>=1.0->aiohttp) (0.3.2)
Requirement already satisfied: typing-extensions>=4.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiosignal>=1.1.2->aiohttp) (4.14.1)
