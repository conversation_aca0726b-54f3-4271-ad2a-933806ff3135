# safe_trading_bot_yahoo.py
"""
SAFE Yahoo Finance Trading Bot with IP Protection
Includes rate limiting, caching, and anti-ban measures
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import random
import json
import os
from functools import wraps
import warnings
warnings.filterwarnings('ignore')

# Import for rotating user agents
from fake_useragent import UserAgent

# For caching
import pickle
from pathlib import Path

class IPSafetyManager:
    """Manages rate limiting and IP protection"""
    
    def __init__(self):
        self.last_request_time = {}
        self.request_count = {}
        self.cache_dir = Path("./cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        # Safety settings
        self.MIN_DELAY = 2  # Minimum 2 seconds between requests
        self.MAX_DELAY = 5  # Maximum 5 seconds
        self.MAX_REQUESTS_PER_MINUTE = 20  # Max 20 requests per minute
        self.MAX_REQUESTS_PER_HOUR = 200  # Max 200 requests per hour
        self.CACHE_EXPIRY_MINUTES = 5  # Cache data for 5 minutes
        
        # Track requests
        self.minute_requests = []
        self.hour_requests = []
        
    def safe_request(self, func):
        """Decorator to make requests safe"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check rate limits
            self._check_rate_limits()
            
            # Add random delay
            delay = random.uniform(self.MIN_DELAY, self.MAX_DELAY)
            time.sleep(delay)
            
            # Track request
            self._track_request()
            
            # Execute request
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                print(f"⚠️ Request failed: {e}")
                # Exponential backoff on failure
                time.sleep(delay * 2)
                raise
                
        return wrapper
    
    def _check_rate_limits(self):
        """Check if we're within rate limits"""
        current_time = time.time()
        
        # Clean old requests
        self.minute_requests = [t for t in self.minute_requests if current_time - t < 60]
        self.hour_requests = [t for t in self.hour_requests if current_time - t < 3600]
        
        # Check minute limit
        if len(self.minute_requests) >= self.MAX_REQUESTS_PER_MINUTE:
            wait_time = 60 - (current_time - self.minute_requests[0])
            print(f"⏸️ Rate limit reached. Waiting {wait_time:.1f} seconds...")
            time.sleep(wait_time + 1)
        
        # Check hour limit
        if len(self.hour_requests) >= self.MAX_REQUESTS_PER_HOUR:
            wait_time = 3600 - (current_time - self.hour_requests[0])
            print(f"⏸️ Hourly limit reached. Waiting {wait_time/60:.1f} minutes...")
            time.sleep(wait_time + 1)
    
    def _track_request(self):
        """Track request for rate limiting"""
        current_time = time.time()
        self.minute_requests.append(current_time)
        self.hour_requests.append(current_time)
    
    def get_cached_data(self, key):
        """Get data from cache if available"""
        cache_file = self.cache_dir / f"{key}.pkl"
        
        if cache_file.exists():
            # Check if cache is still valid
            cache_age = time.time() - cache_file.stat().st_mtime
            if cache_age < self.CACHE_EXPIRY_MINUTES * 60:
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
        
        return None
    
    def save_to_cache(self, key, data):
        """Save data to cache"""
        cache_file = self.cache_dir / f"{key}.pkl"
        with open(cache_file, 'wb') as f:
            pickle.dump(data, f)

class SafeYahooTradingBot:
    """Trading bot with IP protection"""
    
    def __init__(self, symbols=['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']):
        self.symbols = symbols
        self.data = {}
        self.models = {}
        self.safety_manager = IPSafetyManager()
        
        # Configure yfinance for safety
        self._configure_yfinance()
        
    def _configure_yfinance(self):
        """Configure yfinance with safety settings"""
        # Use random user agent
        ua = UserAgent()
        
        # Set headers to look like a real browser
        headers = {
            'User-Agent': ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        # Note: yfinance doesn't directly support custom headers,
        # but we can use a session
        import requests
        session = requests.Session()
        session.headers.update(headers)
    
    @property
    def safe_request(self):
        """Get the safe request decorator"""
        return self.safety_manager.safe_request
    
    def get_realtime_data(self, use_cache=True):
        """Get real-time data with caching and rate limiting"""
        print("\n📊 Getting Real-Time Data SAFELY...")
        print("="*60)
        print("🛡️ IP Protection: ON")
        print(f"⏱️ Rate Limiting: {self.safety_manager.MAX_REQUESTS_PER_MINUTE} req/min")
        print(f"💾 Caching: {self.safety_manager.CACHE_EXPIRY_MINUTES} min expiry")
        print("="*60)
        
        for i, symbol in enumerate(self.symbols):
            try:
                # Check cache first
                cache_key = f"realtime_{symbol}_{datetime.now().strftime('%Y%m%d_%H')}"
                cached_data = self.safety_manager.get_cached_data(cache_key) if use_cache else None
                
                if cached_data:
                    print(f"\n{symbol}: 💾 Using cached data")
                    self.data[symbol] = cached_data
                else:
                    print(f"\n{symbol}: 🌐 Fetching fresh data...")
                    
                    # Make safe request
                    @self.safe_request
                    def fetch_data():
                        stock = yf.Ticker(symbol)
                        return stock.info
                    
                    info = fetch_data()
                    
                    # Process data
                    current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
                    prev_close = info.get('previousClose', 0)
                    change = current_price - prev_close if current_price and prev_close else 0
                    change_percent = (change / prev_close * 100) if prev_close > 0 else 0
                    
                    data = {
                        'price': current_price,
                        'change': change,
                        'change_percent': change_percent,
                        'volume': info.get('volume', 0),
                        'market_cap': info.get('marketCap', 0),
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    self.data[symbol] = data
                    
                    # Save to cache
                    self.safety_manager.save_to_cache(cache_key, data)
                    
                    print(f"  💰 Price: ${current_price:.2f}")
                    print(f"  📈 Change: ${change:.2f} ({change_percent:+.2f}%)")
                
                # Progress indicator
                progress = (i + 1) / len(self.symbols) * 100
                print(f"  ⏳ Progress: {progress:.0f}%")
                
            except Exception as e:
                print(f"\n❌ Error with {symbol}: {e}")
                print("  🔄 Will retry later with exponential backoff")
    
    def get_historical_data_safe(self, period='1mo', interval='1d'):
        """Get historical data with safety measures"""
        print(f"\n📈 Downloading Historical Data SAFELY ({period})...")
        print("🛡️ Using batch downloads to minimize requests")
        
        historical_data = {}
        
        # Download in batches to reduce requests
        @self.safe_request
        def download_batch(symbols_batch):
            # yfinance supports downloading multiple symbols at once
            data = yf.download(
                symbols_batch,
                period=period,
                interval=interval,
                group_by='ticker',
                auto_adjust=True,
                threads=False  # Don't use multiple threads
            )
            return data
        
        # Process in small batches
        batch_size = 3
        for i in range(0, len(self.symbols), batch_size):
            batch = self.symbols[i:i+batch_size]
            print(f"\n📦 Downloading batch: {batch}")
            
            try:
                # Check cache
                cache_key = f"historical_{'_'.join(batch)}_{period}_{datetime.now().strftime('%Y%m%d')}"
                cached_data = self.safety_manager.get_cached_data(cache_key)
                
                if cached_data is not None:
                    print("  💾 Using cached historical data")
                    data = cached_data
                else:
                    data = download_batch(batch)
                    self.safety_manager.save_to_cache(cache_key, data)
                
                # Process each symbol
                for symbol in batch:
                    if len(batch) == 1:
                        hist = data
                    else:
                        hist = data[symbol] if symbol in data.columns.levels[0] else pd.DataFrame()
                    
                    if not hist.empty:
                        # Add technical indicators
                        hist['MA_20'] = hist['Close'].rolling(window=20).mean()
                        hist['MA_50'] = hist['Close'].rolling(window=50).mean()
                        hist['RSI'] = self.calculate_rsi(hist['Close'])
                        
                        historical_data[symbol] = hist
                        print(f"  ✅ {symbol}: {len(hist)} days downloaded")
                    else:
                        print(f"  ⚠️ {symbol}: No data available")
                        
            except Exception as e:
                print(f"  ❌ Batch failed: {e}")
                # Try individual symbols on batch failure
                for symbol in batch:
                    try:
                        @self.safe_request
                        def download_single():
                            return yf.download(symbol, period=period, interval=interval)
                        
                        hist = download_single()
                        if not hist.empty:
                            historical_data[symbol] = hist
                            print(f"  ✅ {symbol}: Recovered individually")
                    except:
                        print(f"  ❌ {symbol}: Failed to recover")
        
        return historical_data
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def run_safe_analysis(self):
        """Run complete analysis with all safety measures"""
        print("\n🚀 Starting SAFE Trading Bot Analysis")
        print("="*60)
        
        # 1. System check
        print("\n🔍 System Safety Check:")
        print(f"  ✅ Rate Limiting: Active")
        print(f"  ✅ Caching: Enabled ({self.safety_manager.cache_dir})")
        print(f"  ✅ Random Delays: {self.safety_manager.MIN_DELAY}-{self.safety_manager.MAX_DELAY}s")
        print(f"  ✅ Request Limits: {self.safety_manager.MAX_REQUESTS_PER_MINUTE}/min")
        
        # 2. Get data safely
        self.get_realtime_data(use_cache=True)
        
        # 3. Get historical data
        historical = self.get_historical_data_safe(period='1mo')
        
        # 4. Analysis summary
        print("\n📊 Analysis Summary:")
        print("="*60)
        
        for symbol, data in self.data.items():
            if data.get('price'):
                print(f"\n{symbol}:")
                print(f"  Current Price: ${data['price']:.2f}")
                print(f"  Change: {data['change_percent']:+.2f}%")
                
                # Simple signal based on RSI
                if symbol in historical and not historical[symbol].empty:
                    latest_rsi = historical[symbol]['RSI'].iloc[-1]
                    if pd.notna(latest_rsi):
                        if latest_rsi < 30:
                            print(f"  🟢 Signal: OVERSOLD (RSI: {latest_rsi:.1f})")
                        elif latest_rsi > 70:
                            print(f"  🔴 Signal: OVERBOUGHT (RSI: {latest_rsi:.1f})")
                        else:
                            print(f"  🟡 Signal: NEUTRAL (RSI: {latest_rsi:.1f})")
        
        # 5. Safety report
        print("\n🛡️ Safety Report:")
        print(f"  Total Requests: {len(self.safety_manager.hour_requests)}")
        print(f"  Requests/Hour: {len(self.safety_manager.hour_requests)}")
        print(f"  Cache Hit Rate: {self._calculate_cache_hit_rate():.1f}%")
        print("  IP Status: ✅ SAFE")

    def _calculate_cache_hit_rate(self):
        """Calculate cache hit rate"""
        # Simple estimation based on cache files
        cache_files = list(self.safety_manager.cache_dir.glob("*.pkl"))
        if cache_files:
            return min(len(cache_files) / len(self.symbols) * 20, 100)
        return 0

def main():
    """Main function with all safety measures"""
    print("🛡️ SAFE Yahoo Finance Trading Bot")
    print("="*60)
    print("This bot includes:")
    print("  ✅ Rate limiting (max 20 requests/minute)")
    print("  ✅ Random delays between requests")
    print("  ✅ Caching to reduce API calls")
    print("  ✅ Exponential backoff on errors")
    print("  ✅ IP protection measures")
    print("="*60)
    
    # Check dependencies
    try:
        import fake_useragent
    except ImportError:
        print("\n📦 Installing safety dependencies...")
        import subprocess
        subprocess.check_call(['pip', 'install', 'fake-useragent'])
    
    # Initialize bot with safety
    bot = SafeYahooTradingBot(symbols=['AAPL', 'MSFT', 'GOOGL'])  # Start with fewer symbols
    
    # Run safe analysis
    bot.run_safe_analysis()
    
    print("\n✅ Analysis complete - Your IP is SAFE!")
    print("💡 Tips for continued safety:")
    print("  - Don't run more than once per 5 minutes")
    print("  - Use cached data when possible")
    print("  - Limit symbols to what you need")
    print("  - Consider using a VPN for extra protection")

if __name__ == "__main__":
    main()