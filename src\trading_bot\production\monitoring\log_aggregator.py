"""
Centralized log aggregation system for AI Trading Bot production deployment.

This module provides comprehensive log collection, processing, and analysis
for production monitoring and debugging:

Log Sources:
- Application logs (trading, ML, risk management)
- System logs (performance, errors, warnings)
- API logs (requests, responses, rate limits)
- Database logs (queries, performance, errors)
- Security logs (authentication, authorization)

Features:
- Real-time log streaming
- Log parsing and structured data extraction
- Log correlation and analysis
- Alert generation based on log patterns
- Log retention and archival
- Performance metrics extraction
"""

import asyncio
import json
import logging
import re
import gzip
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Pattern
from enum import Enum
import aiofiles
from pathlib import Path

from ....core.config import Config
from ....utils.logger import get_logger

logger = get_logger(__name__)


class LogLevel(Enum):
    """Log severity levels."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class LogSource(Enum):
    """Log source types."""
    APPLICATION = "application"
    SYSTEM = "system"
    API = "api"
    DATABASE = "database"
    SECURITY = "security"
    TRADING = "trading"
    ML = "ml"
    RISK = "risk"


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: datetime
    level: LogLevel
    source: LogSource
    component: str
    message: str
    
    # Additional structured data
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    trade_id: Optional[str] = None
    
    # Performance metrics
    duration_ms: Optional[float] = None
    memory_usage: Optional[int] = None
    cpu_usage: Optional[float] = None
    
    # Error details
    error_code: Optional[str] = None
    stack_trace: Optional[str] = None
    
    # Raw log data
    raw_message: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LogPattern:
    """Log pattern for parsing and alerting."""
    name: str
    pattern: Pattern[str]
    source: LogSource
    level: LogLevel
    alert_threshold: int = 10  # Alert after N occurrences
    time_window: int = 300  # Time window in seconds
    description: str = ""


@dataclass
class LogMetrics:
    """Log aggregation metrics."""
    total_logs: int = 0
    logs_by_level: Dict[LogLevel, int] = field(default_factory=dict)
    logs_by_source: Dict[LogSource, int] = field(default_factory=dict)
    error_rate: float = 0.0
    warning_rate: float = 0.0
    
    # Performance metrics extracted from logs
    avg_response_time: float = 0.0
    p95_response_time: float = 0.0
    error_patterns: Dict[str, int] = field(default_factory=dict)
    
    # Time window
    window_start: datetime = field(default_factory=datetime.utcnow)
    window_duration: timedelta = field(default_factory=lambda: timedelta(hours=1))


class LogAggregator:
    """
    Centralized log aggregation and analysis system.
    
    Collects logs from multiple sources, parses them into structured format,
    performs real-time analysis, and generates alerts based on patterns.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.log_entries: List[LogEntry] = []
        self.metrics = LogMetrics()
        self.is_running = False
        
        # Log file paths
        self.log_paths = {
            LogSource.APPLICATION: "logs/application.log",
            LogSource.TRADING: "logs/trading.log",
            LogSource.ML: "logs/ml.log",
            LogSource.RISK: "logs/risk.log",
            LogSource.API: "logs/api.log",
            LogSource.DATABASE: "logs/database.log",
            LogSource.SECURITY: "logs/security.log",
            LogSource.SYSTEM: "logs/system.log"
        }
        
        # Log parsing patterns
        self.patterns = self._initialize_patterns()
        
        # Alert callbacks
        self.alert_callbacks: List[Callable] = []
        
        # Log retention settings
        self.max_memory_entries = 10000
        self.log_retention_days = 30
        self.archive_compression = True
    
    def _initialize_patterns(self) -> List[LogPattern]:
        """Initialize log parsing patterns."""
        patterns = [
            # Trading patterns
            LogPattern(
                name="order_execution_error",
                pattern=re.compile(r"Order execution failed.*order_id:(\w+).*error:(.+)"),
                source=LogSource.TRADING,
                level=LogLevel.ERROR,
                alert_threshold=5,
                description="Order execution failures"
            ),
            LogPattern(
                name="high_latency_order",
                pattern=re.compile(r"Order latency high.*duration:(\d+)ms.*order_id:(\w+)"),
                source=LogSource.TRADING,
                level=LogLevel.WARNING,
                alert_threshold=10,
                description="High latency order executions"
            ),
            
            # ML patterns
            LogPattern(
                name="ml_prediction_error",
                pattern=re.compile(r"ML prediction failed.*model:(\w+).*error:(.+)"),
                source=LogSource.ML,
                level=LogLevel.ERROR,
                alert_threshold=3,
                description="ML prediction failures"
            ),
            LogPattern(
                name="model_accuracy_low",
                pattern=re.compile(r"Model accuracy below threshold.*accuracy:([\d.]+)"),
                source=LogSource.ML,
                level=LogLevel.WARNING,
                alert_threshold=1,
                description="Low model accuracy"
            ),
            
            # Risk patterns
            LogPattern(
                name="risk_limit_breach",
                pattern=re.compile(r"Risk limit breached.*limit:(\w+).*value:([\d.]+)"),
                source=LogSource.RISK,
                level=LogLevel.CRITICAL,
                alert_threshold=1,
                description="Risk limit breaches"
            ),
            
            # API patterns
            LogPattern(
                name="api_rate_limit",
                pattern=re.compile(r"API rate limit exceeded.*endpoint:(\S+)"),
                source=LogSource.API,
                level=LogLevel.WARNING,
                alert_threshold=5,
                description="API rate limit exceeded"
            ),
            LogPattern(
                name="api_timeout",
                pattern=re.compile(r"API timeout.*endpoint:(\S+).*duration:(\d+)ms"),
                source=LogSource.API,
                level=LogLevel.ERROR,
                alert_threshold=3,
                description="API timeouts"
            ),
            
            # Database patterns
            LogPattern(
                name="database_connection_error",
                pattern=re.compile(r"Database connection failed.*error:(.+)"),
                source=LogSource.DATABASE,
                level=LogLevel.ERROR,
                alert_threshold=3,
                description="Database connection failures"
            ),
            LogPattern(
                name="slow_query",
                pattern=re.compile(r"Slow query detected.*duration:(\d+)ms.*query:(.+)"),
                source=LogSource.DATABASE,
                level=LogLevel.WARNING,
                alert_threshold=10,
                description="Slow database queries"
            ),
            
            # Security patterns
            LogPattern(
                name="authentication_failure",
                pattern=re.compile(r"Authentication failed.*user:(\w+).*ip:(\S+)"),
                source=LogSource.SECURITY,
                level=LogLevel.WARNING,
                alert_threshold=5,
                description="Authentication failures"
            ),
            LogPattern(
                name="suspicious_activity",
                pattern=re.compile(r"Suspicious activity detected.*user:(\w+).*activity:(.+)"),
                source=LogSource.SECURITY,
                level=LogLevel.CRITICAL,
                alert_threshold=1,
                description="Suspicious security activity"
            )
        ]
        
        return patterns
    
    async def start(self):
        """Start log aggregation."""
        if self.is_running:
            logger.warning("Log aggregator already running")
            return
        
        self.is_running = True
        logger.info("Starting log aggregator...")
        
        # Start log monitoring tasks
        tasks = []
        for source, path in self.log_paths.items():
            task = asyncio.create_task(self._monitor_log_file(source, path))
            tasks.append(task)
        
        # Start metrics calculation task
        tasks.append(asyncio.create_task(self._calculate_metrics_loop()))
        
        # Start log cleanup task
        tasks.append(asyncio.create_task(self._cleanup_loop()))
        
        # Wait for all tasks
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Log aggregator error: {e}")
        finally:
            self.is_running = False
    
    async def stop(self):
        """Stop log aggregation."""
        self.is_running = False
        logger.info("Log aggregator stopped")
    
    def add_alert_callback(self, callback: Callable):
        """Add alert callback function."""
        self.alert_callbacks.append(callback)
    
    async def get_logs(self, 
                      source: Optional[LogSource] = None,
                      level: Optional[LogLevel] = None,
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None,
                      limit: int = 1000) -> List[LogEntry]:
        """Get filtered log entries."""
        filtered_logs = self.log_entries
        
        # Apply filters
        if source:
            filtered_logs = [log for log in filtered_logs if log.source == source]
        
        if level:
            filtered_logs = [log for log in filtered_logs if log.level == level]
        
        if start_time:
            filtered_logs = [log for log in filtered_logs if log.timestamp >= start_time]
        
        if end_time:
            filtered_logs = [log for log in filtered_logs if log.timestamp <= end_time]
        
        # Sort by timestamp (newest first) and limit
        filtered_logs.sort(key=lambda x: x.timestamp, reverse=True)
        return filtered_logs[:limit]
    
    def get_metrics(self) -> LogMetrics:
        """Get current log metrics."""
        return self.metrics
    
    async def search_logs(self, query: str, limit: int = 100) -> List[LogEntry]:
        """Search logs by text query."""
        matching_logs = []
        
        for log in self.log_entries:
            if (query.lower() in log.message.lower() or 
                query.lower() in log.raw_message.lower()):
                matching_logs.append(log)
                
                if len(matching_logs) >= limit:
                    break
        
        return matching_logs

    async def _monitor_log_file(self, source: LogSource, file_path: str):
        """Monitor a log file for new entries."""
        path = Path(file_path)

        # Create directory if it doesn't exist
        path.parent.mkdir(parents=True, exist_ok=True)

        # Create file if it doesn't exist
        if not path.exists():
            path.touch()

        # Start monitoring
        last_position = 0

        while self.is_running:
            try:
                if path.exists() and path.stat().st_size > last_position:
                    async with aiofiles.open(path, 'r') as f:
                        await f.seek(last_position)
                        new_lines = await f.readlines()
                        last_position = await f.tell()

                    # Process new log lines
                    for line in new_lines:
                        line = line.strip()
                        if line:
                            await self._process_log_line(source, line)

                await asyncio.sleep(1)  # Check every second

            except Exception as e:
                logger.error(f"Error monitoring log file {file_path}: {e}")
                await asyncio.sleep(5)

    async def _process_log_line(self, source: LogSource, line: str):
        """Process a single log line."""
        try:
            # Parse log entry
            log_entry = await self._parse_log_line(source, line)

            if log_entry:
                # Add to memory store
                self.log_entries.append(log_entry)

                # Maintain memory limit
                if len(self.log_entries) > self.max_memory_entries:
                    self.log_entries = self.log_entries[-self.max_memory_entries:]

                # Check for alert patterns
                await self._check_alert_patterns(log_entry)

                # Update metrics
                self._update_metrics(log_entry)

        except Exception as e:
            logger.error(f"Error processing log line: {e}")

    async def _parse_log_line(self, source: LogSource, line: str) -> Optional[LogEntry]:
        """Parse a log line into structured format."""
        # Try to parse as JSON first (structured logging)
        try:
            data = json.loads(line)
            return LogEntry(
                timestamp=datetime.fromisoformat(data.get('timestamp', datetime.utcnow().isoformat())),
                level=LogLevel(data.get('level', 'info').lower()),
                source=source,
                component=data.get('component', 'unknown'),
                message=data.get('message', ''),
                user_id=data.get('user_id'),
                session_id=data.get('session_id'),
                request_id=data.get('request_id'),
                trade_id=data.get('trade_id'),
                duration_ms=data.get('duration_ms'),
                memory_usage=data.get('memory_usage'),
                cpu_usage=data.get('cpu_usage'),
                error_code=data.get('error_code'),
                stack_trace=data.get('stack_trace'),
                raw_message=line,
                metadata=data.get('metadata', {})
            )
        except (json.JSONDecodeError, ValueError):
            pass

        # Parse standard log format: TIMESTAMP LEVEL COMPONENT MESSAGE
        standard_pattern = re.compile(
            r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}[,\.]\d{3})\s+'
            r'(\w+)\s+'
            r'(\w+)\s+'
            r'(.+)'
        )

        match = standard_pattern.match(line)
        if match:
            timestamp_str, level_str, component, message = match.groups()

            try:
                # Parse timestamp
                timestamp = datetime.strptime(timestamp_str.replace(',', '.'), '%Y-%m-%d %H:%M:%S.%f')

                # Parse level
                level = LogLevel(level_str.lower())

                return LogEntry(
                    timestamp=timestamp,
                    level=level,
                    source=source,
                    component=component,
                    message=message,
                    raw_message=line
                )
            except (ValueError, KeyError):
                pass

        # Fallback: create basic log entry
        return LogEntry(
            timestamp=datetime.utcnow(),
            level=LogLevel.INFO,
            source=source,
            component='unknown',
            message=line,
            raw_message=line
        )

    async def _check_alert_patterns(self, log_entry: LogEntry):
        """Check log entry against alert patterns."""
        for pattern in self.patterns:
            if (pattern.source == log_entry.source and
                pattern.level == log_entry.level and
                pattern.pattern.search(log_entry.raw_message)):

                # Count occurrences in time window
                window_start = datetime.utcnow() - timedelta(seconds=pattern.time_window)
                recent_matches = [
                    log for log in self.log_entries
                    if (log.timestamp >= window_start and
                        log.source == pattern.source and
                        log.level == pattern.level and
                        pattern.pattern.search(log.raw_message))
                ]

                if len(recent_matches) >= pattern.alert_threshold:
                    await self._trigger_alert(pattern, recent_matches)

    async def _trigger_alert(self, pattern: LogPattern, matching_logs: List[LogEntry]):
        """Trigger alert for pattern match."""
        alert_data = {
            'pattern_name': pattern.name,
            'description': pattern.description,
            'severity': pattern.level.value,
            'count': len(matching_logs),
            'time_window': pattern.time_window,
            'recent_logs': [log.message for log in matching_logs[-5:]]  # Last 5 messages
        }

        logger.warning(f"Log pattern alert: {pattern.name} - {len(matching_logs)} occurrences")

        # Call alert callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(alert_data)
            except Exception as e:
                logger.error(f"Alert callback error: {e}")

    def _update_metrics(self, log_entry: LogEntry):
        """Update log metrics."""
        self.metrics.total_logs += 1

        # Update by level
        if log_entry.level not in self.metrics.logs_by_level:
            self.metrics.logs_by_level[log_entry.level] = 0
        self.metrics.logs_by_level[log_entry.level] += 1

        # Update by source
        if log_entry.source not in self.metrics.logs_by_source:
            self.metrics.logs_by_source[log_entry.source] = 0
        self.metrics.logs_by_source[log_entry.source] += 1

        # Update error rates
        error_count = self.metrics.logs_by_level.get(LogLevel.ERROR, 0)
        warning_count = self.metrics.logs_by_level.get(LogLevel.WARNING, 0)

        if self.metrics.total_logs > 0:
            self.metrics.error_rate = error_count / self.metrics.total_logs
            self.metrics.warning_rate = warning_count / self.metrics.total_logs

    async def _calculate_metrics_loop(self):
        """Calculate metrics periodically."""
        while self.is_running:
            try:
                await self._calculate_performance_metrics()
                await asyncio.sleep(60)  # Update every minute
            except Exception as e:
                logger.error(f"Metrics calculation error: {e}")
                await asyncio.sleep(60)

    async def _calculate_performance_metrics(self):
        """Calculate performance metrics from logs."""
        # Get recent logs with duration data
        recent_logs = [
            log for log in self.log_entries
            if (log.duration_ms is not None and
                log.timestamp >= datetime.utcnow() - timedelta(hours=1))
        ]

        if recent_logs:
            durations = [log.duration_ms for log in recent_logs]
            self.metrics.avg_response_time = sum(durations) / len(durations)
            self.metrics.p95_response_time = sorted(durations)[int(len(durations) * 0.95)]

    async def _cleanup_loop(self):
        """Clean up old logs periodically."""
        while self.is_running:
            try:
                await self._cleanup_old_logs()
                await asyncio.sleep(3600)  # Cleanup every hour
            except Exception as e:
                logger.error(f"Log cleanup error: {e}")
                await asyncio.sleep(3600)

    async def _cleanup_old_logs(self):
        """Clean up old log files."""
        cutoff_date = datetime.utcnow() - timedelta(days=self.log_retention_days)

        for source, path in self.log_paths.items():
            log_dir = Path(path).parent

            if log_dir.exists():
                # Archive old log files
                for log_file in log_dir.glob("*.log.*"):
                    try:
                        file_date = datetime.fromtimestamp(log_file.stat().st_mtime)
                        if file_date < cutoff_date:
                            if self.archive_compression:
                                await self._compress_log_file(log_file)
                            else:
                                log_file.unlink()
                    except Exception as e:
                        logger.error(f"Error cleaning up log file {log_file}: {e}")

    async def _compress_log_file(self, log_file: Path):
        """Compress and archive log file."""
        compressed_file = log_file.with_suffix(log_file.suffix + '.gz')

        try:
            async with aiofiles.open(log_file, 'rb') as f_in:
                content = await f_in.read()

            with gzip.open(compressed_file, 'wb') as f_out:
                f_out.write(content)

            log_file.unlink()  # Remove original file
            logger.info(f"Compressed log file: {log_file} -> {compressed_file}")

        except Exception as e:
            logger.error(f"Error compressing log file {log_file}: {e}")
