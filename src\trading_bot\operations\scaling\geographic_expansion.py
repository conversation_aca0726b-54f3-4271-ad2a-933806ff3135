"""
Geographic Expansion System

Manages expansion to new markets and geographic regions,
including regulatory compliance and infrastructure deployment.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import numpy as np

from ...core.logger import get_logger

logger = get_logger(__name__)

class MarketRegion(Enum):
    """Geographic market regions"""
    NORTH_AMERICA = "north_america"
    EUROPE = "europe"
    ASIA_PACIFIC = "asia_pacific"
    LATIN_AMERICA = "latin_america"
    MIDDLE_EAST = "middle_east"
    AFRICA = "africa"

class ExpansionStage(Enum):
    """Stages of geographic expansion"""
    RESEARCH = "research"
    REGULATORY_APPROVAL = "regulatory_approval"
    INFRASTRUCTURE_SETUP = "infrastructure_setup"
    PAPER_TRADING = "paper_trading"
    LIMITED_LAUNCH = "limited_launch"
    FULL_DEPLOYMENT = "full_deployment"

@dataclass
class MarketInfo:
    """Information about a target market"""
    region: MarketRegion
    country: str
    market_code: str
    timezone: str
    trading_hours: Tuple[int, int]  # (start_hour, end_hour)
    currency: str
    market_cap: float  # USD billions
    volatility: float
    regulatory_complexity: int  # 1-10 scale
    infrastructure_cost: float  # USD thousands
    potential_volume: float  # Daily volume estimate

@dataclass
class ExpansionPlan:
    """Plan for expanding to a new market"""
    market_info: MarketInfo
    current_stage: ExpansionStage
    target_launch_date: datetime
    allocated_capital: float
    regulatory_requirements: List[str]
    infrastructure_requirements: List[str]
    risk_assessment: Dict[str, float]
    expected_roi: float
    stage_completion_dates: Dict[ExpansionStage, Optional[datetime]]

@dataclass
class ExpansionMetrics:
    """Metrics for tracking expansion progress"""
    market: str
    stage: ExpansionStage
    progress_percentage: float
    capital_deployed: float
    regulatory_approvals: int
    infrastructure_readiness: float
    risk_score: float
    projected_timeline: timedelta
    actual_timeline: Optional[timedelta]

class GeographicExpansion:
    """Manages expansion to new markets and geographic regions"""
    
    def __init__(self):
        self.target_markets = self._initialize_target_markets()
        self.expansion_plans: Dict[str, ExpansionPlan] = {}
        self.active_markets: Dict[str, MarketInfo] = {}
        self.expansion_history: List[Dict] = []
        
        # Current market (starting point)
        self.home_market = MarketInfo(
            region=MarketRegion.NORTH_AMERICA,
            country="United States",
            market_code="US",
            timezone="America/New_York",
            trading_hours=(9, 16),  # 9 AM - 4 PM EST
            currency="USD",
            market_cap=50000.0,  # $50T
            volatility=0.15,
            regulatory_complexity=6,
            infrastructure_cost=0.0,  # Already established
            potential_volume=1000000.0  # $1M daily
        )
        
        self.active_markets["US"] = self.home_market
        
    def _initialize_target_markets(self) -> Dict[str, MarketInfo]:
        """Initialize potential target markets"""
        return {
            "CA": MarketInfo(
                region=MarketRegion.NORTH_AMERICA,
                country="Canada",
                market_code="CA",
                timezone="America/Toronto",
                trading_hours=(9, 16),
                currency="CAD",
                market_cap=3000.0,
                volatility=0.18,
                regulatory_complexity=5,
                infrastructure_cost=50.0,
                potential_volume=100000.0
            ),
            "GB": MarketInfo(
                region=MarketRegion.EUROPE,
                country="United Kingdom",
                market_code="GB",
                timezone="Europe/London",
                trading_hours=(8, 16),
                currency="GBP",
                market_cap=4000.0,
                volatility=0.16,
                regulatory_complexity=7,
                infrastructure_cost=100.0,
                potential_volume=200000.0
            ),
            "DE": MarketInfo(
                region=MarketRegion.EUROPE,
                country="Germany",
                market_code="DE",
                timezone="Europe/Berlin",
                trading_hours=(9, 17),
                currency="EUR",
                market_cap=2500.0,
                volatility=0.17,
                regulatory_complexity=8,
                infrastructure_cost=120.0,
                potential_volume=150000.0
            ),
            "JP": MarketInfo(
                region=MarketRegion.ASIA_PACIFIC,
                country="Japan",
                market_code="JP",
                timezone="Asia/Tokyo",
                trading_hours=(9, 15),
                currency="JPY",
                market_cap=6000.0,
                volatility=0.19,
                regulatory_complexity=9,
                infrastructure_cost=200.0,
                potential_volume=300000.0
            ),
            "AU": MarketInfo(
                region=MarketRegion.ASIA_PACIFIC,
                country="Australia",
                market_code="AU",
                timezone="Australia/Sydney",
                trading_hours=(10, 16),
                currency="AUD",
                market_cap=1800.0,
                volatility=0.20,
                regulatory_complexity=6,
                infrastructure_cost=80.0,
                potential_volume=80000.0
            ),
            "SG": MarketInfo(
                region=MarketRegion.ASIA_PACIFIC,
                country="Singapore",
                market_code="SG",
                timezone="Asia/Singapore",
                trading_hours=(9, 17),
                currency="SGD",
                market_cap=800.0,
                volatility=0.22,
                regulatory_complexity=5,
                infrastructure_cost=60.0,
                potential_volume=50000.0
            )
        }
    
    async def evaluate_expansion_opportunities(self) -> List[Dict[str, Any]]:
        """Evaluate and rank expansion opportunities"""
        logger.info("Evaluating geographic expansion opportunities")
        
        opportunities = []
        
        for market_code, market_info in self.target_markets.items():
            if market_code not in self.active_markets:
                score = await self._calculate_expansion_score(market_info)
                
                opportunities.append({
                    'market_code': market_code,
                    'country': market_info.country,
                    'region': market_info.region.value,
                    'expansion_score': score['total_score'],
                    'market_potential': score['market_potential'],
                    'regulatory_risk': score['regulatory_risk'],
                    'infrastructure_cost': market_info.infrastructure_cost,
                    'expected_roi': score['expected_roi'],
                    'time_to_launch': score['time_to_launch'],
                    'recommendation': score['recommendation']
                })
        
        # Sort by expansion score
        opportunities.sort(key=lambda x: x['expansion_score'], reverse=True)
        
        return opportunities
    
    async def create_expansion_plan(self, market_code: str) -> Optional[ExpansionPlan]:
        """Create detailed expansion plan for a market"""
        
        if market_code not in self.target_markets:
            logger.error(f"Unknown market code: {market_code}")
            return None
        
        if market_code in self.expansion_plans:
            logger.warning(f"Expansion plan already exists for {market_code}")
            return self.expansion_plans[market_code]
        
        market_info = self.target_markets[market_code]
        
        # Calculate timeline and requirements
        regulatory_requirements = await self._get_regulatory_requirements(market_info)
        infrastructure_requirements = await self._get_infrastructure_requirements(market_info)
        risk_assessment = await self._assess_expansion_risks(market_info)
        
        # Estimate timeline
        base_timeline = timedelta(days=180)  # 6 months base
        complexity_factor = market_info.regulatory_complexity / 5.0
        timeline_adjustment = timedelta(days=int(60 * complexity_factor))
        target_launch_date = datetime.now() + base_timeline + timeline_adjustment
        
        # Calculate capital allocation
        base_capital = 100000.0  # $100k base
        market_factor = market_info.market_cap / 1000.0  # Scale by market size
        allocated_capital = base_capital + (market_factor * 1000)
        
        # Expected ROI
        expected_roi = await self._calculate_expected_roi(market_info)
        
        plan = ExpansionPlan(
            market_info=market_info,
            current_stage=ExpansionStage.RESEARCH,
            target_launch_date=target_launch_date,
            allocated_capital=allocated_capital,
            regulatory_requirements=regulatory_requirements,
            infrastructure_requirements=infrastructure_requirements,
            risk_assessment=risk_assessment,
            expected_roi=expected_roi,
            stage_completion_dates={stage: None for stage in ExpansionStage}
        )
        
        self.expansion_plans[market_code] = plan
        
        logger.info(f"Created expansion plan for {market_info.country} ({market_code})")
        return plan
    
    async def execute_expansion_plan(self, market_code: str) -> bool:
        """Execute expansion plan for a market"""
        
        plan = self.expansion_plans.get(market_code)
        if not plan:
            logger.error(f"No expansion plan found for {market_code}")
            return False
        
        logger.info(f"Starting expansion execution for {plan.market_info.country}")
        
        # Execute stages in order
        stages = list(ExpansionStage)
        current_stage_index = stages.index(plan.current_stage)
        
        for stage in stages[current_stage_index:]:
            success = await self._execute_expansion_stage(market_code, stage)
            
            if success:
                plan.current_stage = stage
                plan.stage_completion_dates[stage] = datetime.now()
                logger.info(f"Completed {stage.value} for {market_code}")
                
                if stage == ExpansionStage.FULL_DEPLOYMENT:
                    # Add to active markets
                    self.active_markets[market_code] = plan.market_info
                    logger.info(f"Successfully expanded to {plan.market_info.country}")
                    
                    # Record expansion event
                    self.expansion_history.append({
                        'market_code': market_code,
                        'country': plan.market_info.country,
                        'launch_date': datetime.now(),
                        'capital_deployed': plan.allocated_capital,
                        'timeline': datetime.now() - plan.stage_completion_dates[ExpansionStage.RESEARCH]
                    })
                    
                    return True
            else:
                logger.error(f"Failed to complete {stage.value} for {market_code}")
                return False
        
        return True
    
    async def monitor_expansion_progress(self):
        """Monitor progress of all active expansion plans"""
        
        while True:
            try:
                for market_code, plan in self.expansion_plans.items():
                    if plan.current_stage != ExpansionStage.FULL_DEPLOYMENT:
                        metrics = await self._calculate_expansion_metrics(market_code, plan)
                        
                        # Check for delays or issues
                        if metrics.progress_percentage < 50 and \
                           datetime.now() > plan.target_launch_date - timedelta(days=30):
                            logger.warning(f"Expansion to {plan.market_info.country} may be delayed")
                        
                        # Check for risk threshold breaches
                        if metrics.risk_score > 0.8:
                            logger.warning(f"High risk detected for {plan.market_info.country} expansion")
                
                await asyncio.sleep(3600)  # Check every hour
                
            except Exception as e:
                logger.error(f"Error monitoring expansion progress: {e}")
                await asyncio.sleep(300)
    
    async def _calculate_expansion_score(self, market_info: MarketInfo) -> Dict[str, Any]:
        """Calculate expansion score for a market"""
        
        # Market potential (0-1)
        market_potential = min(market_info.market_cap / 10000.0, 1.0)  # Normalize to $10T
        volume_potential = min(market_info.potential_volume / 500000.0, 1.0)  # Normalize to $500k
        
        # Regulatory risk (0-1, lower is better)
        regulatory_risk = market_info.regulatory_complexity / 10.0
        
        # Infrastructure cost factor (0-1, lower cost is better)
        cost_factor = max(0, 1.0 - (market_info.infrastructure_cost / 500.0))
        
        # Volatility factor (moderate volatility is preferred)
        volatility_factor = 1.0 - abs(market_info.volatility - 0.18) / 0.1
        volatility_factor = max(0, min(1, volatility_factor))
        
        # Calculate expected ROI
        expected_roi = (market_potential * volume_potential * volatility_factor) / \
                      (regulatory_risk + 0.1)  # Avoid division by zero
        
        # Time to launch estimate
        time_to_launch = 120 + (market_info.regulatory_complexity * 20)  # Days
        
        # Total score (weighted combination)
        total_score = (
            market_potential * 0.3 +
            volume_potential * 0.2 +
            cost_factor * 0.2 +
            (1 - regulatory_risk) * 0.2 +
            volatility_factor * 0.1
        )
        
        # Recommendation
        if total_score > 0.7:
            recommendation = "High Priority"
        elif total_score > 0.5:
            recommendation = "Medium Priority"
        else:
            recommendation = "Low Priority"
        
        return {
            'total_score': total_score,
            'market_potential': market_potential,
            'regulatory_risk': regulatory_risk,
            'expected_roi': expected_roi,
            'time_to_launch': time_to_launch,
            'recommendation': recommendation
        }
    
    async def _get_regulatory_requirements(self, market_info: MarketInfo) -> List[str]:
        """Get regulatory requirements for a market"""
        
        base_requirements = [
            "Business registration",
            "Financial services license",
            "Data protection compliance",
            "Tax registration"
        ]
        
        # Add region-specific requirements
        if market_info.region == MarketRegion.EUROPE:
            base_requirements.extend([
                "MiFID II compliance",
                "GDPR compliance",
                "PSD2 compliance"
            ])
        elif market_info.region == MarketRegion.ASIA_PACIFIC:
            base_requirements.extend([
                "Local partnership requirements",
                "Capital adequacy requirements",
                "Market maker registration"
            ])
        
        # Add complexity-based requirements
        if market_info.regulatory_complexity > 7:
            base_requirements.extend([
                "Regulatory sandbox participation",
                "Stress testing requirements",
                "Enhanced reporting obligations"
            ])
        
        return base_requirements
    
    async def _get_infrastructure_requirements(self, market_info: MarketInfo) -> List[str]:
        """Get infrastructure requirements for a market"""
        
        return [
            "Local data center setup",
            "Market data feed integration",
            "Local broker connectivity",
            "Compliance monitoring system",
            "Local customer support",
            "Currency conversion system",
            "Local backup systems"
        ]
    
    async def _assess_expansion_risks(self, market_info: MarketInfo) -> Dict[str, float]:
        """Assess risks for market expansion"""
        
        return {
            'regulatory_risk': market_info.regulatory_complexity / 10.0,
            'market_risk': market_info.volatility,
            'operational_risk': 0.3,  # Base operational risk
            'currency_risk': 0.2 if market_info.currency != "USD" else 0.0,
            'political_risk': np.random.uniform(0.1, 0.4),  # Simplified
            'technology_risk': 0.25,
            'competition_risk': np.random.uniform(0.2, 0.6)
        }
    
    async def _calculate_expected_roi(self, market_info: MarketInfo) -> float:
        """Calculate expected ROI for market expansion"""
        
        # Base ROI calculation
        revenue_potential = market_info.potential_volume * 0.001  # 0.1% of volume as revenue
        costs = market_info.infrastructure_cost * 1000  # Convert to actual cost
        
        # Adjust for market factors
        market_factor = market_info.market_cap / 1000.0
        volatility_bonus = market_info.volatility * 0.5  # Higher volatility = more opportunities
        
        annual_revenue = revenue_potential * 250 * market_factor  # 250 trading days
        annual_costs = costs * 0.3  # 30% annual operating costs
        
        roi = (annual_revenue - annual_costs) / costs if costs > 0 else 0
        
        return max(0, roi + volatility_bonus)
    
    async def _execute_expansion_stage(self, market_code: str, stage: ExpansionStage) -> bool:
        """Execute a specific expansion stage"""
        
        logger.info(f"Executing {stage.value} for {market_code}")
        
        # Simulate stage execution time
        stage_durations = {
            ExpansionStage.RESEARCH: 2,
            ExpansionStage.REGULATORY_APPROVAL: 5,
            ExpansionStage.INFRASTRUCTURE_SETUP: 3,
            ExpansionStage.PAPER_TRADING: 2,
            ExpansionStage.LIMITED_LAUNCH: 1,
            ExpansionStage.FULL_DEPLOYMENT: 1
        }
        
        duration = stage_durations.get(stage, 1)
        await asyncio.sleep(duration)  # Simulate work
        
        # Simulate success rate based on complexity
        market_info = self.target_markets[market_code]
        success_rate = max(0.7, 1.0 - (market_info.regulatory_complexity / 20.0))
        
        return np.random.random() < success_rate
    
    async def _calculate_expansion_metrics(self, market_code: str, plan: ExpansionPlan) -> ExpansionMetrics:
        """Calculate metrics for expansion progress"""
        
        stages = list(ExpansionStage)
        current_stage_index = stages.index(plan.current_stage)
        progress_percentage = (current_stage_index + 1) / len(stages)
        
        # Calculate other metrics
        capital_deployed = plan.allocated_capital * progress_percentage
        regulatory_approvals = len([req for req in plan.regulatory_requirements if np.random.random() > 0.5])
        infrastructure_readiness = progress_percentage * 0.8  # Infrastructure lags slightly
        
        # Risk score (average of all risks)
        risk_score = np.mean(list(plan.risk_assessment.values()))
        
        # Timeline calculations
        start_date = plan.stage_completion_dates.get(ExpansionStage.RESEARCH)
        if start_date:
            actual_timeline = datetime.now() - start_date
        else:
            actual_timeline = None
        
        projected_timeline = plan.target_launch_date - datetime.now()
        
        return ExpansionMetrics(
            market=market_code,
            stage=plan.current_stage,
            progress_percentage=progress_percentage,
            capital_deployed=capital_deployed,
            regulatory_approvals=regulatory_approvals,
            infrastructure_readiness=infrastructure_readiness,
            risk_score=risk_score,
            projected_timeline=projected_timeline,
            actual_timeline=actual_timeline
        )
    
    def get_expansion_summary(self) -> Dict[str, Any]:
        """Get summary of expansion activities"""
        
        return {
            'active_markets': len(self.active_markets),
            'expansion_plans': len(self.expansion_plans),
            'completed_expansions': len(self.expansion_history),
            'markets_by_region': self._get_markets_by_region(),
            'total_capital_deployed': sum(
                event['capital_deployed'] for event in self.expansion_history
            ),
            'average_expansion_time': self._calculate_average_expansion_time(),
            'success_rate': len(self.expansion_history) / max(len(self.expansion_plans), 1)
        }
    
    def _get_markets_by_region(self) -> Dict[str, int]:
        """Get count of markets by region"""
        
        region_counts = {}
        for market_info in self.active_markets.values():
            region = market_info.region.value
            region_counts[region] = region_counts.get(region, 0) + 1
        
        return region_counts
    
    def _calculate_average_expansion_time(self) -> Optional[float]:
        """Calculate average time for expansion"""
        
        if not self.expansion_history:
            return None
        
        timelines = [event['timeline'].days for event in self.expansion_history]
        return np.mean(timelines)
