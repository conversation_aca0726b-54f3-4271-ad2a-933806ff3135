"""
Continuous Improvement Module

Provides automated systems for continuous learning and improvement:
- Performance analysis and pattern recognition
- Strategy evolution using genetic algorithms
- Automated ML model retraining
- Feedback loops for learning from trades
"""

from .performance_analyzer import PerformanceAnalyzer, TradePattern
from .strategy_evolution import StrategyEvolution
from .model_retraining import ModelRetrainer
from .feedback_loop import FeedbackLoop

__all__ = [
    'PerformanceAnalyzer',
    'TradePattern',
    'StrategyEvolution',
    'ModelRetrainer',
    'FeedbackLoop'
]
