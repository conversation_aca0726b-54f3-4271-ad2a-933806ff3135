apiVersion: v1
kind: Service
metadata:
  name: trading-bot-service
  namespace: trading-bot
  labels:
    app: ai-trading-bot
    component: application
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: ai-trading-bot
    component: application

---
apiVersion: v1
kind: Service
metadata:
  name: trading-bot-headless
  namespace: trading-bot
  labels:
    app: ai-trading-bot
    component: application
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  selector:
    app: ai-trading-bot
    component: application

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: trading-bot
  labels:
    app: postgres
    component: database
spec:
  type: ClusterIP
  ports:
  - name: postgres
    port: 5432
    targetPort: postgres
    protocol: TCP
  selector:
    app: postgres
    component: database

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: trading-bot
  labels:
    app: redis
    component: cache
spec:
  type: ClusterIP
  ports:
  - name: redis
    port: 6379
    targetPort: redis
    protocol: TCP
  selector:
    app: redis
    component: cache

---
apiVersion: v1
kind: Service
metadata:
  name: mongodb-service
  namespace: trading-bot
  labels:
    app: mongodb
    component: database
spec:
  type: ClusterIP
  ports:
  - name: mongodb
    port: 27017
    targetPort: mongodb
    protocol: TCP
  selector:
    app: mongodb
    component: database

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: trading-bot
  labels:
    app: prometheus
    component: monitoring
spec:
  type: ClusterIP
  ports:
  - name: prometheus
    port: 9090
    targetPort: 9090
    protocol: TCP
  selector:
    app: prometheus
    component: monitoring

---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: trading-bot
  labels:
    app: grafana
    component: monitoring
spec:
  type: ClusterIP
  ports:
  - name: grafana
    port: 3000
    targetPort: 3000
    protocol: TCP
  selector:
    app: grafana
    component: monitoring

---
# External LoadBalancer for production access
apiVersion: v1
kind: Service
metadata:
  name: trading-bot-external
  namespace: trading-bot
  labels:
    app: ai-trading-bot
    component: application
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:region:account:certificate/cert-id"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
spec:
  type: LoadBalancer
  ports:
  - name: https
    port: 443
    targetPort: http
    protocol: TCP
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  selector:
    app: ai-trading-bot
    component: application
  loadBalancerSourceRanges:
  - 10.0.0.0/8
  - 172.16.0.0/12
  - 192.168.0.0/16

---
# Ingress for HTTP/HTTPS routing
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: trading-bot-ingress
  namespace: trading-bot
  labels:
    app: ai-trading-bot
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - trading-bot.example.com
    - api.trading-bot.example.com
    secretName: trading-bot-tls
  rules:
  - host: trading-bot.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: trading-bot-service
            port:
              number: 8000
  - host: api.trading-bot.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: trading-bot-service
            port:
              number: 8000
      - path: /ws
        pathType: Prefix
        backend:
          service:
            name: trading-bot-service
            port:
              number: 8000
  - host: monitoring.trading-bot.example.com
    http:
      paths:
      - path: /grafana
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000
      - path: /prometheus
        pathType: Prefix
        backend:
          service:
            name: prometheus-service
            port:
              number: 9090

---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: trading-bot-network-policy
  namespace: trading-bot
spec:
  podSelector:
    matchLabels:
      app: ai-trading-bot
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: ai-trading-bot
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app: mongodb
    ports:
    - protocol: TCP
      port: 27017
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: database-network-policy
  namespace: trading-bot
spec:
  podSelector:
    matchLabels:
      component: database
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: ai-trading-bot
    - podSelector:
        matchLabels:
          app: prometheus
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
