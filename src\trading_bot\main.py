"""Main Entry Point - AI Trading Bot System.

This is the main entry point for the AI Trading Bot system that orchestrates
all components including the master controller, API gateway, event bus,
and all trading subsystems for production deployment.
"""

import asyncio
import signal
import sys
from pathlib import Path
from typing import Optional

from .core.config import settings
from .utils.logger import get_structured_logger
from .orchestration.master_controller import <PERSON><PERSON><PERSON>roll<PERSON>, MasterControllerConfig
from .integration.api_gateway import APIGateway
from .integration.event_bus import event_bus

logger = get_structured_logger(__name__)


class TradingBotSystem:
    """
    Main trading bot system that coordinates all components.
    
    This class serves as the top-level orchestrator that manages:
    - Master Controller for trading operations
    - API Gateway for external access
    - Event Bus for internal communication
    - Graceful shutdown and error handling
    """
    
    def __init__(self):
        self.master_controller: Optional[MasterController] = None
        self.api_gateway: Optional[APIGateway] = None
        self.shutdown_event = asyncio.Event()
        self.running_tasks = []
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self):
        """Initialize all system components."""
        logger.info("🚀 Initializing AI Trading Bot System...")
        
        try:
            # 1. Initialize Event Bus first (needed by other components)
            logger.info("Initializing Event Bus...")
            await event_bus.initialize()
            
            # 2. Initialize Master Controller
            logger.info("Initializing Master Controller...")
            controller_config = MasterControllerConfig(
                max_concurrent_orders=settings.trading.max_daily_trades if hasattr(settings.trading, 'max_daily_trades') else 100,
                max_daily_trades=settings.trading.max_daily_trades if hasattr(settings.trading, 'max_daily_trades') else 100,
                emergency_stop_loss=settings.risk.emergency_stop_loss if hasattr(settings.risk, 'emergency_stop_loss') else 0.05,
                max_drawdown_threshold=settings.risk.max_drawdown_threshold if hasattr(settings.risk, 'max_drawdown_threshold') else 0.10
            )
            
            self.master_controller = MasterController(controller_config)
            
            if not await self.master_controller.initialize_system():
                raise Exception("Failed to initialize Master Controller")
            
            # 3. Initialize API Gateway
            logger.info("Initializing API Gateway...")
            api_host = settings.api.host if hasattr(settings.api, 'host') else "0.0.0.0"
            api_port = settings.api.port if hasattr(settings.api, 'port') else 8000
            
            self.api_gateway = APIGateway(host=api_host, port=api_port)
            
            # Register trading endpoints
            await self._register_api_endpoints()
            
            # Start API Gateway
            await self.api_gateway.start()
            
            # 4. Setup event subscriptions
            await self._setup_event_subscriptions()
            
            logger.info("✅ AI Trading Bot System initialized successfully!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize system: {e}")
            await self.shutdown()
            raise
    
    async def _register_api_endpoints(self):
        """Register API endpoints with the gateway."""
        from aiohttp import web
        
        # Trading endpoints
        async def start_trading(request):
            data = await request.json()
            
            if self.master_controller:
                success = await self.master_controller.start_trading()
                
                if success:
                    await event_bus.publish(
                        "system.trading.started",
                        "api_gateway",
                        {"strategy": data.get("strategy", "all")}
                    )
                    
                    return web.json_response({
                        "status": "success",
                        "message": "Trading started successfully"
                    })
                else:
                    return web.json_response({
                        "status": "error",
                        "message": "Failed to start trading"
                    }, status=500)
            else:
                return web.json_response({
                    "status": "error",
                    "message": "Master controller not available"
                }, status=503)
        
        async def stop_trading(request):
            data = await request.json()
            reason = data.get("reason", "API request")
            
            if self.master_controller:
                await self.master_controller.stop_trading(reason)
                
                await event_bus.publish(
                    "system.trading.stopped",
                    "api_gateway",
                    {"reason": reason}
                )
                
                return web.json_response({
                    "status": "success",
                    "message": "Trading stopped successfully"
                })
            else:
                return web.json_response({
                    "status": "error",
                    "message": "Master controller not available"
                }, status=503)
        
        async def get_system_status(request):
            status = {}
            
            if self.master_controller:
                status.update(self.master_controller.get_system_status())
            
            if self.api_gateway:
                status['api_gateway'] = self.api_gateway.get_status()
            
            status['event_bus'] = event_bus.get_metrics()
            
            return web.json_response(status)
        
        async def get_positions(request):
            # This would integrate with the actual position tracking
            # For now, return placeholder data
            return web.json_response({
                "positions": [],
                "total_value": 0.0,
                "timestamp": "2024-01-01T00:00:00Z"
            })
        
        async def get_performance(request):
            # This would integrate with the performance tracker
            # For now, return placeholder data
            return web.json_response({
                "daily_pnl": 0.0,
                "total_pnl": 0.0,
                "win_rate": 0.0,
                "sharpe_ratio": 0.0,
                "timestamp": "2024-01-01T00:00:00Z"
            })
        
        # Register endpoints
        self.api_gateway.app.router.add_post('/api/trading/start', start_trading)
        self.api_gateway.app.router.add_post('/api/trading/stop', stop_trading)
        self.api_gateway.app.router.add_get('/api/status', get_system_status)
        self.api_gateway.app.router.add_get('/api/positions', get_positions)
        self.api_gateway.app.router.add_get('/api/performance', get_performance)
    
    async def _setup_event_subscriptions(self):
        """Setup event subscriptions for system coordination."""
        
        # Subscribe to trading events
        async def handle_trading_event(event):
            logger.info(f"Trading event: {event.type} from {event.source}")
            
            # Broadcast to WebSocket clients
            if self.api_gateway:
                await self.api_gateway.broadcast_websocket({
                    "type": "trading_event",
                    "event": event.type,
                    "data": event.data,
                    "timestamp": event.timestamp.isoformat()
                })
        
        # Subscribe to risk events
        async def handle_risk_event(event):
            logger.warning(f"Risk event: {event.type} - {event.data}")
            
            # Broadcast critical risk events
            if self.api_gateway:
                await self.api_gateway.broadcast_websocket({
                    "type": "risk_alert",
                    "event": event.type,
                    "data": event.data,
                    "timestamp": event.timestamp.isoformat()
                })
        
        # Subscribe to system events
        async def handle_system_event(event):
            logger.info(f"System event: {event.type}")
            
            # Handle emergency shutdown events
            if event.type == "system.emergency.shutdown":
                logger.critical("Emergency shutdown event received!")
                await self.shutdown()
        
        # Register event subscriptions
        event_bus.subscribe("order.*", handle_trading_event)
        event_bus.subscribe("position.*", handle_trading_event)
        event_bus.subscribe("trade.*", handle_trading_event)
        event_bus.subscribe("risk.*", handle_risk_event)
        event_bus.subscribe("system.*", handle_system_event)
    
    async def run(self):
        """Run the main system loop."""
        logger.info("🤖 Starting AI Trading Bot System...")
        
        try:
            # Initialize all components
            await self.initialize()
            
            # Start the master controller trading operations
            if self.master_controller:
                # Perform pre-trading checks
                logger.info("Performing pre-trading checks...")
                
                # Start trading if configured to auto-start
                if settings.trading.auto_start if hasattr(settings.trading, 'auto_start') else False:
                    logger.info("Auto-starting trading operations...")
                    await self.master_controller.start_trading()
            
            # Main system loop
            logger.info("✅ AI Trading Bot System is running!")
            logger.info("Press Ctrl+C to stop the system gracefully")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
        except Exception as e:
            logger.error(f"System error: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Graceful shutdown of all system components."""
        if self.shutdown_event.is_set():
            return  # Already shutting down
        
        logger.info("🛑 Initiating graceful system shutdown...")
        self.shutdown_event.set()
        
        try:
            # Stop trading operations first
            if self.master_controller:
                logger.info("Stopping trading operations...")
                await self.master_controller.stop_trading("System shutdown")
                await self.master_controller.shutdown()
            
            # Stop API Gateway
            if self.api_gateway:
                logger.info("Stopping API Gateway...")
                await self.api_gateway.stop()
            
            # Stop Event Bus
            logger.info("Stopping Event Bus...")
            await event_bus.shutdown()
            
            # Cancel any remaining tasks
            if self.running_tasks:
                logger.info("Cancelling remaining tasks...")
                for task in self.running_tasks:
                    task.cancel()
                
                await asyncio.gather(*self.running_tasks, return_exceptions=True)
            
            logger.info("✅ System shutdown completed successfully")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


async def main():
    """Main entry point for the AI Trading Bot."""
    # Print startup banner
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    AI Trading Bot System                     ║
    ║                                                              ║
    ║  🤖 Intelligent • 📊 Data-Driven • ⚡ Real-Time • 🛡️ Secure  ║
    ║                                                              ║
    ║  Phase 2.4: System Integration & Orchestration              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Initialize and run the system
    system = TradingBotSystem()
    
    try:
        await system.run()
    except Exception as e:
        logger.error(f"Fatal system error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the main system
    asyncio.run(main())
