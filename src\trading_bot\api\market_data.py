"""Market data API client for Webull."""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union

from ..models.enums import APIEndpointType, Interval
from ..models.market import Bar, MarketHours, Quote, Symbol, Trade, Watchlist
from ..utils.exceptions import DataValidationError
from ..utils.logger import get_structured_logger
from .auth import AuthenticationManager

logger = get_structured_logger(__name__)


class MarketDataClient(AuthenticationManager):
    """Market data API client with caching and validation."""
    
    def __init__(self):
        super().__init__()
        
    async def get_quote(self, symbol: str, use_cache: bool = True) -> Quote:
        """
        Get real-time quote for a symbol.
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            use_cache: Whether to use cached data
            
        Returns:
            Quote object with current market data
            
        Raises:
            DataValidationError: If symbol is invalid
        """
        if not symbol or not isinstance(symbol, str):
            raise DataValidationError(
                "Invalid symbol",
                data_type="symbol",
                validation_errors={"symbol": "Must be a non-empty string"},
            )
        
        symbol = symbol.upper().strip()
        
        try:
            response = await self._make_request(
                "GET",
                APIEndpointType.QUOTE,
                "get_quote",
                params={"symbol": symbol},
                auth_required=False,
                use_cache=use_cache,
                cache_key=f"quote:{symbol}",
            )
            
            quote = Quote.from_dict(response)
            
            logger.market_data_event(
                "quote_received",
                symbol=symbol,
                price=float(quote.price),
                volume=quote.volume,
                bid=float(quote.bid) if quote.bid else None,
                ask=float(quote.ask) if quote.ask else None,
                data_source="webull",
            )
            
            return quote
            
        except Exception as e:
            logger.error(f"Failed to get quote for {symbol}: {e}")
            raise
    
    async def get_quotes(self, symbols: List[str], use_cache: bool = True) -> Dict[str, Quote]:
        """
        Get real-time quotes for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            use_cache: Whether to use cached data
            
        Returns:
            Dictionary mapping symbols to Quote objects
        """
        if not symbols:
            return {}
        
        # Validate symbols
        valid_symbols = []
        for symbol in symbols:
            if symbol and isinstance(symbol, str):
                valid_symbols.append(symbol.upper().strip())
        
        if not valid_symbols:
            raise DataValidationError(
                "No valid symbols provided",
                data_type="symbols",
                validation_errors={"symbols": "Must contain at least one valid symbol"},
            )
        
        try:
            # Webull API supports batch quotes
            response = await self._make_request(
                "GET",
                APIEndpointType.QUOTE,
                "get_quotes",
                params={"symbols": ",".join(valid_symbols)},
                auth_required=False,
                use_cache=use_cache,
                cache_key=f"quotes:{':'.join(sorted(valid_symbols))}",
            )
            
            quotes = {}
            for symbol_data in response.get("data", []):
                symbol = symbol_data.get("symbol")
                if symbol:
                    quotes[symbol] = Quote.from_dict(symbol_data)
            
            logger.info(f"Retrieved quotes for {len(quotes)} symbols")
            
            return quotes
            
        except Exception as e:
            logger.error(f"Failed to get quotes for symbols {valid_symbols}: {e}")
            raise
    
    async def get_bars(
        self,
        symbol: str,
        interval: Union[str, Interval] = Interval.ONE_MINUTE,
        count: int = 100,
        extend_trading: bool = False,
        use_cache: bool = True,
    ) -> List[Bar]:
        """
        Get historical bars for a symbol.
        
        Args:
            symbol: Stock symbol
            interval: Time interval for bars
            count: Number of bars to retrieve (max 1000)
            extend_trading: Include extended trading hours
            use_cache: Whether to use cached data
            
        Returns:
            List of Bar objects
        """
        if not symbol or not isinstance(symbol, str):
            raise DataValidationError(
                "Invalid symbol",
                data_type="symbol",
                validation_errors={"symbol": "Must be a non-empty string"},
            )
        
        symbol = symbol.upper().strip()
        
        # Validate interval
        if isinstance(interval, str):
            try:
                interval = Interval(interval)
            except ValueError:
                raise DataValidationError(
                    f"Invalid interval: {interval}",
                    data_type="interval",
                    validation_errors={"interval": f"Must be one of {list(Interval)}"},
                )
        
        # Validate count
        if not isinstance(count, int) or count <= 0 or count > 1000:
            raise DataValidationError(
                "Invalid count",
                data_type="count",
                validation_errors={"count": "Must be an integer between 1 and 1000"},
            )
        
        try:
            params = {
                "symbol": symbol,
                "type": interval.value,
                "count": count,
                "extendTrading": 1 if extend_trading else 0,
            }
            
            response = await self._make_request(
                "GET",
                APIEndpointType.QUOTE,
                "get_bars",
                params=params,
                auth_required=False,
                use_cache=use_cache,
                cache_key=f"bars:{symbol}:{interval.value}:{count}:{extend_trading}",
            )
            
            bars = []
            for bar_data in response.get("data", []):
                bar = Bar.from_dict(bar_data, symbol, interval)
                bars.append(bar)
            
            # Sort bars by timestamp
            bars.sort(key=lambda x: x.timestamp)
            
            logger.info(
                f"Retrieved {len(bars)} bars for {symbol}",
                extra={
                    "symbol": symbol,
                    "interval": interval.value,
                    "count": len(bars),
                    "extend_trading": extend_trading,
                }
            )
            
            return bars
            
        except Exception as e:
            logger.error(f"Failed to get bars for {symbol}: {e}")
            raise
    
    async def search_stocks(self, query: str, limit: int = 20) -> List[Symbol]:
        """
        Search for stocks by keyword.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of Symbol objects
        """
        if not query or not isinstance(query, str):
            raise DataValidationError(
                "Invalid query",
                data_type="query",
                validation_errors={"query": "Must be a non-empty string"},
            )
        
        query = query.strip()
        
        try:
            response = await self._make_request(
                "GET",
                APIEndpointType.QUOTE,
                "search_stocks",
                params={"keyword": query, "limit": limit},
                auth_required=False,
                use_cache=True,
                cache_key=f"search:{query}:{limit}",
            )
            
            symbols = []
            for symbol_data in response.get("data", [])[:limit]:
                symbol = Symbol.from_dict(symbol_data)
                symbols.append(symbol)
            
            logger.info(f"Search for '{query}' returned {len(symbols)} results")
            
            return symbols
            
        except Exception as e:
            logger.error(f"Failed to search stocks for '{query}': {e}")
            raise
    
    async def get_market_hours(self, use_cache: bool = True) -> MarketHours:
        """
        Get current market hours information.
        
        Args:
            use_cache: Whether to use cached data
            
        Returns:
            MarketHours object
        """
        try:
            response = await self._make_request(
                "GET",
                APIEndpointType.QUOTE,
                "get_market_hours",
                auth_required=False,
                use_cache=use_cache,
                cache_key="market_hours",
            )
            
            market_hours = MarketHours.from_dict(response)
            
            logger.info(
                f"Market hours: {'Open' if market_hours.is_open else 'Closed'}",
                extra={
                    "is_open": market_hours.is_open,
                    "session": market_hours.session.value,
                    "next_open": market_hours.next_open.isoformat() if market_hours.next_open else None,
                    "next_close": market_hours.next_close.isoformat() if market_hours.next_close else None,
                }
            )
            
            return market_hours
            
        except Exception as e:
            logger.error(f"Failed to get market hours: {e}")
            raise
    
    async def get_movers(
        self,
        direction: str = "up",
        change_type: str = "percent",
        limit: int = 20,
    ) -> List[Dict]:
        """
        Get market movers (top gainers/losers).
        
        Args:
            direction: 'up' for gainers, 'down' for losers
            change_type: 'percent' or 'price'
            limit: Maximum number of results
            
        Returns:
            List of mover data
        """
        if direction not in ["up", "down"]:
            raise DataValidationError(
                "Invalid direction",
                data_type="direction",
                validation_errors={"direction": "Must be 'up' or 'down'"},
            )
        
        if change_type not in ["percent", "price"]:
            raise DataValidationError(
                "Invalid change_type",
                data_type="change_type",
                validation_errors={"change_type": "Must be 'percent' or 'price'"},
            )
        
        try:
            params = {
                "direction": direction,
                "changeType": change_type,
                "limit": limit,
            }
            
            response = await self._make_request(
                "GET",
                APIEndpointType.QUOTE,
                "get_movers",
                params=params,
                auth_required=False,
                use_cache=True,
                cache_key=f"movers:{direction}:{change_type}:{limit}",
            )
            
            movers = response.get("data", [])
            
            logger.info(
                f"Retrieved {len(movers)} {direction} movers",
                extra={
                    "direction": direction,
                    "change_type": change_type,
                    "count": len(movers),
                }
            )
            
            return movers
            
        except Exception as e:
            logger.error(f"Failed to get movers: {e}")
            raise
    
    async def get_news(
        self,
        symbol: Optional[str] = None,
        limit: int = 20,
        use_cache: bool = True,
    ) -> List[Dict]:
        """
        Get news articles.
        
        Args:
            symbol: Stock symbol (None for general market news)
            limit: Maximum number of articles
            use_cache: Whether to use cached data
            
        Returns:
            List of news articles
        """
        try:
            params = {"limit": limit}
            if symbol:
                params["symbol"] = symbol.upper().strip()
            
            cache_key = f"news:{symbol or 'market'}:{limit}"
            
            response = await self._make_request(
                "GET",
                APIEndpointType.QUOTE,
                "get_news",
                params=params,
                auth_required=False,
                use_cache=use_cache,
                cache_key=cache_key,
            )
            
            news = response.get("data", [])
            
            logger.info(
                f"Retrieved {len(news)} news articles",
                extra={
                    "symbol": symbol,
                    "count": len(news),
                }
            )
            
            return news
            
        except Exception as e:
            logger.error(f"Failed to get news: {e}")
            raise
