"""
Comprehensive Risk Management System Example

This example demonstrates the complete risk management framework including:
- Position sizing with Kelly Criterion and volatility adjustment
- Dynamic stop-loss management
- Real-time portfolio risk metrics (VaR, CVaR, Sharpe ratio, etc.)
- Risk limit enforcement with circuit breakers
- Correlation tracking and diversification analysis
- Drawdown monitoring and recovery planning
- Real-time monitoring and alerting
- Monte Carlo simulation and stress testing
- Portfolio optimization
"""

import asyncio
import json
from datetime import datetime
from decimal import Decimal

from src.trading_bot.risk import (
    RiskManager,
    AlertManager,
    RiskMonitor,
    MonteCarloSimulator,
    StressTester,
    PortfolioOptimizer
)
from src.trading_bot.risk.monitoring import AlertSeverity, AlertChannel
from src.trading_bot.core.logger import get_logger

logger = get_logger(__name__)


async def main():
    """Demonstrate comprehensive risk management system."""
    
    print("🛡️  AI Trading Bot - Comprehensive Risk Management System Demo")
    print("=" * 70)
    
    # Initialize risk manager
    risk_manager = RiskManager()
    
    try:
        # Initialize the risk management system
        print("\n📊 Initializing Risk Management System...")
        await risk_manager.initialize()
        print("✅ Risk management system initialized successfully")
        
        # Simulate portfolio positions
        sample_positions = {
            "AAPL": {
                "quantity": 100,
                "side": "LONG",
                "entry_price": 150.0,
                "current_price": 155.0,
                "unrealized_pnl": 500.0
            },
            "MSFT": {
                "quantity": 50,
                "side": "LONG", 
                "entry_price": 300.0,
                "current_price": 295.0,
                "unrealized_pnl": -250.0
            },
            "GOOGL": {
                "quantity": 25,
                "side": "LONG",
                "entry_price": 2800.0,
                "current_price": 2850.0,
                "unrealized_pnl": 1250.0
            },
            "TSLA": {
                "quantity": 75,
                "side": "LONG",
                "entry_price": 800.0,
                "current_price": 790.0,
                "unrealized_pnl": -750.0
            }
        }
        
        # Update risk manager with sample positions
        risk_manager.current_positions = sample_positions
        risk_manager.portfolio_value = 100000.0
        risk_manager.daily_pnl = 750.0  # Net positive P&L
        
        print(f"\n💼 Portfolio Overview:")
        print(f"   Total Value: ${risk_manager.portfolio_value:,.2f}")
        print(f"   Daily P&L: ${risk_manager.daily_pnl:,.2f}")
        print(f"   Positions: {len(sample_positions)}")
        
        # Demonstrate position sizing
        print("\n📏 Position Sizing Examples:")
        print("-" * 40)
        
        # Kelly Criterion sizing
        strategy_performance = {
            "win_rate": 0.65,
            "avg_win": 0.08,
            "avg_loss": 0.03,
            "profit_factor": 1.8,
            "sharpe_ratio": 1.2
        }
        
        kelly_size = await risk_manager.calculate_comprehensive_position_size(
            symbol="NVDA",
            entry_price=500.0,
            stop_loss=485.0,
            signal_strength=0.8,
            strategy_performance=strategy_performance,
            sizing_method="kelly"
        )
        print(f"   Kelly Criterion Size (NVDA): {kelly_size} shares (${kelly_size * 500:,.2f})")
        
        # Volatility-adjusted sizing
        vol_size = await risk_manager.calculate_comprehensive_position_size(
            symbol="NVDA",
            entry_price=500.0,
            stop_loss=485.0,
            sizing_method="volatility"
        )
        print(f"   Volatility-Adjusted Size (NVDA): {vol_size} shares (${vol_size * 500:,.2f})")
        
        # Dynamic sizing
        dynamic_size = await risk_manager.calculate_comprehensive_position_size(
            symbol="NVDA",
            entry_price=500.0,
            stop_loss=485.0,
            signal_strength=0.9,
            strategy_performance=strategy_performance,
            sizing_method="dynamic"
        )
        print(f"   Dynamic Size (NVDA): {dynamic_size} shares (${dynamic_size * 500:,.2f})")
        
        # Demonstrate stop-loss calculations
        print("\n🛑 Stop-Loss Examples:")
        print("-" * 40)
        
        # ATR-based stop
        atr_stop = await risk_manager.calculate_advanced_stop_loss(
            symbol="NVDA",
            entry_price=500.0,
            side="LONG",
            stop_type="atr",
            multiplier=2.0
        )
        print(f"   ATR-Based Stop (NVDA): ${atr_stop:.2f}")
        
        # Volatility-adjusted stop
        vol_stop = await risk_manager.calculate_advanced_stop_loss(
            symbol="NVDA",
            entry_price=500.0,
            side="LONG",
            stop_type="volatility",
            multiplier=1.5
        )
        print(f"   Volatility-Adjusted Stop (NVDA): ${vol_stop:.2f}")
        
        # Get comprehensive risk metrics
        print("\n📈 Portfolio Risk Metrics:")
        print("-" * 40)
        
        risk_metrics = await risk_manager.get_comprehensive_risk_metrics()
        
        if "error" not in risk_metrics:
            print(f"   Portfolio Value: ${risk_metrics['portfolio_value']:,.2f}")
            print(f"   VaR (95%): ${risk_metrics['var_95']:,.2f}")
            print(f"   VaR (99%): ${risk_metrics['var_99']:,.2f}")
            print(f"   CVaR (95%): ${risk_metrics['cvar_95']:,.2f}")
            print(f"   Sharpe Ratio: {risk_metrics['sharpe_ratio']:.4f}")
            print(f"   Sortino Ratio: {risk_metrics['sortino_ratio']:.4f}")
            print(f"   Beta: {risk_metrics['beta']:.4f}")
            print(f"   Current Drawdown: {risk_metrics['current_drawdown']:.2%}")
            print(f"   Max Drawdown: {risk_metrics['max_drawdown']:.2%}")
            print(f"   Avg Correlation: {risk_metrics['avg_correlation']:.4f}")
            print(f"   Max Correlation: {risk_metrics['max_correlation']:.4f}")
            print(f"   Portfolio Volatility: {risk_metrics['portfolio_volatility']:.2%}")
            
            if risk_metrics['high_correlations']:
                print(f"\n   ⚠️  High Correlations Detected:")
                for corr in risk_metrics['high_correlations'][:3]:  # Show top 3
                    print(f"      {corr['symbol1']} - {corr['symbol2']}: {corr['correlation']:.3f}")
        
        # Check risk limits
        print("\n🚨 Risk Limit Analysis:")
        print("-" * 40)
        
        limit_results = await risk_manager.check_all_risk_limits()
        
        if limit_results.get("total_breaches", 0) > 0:
            print(f"   ❌ Risk Breaches Detected: {limit_results['total_breaches']}")
            
            for breach in limit_results.get("limit_breaches", []):
                print(f"      • {breach['message']}")
            
            for alert in limit_results.get("drawdown_alerts", []):
                print(f"      • {alert['message']}")
            
            if limit_results.get("circuit_breaker_level"):
                print(f"   🔴 Circuit Breaker Level: {limit_results['circuit_breaker_level'].value}")
            
            if limit_results.get("actions"):
                print(f"   📋 Recommended Actions:")
                for action in limit_results["actions"]:
                    print(f"      • {action}")
        else:
            print("   ✅ All risk limits within acceptable ranges")
        
        # Demonstrate correlation analysis
        print("\n🔗 Correlation Analysis:")
        print("-" * 40)
        
        correlation_analysis = await risk_manager.correlation_tracker.get_portfolio_correlations(
            sample_positions, threshold=0.6
        )
        
        print(f"   Average Correlation: {correlation_analysis['avg_correlation']:.3f}")
        print(f"   Maximum Correlation: {correlation_analysis['max_correlation']:.3f}")
        print(f"   Correlation Risk Score: {correlation_analysis['correlation_risk_score']:.3f}")
        
        if correlation_analysis['high_correlations']:
            print(f"   High Correlation Pairs:")
            for corr in correlation_analysis['high_correlations']:
                print(f"      {corr['symbol1']} - {corr['symbol2']}: {corr['correlation']:.3f}")
        
        # Get diversification suggestions
        diversification_suggestions = await risk_manager.correlation_tracker.get_diversification_suggestions(
            sample_positions
        )
        
        if diversification_suggestions:
            print(f"\n💡 Diversification Suggestions:")
            for suggestion in diversification_suggestions[:3]:  # Show top 3
                print(f"   • {suggestion['suggestion']}")
        
        # Demonstrate drawdown monitoring
        print("\n📉 Drawdown Analysis:")
        print("-" * 40)
        
        # Simulate some drawdown
        risk_manager.drawdown_monitor.current_peak = 105000.0
        drawdown_metrics = risk_manager.drawdown_monitor.update_drawdown(98000.0)  # 6.7% drawdown
        
        print(f"   Current Peak: ${drawdown_metrics['current_peak']:,.2f}")
        print(f"   Current Drawdown: {drawdown_metrics['current_drawdown']:.2%}")
        print(f"   Max Drawdown: {drawdown_metrics['max_drawdown']:.2%}")
        print(f"   Recovery Needed: {drawdown_metrics['recovery_percentage']:.2%}")
        
        if drawdown_metrics['is_in_drawdown']:
            print(f"   Drawdown Duration: {drawdown_metrics['drawdown_duration']} days")
            
            # Get recovery plan
            recovery_plan = risk_manager.drawdown_monitor.get_recovery_plan()
            print(f"\n   📋 Recovery Plan:")
            for recommendation in recovery_plan['recommendations']:
                print(f"      • {recommendation}")
        
        # Update portfolio metrics
        print("\n🔄 Updating Portfolio Metrics...")
        await risk_manager.update_portfolio_metrics()
        print("   ✅ Portfolio metrics updated successfully")
        
        # Simulate a trade validation
        print("\n✅ Trade Validation Example:")
        print("-" * 40)
        
        is_valid, reason = await risk_manager.validate_order(
            symbol="AMD",
            side="BUY",
            quantity=200,
            price=100.0
        )
        
        print(f"   Trade: BUY 200 AMD @ $100.00")
        print(f"   Valid: {'✅ Yes' if is_valid else '❌ No'}")
        print(f"   Reason: {reason}")
        
        # Show example of risk metrics over time
        print("\n📊 Risk Monitoring Summary:")
        print("-" * 40)
        print("   The risk management system continuously monitors:")
        print("   • Position sizes and concentration limits")
        print("   • Portfolio-level risk metrics (VaR, CVaR, Sharpe ratio)")
        print("   • Correlation between positions")
        print("   • Drawdown levels and duration")
        print("   • Stop-loss levels and trailing stops")
        print("   • Circuit breaker conditions")
        print("   • Sector exposure limits")
        print("   • Real-time risk limit enforcement")
        
        print("\n🎯 Key Features Demonstrated:")
        print("   ✅ Kelly Criterion position sizing")
        print("   ✅ Volatility-adjusted position sizing")
        print("   ✅ Dynamic stop-loss management")
        print("   ✅ Real-time VaR and CVaR calculation")
        print("   ✅ Correlation tracking and analysis")
        print("   ✅ Drawdown monitoring and recovery planning")
        print("   ✅ Risk limit enforcement with circuit breakers")
        print("   ✅ Comprehensive risk metrics dashboard")
        print("   ✅ Trade validation and risk checks")

        # Demonstrate advanced features
        await demonstrate_advanced_features(sample_positions)

    except Exception as e:
        logger.error(f"Error in risk management demo: {e}")
        print(f"❌ Error: {e}")

    print("\n" + "=" * 70)
    print("🛡️  Risk Management System Demo Complete")


async def demonstrate_advanced_features(positions):
    """Demonstrate advanced risk management features."""
    print("\n" + "=" * 70)
    print("🚀 ADVANCED RISK MANAGEMENT FEATURES")
    print("=" * 70)

    # 1. Real-time Monitoring and Alerting
    print("\n📡 Real-time Monitoring & Alerting:")
    print("-" * 40)

    alert_manager = AlertManager()
    risk_monitor = RiskMonitor()

    # Send test alert
    await alert_manager.send_alert(
        "Portfolio VaR exceeds 5% threshold",
        AlertSeverity.HIGH,
        [AlertChannel.LOG, AlertChannel.TELEGRAM],
        metadata={
            "var_95": 5200.0,
            "portfolio_value": 100000.0,
            "var_percentage": 0.052
        }
    )

    print("   ✅ Alert sent successfully")
    print(f"   📊 Alert history: {len(alert_manager.alert_history)} alerts")

    # Start monitoring (briefly)
    await risk_monitor.start_monitoring()
    print("   🔄 Risk monitoring started")

    # Get dashboard data
    dashboard_data = risk_monitor.get_dashboard_data()
    print(f"   📈 System status: {dashboard_data['system_status']}")
    print(f"   🕐 Last update: {dashboard_data['last_update']}")

    await risk_monitor.stop_monitoring()
    print("   ⏹️  Risk monitoring stopped")

    # 2. Monte Carlo Simulation
    print("\n🎲 Monte Carlo Simulation:")
    print("-" * 40)

    monte_carlo = MonteCarloSimulator()

    # Mock some historical data for simulation
    import pandas as pd
    import numpy as np

    with patch.object(monte_carlo, '_get_historical_returns') as mock_returns:
        # Create realistic mock returns
        mock_returns.return_value = pd.DataFrame({
            "AAPL": np.random.normal(0.0008, 0.02, 252),  # 20% annual vol
            "MSFT": np.random.normal(0.0006, 0.018, 252),  # 18% annual vol
            "GOOGL": np.random.normal(0.001, 0.025, 252)   # 25% annual vol
        })

        simulation_results = await monte_carlo.simulate_portfolio_returns(
            positions, num_simulations=5000, time_horizon=30
        )

        if "error" not in simulation_results:
            print(f"   🎯 Simulations: {simulation_results['num_simulations']:,}")
            print(f"   📅 Time horizon: {simulation_results['time_horizon_days']} days")
            print(f"   📈 Expected return: {simulation_results['mean_return']:.2%}")
            print(f"   📊 Volatility: {simulation_results['std_return']:.2%}")
            print(f"   🔻 VaR (95%): {simulation_results['var_95']:.2%}")
            print(f"   🔻 CVaR (95%): {simulation_results['cvar_95']:.2%}")
            print(f"   📉 Probability of loss: {simulation_results['probability_of_loss']:.1%}")
            print(f"   🚨 Probability of >10% loss: {simulation_results['probability_of_large_loss']:.1%}")

            print(f"\n   📊 Return Percentiles:")
            for percentile in [1, 5, 25, 50, 75, 95, 99]:
                value = simulation_results['percentiles'][str(percentile)]
                print(f"      {percentile:2d}%: {value:+.2%}")
        else:
            print(f"   ❌ Simulation error: {simulation_results['error']}")

    # 3. Stress Testing
    print("\n🧪 Stress Testing:")
    print("-" * 40)

    stress_tester = StressTester()
    stress_results = await stress_tester.run_stress_tests(positions)

    if "error" not in stress_results:
        print(f"   🧪 Scenarios tested: {len(stress_results) - 1}")  # -1 for summary

        # Show worst case scenario
        summary = stress_results.get("summary", {})
        if "worst_scenario" in summary:
            worst_scenario = summary["worst_scenario"]
            worst_return = summary["worst_case_return"]
            print(f"   🔻 Worst scenario: {worst_scenario}")
            print(f"   📉 Worst case return: {worst_return:.2%}")

            # Show details of worst scenario
            worst_details = stress_results[worst_scenario]
            print(f"   💰 Portfolio impact: ${worst_details['total_pnl']:,.2f}")

            if worst_details.get("worst_position"):
                worst_pos = worst_details["worst_position"]
                print(f"   📉 Worst position: {worst_pos}")

        print(f"   📊 Scenarios with losses: {summary.get('scenarios_with_loss', 0)}")
        print(f"   🚨 Scenarios with >10% loss: {summary.get('scenarios_with_large_loss', 0)}")

        # Show individual scenario results
        print(f"\n   📋 Scenario Results:")
        for scenario_name, result in stress_results.items():
            if scenario_name == "summary" or "error" in result:
                continue
            print(f"      {scenario_name}: {result['portfolio_return']:+.1%}")
    else:
        print(f"   ❌ Stress test error: {stress_results['error']}")

    # 4. Portfolio Optimization
    print("\n⚖️  Portfolio Optimization:")
    print("-" * 40)

    optimizer = PortfolioOptimizer()
    symbols = list(positions.keys())

    with patch.object(optimizer, '_get_returns_data') as mock_returns:
        # Create realistic mock returns for optimization
        mock_returns.return_value = pd.DataFrame({
            "AAPL": np.random.normal(0.0008, 0.02, 252),
            "MSFT": np.random.normal(0.0006, 0.018, 252),
            "GOOGL": np.random.normal(0.001, 0.025, 252)
        })

        optimization_results = await optimizer.optimize_portfolio(
            symbols=symbols,
            risk_tolerance=0.15,
            constraints={"max_weight": 0.5, "min_weight": 0.1}
        )

        if "error" not in optimization_results:
            print(f"   🎯 Optimized portfolios: {len(optimization_results)}")

            # Show minimum variance portfolio
            if "minimum_variance" in optimization_results:
                min_var = optimization_results["minimum_variance"]
                print(f"\n   📊 Minimum Variance Portfolio:")
                print(f"      Expected return: {min_var['expected_return']:.2%}")
                print(f"      Volatility: {min_var['volatility']:.2%}")
                print(f"      Sharpe ratio: {min_var['sharpe_ratio']:.3f}")
                print(f"      Weights:")
                for symbol, weight in min_var["weights"].items():
                    print(f"         {symbol}: {weight:.1%}")

            # Show maximum Sharpe portfolio
            if "maximum_sharpe" in optimization_results:
                max_sharpe = optimization_results["maximum_sharpe"]
                print(f"\n   🏆 Maximum Sharpe Portfolio:")
                print(f"      Expected return: {max_sharpe['expected_return']:.2%}")
                print(f"      Volatility: {max_sharpe['volatility']:.2%}")
                print(f"      Sharpe ratio: {max_sharpe['sharpe_ratio']:.3f}")
                print(f"      Weights:")
                for symbol, weight in max_sharpe["weights"].items():
                    print(f"         {symbol}: {weight:.1%}")
        else:
            print(f"   ❌ Optimization error: {optimization_results['error']}")

    print("\n🎯 Advanced Features Summary:")
    print("   ✅ Real-time monitoring and alerting system")
    print("   ✅ Monte Carlo simulation for risk forecasting")
    print("   ✅ Comprehensive stress testing scenarios")
    print("   ✅ Modern portfolio theory optimization")
    print("   ✅ Telegram/webhook alert integration")
    print("   ✅ Dashboard and reporting capabilities")


if __name__ == "__main__":
    # Import patch for mocking
    from unittest.mock import patch
    asyncio.run(main())
