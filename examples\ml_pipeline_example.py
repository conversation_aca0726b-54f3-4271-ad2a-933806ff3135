"""Example usage of the ML Pipeline for trading bot."""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from trading_bot.ml import TradingMLPipeline
from trading_bot.ml.pipeline import MLPipelineConfig
from trading_bot.ml.training.trainer import TrainingConfig
from trading_bot.ml.prediction.predictor import PredictionConfig
from trading_bot.ml.prediction.signal_generator import SignalConfig
from trading_bot.ml.prediction.confidence_scorer import ConfidenceConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_sample_data(symbol: str, 
                        start_date: str = "2020-01-01", 
                        end_date: str = "2024-01-01",
                        freq: str = '1H') -> pd.DataFrame:
    """Generate sample market data for testing."""
    
    # Create date range
    dates = pd.date_range(start=start_date, end=end_date, freq=freq)
    n_periods = len(dates)
    
    # Generate realistic price data
    np.random.seed(42)  # For reproducible results
    
    # Starting price
    initial_price = 100.0
    
    # Generate returns with some trends and volatility clustering
    returns = []
    volatility = 0.02  # Base volatility
    
    for i in range(n_periods):
        # Add some trend and mean reversion
        trend = 0.0001 * np.sin(i / 100)  # Long-term trend
        noise = np.random.normal(0, volatility)
        
        # Volatility clustering
        if i > 0 and abs(returns[-1]) > 0.03:
            volatility = min(0.05, volatility * 1.2)  # Increase volatility after large moves
        else:
            volatility = max(0.01, volatility * 0.95)  # Decrease volatility gradually
        
        daily_return = trend + noise
        returns.append(daily_return)
    
    # Convert returns to prices
    returns = np.array(returns)
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Generate OHLCV data
    data = []
    for i, date in enumerate(dates):
        close = prices[i]
        volatility_factor = abs(returns[i]) * 10 + 0.5
        
        # Generate high/low around close
        high = close * (1 + np.random.uniform(0, 0.01) * volatility_factor)
        low = close * (1 - np.random.uniform(0, 0.01) * volatility_factor)
        
        # Open is close of previous period with some gap
        if i == 0:
            open_price = close
        else:
            gap = np.random.normal(0, 0.002)
            open_price = prices[i-1] * (1 + gap)
        
        # Ensure OHLC consistency
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # Generate volume (higher volume on larger price moves)
        base_volume = 1000000
        volume_multiplier = 1 + abs(returns[i]) * 20
        volume = int(base_volume * volume_multiplier * np.random.uniform(0.5, 2.0))
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    
    # Add target column (5-period forward return)
    df['target'] = df['close'].pct_change(5).shift(-5)
    
    return df


async def data_source_example(symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
    """Example data source function."""
    logger.info(f"Fetching data for {symbol} from {start_date} to {end_date}")
    return generate_sample_data(symbol, start_date, end_date)


async def main():
    """Main example function."""
    
    logger.info("=== Trading ML Pipeline Example ===")
    
    # 1. Configure the pipeline
    training_config = TrainingConfig(
        models_to_train=['lstm', 'xgboost', 'transformer'],  # Skip RL for this example
        max_epochs=10,  # Reduced for quick example
        batch_size=32,
        track_with_mlflow=False  # Disable MLflow for this example
    )
    
    prediction_config = PredictionConfig(
        max_inference_time_ms=100,
        confidence_threshold=0.6
    )
    
    signal_config = SignalConfig(
        min_confidence=0.6,
        strong_confidence=0.8,
        base_position_size=0.1
    )
    
    confidence_config = ConfidenceConfig(
        performance_window=50,
        min_historical_samples=5
    )
    
    pipeline_config = MLPipelineConfig(
        training_config=training_config,
        prediction_config=prediction_config,
        signal_config=signal_config,
        confidence_config=confidence_config,
        model_save_dir="example_models",
        results_save_dir="example_results"
    )
    
    # 2. Initialize the pipeline
    ml_pipeline = TradingMLPipeline(
        models=['lstm', 'xgboost', 'transformer'],
        ensemble_method='weighted_voting',
        confidence_threshold=0.7,
        config=pipeline_config
    )
    
    # 3. Train the models
    logger.info("Training models...")
    symbols = ['AAPL', 'GOOGL']
    
    try:
        training_results = await ml_pipeline.train(
            symbols=symbols,
            start_date="2020-01-01",
            end_date="2023-01-01",
            validation_split=0.2,
            data_source=data_source_example
        )
        
        logger.info("Training completed successfully!")
        logger.info(f"Training results: {list(training_results.keys())}")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        return
    
    # 4. Test predictions
    logger.info("Testing predictions...")
    
    # Generate test data
    test_data = generate_sample_data('AAPL', '2023-01-01', '2023-02-01')
    
    try:
        # Make prediction
        prediction_result = await ml_pipeline.predict(
            data=test_data,
            symbol='AAPL',
            return_confidence=True,
            return_signals=True
        )
        
        logger.info("Prediction successful!")
        logger.info(f"Signal: {prediction_result.get('signal')}")
        logger.info(f"Confidence: {prediction_result.get('confidence')}")
        
    except Exception as e:
        logger.error(f"Prediction failed: {e}")
    
    # 5. Run backtest
    logger.info("Running backtest...")
    
    try:
        backtest_data = generate_sample_data('AAPL', '2023-01-01', '2023-06-01')
        
        backtest_results = await ml_pipeline.backtest(
            data=backtest_data,
            initial_capital=100000,
            commission=0.001,
            symbol='AAPL'
        )
        
        logger.info("Backtest completed!")
        if 'report' in backtest_results:
            report = backtest_results['report']
            logger.info(f"Total Return: {report['summary'].get('Total Return', 'N/A')}")
            logger.info(f"Sharpe Ratio: {report['summary'].get('Sharpe Ratio', 'N/A')}")
            logger.info(f"Max Drawdown: {report['summary'].get('Max Drawdown', 'N/A')}")
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
    
    # 6. Check pipeline health
    health_status = await ml_pipeline.health_check()
    logger.info(f"Pipeline health: {health_status['status']}")
    
    # 7. Get performance metrics
    performance = ml_pipeline.get_performance_metrics()
    logger.info(f"Performance metrics available: {list(performance.keys())}")
    
    # 8. Test real-time pipeline (briefly)
    logger.info("Testing real-time pipeline...")
    
    async def prediction_callback(prediction):
        logger.info(f"Real-time prediction: {prediction.signal} ({prediction.confidence:.2f})")
    
    try:
        # Start real-time pipeline
        await ml_pipeline.start_real_time_pipeline(
            data_callback=data_source_example,
            prediction_callback=prediction_callback
        )
        
        # Simulate some real-time data
        await asyncio.sleep(2)
        
        # Stop pipeline
        ml_pipeline.stop_real_time_pipeline()
        logger.info("Real-time pipeline test completed")
        
    except Exception as e:
        logger.error(f"Real-time pipeline test failed: {e}")
    
    # 9. Save pipeline state
    try:
        ml_pipeline.save_pipeline_state("example_pipeline_state.json")
        logger.info("Pipeline state saved")
    except Exception as e:
        logger.error(f"Failed to save pipeline state: {e}")
    
    logger.info("=== Example completed ===")


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())