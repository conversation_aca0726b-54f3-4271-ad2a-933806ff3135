"""Real-time news analysis for sentiment extraction."""

import asyncio
import aiohttp
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from textblob import TextBlob
import pandas as pd
import numpy as np

from ...core.config import settings
from ...core.logger import get_logger
from ...utils.cache import cache_manager
from ...utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


@dataclass
class NewsArticle:
    """News article data structure."""
    title: str
    content: str
    source: str
    published_at: datetime
    url: str
    symbols: List[str]
    sentiment_score: Optional[float] = None
    relevance_score: Optional[float] = None
    impact_score: Optional[float] = None


@dataclass
class SentimentAnalysis:
    """Sentiment analysis results."""
    compound_score: float
    positive: float
    negative: float
    neutral: float
    polarity: float
    subjectivity: float
    confidence: float


class NewsAnalyzer:
    """Real-time news analysis with sentiment scoring."""
    
    def __init__(self):
        self.rate_limiter = RateLimiter(max_requests=100, time_window=60)
        self.financial_keywords = self._load_financial_keywords()
        self.impact_keywords = self._load_impact_keywords()
        
    async def analyze_news_sentiment(
        self,
        symbols: List[str],
        hours_back: int = 24,
        min_relevance: float = 0.3
    ) -> Dict[str, Any]:
        """
        Analyze news sentiment for given symbols.
        
        Args:
            symbols: List of stock symbols
            hours_back: Hours to look back for news
            min_relevance: Minimum relevance score to include
            
        Returns:
            Dictionary with sentiment analysis results
        """
        try:
            # Fetch news articles
            articles = await self._fetch_news_articles(symbols, hours_back)
            
            if not articles:
                logger.warning(f"No news articles found for symbols: {symbols}")
                return self._empty_sentiment_result()
            
            # Analyze sentiment for each article
            analyzed_articles = []
            for article in articles:
                sentiment = await self._analyze_article_sentiment(article)
                relevance = self._calculate_relevance_score(article, symbols)
                impact = self._calculate_impact_score(article)
                
                if relevance >= min_relevance:
                    article.sentiment_score = sentiment.compound_score
                    article.relevance_score = relevance
                    article.impact_score = impact
                    analyzed_articles.append(article)
            
            # Aggregate sentiment by symbol
            symbol_sentiment = self._aggregate_sentiment_by_symbol(
                analyzed_articles, symbols
            )
            
            # Calculate overall market sentiment
            overall_sentiment = self._calculate_overall_sentiment(analyzed_articles)
            
            return {
                'timestamp': datetime.utcnow(),
                'symbols': symbol_sentiment,
                'overall_sentiment': overall_sentiment,
                'article_count': len(analyzed_articles),
                'articles': [self._article_to_dict(a) for a in analyzed_articles[:10]]  # Top 10
            }
            
        except Exception as e:
            logger.error(f"Error analyzing news sentiment: {e}")
            return self._empty_sentiment_result()
    
    async def _fetch_news_articles(
        self,
        symbols: List[str],
        hours_back: int
    ) -> List[NewsArticle]:
        """Fetch news articles from multiple sources."""
        articles = []
        
        # Fetch from multiple news sources
        sources = [
            self._fetch_alpha_vantage_news,
            self._fetch_benzinga_news,
            self._fetch_newsapi_news
        ]
        
        tasks = []
        for source_func in sources:
            for symbol in symbols:
                tasks.append(source_func(symbol, hours_back))
        
        # Execute all fetches concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, list):
                articles.extend(result)
            elif isinstance(result, Exception):
                logger.warning(f"News fetch failed: {result}")
        
        # Remove duplicates based on URL
        seen_urls = set()
        unique_articles = []
        for article in articles:
            if article.url not in seen_urls:
                seen_urls.add(article.url)
                unique_articles.append(article)
        
        return unique_articles
    
    async def _fetch_alpha_vantage_news(
        self,
        symbol: str,
        hours_back: int
    ) -> List[NewsArticle]:
        """Fetch news from Alpha Vantage."""
        if not hasattr(settings, 'alpha_vantage_api_key'):
            return []
        
        await self.rate_limiter.acquire()
        
        try:
            url = "https://www.alphavantage.co/query"
            params = {
                'function': 'NEWS_SENTIMENT',
                'tickers': symbol,
                'apikey': settings.alpha_vantage_api_key,
                'limit': 50
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    data = await response.json()
            
            articles = []
            if 'feed' in data:
                for item in data['feed']:
                    try:
                        published_at = datetime.strptime(
                            item['time_published'], '%Y%m%dT%H%M%S'
                        )
                        
                        # Check if within time window
                        if published_at > datetime.utcnow() - timedelta(hours=hours_back):
                            article = NewsArticle(
                                title=item.get('title', ''),
                                content=item.get('summary', ''),
                                source=item.get('source', 'Alpha Vantage'),
                                published_at=published_at,
                                url=item.get('url', ''),
                                symbols=[symbol]
                            )
                            articles.append(article)
                    except Exception as e:
                        logger.warning(f"Error parsing Alpha Vantage article: {e}")
                        continue
            
            return articles
            
        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage news: {e}")
            return []
    
    async def _fetch_benzinga_news(
        self,
        symbol: str,
        hours_back: int
    ) -> List[NewsArticle]:
        """Fetch news from Benzinga."""
        # Implementation would require Benzinga API key
        # Placeholder for now
        return []
    
    async def _fetch_newsapi_news(
        self,
        symbol: str,
        hours_back: int
    ) -> List[NewsArticle]:
        """Fetch news from NewsAPI."""
        # Implementation would require NewsAPI key
        # Placeholder for now
        return []
    
    async def _analyze_article_sentiment(self, article: NewsArticle) -> SentimentAnalysis:
        """Analyze sentiment of a single article."""
        try:
            # Combine title and content
            text = f"{article.title} {article.content}"
            
            # Basic sentiment analysis using TextBlob
            blob = TextBlob(text)
            
            # Financial-specific sentiment analysis
            financial_score = self._calculate_financial_sentiment(text)
            
            # Combine scores
            compound_score = (blob.sentiment.polarity + financial_score) / 2
            
            # Calculate confidence based on text length and keyword presence
            confidence = self._calculate_confidence(text)
            
            return SentimentAnalysis(
                compound_score=compound_score,
                positive=max(0, compound_score),
                negative=abs(min(0, compound_score)),
                neutral=1 - abs(compound_score),
                polarity=blob.sentiment.polarity,
                subjectivity=blob.sentiment.subjectivity,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error analyzing article sentiment: {e}")
            return SentimentAnalysis(0, 0, 0, 1, 0, 0, 0)
    
    def _calculate_financial_sentiment(self, text: str) -> float:
        """Calculate financial-specific sentiment score."""
        text_lower = text.lower()
        
        positive_score = 0
        negative_score = 0
        
        # Check for positive financial keywords
        for keyword, weight in self.financial_keywords['positive'].items():
            count = len(re.findall(r'\b' + keyword + r'\b', text_lower))
            positive_score += count * weight
        
        # Check for negative financial keywords
        for keyword, weight in self.financial_keywords['negative'].items():
            count = len(re.findall(r'\b' + keyword + r'\b', text_lower))
            negative_score += count * weight
        
        # Normalize score
        total_score = positive_score - negative_score
        max_possible = len(text.split()) * 0.1  # Rough normalization
        
        return np.tanh(total_score / max(max_possible, 1))
    
    def _calculate_relevance_score(
        self,
        article: NewsArticle,
        symbols: List[str]
    ) -> float:
        """Calculate how relevant the article is to the given symbols."""
        text = f"{article.title} {article.content}".lower()
        
        relevance = 0
        for symbol in symbols:
            # Direct symbol mention
            if symbol.lower() in text:
                relevance += 0.5
            
            # Company name mention (would need symbol-to-company mapping)
            # This is a simplified version
            if len(symbol) > 2 and symbol.lower() in text:
                relevance += 0.3
        
        # Industry/sector keywords
        for keyword in ['earnings', 'revenue', 'profit', 'loss', 'guidance']:
            if keyword in text:
                relevance += 0.1
        
        return min(relevance, 1.0)
    
    def _calculate_impact_score(self, article: NewsArticle) -> float:
        """Calculate potential market impact of the article."""
        text = f"{article.title} {article.content}".lower()
        
        impact = 0
        for keyword, weight in self.impact_keywords.items():
            count = len(re.findall(r'\b' + keyword + r'\b', text))
            impact += count * weight
        
        # Source credibility factor
        source_weights = {
            'reuters': 1.0,
            'bloomberg': 1.0,
            'wsj': 0.9,
            'cnbc': 0.8,
            'marketwatch': 0.7
        }
        
        source_weight = source_weights.get(article.source.lower(), 0.5)
        impact *= source_weight
        
        return min(impact, 1.0)
    
    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence in sentiment analysis."""
        # Factors affecting confidence:
        # 1. Text length
        # 2. Presence of financial keywords
        # 3. Clarity of sentiment
        
        word_count = len(text.split())
        length_factor = min(word_count / 100, 1.0)  # Normalize to 100 words
        
        # Financial keyword presence
        keyword_count = 0
        for keywords in self.financial_keywords.values():
            for keyword in keywords:
                if keyword in text.lower():
                    keyword_count += 1
        
        keyword_factor = min(keyword_count / 10, 1.0)
        
        return (length_factor + keyword_factor) / 2
    
    def _aggregate_sentiment_by_symbol(
        self,
        articles: List[NewsArticle],
        symbols: List[str]
    ) -> Dict[str, Dict[str, float]]:
        """Aggregate sentiment scores by symbol."""
        symbol_sentiment = {}
        
        for symbol in symbols:
            relevant_articles = [
                a for a in articles if symbol in a.symbols
            ]
            
            if not relevant_articles:
                symbol_sentiment[symbol] = {
                    'sentiment_score': 0.0,
                    'confidence': 0.0,
                    'article_count': 0
                }
                continue
            
            # Weighted average by relevance and impact
            total_weight = 0
            weighted_sentiment = 0
            
            for article in relevant_articles:
                weight = article.relevance_score * article.impact_score
                weighted_sentiment += article.sentiment_score * weight
                total_weight += weight
            
            avg_sentiment = weighted_sentiment / total_weight if total_weight > 0 else 0
            
            symbol_sentiment[symbol] = {
                'sentiment_score': avg_sentiment,
                'confidence': total_weight / len(relevant_articles),
                'article_count': len(relevant_articles)
            }
        
        return symbol_sentiment
    
    def _calculate_overall_sentiment(self, articles: List[NewsArticle]) -> Dict[str, float]:
        """Calculate overall market sentiment."""
        if not articles:
            return {'sentiment_score': 0.0, 'confidence': 0.0}
        
        # Weight by impact and recency
        total_weight = 0
        weighted_sentiment = 0
        
        now = datetime.utcnow()
        
        for article in articles:
            # Recency weight (more recent = higher weight)
            hours_old = (now - article.published_at).total_seconds() / 3600
            recency_weight = max(0, 1 - (hours_old / 24))  # Linear decay over 24 hours
            
            weight = article.impact_score * recency_weight
            weighted_sentiment += article.sentiment_score * weight
            total_weight += weight
        
        avg_sentiment = weighted_sentiment / total_weight if total_weight > 0 else 0
        confidence = min(total_weight / len(articles), 1.0)
        
        return {
            'sentiment_score': avg_sentiment,
            'confidence': confidence
        }
    
    def _load_financial_keywords(self) -> Dict[str, Dict[str, float]]:
        """Load financial sentiment keywords with weights."""
        return {
            'positive': {
                'profit': 0.8, 'growth': 0.7, 'beat': 0.9, 'exceed': 0.8,
                'strong': 0.6, 'bullish': 0.9, 'upgrade': 0.8, 'buy': 0.7,
                'outperform': 0.8, 'rally': 0.7, 'surge': 0.8, 'gain': 0.6,
                'revenue': 0.5, 'earnings': 0.5, 'dividend': 0.6
            },
            'negative': {
                'loss': 0.8, 'decline': 0.7, 'miss': 0.9, 'fall': 0.6,
                'weak': 0.6, 'bearish': 0.9, 'downgrade': 0.8, 'sell': 0.7,
                'underperform': 0.8, 'crash': 0.9, 'plunge': 0.8, 'drop': 0.6,
                'bankruptcy': 1.0, 'lawsuit': 0.7, 'investigation': 0.8
            }
        }
    
    def _load_impact_keywords(self) -> Dict[str, float]:
        """Load keywords that indicate high market impact."""
        return {
            'merger': 1.0, 'acquisition': 1.0, 'buyout': 1.0,
            'earnings': 0.8, 'guidance': 0.8, 'forecast': 0.7,
            'fda': 0.9, 'approval': 0.8, 'clinical': 0.7,
            'fed': 1.0, 'interest': 0.9, 'rate': 0.8,
            'bankruptcy': 1.0, 'delisting': 1.0, 'halt': 0.9,
            'ceo': 0.7, 'resignation': 0.8, 'appointment': 0.6
        }
    
    def _empty_sentiment_result(self) -> Dict[str, Any]:
        """Return empty sentiment result."""
        return {
            'timestamp': datetime.utcnow(),
            'symbols': {},
            'overall_sentiment': {'sentiment_score': 0.0, 'confidence': 0.0},
            'article_count': 0,
            'articles': []
        }
    
    def _article_to_dict(self, article: NewsArticle) -> Dict[str, Any]:
        """Convert article to dictionary."""
        return {
            'title': article.title,
            'source': article.source,
            'published_at': article.published_at.isoformat(),
            'url': article.url,
            'symbols': article.symbols,
            'sentiment_score': article.sentiment_score,
            'relevance_score': article.relevance_score,
            'impact_score': article.impact_score
        }
