"""
Data Validation System

Validates all incoming market data, trade data, and system data
to ensure accuracy and prevent trading on bad data.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np
import pandas as pd
from decimal import Decimal

from ..core.logger import get_logger

logger = get_logger(__name__)

class ValidationLevel(Enum):
    """Data validation levels"""
    BASIC = "basic"           # Basic format and type checks
    STANDARD = "standard"     # Standard business logic validation
    STRICT = "strict"         # Strict validation with cross-checks
    PARANOID = "paranoid"     # Maximum validation with external verification

class ValidationResult(Enum):
    """Validation results"""
    VALID = "valid"
    WARNING = "warning"
    INVALID = "invalid"
    SUSPICIOUS = "suspicious"

@dataclass
class ValidationError:
    """Validation error details"""
    field: str
    error_type: str
    message: str
    severity: str
    expected_value: Optional[Any] = None
    actual_value: Optional[Any] = None
    suggestion: Optional[str] = None

@dataclass
class ValidationReport:
    """Validation report"""
    data_type: str
    validation_level: ValidationLevel
    result: ValidationResult
    timestamp: datetime
    errors: List[ValidationError]
    warnings: List[ValidationError]
    data_quality_score: float  # 0.0 to 1.0
    processing_time: float

class DataValidator:
    """Comprehensive data validation system"""
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.STANDARD):
        self.validation_level = validation_level
        self.validation_history = []
        self.data_quality_metrics = {}
        
        # Validation thresholds
        self.thresholds = {
            'price_change_max': 0.20,      # 20% max price change
            'volume_spike_max': 10.0,      # 10x volume spike
            'spread_max': 0.05,            # 5% max bid-ask spread
            'latency_max': 5.0,            # 5 seconds max data latency
            'missing_data_max': 0.05,      # 5% max missing data
            'outlier_zscore_max': 4.0      # 4 standard deviations
        }
        
        # Historical data for comparison
        self.price_history = {}
        self.volume_history = {}
        
    async def validate_market_data(self, data: Dict[str, Any]) -> ValidationReport:
        """Validate market data (quotes, bars, etc.)"""
        
        start_time = datetime.now()
        errors = []
        warnings = []
        
        # Basic format validation
        errors.extend(await self._validate_market_data_format(data))
        
        # Price validation
        price_errors, price_warnings = await self._validate_prices(data)
        errors.extend(price_errors)
        warnings.extend(price_warnings)
        
        # Volume validation
        volume_errors, volume_warnings = await self._validate_volume(data)
        errors.extend(volume_errors)
        warnings.extend(volume_warnings)
        
        # Timestamp validation
        time_errors = await self._validate_timestamps(data)
        errors.extend(time_errors)
        
        # Cross-field validation
        if self.validation_level in [ValidationLevel.STRICT, ValidationLevel.PARANOID]:
            cross_errors = await self._validate_market_data_consistency(data)
            errors.extend(cross_errors)
        
        # External validation for paranoid mode
        if self.validation_level == ValidationLevel.PARANOID:
            external_errors = await self._validate_against_external_sources(data)
            errors.extend(external_errors)
        
        # Calculate data quality score
        quality_score = self._calculate_quality_score(errors, warnings)
        
        # Determine overall result
        if any(error.severity == 'critical' for error in errors):
            result = ValidationResult.INVALID
        elif any(error.severity == 'high' for error in errors):
            result = ValidationResult.SUSPICIOUS
        elif warnings:
            result = ValidationResult.WARNING
        else:
            result = ValidationResult.VALID
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        report = ValidationReport(
            data_type="market_data",
            validation_level=self.validation_level,
            result=result,
            timestamp=datetime.now(),
            errors=errors,
            warnings=warnings,
            data_quality_score=quality_score,
            processing_time=processing_time
        )
        
        # Store validation history
        self.validation_history.append(report)
        
        # Update data quality metrics
        await self._update_quality_metrics(report)
        
        return report
    
    async def validate_trade_data(self, trade_data: Dict[str, Any]) -> ValidationReport:
        """Validate trade execution data"""
        
        start_time = datetime.now()
        errors = []
        warnings = []
        
        # Basic format validation
        errors.extend(await self._validate_trade_format(trade_data))
        
        # Trade logic validation
        logic_errors = await self._validate_trade_logic(trade_data)
        errors.extend(logic_errors)
        
        # Price validation against market
        price_errors = await self._validate_trade_prices(trade_data)
        errors.extend(price_errors)
        
        # Quantity validation
        quantity_errors = await self._validate_trade_quantities(trade_data)
        errors.extend(quantity_errors)
        
        # Timing validation
        timing_errors = await self._validate_trade_timing(trade_data)
        errors.extend(timing_errors)
        
        quality_score = self._calculate_quality_score(errors, warnings)
        
        if any(error.severity == 'critical' for error in errors):
            result = ValidationResult.INVALID
        elif any(error.severity == 'high' for error in errors):
            result = ValidationResult.SUSPICIOUS
        elif warnings:
            result = ValidationResult.WARNING
        else:
            result = ValidationResult.VALID
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        report = ValidationReport(
            data_type="trade_data",
            validation_level=self.validation_level,
            result=result,
            timestamp=datetime.now(),
            errors=errors,
            warnings=warnings,
            data_quality_score=quality_score,
            processing_time=processing_time
        )
        
        self.validation_history.append(report)
        await self._update_quality_metrics(report)
        
        return report
    
    async def validate_position_data(self, position_data: Dict[str, Any]) -> ValidationReport:
        """Validate position data"""
        
        start_time = datetime.now()
        errors = []
        warnings = []
        
        # Basic format validation
        errors.extend(await self._validate_position_format(position_data))
        
        # Position consistency validation
        consistency_errors = await self._validate_position_consistency(position_data)
        errors.extend(consistency_errors)
        
        # Risk validation
        risk_errors = await self._validate_position_risk(position_data)
        errors.extend(risk_errors)
        
        quality_score = self._calculate_quality_score(errors, warnings)
        
        if any(error.severity == 'critical' for error in errors):
            result = ValidationResult.INVALID
        elif any(error.severity == 'high' for error in errors):
            result = ValidationResult.SUSPICIOUS
        elif warnings:
            result = ValidationResult.WARNING
        else:
            result = ValidationResult.VALID
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        report = ValidationReport(
            data_type="position_data",
            validation_level=self.validation_level,
            result=result,
            timestamp=datetime.now(),
            errors=errors,
            warnings=warnings,
            data_quality_score=quality_score,
            processing_time=processing_time
        )
        
        self.validation_history.append(report)
        await self._update_quality_metrics(report)
        
        return report
    
    async def _validate_market_data_format(self, data: Dict[str, Any]) -> List[ValidationError]:
        """Validate basic market data format"""
        
        errors = []
        
        # Required fields
        required_fields = ['symbol', 'price', 'timestamp']
        for field in required_fields:
            if field not in data:
                errors.append(ValidationError(
                    field=field,
                    error_type='missing_field',
                    message=f'Required field {field} is missing',
                    severity='critical'
                ))
        
        # Data types
        if 'price' in data:
            try:
                price = float(data['price'])
                if price <= 0:
                    errors.append(ValidationError(
                        field='price',
                        error_type='invalid_value',
                        message='Price must be positive',
                        severity='critical',
                        actual_value=price
                    ))
            except (ValueError, TypeError):
                errors.append(ValidationError(
                    field='price',
                    error_type='invalid_type',
                    message='Price must be a number',
                    severity='critical',
                    actual_value=data['price']
                ))
        
        # Symbol validation
        if 'symbol' in data:
            symbol = data['symbol']
            if not isinstance(symbol, str) or len(symbol) < 1:
                errors.append(ValidationError(
                    field='symbol',
                    error_type='invalid_format',
                    message='Symbol must be a non-empty string',
                    severity='critical',
                    actual_value=symbol
                ))
        
        return errors
    
    async def _validate_prices(self, data: Dict[str, Any]) -> Tuple[List[ValidationError], List[ValidationError]]:
        """Validate price data"""
        
        errors = []
        warnings = []
        
        symbol = data.get('symbol')
        current_price = data.get('price')
        
        if not symbol or not current_price:
            return errors, warnings
        
        # Check against historical prices
        if symbol in self.price_history:
            last_price = self.price_history[symbol][-1] if self.price_history[symbol] else None
            
            if last_price:
                price_change = abs(current_price - last_price) / last_price
                
                if price_change > self.thresholds['price_change_max']:
                    errors.append(ValidationError(
                        field='price',
                        error_type='price_spike',
                        message=f'Price change of {price_change:.2%} exceeds threshold',
                        severity='high',
                        expected_value=f'<{self.thresholds["price_change_max"]:.2%}',
                        actual_value=f'{price_change:.2%}'
                    ))
        
        # Validate bid-ask spread if available
        if 'bid' in data and 'ask' in data:
            bid = data['bid']
            ask = data['ask']
            
            if bid >= ask:
                errors.append(ValidationError(
                    field='bid_ask',
                    error_type='invalid_spread',
                    message='Bid price must be less than ask price',
                    severity='critical',
                    actual_value=f'bid={bid}, ask={ask}'
                ))
            else:
                spread = (ask - bid) / ((ask + bid) / 2)
                if spread > self.thresholds['spread_max']:
                    warnings.append(ValidationError(
                        field='spread',
                        error_type='wide_spread',
                        message=f'Bid-ask spread of {spread:.2%} is unusually wide',
                        severity='medium',
                        actual_value=f'{spread:.2%}'
                    ))
        
        # Update price history
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append(current_price)
        
        # Keep only recent history
        if len(self.price_history[symbol]) > 1000:
            self.price_history[symbol] = self.price_history[symbol][-1000:]
        
        return errors, warnings
    
    async def _validate_volume(self, data: Dict[str, Any]) -> Tuple[List[ValidationError], List[ValidationError]]:
        """Validate volume data"""
        
        errors = []
        warnings = []
        
        symbol = data.get('symbol')
        current_volume = data.get('volume')
        
        if not symbol or current_volume is None:
            return errors, warnings
        
        # Volume must be non-negative
        if current_volume < 0:
            errors.append(ValidationError(
                field='volume',
                error_type='negative_volume',
                message='Volume cannot be negative',
                severity='critical',
                actual_value=current_volume
            ))
            return errors, warnings
        
        # Check against historical volume
        if symbol in self.volume_history:
            recent_volumes = self.volume_history[symbol][-20:]  # Last 20 periods
            
            if recent_volumes:
                avg_volume = np.mean(recent_volumes)
                
                if avg_volume > 0:
                    volume_ratio = current_volume / avg_volume
                    
                    if volume_ratio > self.thresholds['volume_spike_max']:
                        warnings.append(ValidationError(
                            field='volume',
                            error_type='volume_spike',
                            message=f'Volume spike of {volume_ratio:.1f}x average',
                            severity='medium',
                            actual_value=f'{volume_ratio:.1f}x'
                        ))
        
        # Update volume history
        if symbol not in self.volume_history:
            self.volume_history[symbol] = []
        
        self.volume_history[symbol].append(current_volume)
        
        # Keep only recent history
        if len(self.volume_history[symbol]) > 1000:
            self.volume_history[symbol] = self.volume_history[symbol][-1000:]
        
        return errors, warnings
    
    async def _validate_timestamps(self, data: Dict[str, Any]) -> List[ValidationError]:
        """Validate timestamp data"""
        
        errors = []
        
        timestamp = data.get('timestamp')
        if not timestamp:
            return errors
        
        # Parse timestamp
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            elif isinstance(timestamp, (int, float)):
                dt = datetime.fromtimestamp(timestamp)
            else:
                dt = timestamp
        except (ValueError, TypeError):
            errors.append(ValidationError(
                field='timestamp',
                error_type='invalid_format',
                message='Invalid timestamp format',
                severity='critical',
                actual_value=timestamp
            ))
            return errors
        
        # Check data latency
        now = datetime.now()
        latency = (now - dt).total_seconds()
        
        if latency > self.thresholds['latency_max']:
            errors.append(ValidationError(
                field='timestamp',
                error_type='stale_data',
                message=f'Data is {latency:.1f} seconds old',
                severity='high',
                actual_value=f'{latency:.1f}s'
            ))
        
        # Check for future timestamps
        if dt > now + timedelta(seconds=60):  # Allow 1 minute clock skew
            errors.append(ValidationError(
                field='timestamp',
                error_type='future_timestamp',
                message='Timestamp is in the future',
                severity='high',
                actual_value=dt.isoformat()
            ))
        
        return errors
    
    async def _validate_market_data_consistency(self, data: Dict[str, Any]) -> List[ValidationError]:
        """Validate cross-field consistency"""
        
        errors = []
        
        # OHLC consistency
        if all(field in data for field in ['open', 'high', 'low', 'close']):
            open_price = data['open']
            high_price = data['high']
            low_price = data['low']
            close_price = data['close']
            
            if high_price < max(open_price, close_price):
                errors.append(ValidationError(
                    field='high',
                    error_type='ohlc_inconsistency',
                    message='High price is less than open or close',
                    severity='critical'
                ))
            
            if low_price > min(open_price, close_price):
                errors.append(ValidationError(
                    field='low',
                    error_type='ohlc_inconsistency',
                    message='Low price is greater than open or close',
                    severity='critical'
                ))
        
        return errors
    
    async def _validate_against_external_sources(self, data: Dict[str, Any]) -> List[ValidationError]:
        """Validate against external data sources (paranoid mode)"""
        
        errors = []
        
        # This would integrate with external data sources for cross-validation
        # For now, placeholder implementation
        
        return errors
    
    def _calculate_quality_score(self, errors: List[ValidationError], warnings: List[ValidationError]) -> float:
        """Calculate data quality score (0.0 to 1.0)"""
        
        if not errors and not warnings:
            return 1.0
        
        # Weight errors by severity
        severity_weights = {
            'critical': 1.0,
            'high': 0.7,
            'medium': 0.4,
            'low': 0.2
        }
        
        total_penalty = 0
        for error in errors:
            total_penalty += severity_weights.get(error.severity, 0.5)
        
        for warning in warnings:
            total_penalty += severity_weights.get(warning.severity, 0.2) * 0.5
        
        # Calculate score
        score = max(0.0, 1.0 - (total_penalty / 10.0))  # Normalize to 0-1
        
        return score
    
    async def _update_quality_metrics(self, report: ValidationReport):
        """Update data quality metrics"""
        
        data_type = report.data_type
        
        if data_type not in self.data_quality_metrics:
            self.data_quality_metrics[data_type] = {
                'total_validations': 0,
                'valid_count': 0,
                'warning_count': 0,
                'invalid_count': 0,
                'suspicious_count': 0,
                'avg_quality_score': 0.0,
                'avg_processing_time': 0.0
            }
        
        metrics = self.data_quality_metrics[data_type]
        
        # Update counts
        metrics['total_validations'] += 1
        
        if report.result == ValidationResult.VALID:
            metrics['valid_count'] += 1
        elif report.result == ValidationResult.WARNING:
            metrics['warning_count'] += 1
        elif report.result == ValidationResult.INVALID:
            metrics['invalid_count'] += 1
        elif report.result == ValidationResult.SUSPICIOUS:
            metrics['suspicious_count'] += 1
        
        # Update averages
        total = metrics['total_validations']
        metrics['avg_quality_score'] = (
            (metrics['avg_quality_score'] * (total - 1) + report.data_quality_score) / total
        )
        metrics['avg_processing_time'] = (
            (metrics['avg_processing_time'] * (total - 1) + report.processing_time) / total
        )
    
    def get_quality_metrics(self) -> Dict[str, Any]:
        """Get data quality metrics"""
        
        return {
            'validation_level': self.validation_level.value,
            'total_validations': len(self.validation_history),
            'metrics_by_type': self.data_quality_metrics,
            'recent_validations': [
                {
                    'data_type': report.data_type,
                    'result': report.result.value,
                    'quality_score': report.data_quality_score,
                    'timestamp': report.timestamp.isoformat()
                }
                for report in self.validation_history[-10:]
            ]
        }
    
    async def _validate_trade_format(self, trade_data: Dict[str, Any]) -> List[ValidationError]:
        """Validate trade data format"""
        
        errors = []
        
        required_fields = ['symbol', 'quantity', 'price', 'side', 'timestamp']
        for field in required_fields:
            if field not in trade_data:
                errors.append(ValidationError(
                    field=field,
                    error_type='missing_field',
                    message=f'Required field {field} is missing',
                    severity='critical'
                ))
        
        return errors
    
    async def _validate_trade_logic(self, trade_data: Dict[str, Any]) -> List[ValidationError]:
        """Validate trade business logic"""
        
        errors = []
        
        # Validate side
        side = trade_data.get('side', '').lower()
        if side not in ['buy', 'sell']:
            errors.append(ValidationError(
                field='side',
                error_type='invalid_value',
                message='Side must be "buy" or "sell"',
                severity='critical',
                actual_value=side
            ))
        
        # Validate quantity
        quantity = trade_data.get('quantity')
        if quantity is not None:
            if quantity <= 0:
                errors.append(ValidationError(
                    field='quantity',
                    error_type='invalid_value',
                    message='Quantity must be positive',
                    severity='critical',
                    actual_value=quantity
                ))
        
        return errors
    
    async def _validate_trade_prices(self, trade_data: Dict[str, Any]) -> List[ValidationError]:
        """Validate trade prices against market"""
        
        errors = []
        
        # This would validate trade prices against current market prices
        # Implementation would depend on available market data
        
        return errors
    
    async def _validate_trade_quantities(self, trade_data: Dict[str, Any]) -> List[ValidationError]:
        """Validate trade quantities"""
        
        errors = []
        
        # This would validate quantities against position limits, buying power, etc.
        
        return errors
    
    async def _validate_trade_timing(self, trade_data: Dict[str, Any]) -> List[ValidationError]:
        """Validate trade timing"""
        
        errors = []
        
        # This would validate trade timing against market hours, etc.
        
        return errors
    
    async def _validate_position_format(self, position_data: Dict[str, Any]) -> List[ValidationError]:
        """Validate position data format"""
        
        errors = []
        
        required_fields = ['symbol', 'quantity', 'avg_price']
        for field in required_fields:
            if field not in position_data:
                errors.append(ValidationError(
                    field=field,
                    error_type='missing_field',
                    message=f'Required field {field} is missing',
                    severity='critical'
                ))
        
        return errors
    
    async def _validate_position_consistency(self, position_data: Dict[str, Any]) -> List[ValidationError]:
        """Validate position consistency"""
        
        errors = []
        
        # This would validate position data consistency
        
        return errors
    
    async def _validate_position_risk(self, position_data: Dict[str, Any]) -> List[ValidationError]:
        """Validate position risk parameters"""
        
        errors = []
        
        # This would validate position sizes against risk limits
        
        return errors

# Global validator instance
_data_validator = None

def get_data_validator(validation_level: ValidationLevel = ValidationLevel.STANDARD) -> DataValidator:
    """Get global data validator instance"""
    global _data_validator
    
    if _data_validator is None:
        _data_validator = DataValidator(validation_level)
    
    return _data_validator
