"""
Regulatory Monitoring System

Monitors regulatory changes, compliance requirements, and ensures
the trading bot operates within legal boundaries across all jurisdictions.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json
import aiohttp
from pathlib import Path

from ...core.logger import get_logger

logger = get_logger(__name__)

class RegulatoryJurisdiction(Enum):
    """Regulatory jurisdictions"""
    SEC_US = "sec_us"           # US Securities and Exchange Commission
    FINRA_US = "finra_us"       # Financial Industry Regulatory Authority
    CFTC_US = "cftc_us"         # Commodity Futures Trading Commission
    FCA_UK = "fca_uk"           # Financial Conduct Authority (UK)
    ESMA_EU = "esma_eu"         # European Securities and Markets Authority
    ASIC_AU = "asic_au"         # Australian Securities and Investments Commission
    FSA_JP = "fsa_jp"           # Financial Services Agency (Japan)
    MAS_SG = "mas_sg"           # Monetary Authority of Singapore

class ComplianceStatus(Enum):
    """Compliance status levels"""
    COMPLIANT = "compliant"
    WARNING = "warning"
    VIOLATION = "violation"
    UNDER_REVIEW = "under_review"

class RegulatoryChangeType(Enum):
    """Types of regulatory changes"""
    NEW_RULE = "new_rule"
    RULE_AMENDMENT = "rule_amendment"
    GUIDANCE_UPDATE = "guidance_update"
    ENFORCEMENT_ACTION = "enforcement_action"
    CONSULTATION = "consultation"
    DEADLINE_CHANGE = "deadline_change"

@dataclass
class RegulatoryChange:
    """Represents a regulatory change or update"""
    id: str
    jurisdiction: RegulatoryJurisdiction
    change_type: RegulatoryChangeType
    title: str
    description: str
    effective_date: datetime
    impact_level: str  # 'low', 'medium', 'high', 'critical'
    affected_areas: List[str]
    compliance_deadline: Optional[datetime]
    source_url: str
    status: str
    created_date: datetime

@dataclass
class ComplianceRequirement:
    """A specific compliance requirement"""
    id: str
    jurisdiction: RegulatoryJurisdiction
    category: str
    title: str
    description: str
    mandatory: bool
    deadline: Optional[datetime]
    implementation_status: str
    risk_level: str
    related_regulations: List[str]

@dataclass
class ComplianceAlert:
    """Alert for compliance issues"""
    id: str
    alert_type: str
    severity: str
    title: str
    description: str
    jurisdiction: RegulatoryJurisdiction
    created_date: datetime
    due_date: Optional[datetime]
    status: str
    action_required: str

class RegulatoryMonitor:
    """Monitors regulatory changes and compliance requirements"""
    
    def __init__(self, storage_path: str = "compliance_data"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        self.regulatory_changes: List[RegulatoryChange] = []
        self.compliance_requirements: List[ComplianceRequirement] = []
        self.compliance_alerts: List[ComplianceAlert] = []
        
        # Monitoring configuration
        self.monitored_jurisdictions = [
            RegulatoryJurisdiction.SEC_US,
            RegulatoryJurisdiction.FINRA_US,
            RegulatoryJurisdiction.CFTC_US
        ]
        
        self.monitoring_keywords = [
            "algorithmic trading", "automated trading", "high frequency trading",
            "market making", "risk management", "position limits",
            "reporting requirements", "best execution", "market manipulation",
            "insider trading", "compliance", "surveillance"
        ]
        
        # Compliance framework
        self.compliance_framework = self._initialize_compliance_framework()
        
    def _initialize_compliance_framework(self) -> Dict[str, Any]:
        """Initialize the compliance framework with key requirements"""
        return {
            RegulatoryJurisdiction.SEC_US.value: {
                "position_limits": {
                    "single_security": 0.05,  # 5% of outstanding shares
                    "sector_concentration": 0.25,  # 25% of portfolio
                    "daily_volume": 0.10  # 10% of average daily volume
                },
                "reporting_requirements": {
                    "large_trader_threshold": 2000000,  # $2M in transactions
                    "13f_threshold": 100000000,  # $100M in assets
                    "form_pf_threshold": 150000000  # $150M in assets
                },
                "risk_controls": {
                    "pre_trade_risk_checks": True,
                    "position_monitoring": True,
                    "market_access_controls": True
                }
            },
            RegulatoryJurisdiction.FINRA_US.value: {
                "market_making": {
                    "registration_required": True,
                    "capital_requirements": 1000000,  # $1M minimum
                    "surveillance_systems": True
                },
                "best_execution": {
                    "order_routing_disclosure": True,
                    "execution_quality_reports": True,
                    "regular_review_required": True
                }
            },
            RegulatoryJurisdiction.CFTC_US.value: {
                "derivatives_trading": {
                    "registration_threshold": 5000000,  # $5M notional
                    "clearing_requirements": True,
                    "margin_requirements": True
                }
            }
        }
    
    async def start_monitoring(self):
        """Start continuous regulatory monitoring"""
        logger.info("Starting regulatory monitoring")
        
        # Load existing data
        await self._load_compliance_data()
        
        # Start monitoring tasks
        tasks = [
            self._monitor_regulatory_feeds(),
            self._check_compliance_deadlines(),
            self._update_compliance_status(),
            self._generate_compliance_reports()
        ]
        
        await asyncio.gather(*tasks)
    
    async def _monitor_regulatory_feeds(self):
        """Monitor regulatory feeds for changes"""
        while True:
            try:
                for jurisdiction in self.monitored_jurisdictions:
                    changes = await self._fetch_regulatory_changes(jurisdiction)
                    
                    for change in changes:
                        if await self._is_relevant_change(change):
                            await self._process_regulatory_change(change)
                
                # Sleep before next check
                await asyncio.sleep(3600)  # Check every hour
                
            except Exception as e:
                logger.error(f"Error monitoring regulatory feeds: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _fetch_regulatory_changes(self, jurisdiction: RegulatoryJurisdiction) -> List[RegulatoryChange]:
        """Fetch regulatory changes from official sources"""
        changes = []
        
        try:
            # This would integrate with actual regulatory APIs/RSS feeds
            # For now, simulate some changes
            
            if jurisdiction == RegulatoryJurisdiction.SEC_US:
                changes.extend(await self._fetch_sec_changes())
            elif jurisdiction == RegulatoryJurisdiction.FINRA_US:
                changes.extend(await self._fetch_finra_changes())
            elif jurisdiction == RegulatoryJurisdiction.CFTC_US:
                changes.extend(await self._fetch_cftc_changes())
                
        except Exception as e:
            logger.error(f"Error fetching changes for {jurisdiction.value}: {e}")
        
        return changes
    
    async def _fetch_sec_changes(self) -> List[RegulatoryChange]:
        """Fetch SEC regulatory changes"""
        # This would integrate with SEC.gov APIs or RSS feeds
        # For now, return simulated changes
        
        simulated_changes = [
            RegulatoryChange(
                id=f"sec_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                jurisdiction=RegulatoryJurisdiction.SEC_US,
                change_type=RegulatoryChangeType.GUIDANCE_UPDATE,
                title="Updated Guidance on Algorithmic Trading Risk Controls",
                description="SEC provides updated guidance on risk management controls for algorithmic trading systems",
                effective_date=datetime.now() + timedelta(days=30),
                impact_level="medium",
                affected_areas=["risk_management", "algorithmic_trading"],
                compliance_deadline=datetime.now() + timedelta(days=90),
                source_url="https://www.sec.gov/rules/guidance/example",
                status="published",
                created_date=datetime.now()
            )
        ]
        
        return simulated_changes
    
    async def _fetch_finra_changes(self) -> List[RegulatoryChange]:
        """Fetch FINRA regulatory changes"""
        return []  # Placeholder
    
    async def _fetch_cftc_changes(self) -> List[RegulatoryChange]:
        """Fetch CFTC regulatory changes"""
        return []  # Placeholder
    
    async def _is_relevant_change(self, change: RegulatoryChange) -> bool:
        """Check if a regulatory change is relevant to our operations"""
        
        # Check if any keywords match
        text_to_check = f"{change.title} {change.description}".lower()
        
        for keyword in self.monitoring_keywords:
            if keyword.lower() in text_to_check:
                return True
        
        # Check affected areas
        relevant_areas = [
            "algorithmic_trading", "risk_management", "market_making",
            "position_limits", "reporting", "surveillance"
        ]
        
        for area in change.affected_areas:
            if area.lower() in relevant_areas:
                return True
        
        return False
    
    async def _process_regulatory_change(self, change: RegulatoryChange):
        """Process a relevant regulatory change"""
        
        # Check if we already have this change
        existing_change = next(
            (c for c in self.regulatory_changes if c.id == change.id),
            None
        )
        
        if existing_change:
            return  # Already processed
        
        # Add to our list
        self.regulatory_changes.append(change)
        
        # Assess impact and create compliance requirements
        await self._assess_change_impact(change)
        
        # Create alerts if necessary
        if change.impact_level in ['high', 'critical']:
            await self._create_compliance_alert(change)
        
        # Save updated data
        await self._save_compliance_data()
        
        logger.info(f"Processed regulatory change: {change.title}")
    
    async def _assess_change_impact(self, change: RegulatoryChange):
        """Assess the impact of a regulatory change"""
        
        # Create compliance requirements based on the change
        if "risk management" in change.affected_areas:
            requirement = ComplianceRequirement(
                id=f"req_{change.id}",
                jurisdiction=change.jurisdiction,
                category="risk_management",
                title=f"Implement changes for: {change.title}",
                description=f"Update risk management systems to comply with: {change.description}",
                mandatory=True,
                deadline=change.compliance_deadline,
                implementation_status="pending",
                risk_level=change.impact_level,
                related_regulations=[change.id]
            )
            
            self.compliance_requirements.append(requirement)
        
        if "reporting" in change.affected_areas:
            requirement = ComplianceRequirement(
                id=f"req_report_{change.id}",
                jurisdiction=change.jurisdiction,
                category="reporting",
                title=f"Update reporting for: {change.title}",
                description=f"Modify reporting procedures to comply with: {change.description}",
                mandatory=True,
                deadline=change.compliance_deadline,
                implementation_status="pending",
                risk_level=change.impact_level,
                related_regulations=[change.id]
            )
            
            self.compliance_requirements.append(requirement)
    
    async def _create_compliance_alert(self, change: RegulatoryChange):
        """Create a compliance alert for important changes"""
        
        alert = ComplianceAlert(
            id=f"alert_{change.id}",
            alert_type="regulatory_change",
            severity=change.impact_level,
            title=f"Action Required: {change.title}",
            description=f"New regulatory change requires attention: {change.description}",
            jurisdiction=change.jurisdiction,
            created_date=datetime.now(),
            due_date=change.compliance_deadline,
            status="open",
            action_required="Review and implement necessary changes"
        )
        
        self.compliance_alerts.append(alert)
        
        # Send notification (would integrate with alerting system)
        logger.warning(f"Compliance alert created: {alert.title}")
    
    async def _check_compliance_deadlines(self):
        """Check for upcoming compliance deadlines"""
        while True:
            try:
                upcoming_deadlines = []
                now = datetime.now()
                
                # Check requirements with deadlines in next 30 days
                for requirement in self.compliance_requirements:
                    if (requirement.deadline and 
                        requirement.implementation_status != "completed" and
                        requirement.deadline - now <= timedelta(days=30)):
                        
                        upcoming_deadlines.append(requirement)
                
                # Create alerts for upcoming deadlines
                for requirement in upcoming_deadlines:
                    days_remaining = (requirement.deadline - now).days
                    
                    if days_remaining <= 7:  # One week warning
                        await self._create_deadline_alert(requirement, days_remaining)
                
                await asyncio.sleep(86400)  # Check daily
                
            except Exception as e:
                logger.error(f"Error checking compliance deadlines: {e}")
                await asyncio.sleep(3600)
    
    async def _create_deadline_alert(self, requirement: ComplianceRequirement, days_remaining: int):
        """Create alert for upcoming deadline"""
        
        # Check if alert already exists
        existing_alert = next(
            (a for a in self.compliance_alerts 
             if f"deadline_{requirement.id}" in a.id and a.status == "open"),
            None
        )
        
        if existing_alert:
            return  # Alert already exists
        
        severity = "critical" if days_remaining <= 3 else "high"
        
        alert = ComplianceAlert(
            id=f"deadline_{requirement.id}_{datetime.now().strftime('%Y%m%d')}",
            alert_type="deadline_warning",
            severity=severity,
            title=f"Compliance Deadline Approaching: {requirement.title}",
            description=f"Deadline in {days_remaining} days: {requirement.description}",
            jurisdiction=requirement.jurisdiction,
            created_date=datetime.now(),
            due_date=requirement.deadline,
            status="open",
            action_required="Complete implementation before deadline"
        )
        
        self.compliance_alerts.append(alert)
        logger.warning(f"Deadline alert: {alert.title}")
    
    async def _update_compliance_status(self):
        """Update overall compliance status"""
        while True:
            try:
                # Check current compliance status
                status = await self._calculate_compliance_status()
                
                # Log status changes
                if hasattr(self, '_last_compliance_status'):
                    if status != self._last_compliance_status:
                        logger.info(f"Compliance status changed: {self._last_compliance_status} -> {status}")
                
                self._last_compliance_status = status
                
                await asyncio.sleep(3600)  # Update hourly
                
            except Exception as e:
                logger.error(f"Error updating compliance status: {e}")
                await asyncio.sleep(300)
    
    async def _calculate_compliance_status(self) -> ComplianceStatus:
        """Calculate overall compliance status"""
        
        # Check for any violations
        violations = [alert for alert in self.compliance_alerts 
                     if alert.severity == "critical" and alert.status == "open"]
        
        if violations:
            return ComplianceStatus.VIOLATION
        
        # Check for overdue requirements
        now = datetime.now()
        overdue_requirements = [
            req for req in self.compliance_requirements
            if (req.deadline and req.deadline < now and 
                req.implementation_status != "completed")
        ]
        
        if overdue_requirements:
            return ComplianceStatus.WARNING
        
        # Check for pending high-risk requirements
        high_risk_pending = [
            req for req in self.compliance_requirements
            if (req.risk_level in ["high", "critical"] and 
                req.implementation_status == "pending")
        ]
        
        if high_risk_pending:
            return ComplianceStatus.UNDER_REVIEW
        
        return ComplianceStatus.COMPLIANT
    
    async def _generate_compliance_reports(self):
        """Generate periodic compliance reports"""
        while True:
            try:
                # Generate weekly compliance report
                report = await self._create_compliance_report()
                
                # Save report
                report_path = self.storage_path / f"compliance_report_{datetime.now().strftime('%Y%m%d')}.json"
                with open(report_path, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
                
                logger.info(f"Generated compliance report: {report_path}")
                
                await asyncio.sleep(604800)  # Generate weekly (7 days)
                
            except Exception as e:
                logger.error(f"Error generating compliance reports: {e}")
                await asyncio.sleep(86400)  # Retry daily on error
    
    async def _create_compliance_report(self) -> Dict[str, Any]:
        """Create comprehensive compliance report"""
        
        now = datetime.now()
        
        return {
            "report_date": now.isoformat(),
            "overall_status": (await self._calculate_compliance_status()).value,
            "summary": {
                "total_requirements": len(self.compliance_requirements),
                "completed_requirements": len([r for r in self.compliance_requirements 
                                             if r.implementation_status == "completed"]),
                "pending_requirements": len([r for r in self.compliance_requirements 
                                           if r.implementation_status == "pending"]),
                "open_alerts": len([a for a in self.compliance_alerts if a.status == "open"]),
                "critical_alerts": len([a for a in self.compliance_alerts 
                                      if a.severity == "critical" and a.status == "open"])
            },
            "upcoming_deadlines": [
                {
                    "requirement_id": req.id,
                    "title": req.title,
                    "deadline": req.deadline.isoformat() if req.deadline else None,
                    "days_remaining": (req.deadline - now).days if req.deadline else None
                }
                for req in self.compliance_requirements
                if (req.deadline and req.deadline > now and 
                    req.implementation_status != "completed")
            ][:10],  # Top 10 upcoming deadlines
            "recent_changes": [
                {
                    "id": change.id,
                    "title": change.title,
                    "jurisdiction": change.jurisdiction.value,
                    "impact_level": change.impact_level,
                    "effective_date": change.effective_date.isoformat()
                }
                for change in self.regulatory_changes
                if change.created_date > now - timedelta(days=30)
            ]
        }
    
    async def _load_compliance_data(self):
        """Load existing compliance data"""
        try:
            # Load regulatory changes
            changes_file = self.storage_path / "regulatory_changes.json"
            if changes_file.exists():
                with open(changes_file, 'r') as f:
                    data = json.load(f)
                    # Convert back to objects (simplified)
                    logger.info(f"Loaded {len(data)} regulatory changes")
            
            # Load compliance requirements
            requirements_file = self.storage_path / "compliance_requirements.json"
            if requirements_file.exists():
                with open(requirements_file, 'r') as f:
                    data = json.load(f)
                    logger.info(f"Loaded {len(data)} compliance requirements")
            
            # Load alerts
            alerts_file = self.storage_path / "compliance_alerts.json"
            if alerts_file.exists():
                with open(alerts_file, 'r') as f:
                    data = json.load(f)
                    logger.info(f"Loaded {len(data)} compliance alerts")
                    
        except Exception as e:
            logger.error(f"Error loading compliance data: {e}")
    
    async def _save_compliance_data(self):
        """Save compliance data to storage"""
        try:
            # Save regulatory changes
            changes_file = self.storage_path / "regulatory_changes.json"
            with open(changes_file, 'w') as f:
                json.dump([change.__dict__ for change in self.regulatory_changes], 
                         f, indent=2, default=str)
            
            # Save compliance requirements
            requirements_file = self.storage_path / "compliance_requirements.json"
            with open(requirements_file, 'w') as f:
                json.dump([req.__dict__ for req in self.compliance_requirements], 
                         f, indent=2, default=str)
            
            # Save alerts
            alerts_file = self.storage_path / "compliance_alerts.json"
            with open(alerts_file, 'w') as f:
                json.dump([alert.__dict__ for alert in self.compliance_alerts], 
                         f, indent=2, default=str)
                         
        except Exception as e:
            logger.error(f"Error saving compliance data: {e}")
    
    def get_compliance_summary(self) -> Dict[str, Any]:
        """Get current compliance summary"""
        
        now = datetime.now()
        
        return {
            "overall_status": self._last_compliance_status.value if hasattr(self, '_last_compliance_status') else "unknown",
            "monitored_jurisdictions": [j.value for j in self.monitored_jurisdictions],
            "total_requirements": len(self.compliance_requirements),
            "open_alerts": len([a for a in self.compliance_alerts if a.status == "open"]),
            "upcoming_deadlines": len([
                req for req in self.compliance_requirements
                if (req.deadline and req.deadline > now and 
                    req.deadline - now <= timedelta(days=30) and
                    req.implementation_status != "completed")
            ]),
            "recent_changes": len([
                change for change in self.regulatory_changes
                if change.created_date > now - timedelta(days=7)
            ])
        }
