# Unified Async API Framework

The unified async API framework provides a comprehensive, production-ready interface for Webull trading operations. It combines all functionality into a single, easy-to-use API with advanced features like rate limiting, caching, error handling, and real-time data streaming.

## Features

### 🚀 Core Features
- **Fully Asynchronous**: Built with Python's asyncio for high performance
- **Unified Interface**: Single API client for all operations
- **Type Safety**: Complete type hints throughout
- **Error Handling**: Robust error handling with retry logic and circuit breakers
- **Rate Limiting**: Intelligent rate limiting (3 req/s for trading, 30 req/s for data)
- **Caching**: Redis-based caching for improved performance
- **Real-time Data**: WebSocket support for live market data
- **Paper Trading**: Full paper trading simulation support

### 🛡️ Production Ready
- **Connection Pooling**: Efficient HTTP connection management
- **Automatic Reconnection**: WebSocket auto-reconnection with exponential backoff
- **Health Monitoring**: Comprehensive health checks and metrics
- **Structured Logging**: Detailed logging with context
- **Session Management**: Automatic token refresh and session handling

## Quick Start

### Basic Usage

```python
import asyncio
from trading_bot.api import WebullAPI
from trading_bot.models.enums import OrderSide, OrderType
from trading_bot.models.orders import OrderRequest
from decimal import Decimal

async def main():
    # Initialize API (paper trading by default)
    async with WebullAPI() as api:
        # Login
        await api.login("username", "password")
        
        # Get account info
        account = await api.get_account_summary()
        print(f"Account Value: ${account.balance.total_value}")
        
        # Get real-time quote
        quote = await api.get_quote("AAPL")
        print(f"AAPL: ${quote.price}")
        
        # Place an order
        order_request = OrderRequest(
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=10,
            price=Decimal("150.00")
        )
        
        order = await api.place_order(order_request)
        print(f"Order placed: {order.id}")

asyncio.run(main())
```

### Real-time Data Streaming

```python
async def quote_callback(quote):
    print(f"{quote.symbol}: ${quote.price}")

async def main():
    async with WebullAPI() as api:
        await api.login()
        
        # Connect WebSocket
        await api.connect_websocket()
        
        # Subscribe to real-time quotes
        await api.subscribe_quotes(
            ["AAPL", "MSFT", "GOOGL"], 
            callback=quote_callback
        )
        
        # Keep running
        await asyncio.sleep(60)

asyncio.run(main())
```

## API Reference

### Authentication

```python
# Login with credentials
await api.login("username", "password")

# Handle MFA if required
await api.verify_mfa("123456")

# Check authentication status
if api.is_authenticated:
    print("Authenticated successfully")

# Logout
await api.logout()
```

### Market Data

```python
# Get single quote
quote = await api.get_quote("AAPL")

# Get multiple quotes
quotes = await api.get_quotes(["AAPL", "MSFT", "GOOGL"])

# Get historical bars
bars = await api.get_bars("AAPL", interval="1m", count=100)

# Search stocks
results = await api.search_stocks("technology")

# Get market hours
hours = await api.get_market_hours()

# Get market movers
gainers = await api.get_movers("up", limit=10)

# Get news
news = await api.get_news("AAPL", limit=5)
```

### Trading Operations

```python
# Get account summary
account = await api.get_account_summary()

# Get portfolio
portfolio = await api.get_portfolio()

# Get positions
positions = await api.get_positions()

# Get orders
orders = await api.get_orders()

# Place order
order_request = OrderRequest(
    symbol="AAPL",
    side=OrderSide.BUY,
    order_type=OrderType.MARKET,
    quantity=100
)
order = await api.place_order(order_request)

# Cancel order
await api.cancel_order(order.id)

# Modify order
modified_order = await api.modify_order(
    order.id, 
    quantity=50, 
    price=Decimal("155.00")
)

# Get buying power
buying_power = await api.get_buying_power()
```

### Paper Trading

```python
# Initialize in paper trading mode
api = WebullAPI(paper_trading=True)

# Reset paper account
await api.reset_paper_account(initial_balance=100000.0)

# Get performance metrics
performance = await api.get_paper_performance()
print(f"Total P&L: ${performance['total_pnl']}")
print(f"Win Rate: {performance['win_rate']:.1%}")

# Simulate order scenarios
scenarios = [
    {"name": "Bull Market", "price": 160.0, "volatility": 0.01},
    {"name": "Bear Market", "price": 140.0, "volatility": 0.03},
]
results = await api.simulate_order_scenarios(order_request, scenarios)
```

### WebSocket Real-time Data

```python
# Connect to WebSocket
await api.connect_websocket()

# Subscribe to quotes
await api.subscribe_quotes(["AAPL", "MSFT"], callback=quote_handler)

# Unsubscribe
await api.unsubscribe_quotes(["AAPL"])

# Disconnect
await api.disconnect_websocket()

# Check connection status
if api.is_websocket_connected:
    print("WebSocket connected")
```

### Utility Functions

```python
# Health check
health = await api.health_check()
print(f"API Status: {health['status']}")

# Cache management
cache_stats = await api.get_cache_stats()
cleared = await api.clear_cache("quotes")

# Rate limit status
limits = await api.client.get_rate_limit_status()
```

## Configuration

The API uses environment variables for configuration. Key settings:

```bash
# Webull credentials
WEBULL_USERNAME=your_username
WEBULL_PASSWORD=your_password
WEBULL_PAPER_TRADING=true

# Database connections
REDIS_HOST=localhost
REDIS_PORT=6379
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Rate limiting
WEBULL_MAX_REQUESTS_PER_MINUTE=60
WEBULL_REQUEST_DELAY=1.0

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log
```

## Error Handling

The framework provides comprehensive error handling:

```python
from trading_bot.utils.exceptions import (
    APIConnectionError,
    AuthenticationError,
    APIRateLimitError,
    OrderValidationError,
    WebSocketError
)

try:
    await api.place_order(order_request)
except OrderValidationError as e:
    print(f"Order validation failed: {e.message}")
except APIRateLimitError as e:
    print(f"Rate limit exceeded, retry in {e.reset_time}s")
except APIConnectionError as e:
    print(f"Connection error: {e.message}")
```

## Advanced Features

### Custom Rate Limiting

```python
from trading_bot.utils.rate_limiter import rate_limiter
from trading_bot.models.enums import RateLimitType

# Set custom limits
rate_limiter.set_limit(RateLimitType.TRADING, 5)  # 5 req/s for trading

# Check status
status = rate_limiter.get_status(RateLimitType.DATA)
print(f"Remaining: {status['remaining']}")
```

### Circuit Breaker

```python
from trading_bot.utils.retry import retry_manager

# Reset circuit breaker
retry_manager.reset_circuit_breaker("webull_api")

# Check status
status = retry_manager.get_circuit_breaker_status("webull_api")
print(f"Circuit breaker state: {status['state']}")
```

### Structured Logging

```python
from trading_bot.utils.logger import get_structured_logger

logger = get_structured_logger(__name__)

# Log with context
logger.order_event(
    "placed",
    order_id="12345",
    symbol="AAPL",
    side="BUY",
    quantity=100
)

logger.market_data_event(
    "quote_received",
    symbol="AAPL",
    price=150.00,
    data_source="webull"
)
```

## Migration from Legacy Client

If you're using the old `WebullClient`, migration is straightforward:

```python
# Old way
from trading_bot.api.webull_client import WebullClient
client = WebullClient()

# New way
from trading_bot.api import WebullAPI
api = WebullAPI()
```

The old client is still available for backward compatibility but is deprecated.

## Performance Tips

1. **Use caching**: Enable caching for frequently accessed data
2. **Batch requests**: Use `get_quotes()` instead of multiple `get_quote()` calls
3. **Connection pooling**: Reuse the same API instance
4. **WebSocket for real-time**: Use WebSocket for live data instead of polling
5. **Paper trading**: Use paper trading for development and testing

## Support

For issues and questions:
- Check the logs for detailed error information
- Use the health check endpoint to diagnose problems
- Review rate limit status if experiencing delays
- Check WebSocket connection status for real-time data issues
