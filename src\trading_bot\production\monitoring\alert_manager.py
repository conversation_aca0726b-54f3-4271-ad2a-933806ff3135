"""
Alert management system for AI Trading Bot.

This module provides comprehensive alert management including
notification routing, escalation, and integration with external
notification systems for production monitoring.
"""

import asyncio
import json
import logging
import smtplib
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
import aiohttp
import telegram

from ...core.config import Config
from ...utils.logger import get_logger

logger = get_logger(__name__)


class NotificationChannel(Enum):
    """Notification channel types."""
    EMAIL = "email"
    SLACK = "slack"
    TELEGRAM = "telegram"
    WEBHOOK = "webhook"
    SMS = "sms"


class AlertPriority(Enum):
    """Alert priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class AlertRule:
    """Alert rule configuration."""
    name: str
    condition: str
    threshold: float
    priority: AlertPriority
    channels: List[NotificationChannel]
    cooldown_minutes: int = 5
    escalation_minutes: int = 30
    auto_resolve: bool = False
    enabled: bool = True


@dataclass
class NotificationConfig:
    """Notification configuration for different channels."""
    email: Dict[str, Any] = field(default_factory=dict)
    slack: Dict[str, Any] = field(default_factory=dict)
    telegram: Dict[str, Any] = field(default_factory=dict)
    webhook: Dict[str, Any] = field(default_factory=dict)
    sms: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Alert:
    """Enhanced alert with notification tracking."""
    id: str
    rule_name: str
    priority: AlertPriority
    title: str
    message: str
    component: str
    metric_name: str
    current_value: float
    threshold_value: float
    timestamp: datetime = field(default_factory=datetime.utcnow)
    acknowledged: bool = False
    resolved: bool = False
    notifications_sent: List[str] = field(default_factory=list)
    escalated: bool = False
    auto_resolved: bool = False


class AlertManager:
    """
    Comprehensive alert management system.
    
    Manages alert lifecycle including:
    - Alert rule evaluation
    - Notification routing and delivery
    - Alert escalation and de-escalation
    - Integration with external systems
    - Alert suppression and grouping
    - Performance monitoring of alert system
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.notification_config = NotificationConfig()
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        
        # Notification clients
        self.telegram_bot = None
        self.email_client = None
        
        # Alert processing
        self.is_running = False
        self.processing_tasks: List[asyncio.Task] = []
        
        # Load configuration
        self._load_alert_configuration()
        self._setup_notification_clients()
    
    def _load_alert_configuration(self):
        """Load alert rules and notification configuration."""
        # Default alert rules
        self.alert_rules = {
            'daily_loss': AlertRule(
                name='daily_loss',
                condition='daily_pnl < -threshold',
                threshold=1000.0,  # $1000 daily loss
                priority=AlertPriority.CRITICAL,
                channels=[NotificationChannel.EMAIL, NotificationChannel.TELEGRAM],
                cooldown_minutes=5,
                escalation_minutes=15
            ),
            'high_cpu': AlertRule(
                name='high_cpu',
                condition='cpu_usage > threshold',
                threshold=80.0,  # 80% CPU
                priority=AlertPriority.HIGH,
                channels=[NotificationChannel.EMAIL],
                cooldown_minutes=10,
                escalation_minutes=30
            ),
            'high_memory': AlertRule(
                name='high_memory',
                condition='memory_usage > threshold',
                threshold=85.0,  # 85% memory
                priority=AlertPriority.CRITICAL,
                channels=[NotificationChannel.EMAIL, NotificationChannel.TELEGRAM],
                cooldown_minutes=5,
                escalation_minutes=20
            ),
            'api_errors': AlertRule(
                name='api_errors',
                condition='error_rate > threshold',
                threshold=0.05,  # 5% error rate
                priority=AlertPriority.HIGH,
                channels=[NotificationChannel.EMAIL],
                cooldown_minutes=15,
                escalation_minutes=45
            ),
            'model_accuracy': AlertRule(
                name='model_accuracy',
                condition='model_accuracy < threshold',
                threshold=0.60,  # 60% accuracy
                priority=AlertPriority.HIGH,
                channels=[NotificationChannel.EMAIL, NotificationChannel.SLACK],
                cooldown_minutes=30,
                escalation_minutes=60
            ),
            'position_breach': AlertRule(
                name='position_breach',
                condition='position_size > threshold',
                threshold=0.05,  # 5% position size
                priority=AlertPriority.CRITICAL,
                channels=[NotificationChannel.EMAIL, NotificationChannel.TELEGRAM],
                cooldown_minutes=1,
                escalation_minutes=5
            )
        }
        
        # Load notification configuration
        self.notification_config = NotificationConfig(
            email={
                'smtp_server': self.config.get('alerts.email.smtp_server', 'smtp.gmail.com'),
                'smtp_port': self.config.get('alerts.email.smtp_port', 587),
                'username': self.config.get('alerts.email.username', ''),
                'password': self.config.get('alerts.email.password', ''),
                'recipients': self.config.get('alerts.email.recipients', [])
            },
            telegram={
                'bot_token': self.config.get('alerts.telegram.bot_token', ''),
                'chat_id': self.config.get('alerts.telegram.chat_id', '')
            },
            slack={
                'webhook_url': self.config.get('alerts.slack.webhook_url', ''),
                'channel': self.config.get('alerts.slack.channel', '#alerts')
            }
        )
    
    def _setup_notification_clients(self):
        """Set up notification clients."""
        try:
            # Setup Telegram bot
            if self.notification_config.telegram.get('bot_token'):
                self.telegram_bot = telegram.Bot(
                    token=self.notification_config.telegram['bot_token']
                )
            
            logger.info("Notification clients setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup notification clients: {e}")
    
    async def start(self):
        """Start alert manager."""
        if self.is_running:
            logger.warning("Alert manager already running")
            return
        
        self.is_running = True
        logger.info("Starting alert manager...")
        
        # Start processing tasks
        self.processing_tasks = [
            asyncio.create_task(self._process_alerts()),
            asyncio.create_task(self._check_escalations()),
            asyncio.create_task(self._cleanup_old_alerts()),
        ]
        
        logger.info("Alert manager started successfully")
    
    async def stop(self):
        """Stop alert manager."""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("Stopping alert manager...")
        
        # Cancel processing tasks
        for task in self.processing_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.processing_tasks, return_exceptions=True)
        
        self.processing_tasks.clear()
        logger.info("Alert manager stopped")
    
    async def create_alert(self, rule_name: str, title: str, message: str,
                         component: str, metric_name: str, current_value: float,
                         threshold_value: float) -> Optional[Alert]:
        """Create a new alert."""
        if rule_name not in self.alert_rules:
            logger.error(f"Unknown alert rule: {rule_name}")
            return None
        
        rule = self.alert_rules[rule_name]
        if not rule.enabled:
            return None
        
        # Check for existing alert with cooldown
        existing_alert_key = f"{rule_name}_{component}_{metric_name}"
        existing_alert = self.active_alerts.get(existing_alert_key)
        
        if existing_alert:
            # Check cooldown period
            time_since_last = datetime.utcnow() - existing_alert.timestamp
            if time_since_last.total_seconds() < rule.cooldown_minutes * 60:
                # Update existing alert values
                existing_alert.current_value = current_value
                existing_alert.timestamp = datetime.utcnow()
                return existing_alert
        
        # Create new alert
        alert_id = f"{rule_name}_{component}_{metric_name}_{int(datetime.utcnow().timestamp())}"
        
        alert = Alert(
            id=alert_id,
            rule_name=rule_name,
            priority=rule.priority,
            title=title,
            message=message,
            component=component,
            metric_name=metric_name,
            current_value=current_value,
            threshold_value=threshold_value
        )
        
        # Store alert
        self.active_alerts[existing_alert_key] = alert
        self.alert_history.append(alert)
        
        logger.warning(f"Alert created: {title} - {message}")
        
        # Queue for notification processing
        await self._queue_alert_for_processing(alert)
        
        return alert
    
    async def _queue_alert_for_processing(self, alert: Alert):
        """Queue alert for notification processing."""
        # This would typically use a message queue in production
        # For now, process immediately
        await self._send_notifications(alert)
    
    async def _process_alerts(self):
        """Process alerts and send notifications."""
        while self.is_running:
            try:
                # Process any pending alerts
                for alert in list(self.active_alerts.values()):
                    if not alert.notifications_sent:
                        await self._send_notifications(alert)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error processing alerts: {e}")
                await asyncio.sleep(30)
    
    async def _send_notifications(self, alert: Alert):
        """Send notifications for an alert."""
        rule = self.alert_rules.get(alert.rule_name)
        if not rule:
            return
        
        try:
            for channel in rule.channels:
                if channel in alert.notifications_sent:
                    continue
                
                success = False
                
                if channel == NotificationChannel.EMAIL:
                    success = await self._send_email_notification(alert)
                elif channel == NotificationChannel.TELEGRAM:
                    success = await self._send_telegram_notification(alert)
                elif channel == NotificationChannel.SLACK:
                    success = await self._send_slack_notification(alert)
                elif channel == NotificationChannel.WEBHOOK:
                    success = await self._send_webhook_notification(alert)
                
                if success:
                    alert.notifications_sent.append(channel.value)
                    logger.info(f"Notification sent via {channel.value} for alert {alert.id}")
                else:
                    logger.error(f"Failed to send notification via {channel.value} for alert {alert.id}")
        
        except Exception as e:
            logger.error(f"Error sending notifications for alert {alert.id}: {e}")
    
    async def _send_email_notification(self, alert: Alert) -> bool:
        """Send email notification."""
        try:
            email_config = self.notification_config.email
            
            if not email_config.get('username') or not email_config.get('recipients'):
                return False
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = email_config['username']
            msg['To'] = ', '.join(email_config['recipients'])
            msg['Subject'] = f"[{alert.priority.value.upper()}] {alert.title}"
            
            # Email body
            body = f"""
Alert Details:
- Priority: {alert.priority.value.upper()}
- Component: {alert.component}
- Metric: {alert.metric_name}
- Current Value: {alert.current_value}
- Threshold: {alert.threshold_value}
- Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}

Message: {alert.message}

Alert ID: {alert.id}
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return False
    
    async def _send_telegram_notification(self, alert: Alert) -> bool:
        """Send Telegram notification."""
        try:
            if not self.telegram_bot or not self.notification_config.telegram.get('chat_id'):
                return False
            
            # Format message
            priority_emoji = {
                AlertPriority.LOW: "ℹ️",
                AlertPriority.MEDIUM: "⚠️",
                AlertPriority.HIGH: "🚨",
                AlertPriority.CRITICAL: "🔥",
                AlertPriority.EMERGENCY: "💥"
            }
            
            emoji = priority_emoji.get(alert.priority, "⚠️")
            
            message = f"""
{emoji} *{alert.priority.value.upper()} ALERT*

*{alert.title}*

Component: `{alert.component}`
Metric: `{alert.metric_name}`
Current: `{alert.current_value}`
Threshold: `{alert.threshold_value}`
Time: `{alert.timestamp.strftime('%H:%M:%S UTC')}`

{alert.message}
            """
            
            await self.telegram_bot.send_message(
                chat_id=self.notification_config.telegram['chat_id'],
                text=message,
                parse_mode='Markdown'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")
            return False
    
    async def _send_slack_notification(self, alert: Alert) -> bool:
        """Send Slack notification."""
        try:
            webhook_url = self.notification_config.slack.get('webhook_url')
            if not webhook_url:
                return False
            
            # Format Slack message
            color = {
                AlertPriority.LOW: "good",
                AlertPriority.MEDIUM: "warning",
                AlertPriority.HIGH: "danger",
                AlertPriority.CRITICAL: "danger",
                AlertPriority.EMERGENCY: "danger"
            }.get(alert.priority, "warning")
            
            payload = {
                "channel": self.notification_config.slack.get('channel', '#alerts'),
                "username": "Trading Bot Alerts",
                "icon_emoji": ":warning:",
                "attachments": [
                    {
                        "color": color,
                        "title": f"{alert.priority.value.upper()} Alert: {alert.title}",
                        "text": alert.message,
                        "fields": [
                            {"title": "Component", "value": alert.component, "short": True},
                            {"title": "Metric", "value": alert.metric_name, "short": True},
                            {"title": "Current Value", "value": str(alert.current_value), "short": True},
                            {"title": "Threshold", "value": str(alert.threshold_value), "short": True}
                        ],
                        "timestamp": int(alert.timestamp.timestamp())
                    }
                ]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    return response.status == 200
            
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {e}")
            return False
    
    async def _send_webhook_notification(self, alert: Alert) -> bool:
        """Send webhook notification."""
        try:
            webhook_url = self.notification_config.webhook.get('url')
            if not webhook_url:
                return False
            
            payload = {
                "alert_id": alert.id,
                "rule_name": alert.rule_name,
                "priority": alert.priority.value,
                "title": alert.title,
                "message": alert.message,
                "component": alert.component,
                "metric_name": alert.metric_name,
                "current_value": alert.current_value,
                "threshold_value": alert.threshold_value,
                "timestamp": alert.timestamp.isoformat()
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    return response.status == 200
            
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")
            return False
