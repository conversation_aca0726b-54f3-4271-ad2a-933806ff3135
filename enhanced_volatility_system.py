#!/usr/bin/env python3
"""
Enhanced Volatility System with Premium Trading Targets
Integrates high-quality stocks/ETFs for better ML training and trading
"""
import asyncio
import sys
import os
import joblib
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import yfinance as yf
import json

@dataclass
class VolatilityOpportunity:
    """Enhanced volatility opportunity with market context"""
    symbol: str
    strategy_type: str
    entry_price: float
    target_price: float
    stop_loss: float
    daily_volatility: float
    volume_ratio: float
    confidence: float
    kelly_fraction: float
    position_size: int
    expected_return: float
    risk_reward_ratio: float
    signals: Dict[str, float]
    timestamp: datetime
    market_cap_category: str = "unknown"  # large, mid, small
    sector: str = "unknown"
    ml_features: Dict[str, float] = None

class PremiumVolatilityHunter:
    """
    Enhanced volatility hunter with premium trading targets
    Focuses on high-quality, liquid, predictable instruments
    """
    
    def __init__(self):
        # 🔥 PREMIUM TRADING TARGETS - High Quality, High Volume, Predictable
        self.premium_targets = {
            # Major ETFs - Excellent for ML training (predictable patterns)
            'etf_majors': {
                'SPY': {'name': 'S&P 500 ETF', 'category': 'large', 'sector': 'broad_market'},
                'QQQ': {'name': 'Nasdaq-100 ETF', 'category': 'large', 'sector': 'tech'},
                'IWM': {'name': 'Russell 2000 ETF', 'category': 'small', 'sector': 'small_cap'},
                'DIA': {'name': 'Dow Jones ETF', 'category': 'large', 'sector': 'blue_chip'}
            },
            
            # FAANG + Mega Tech - Massive liquidity, news-driven volatility
            'mega_tech': {
                'AAPL': {'name': 'Apple', 'category': 'large', 'sector': 'tech'},
                'MSFT': {'name': 'Microsoft', 'category': 'large', 'sector': 'tech'},
                'GOOGL': {'name': 'Alphabet', 'category': 'large', 'sector': 'tech'},
                'AMZN': {'name': 'Amazon', 'category': 'large', 'sector': 'tech'},
                'META': {'name': 'Meta', 'category': 'large', 'sector': 'tech'},
                'TSLA': {'name': 'Tesla', 'category': 'large', 'sector': 'automotive_tech'}
            },
            
            # AI/Chip Sector - High growth, high volatility, correlated movements
            'ai_chips': {
                'NVDA': {'name': 'NVIDIA', 'category': 'large', 'sector': 'semiconductors'},
                'AMD': {'name': 'AMD', 'category': 'large', 'sector': 'semiconductors'},
                'SMCI': {'name': 'Super Micro', 'category': 'mid', 'sector': 'semiconductors'},
                'ARM': {'name': 'Arm Holdings', 'category': 'mid', 'sector': 'semiconductors'},
                'INTC': {'name': 'Intel', 'category': 'large', 'sector': 'semiconductors'}
            },
            
            # Volatility Products - Direct volatility exposure
            'volatility': {
                'UVXY': {'name': 'Ultra VIX Short Term', 'category': 'etf', 'sector': 'volatility'},
                'VIXY': {'name': 'VIX Short Term', 'category': 'etf', 'sector': 'volatility'},
                'VXX': {'name': 'VIX ETN', 'category': 'etf', 'sector': 'volatility'}
            },
            
            # Leveraged ETFs - High intraday movement
            'leveraged': {
                'TQQQ': {'name': '3x Nasdaq Bull', 'category': 'etf', 'sector': 'leveraged_tech'},
                'SQQQ': {'name': '3x Nasdaq Bear', 'category': 'etf', 'sector': 'leveraged_tech'},
                'SOXL': {'name': '3x Semiconductor Bull', 'category': 'etf', 'sector': 'leveraged_semi'},
                'SOXS': {'name': '3x Semiconductor Bear', 'category': 'etf', 'sector': 'leveraged_semi'},
                'UPRO': {'name': '3x S&P Bull', 'category': 'etf', 'sector': 'leveraged_broad'},
                'SPXU': {'name': '3x S&P Bear', 'category': 'etf', 'sector': 'leveraged_broad'}
            }
        }
        
        # Flatten into single watchlist with metadata
        self.enhanced_watchlist = {}
        for category, symbols in self.premium_targets.items():
            for symbol, info in symbols.items():
                self.enhanced_watchlist[symbol] = {
                    'category': category,
                    'market_cap': info['category'],
                    'sector': info['sector'],
                    'name': info['name']
                }
        
        # Strategy parameters optimized for quality targets
        self.min_daily_range = 0.02  # 2% minimum (lower for quality stocks)
        self.max_daily_range = 0.20  # 20% maximum
        self.min_volume_ratio = 1.2  # Lower threshold for liquid stocks
        self.gap_fill_probability = 0.75  # Conservative for quality stocks
        
        # ML model storage
        self.models = {}
        self.feature_names = []
        self.model_dir = "models/volatility"
        self._load_models()
        
        print(f"✅ Enhanced system initialized with {len(self.enhanced_watchlist)} premium targets")
    
    def _load_models(self):
        """Load trained ML models"""
        try:
            if os.path.exists(f"{self.model_dir}/random_forest.pkl"):
                self.models['random_forest'] = joblib.load(f"{self.model_dir}/random_forest.pkl")
                print("✅ Loaded Random Forest model")
            
            if os.path.exists(f"{self.model_dir}/feature_names.pkl"):
                self.feature_names = joblib.load(f"{self.model_dir}/feature_names.pkl")
                print(f"✅ Loaded {len(self.feature_names)} feature names")
                
        except Exception as e:
            print(f"⚠️ Error loading models: {e}")
    
    def scan_for_opportunities(self, 
                             additional_symbols: List[str] = None,
                             portfolio_value: float = 10000,
                             focus_categories: List[str] = None) -> List[VolatilityOpportunity]:
        """
        Enhanced scan with category filtering and ML integration
        
        Args:
            focus_categories: Filter by category ['etf_majors', 'mega_tech', 'ai_chips', etc.]
        """
        # Determine symbols to scan
        if focus_categories:
            symbols_to_scan = []
            for category in focus_categories:
                if category in self.premium_targets:
                    symbols_to_scan.extend(list(self.premium_targets[category].keys()))
        else:
            symbols_to_scan = list(self.enhanced_watchlist.keys())
        
        # Add any additional symbols
        if additional_symbols:
            symbols_to_scan.extend(additional_symbols)
        
        opportunities = []
        
        print(f"🔍 Scanning {len(symbols_to_scan)} premium targets for volatility opportunities...")
        
        for symbol in symbols_to_scan:
            try:
                # Get market metadata
                symbol_info = self.enhanced_watchlist.get(symbol, {})
                
                # Get data from Yahoo Finance
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="2mo", interval="1d")
                
                if hist.empty or len(hist) < 30:
                    continue
                
                # Get current price
                info = ticker.info
                current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
                
                if current_price == 0:
                    current_price = hist['Close'].iloc[-1]
                
                # Calculate enhanced technical indicators
                df = self._calculate_enhanced_indicators(hist)
                
                # Create ML features
                ml_features = self._create_ml_features(symbol, df)
                
                # Get ML prediction
                ml_confidence = self._get_ml_prediction(ml_features) if self.models else 0.5
                
                # Check each strategy with enhanced logic
                opportunities_found = []
                
                # 1. Enhanced Gap Fill Strategy
                gap_opp = self._check_enhanced_gap_fill(symbol, current_price, df, portfolio_value, symbol_info, ml_features, ml_confidence)
                if gap_opp:
                    opportunities_found.append(gap_opp)
                
                # 2. Enhanced Momentum Strategy (for quality stocks)
                momentum_opp = self._check_momentum_breakout(symbol, current_price, df, portfolio_value, symbol_info, ml_features, ml_confidence)
                if momentum_opp:
                    opportunities_found.append(momentum_opp)
                
                # 3. Enhanced Mean Reversion (for ETFs)
                if symbol_info.get('category') in ['etf_majors', 'leveraged']:
                    reversion_opp = self._check_mean_reversion(symbol, current_price, df, portfolio_value, symbol_info, ml_features, ml_confidence)
                    if reversion_opp:
                        opportunities_found.append(reversion_opp)
                
                opportunities.extend(opportunities_found)
                
            except Exception as e:
                print(f"Error scanning {symbol}: {e}")
                continue
        
        # Enhanced sorting - prioritize by combined score
        opportunities.sort(key=lambda x: (x.confidence * x.risk_reward_ratio), reverse=True)
        
        print(f"✅ Found {len(opportunities)} enhanced opportunities")
        return opportunities[:15]  # Return top 15
    
    def _calculate_enhanced_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate enhanced technical indicators optimized for quality stocks"""
        # Price movement
        df['daily_range'] = (df['High'] - df['Low']) / df['Open']
        df['daily_return'] = df['Close'].pct_change()
        df['gap'] = df['Open'] / df['Close'].shift(1) - 1
        
        # Enhanced volume analysis
        df['volume_sma_10'] = df['Volume'].rolling(10).mean()
        df['volume_sma_30'] = df['Volume'].rolling(30).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma_30']
        df['volume_trend'] = df['volume_sma_10'] / df['volume_sma_30']
        
        # Multiple timeframe RSI
        df['rsi_14'] = self._calculate_rsi(df['Close'], 14)
        df['rsi_7'] = self._calculate_rsi(df['Close'], 7)
        df['rsi_30'] = self._calculate_rsi(df['Close'], 30)
        
        # Enhanced Bollinger Bands
        for period in [20, 10]:
            df[f'bb_middle_{period}'] = df['Close'].rolling(period).mean()
            df[f'bb_std_{period}'] = df['Close'].rolling(period).std()
            df[f'bb_upper_{period}'] = df[f'bb_middle_{period}'] + (2 * df[f'bb_std_{period}'])
            df[f'bb_lower_{period}'] = df[f'bb_middle_{period}'] - (2 * df[f'bb_std_{period}'])
            df[f'bb_position_{period}'] = (df['Close'] - df[f'bb_lower_{period}']) / (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}'])
        
        # Enhanced volatility measures
        df['atr_14'] = self._calculate_atr(df, 14)
        df['atr_7'] = self._calculate_atr(df, 7)
        df['volatility_ratio'] = df['atr_7'] / df['atr_14']
        
        # Momentum indicators
        df['price_momentum_5'] = df['Close'] / df['Close'].shift(5) - 1
        df['price_momentum_10'] = df['Close'] / df['Close'].shift(10) - 1
        
        # Support/Resistance with multiple timeframes
        df['resistance_20'] = df['High'].rolling(20).max()
        df['support_20'] = df['Low'].rolling(20).min()
        df['resistance_10'] = df['High'].rolling(10).max()
        df['support_10'] = df['Low'].rolling(10).min()
        
        return df
    
    def _create_ml_features(self, symbol: str, df: pd.DataFrame) -> Dict[str, float]:
        """Create comprehensive ML features for prediction"""
        if len(df) < 30:
            return {}
        
        latest = df.iloc[-1]
        prev = df.iloc[-2] if len(df) > 1 else latest
        
        features = {
            # Price features
            'returns_1d': latest['daily_return'] if not pd.isna(latest['daily_return']) else 0,
            'returns_5d': df['Close'].pct_change(5).iloc[-1] if len(df) > 5 else 0,
            'gap': latest['gap'] if not pd.isna(latest['gap']) else 0,
            'gap_abs': abs(latest['gap']) if not pd.isna(latest['gap']) else 0,
            'intraday_range': latest['daily_range'] if not pd.isna(latest['daily_range']) else 0,
            
            # Volume features
            'volume_ratio': latest['volume_ratio'] if not pd.isna(latest['volume_ratio']) else 1,
            'volume_trend': latest['volume_trend'] if not pd.isna(latest['volume_trend']) else 1,
            'volume_spike': 1 if latest['volume_ratio'] > 2 else 0,
            
            # Technical indicators
            'rsi_14': latest['rsi_14'] if not pd.isna(latest['rsi_14']) else 50,
            'rsi_7': latest['rsi_7'] if not pd.isna(latest['rsi_7']) else 50,
            'rsi_oversold': 1 if latest['rsi_14'] < 30 else 0,
            'rsi_overbought': 1 if latest['rsi_14'] > 70 else 0,
            
            # Bollinger Bands
            'bb_position_20': latest['bb_position_20'] if not pd.isna(latest['bb_position_20']) else 0.5,
            'bb_position_10': latest['bb_position_10'] if not pd.isna(latest['bb_position_10']) else 0.5,
            
            # Volatility
            'atr_ratio': latest['atr_14'] / latest['Close'] if not pd.isna(latest['atr_14']) else 0.02,
            'volatility_ratio': latest['volatility_ratio'] if not pd.isna(latest['volatility_ratio']) else 1,
            
            # Momentum
            'price_momentum_5': latest['price_momentum_5'] if not pd.isna(latest['price_momentum_5']) else 0,
            'price_momentum_10': latest['price_momentum_10'] if not pd.isna(latest['price_momentum_10']) else 0,
            
            # Pattern features
            'near_resistance': 1 if latest['Close'] > latest['resistance_20'] * 0.98 else 0,
            'near_support': 1 if latest['Close'] < latest['support_20'] * 1.02 else 0,
            
            # Market context (symbol metadata)
            'is_etf': 1 if 'etf' in self.enhanced_watchlist.get(symbol, {}).get('category', '') else 0,
            'is_tech': 1 if 'tech' in self.enhanced_watchlist.get(symbol, {}).get('sector', '') else 0,
            'is_leveraged': 1 if 'leveraged' in self.enhanced_watchlist.get(symbol, {}).get('category', '') else 0,
        }
        
        return features
    
    def _get_ml_prediction(self, features: Dict[str, float]) -> float:
        """Get ML prediction using trained models"""
        if not self.models or not features:
            return 0.5
        
        try:
            # Create feature vector
            feature_vector = []
            for fname in self.feature_names:
                feature_vector.append(features.get(fname, 0))
            
            X = np.array(feature_vector).reshape(1, -1)
            
            # Get prediction from Random Forest
            if 'random_forest' in self.models:
                prediction = self.models['random_forest'].predict_proba(X)[0, 1]
                return prediction
            else:
                return 0.5
                
        except Exception as e:
            print(f"Error in ML prediction: {e}")
            return 0.5
    
    def _check_enhanced_gap_fill(self, symbol: str, current_price: float, df: pd.DataFrame, 
                                portfolio_value: float, symbol_info: dict, ml_features: dict, ml_confidence: float) -> Optional[VolatilityOpportunity]:
        """Enhanced gap fill strategy for quality stocks"""
        try:
            latest = df.iloc[-1]
            
            # Enhanced gap detection
            gap_size = abs(latest['gap'])
            if gap_size < 0.01 or gap_size > 0.08:  # Tighter range for quality stocks
                return None
            
            # Volume confirmation (relaxed for liquid stocks)
            if latest['volume_ratio'] < self.min_volume_ratio:
                return None
            
            # Direction and targets
            is_gap_up = latest['gap'] > 0
            prev_close = df.iloc[-2]['Close']
            
            entry_price = current_price
            target_price = prev_close
            
            # Enhanced stop loss using ATR
            atr = latest['atr_14']
            if is_gap_up:
                stop_loss = latest['High'] + (atr * 0.5)
                if entry_price >= target_price:
                    return None
            else:
                stop_loss = latest['Low'] - (atr * 0.5)
                if entry_price <= target_price:
                    return None
            
            # Enhanced confidence calculation
            base_confidence = self.gap_fill_probability
            
            # Adjust for symbol quality
            if symbol_info.get('category') == 'etf_majors':
                base_confidence += 0.05  # ETFs more predictable
            elif symbol_info.get('category') == 'mega_tech':
                base_confidence += 0.03  # Large caps more predictable
            
            # Combine with ML
            combined_confidence = (base_confidence * 0.7) + (ml_confidence * 0.3)
            
            # Position sizing
            win_rate = combined_confidence
            avg_win = abs(target_price - entry_price) / entry_price
            avg_loss = abs(stop_loss - entry_price) / entry_price
            
            position_size = self._calculate_kelly_position_size(
                symbol, entry_price, stop_loss, win_rate, 
                avg_win, avg_loss, portfolio_value, 0.25
            )
            
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss if avg_loss > 0 else 0
            
            # Enhanced signals
            signals = {
                'gap_size': gap_size,
                'volume_surge': latest['volume_ratio'],
                'atr_ratio': latest['atr_14'] / current_price,
                'ml_confidence': ml_confidence,
                'symbol_quality': symbol_info.get('category', 'unknown')
            }
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='enhanced_gap_fill',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=combined_confidence,
                kelly_fraction=position_size * entry_price / portfolio_value,
                position_size=position_size,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals=signals,
                timestamp=datetime.now(),
                market_cap_category=symbol_info.get('market_cap', 'unknown'),
                sector=symbol_info.get('sector', 'unknown'),
                ml_features=ml_features
            )
            
        except Exception as e:
            print(f"Error in enhanced gap fill for {symbol}: {e}")
            return None
    
    def _check_momentum_breakout(self, symbol: str, current_price: float, df: pd.DataFrame,
                                portfolio_value: float, symbol_info: dict, ml_features: dict, ml_confidence: float) -> Optional[VolatilityOpportunity]:
        """Momentum breakout strategy optimized for tech/growth stocks"""
        try:
            latest = df.iloc[-1]
            
            # Check for breakout above resistance
            resistance = latest['resistance_20']
            if current_price < resistance * 1.005:  # Must break above with margin
                return None
            
            # Volume confirmation
            if latest['volume_ratio'] < 1.5:
                return None
            
            # Momentum confirmation
            if latest['price_momentum_5'] < 0.01:  # Must have positive 5-day momentum
                return None
            
            # RSI not overbought
            if latest['rsi_14'] > 75:
                return None
            
            entry_price = current_price
            target_price = resistance * 1.03  # 3% above resistance
            stop_loss = resistance * 0.995   # Just below breakout level
            
            # Enhanced confidence for tech stocks
            base_confidence = 0.65
            if symbol_info.get('sector') in ['tech', 'semiconductors']:
                base_confidence += 0.05
            
            combined_confidence = (base_confidence * 0.6) + (ml_confidence * 0.4)
            
            win_rate = combined_confidence
            avg_win = (target_price - entry_price) / entry_price
            avg_loss = (entry_price - stop_loss) / entry_price
            
            if avg_win < avg_loss * 1.5:  # Require good risk/reward
                return None
            
            position_size = self._calculate_kelly_position_size(
                symbol, entry_price, stop_loss, win_rate,
                avg_win, avg_loss, portfolio_value, 0.20
            )
            
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss
            
            signals = {
                'breakout_strength': (current_price - resistance) / resistance,
                'volume_surge': latest['volume_ratio'],
                'momentum_5d': latest['price_momentum_5'],
                'rsi': latest['rsi_14'],
                'ml_confidence': ml_confidence
            }
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='momentum_breakout',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=combined_confidence,
                kelly_fraction=position_size * entry_price / portfolio_value,
                position_size=position_size,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals=signals,
                timestamp=datetime.now(),
                market_cap_category=symbol_info.get('market_cap', 'unknown'),
                sector=symbol_info.get('sector', 'unknown'),
                ml_features=ml_features
            )
            
        except Exception as e:
            print(f"Error in momentum breakout for {symbol}: {e}")
            return None
    
    def _check_mean_reversion(self, symbol: str, current_price: float, df: pd.DataFrame,
                             portfolio_value: float, symbol_info: dict, ml_features: dict, ml_confidence: float) -> Optional[VolatilityOpportunity]:
        """Mean reversion strategy for ETFs and stable instruments"""
        try:
            latest = df.iloc[-1]
            
            # Check for oversold conditions
            if latest['rsi_14'] > 35:  # Not oversold enough
                return None
            
            # Check Bollinger Band position
            if latest['bb_position_20'] > 0.2:  # Not near lower band
                return None
            
            # Volume confirmation
            if latest['volume_ratio'] < 1.3:
                return None
            
            entry_price = current_price
            target_price = latest['bb_middle_20']  # Target middle BB
            stop_loss = latest['support_20'] * 0.98  # Below support
            
            # Higher confidence for ETFs (more predictable)
            base_confidence = 0.70 if symbol_info.get('category') == 'etf_majors' else 0.60
            combined_confidence = (base_confidence * 0.65) + (ml_confidence * 0.35)
            
            win_rate = combined_confidence
            avg_win = (target_price - entry_price) / entry_price
            avg_loss = (entry_price - stop_loss) / entry_price
            
            if avg_win < avg_loss * 1.2:  # Require reasonable risk/reward
                return None
            
            position_size = self._calculate_kelly_position_size(
                symbol, entry_price, stop_loss, win_rate,
                avg_win, avg_loss, portfolio_value, 0.25
            )
            
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss
            
            signals = {
                'rsi_oversold': latest['rsi_14'],
                'bb_position': latest['bb_position_20'],
                'volume_surge': latest['volume_ratio'],
                'distance_to_support': (current_price - latest['support_20']) / current_price,
                'ml_confidence': ml_confidence
            }
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='mean_reversion',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=combined_confidence,
                kelly_fraction=position_size * entry_price / portfolio_value,
                position_size=position_size,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals=signals,
                timestamp=datetime.now(),
                market_cap_category=symbol_info.get('market_cap', 'unknown'),
                sector=symbol_info.get('sector', 'unknown'),
                ml_features=ml_features
            )
            
        except Exception as e:
            print(f"Error in mean reversion for {symbol}: {e}")
            return None
    
    # Helper methods (RSI, ATR, Kelly) - same as before
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        high_low = df['High'] - df['Low']
        high_close = abs(df['High'] - df['Close'].shift())
        low_close = abs(df['Low'] - df['Close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(period).mean()
    
    def _calculate_kelly_position_size(self, symbol: str, entry_price: float, 
                                     stop_loss: float, win_rate: float,
                                     avg_win: float, avg_loss: float,
                                     portfolio_value: float,
                                     safety_factor: float = 0.25) -> int:
        if avg_loss <= 0 or win_rate <= 0 or win_rate >= 1:
            return min(100, int(portfolio_value * 0.1 / entry_price))
        
        kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
        kelly_fraction = max(0, min(kelly_fraction * safety_factor, 0.25))
        
        kelly_amount = portfolio_value * kelly_fraction
        position_size = int(kelly_amount / entry_price)
        
        return max(position_size, 1)
    
    def get_daily_summary(self, opportunities: List[VolatilityOpportunity]) -> str:
        """Enhanced daily summary with category breakdown"""
        if not opportunities:
            return "No enhanced volatility opportunities found today."
        
        summary = f"\n📊 ENHANCED VOLATILITY HUNTER DAILY SUMMARY - {datetime.now().strftime('%Y-%m-%d')}\n"
        summary += "=" * 70 + "\n"
        
        # Group by strategy and sector
        strategies = {}
        sectors = {}
        
        for opp in opportunities:
            # By strategy
            if opp.strategy_type not in strategies:
                strategies[opp.strategy_type] = []
            strategies[opp.strategy_type].append(opp)
            
            # By sector
            if opp.sector not in sectors:
                sectors[opp.sector] = []
            sectors[opp.sector].append(opp)
        
        summary += f"\n🎯 Total Opportunities: {len(opportunities)}\n"
        summary += "\n📈 BY STRATEGY:\n"
        for strategy, opps in strategies.items():
            avg_confidence = np.mean([o.confidence for o in opps])
            summary += f"   - {strategy.replace('_', ' ').title()}: {len(opps)} (avg confidence: {avg_confidence:.1%})\n"
        
        summary += "\n🏢 BY SECTOR:\n"
        for sector, opps in sectors.items():
            avg_return = np.mean([o.expected_return for o in opps])
            summary += f"   - {sector.replace('_', ' ').title()}: {len(opps)} (avg expected return: {avg_return:.1%})\n"
        
        summary += "\n🔥 TOP 5 OPPORTUNITIES:\n"
        for i, opp in enumerate(opportunities[:5], 1):
            summary += f"\n{i}. {opp.symbol} ({opp.sector}) - {opp.strategy_type.upper()}\n"
            summary += f"   Expected Return: {opp.expected_return*100:.1f}%\n"
            summary += f"   Risk/Reward: {opp.risk_reward_ratio:.2f}\n"
            summary += f"   Confidence: {opp.confidence*100:.0f}% (ML: {opp.signals.get('ml_confidence', 0)*100:.0f}%)\n"
            summary += f"   Entry: ${opp.entry_price:.2f} | Target: ${opp.target_price:.2f}\n"
        
        return summary

# Enhanced Paper Trading Engine
class EnhancedPaperTradingEngine:
    """Enhanced paper trading with category-based position management"""
    
    def __init__(self, starting_capital: float = 10000):
        self.capital = starting_capital
        self.starting_capital = starting_capital
        self.positions = {}
        self.closed_trades = []
        self.trade_log_file = "enhanced_paper_trades.json"
        self.volatility_hunter = PremiumVolatilityHunter()
        
        print(f"🚀 Enhanced Paper Trading Engine initialized with ${starting_capital:,.2f}")
    
    async def run_enhanced_trading(self, max_iterations: int = 5, focus_categories: List[str] = None):
        """Run enhanced paper trading with category focus"""
        print(f"\n💰 Starting enhanced paper trading...")
        if focus_categories:
            print(f"📊 Focusing on categories: {', '.join(focus_categories)}")
        
        for iteration in range(max_iterations):
            try:
                print(f"\n🔄 Enhanced trading iteration {iteration + 1}/{max_iterations}")
                
                # Update positions
                await self._update_positions()
                
                # Scan for opportunities
                opportunities = self.volatility_hunter.scan_for_opportunities(
                    portfolio_value=self.capital,
                    focus_categories=focus_categories
                )
                
                # Execute trades with enhanced logic
                executed = 0
                for opp in opportunities[:3]:
                    if self._can_trade_enhanced(opp):
                        await self._execute_enhanced_trade(opp)
                        executed += 1
                
                print(f"✅ Executed {executed} enhanced trades")
                
                # Display enhanced status
                self._display_enhanced_status()
                
                # Save state
                self._save_trades()
                
                if iteration < max_iterations - 1:
                    print("⏳ Waiting 10 seconds...")
                    await asyncio.sleep(10)
                    
            except Exception as e:
                print(f"Error in enhanced trading: {e}")
                await asyncio.sleep(5)
        
        print(f"\n🏁 Enhanced trading completed!")
        self._display_final_summary()
    
    def _can_trade_enhanced(self, opportunity: VolatilityOpportunity) -> bool:
        """Enhanced trade validation"""
        if opportunity.symbol in self.positions:
            return False
        
        required_capital = opportunity.entry_price * opportunity.position_size
        
        # Category-based position limits
        max_per_position = 0.15  # 15% default
        if opportunity.market_cap_category == 'large':
            max_per_position = 0.20  # 20% for large caps
        elif 'leveraged' in opportunity.sector:
            max_per_position = 0.10  # 10% for leveraged
        elif 'volatility' in opportunity.sector:
            max_per_position = 0.05  # 5% for volatility products
        
        if required_capital > self.capital * max_per_position:
            return False
        
        if len(self.positions) >= 5:
            return False
        
        # Minimum confidence threshold
        if opportunity.confidence < 0.6:
            return False
        
        return True
    
    async def _execute_enhanced_trade(self, opportunity: VolatilityOpportunity):
        """Execute enhanced trade with metadata"""
        from test_paper_trading_standalone import PaperTrade
        
        trade = PaperTrade(
            trade_id=f"{opportunity.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            symbol=opportunity.symbol,
            strategy=f"{opportunity.strategy_type} ({opportunity.sector})",
            entry_price=opportunity.entry_price,
            target_price=opportunity.target_price,
            stop_loss=opportunity.stop_loss,
            position_size=opportunity.position_size,
            entry_time=datetime.now()
        )
        
        self.positions[opportunity.symbol] = trade
        required_capital = trade.entry_price * trade.position_size
        self.capital -= required_capital
        
        print(f"\n✅ ENHANCED TRADE EXECUTED:")
        print(f"   Symbol: {trade.symbol} ({opportunity.sector})")
        print(f"   Strategy: {opportunity.strategy_type}")
        print(f"   ML Confidence: {opportunity.signals.get('ml_confidence', 0)*100:.1f}%")
        print(f"   Combined Confidence: {opportunity.confidence*100:.1f}%")
        print(f"   Entry: ${trade.entry_price:.2f}")
        print(f"   Target: ${trade.target_price:.2f} (R/R: {opportunity.risk_reward_ratio:.2f})")
        print(f"   Size: {trade.position_size} shares (${required_capital:.2f})")
    
    async def _update_positions(self):
        """Update positions with simulated price movement"""
        if not self.positions:
            print("📝 No open positions to update")
            return
        
        print(f"🔄 Updating {len(self.positions)} positions...")
        
        for symbol, trade in list(self.positions.items()):
            try:
                # Simulate realistic price movement based on volatility
                volatility = 0.02  # Default 2% daily volatility
                
                # Adjust volatility by sector
                if 'leveraged' in trade.strategy:
                    volatility = 0.05
                elif 'tech' in trade.strategy:
                    volatility = 0.03
                elif 'broad_market' in trade.strategy:
                    volatility = 0.015
                
                price_change = np.random.normal(0, volatility)
                current_price = trade.entry_price * (1 + price_change)
                
                print(f"   {symbol}: ${current_price:.2f} (entry: ${trade.entry_price:.2f})")
                
                # Check exit conditions
                if not pd.isna(trade.stop_loss) and current_price <= trade.stop_loss:
                    await self._close_enhanced_position(trade, current_price, "STOPPED")
                elif current_price >= trade.target_price:
                    await self._close_enhanced_position(trade, current_price, "TARGET")
                else:
                    trade.pnl = (current_price - trade.entry_price) * trade.position_size
                    trade.pnl_percent = ((current_price / trade.entry_price) - 1) * 100
                    
            except Exception as e:
                print(f"Error updating {symbol}: {e}")
    
    async def _close_enhanced_position(self, trade, exit_price: float, reason: str):
        """Close position with enhanced tracking"""
        trade.exit_time = datetime.now()
        trade.exit_price = exit_price
        trade.status = reason
        trade.pnl = (exit_price - trade.entry_price) * trade.position_size
        trade.pnl_percent = ((exit_price / trade.entry_price) - 1) * 100
        
        self.capital += exit_price * trade.position_size
        self.closed_trades.append(trade)
        del self.positions[trade.symbol]
        
        print(f"\n💰 ENHANCED POSITION CLOSED:")
        print(f"   Symbol: {trade.symbol}")
        print(f"   Strategy: {trade.strategy}")
        print(f"   Reason: {reason}")
        print(f"   P&L: ${trade.pnl:.2f} ({trade.pnl_percent:.1f}%)")
        print(f"   Hold Time: {(trade.exit_time - trade.entry_time).total_seconds():.0f} seconds")
    
    def _display_enhanced_status(self):
        """Display enhanced status with sector breakdown"""
        unrealized_pnl = sum(t.pnl for t in self.positions.values())
        total_value = self.capital + unrealized_pnl
        
        print(f"\n📊 ENHANCED PAPER TRADING STATUS")
        print(f"{'='*60}")
        print(f"Capital: ${self.capital:,.2f}")
        print(f"Open Positions: {len(self.positions)}")
        print(f"Unrealized P&L: ${unrealized_pnl:.2f}")
        print(f"Total Value: ${total_value:,.2f}")
        print(f"Total Return: {((total_value/self.starting_capital)-1)*100:.1f}%")
        
        if self.closed_trades:
            wins = sum(1 for t in self.closed_trades if t.pnl > 0)
            total_trades = len(self.closed_trades)
            win_rate = (wins / total_trades) * 100
            
            print(f"\nClosed Trades: {total_trades}")
            print(f"Win Rate: {win_rate:.1f}%")
            print(f"Realized P&L: ${sum(t.pnl for t in self.closed_trades):.2f}")
    
    def _display_final_summary(self):
        """Enhanced final summary"""
        unrealized_pnl = sum(t.pnl for t in self.positions.values())
        total_value = self.capital + unrealized_pnl
        
        print(f"\n🎯 ENHANCED FINAL SUMMARY")
        print(f"{'='*70}")
        print(f"Starting Capital: ${self.starting_capital:,.2f}")
        print(f"Final Value: ${total_value:,.2f}")
        print(f"Total Return: {((total_value/self.starting_capital)-1)*100:.2f}%")
        
        if self.closed_trades:
            wins = sum(1 for t in self.closed_trades if t.pnl > 0)
            total_trades = len(self.closed_trades)
            win_rate = (wins / total_trades) * 100
            
            print(f"\n📈 TRADING PERFORMANCE:")
            print(f"Total Trades: {total_trades}")
            print(f"Win Rate: {win_rate:.1f}% ({wins}/{total_trades})")
            
            if wins > 0:
                avg_win = np.mean([t.pnl for t in self.closed_trades if t.pnl > 0])
                print(f"Average Win: ${avg_win:.2f}")
            
            if total_trades > wins:
                avg_loss = np.mean([t.pnl for t in self.closed_trades if t.pnl < 0])
                print(f"Average Loss: ${avg_loss:.2f}")
                
                if wins > 0:
                    profit_factor = abs(avg_win * wins / (avg_loss * (total_trades - wins)))
                    print(f"Profit Factor: {profit_factor:.2f}")
            
            # Strategy breakdown
            strategy_performance = {}
            for trade in self.closed_trades:
                strategy = trade.strategy.split('(')[0].strip()
                if strategy not in strategy_performance:
                    strategy_performance[strategy] = {'wins': 0, 'total': 0, 'pnl': 0}
                
                strategy_performance[strategy]['total'] += 1
                strategy_performance[strategy]['pnl'] += trade.pnl
                if trade.pnl > 0:
                    strategy_performance[strategy]['wins'] += 1
            
            print(f"\n📊 STRATEGY BREAKDOWN:")
            for strategy, stats in strategy_performance.items():
                win_rate = (stats['wins'] / stats['total']) * 100
                print(f"{strategy}: {win_rate:.1f}% win rate, ${stats['pnl']:.2f} P&L")
    
    def _save_trades(self):
        """Save enhanced trades"""
        data = {
            'capital': self.capital,
            'starting_capital': self.starting_capital,
            'positions': [asdict(t) for t in self.positions.values()],
            'closed_trades': [asdict(t) for t in self.closed_trades]
        }
        
        with open(self.trade_log_file, 'w') as f:
            json.dump(data, f, indent=2, default=str)

# Main execution
async def main():
    print("🔥 PREMIUM VOLATILITY TRADING SYSTEM")
    print("="*70)
    print("Enhanced with high-quality trading targets for superior ML training")
    
    # Demo different focus categories
    categories_to_test = [
        ['etf_majors', 'mega_tech'],  # Conservative, high-quality
        ['ai_chips'],                 # High-growth tech
        ['leveraged']                 # High volatility
    ]
    
    for i, categories in enumerate(categories_to_test, 1):
        print(f"\n{'='*50}")
        print(f"🎯 TEST RUN {i}: {', '.join(categories).upper()}")
        print(f"{'='*50}")
        
        engine = EnhancedPaperTradingEngine(starting_capital=10000)
        await engine.run_enhanced_trading(max_iterations=3, focus_categories=categories)
        
        if i < len(categories_to_test):
            print("\n⏳ Waiting 5 seconds before next test...")
            await asyncio.sleep(5)

if __name__ == "__main__":
    asyncio.run(main())