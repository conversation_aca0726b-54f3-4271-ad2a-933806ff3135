# Production Configuration for AI Trading Bot
# This file contains production-ready settings for 24/7 operations

# Application Configuration
app:
  name: "AI Trading Bot"
  version: "1.0.0"
  environment: "production"
  debug: false
  timezone: "UTC"

# Logging Configuration
logging:
  level: "INFO"
  format: "json"
  rotation: "1 day"
  retention: "30 days"
  max_file_size: "100MB"
  backup_count: 30
  structured: true
  
  # Log destinations
  handlers:
    - type: "file"
      filename: "logs/trading_bot.log"
    - type: "syslog"
      address: ["localhost", 514]
    - type: "elasticsearch"
      hosts: ["elasticsearch:9200"]
      index: "trading-bot-logs"

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  timeout: 30
  max_request_size: "10MB"
  cors_origins: ["https://trading-dashboard.example.com"]
  
  # Rate limiting
  rate_limits:
    default: 100  # requests per minute
    trading: 30   # trading requests per minute
    data: 300     # data requests per minute
  
  # Security
  jwt_secret: "${JWT_SECRET}"
  jwt_expiry: "24h"
  ssl_enabled: true
  ssl_cert: "/etc/ssl/certs/trading-bot.crt"
  ssl_key: "/etc/ssl/private/trading-bot.key"

# Trading Configuration
trading:
  mode: "live"  # live, paper, simulation
  max_positions: 20
  max_daily_trades: 100
  position_check_interval: 30  # seconds
  order_timeout: 300  # seconds
  
  # Market hours
  market_hours:
    pre_market_start: "04:00"
    market_open: "09:30"
    market_close: "16:00"
    after_hours_end: "20:00"
    timezone: "America/New_York"
  
  # Trading limits
  limits:
    max_order_value: 50000  # USD
    max_position_concentration: 0.1  # 10% of portfolio
    max_sector_exposure: 0.3  # 30% per sector

# Risk Management Configuration
risk:
  # Daily limits
  max_daily_loss: 0.02  # 2% of portfolio
  max_daily_trades: 100
  
  # Position limits
  max_position_size: 0.05  # 5% of portfolio
  max_positions: 20
  
  # Drawdown limits
  max_drawdown_threshold: 0.10  # 10%
  emergency_stop_loss: 0.05  # 5%
  
  # Risk monitoring
  monitoring_interval: 30  # seconds
  alert_thresholds:
    drawdown_warning: 0.03  # 3%
    drawdown_critical: 0.05  # 5%
    correlation_warning: 0.7
    var_threshold: 0.02  # 2%
  
  # Circuit breakers
  circuit_breakers:
    daily_loss: -0.02  # -2%
    hourly_loss: -0.005  # -0.5%
    consecutive_losses: 5

# Machine Learning Configuration
ml:
  # Model settings
  models:
    - name: "lstm"
      enabled: true
      weight: 0.3
    - name: "xgboost"
      enabled: true
      weight: 0.3
    - name: "transformer"
      enabled: true
      weight: 0.4
  
  # Training settings
  training:
    auto_retrain: true
    retrain_interval: "daily"
    retrain_time: "02:00"  # UTC
    validation_split: 0.2
    test_split: 0.1
  
  # Prediction settings
  prediction:
    batch_size: 32
    confidence_threshold: 0.7
    prediction_frequency: 60  # seconds
    max_inference_time: 100  # milliseconds
  
  # Feature engineering
  features:
    technical_indicators: true
    market_microstructure: true
    sentiment_analysis: true
    economic_indicators: true
    lookback_window: 252  # trading days

# Database Configuration
databases:
  # PostgreSQL - Primary database
  postgres:
    host: "${POSTGRES_HOST}"
    port: 5432
    database: "${POSTGRES_DB}"
    username: "${POSTGRES_USER}"
    password: "${POSTGRES_PASSWORD}"
    
    # Connection pool
    pool_size: 20
    max_overflow: 30
    pool_timeout: 30
    pool_recycle: 3600
    
    # Performance
    statement_timeout: 30000  # 30 seconds
    lock_timeout: 10000  # 10 seconds
    
    # SSL
    ssl_mode: "require"
    ssl_cert: "/etc/ssl/certs/postgres-client.crt"
    ssl_key: "/etc/ssl/private/postgres-client.key"
    ssl_ca: "/etc/ssl/certs/postgres-ca.crt"
  
  # Redis - Cache and session store
  redis:
    host: "${REDIS_HOST}"
    port: 6379
    password: "${REDIS_PASSWORD}"
    db: 0
    
    # Connection settings
    max_connections: 50
    socket_timeout: 5
    socket_connect_timeout: 5
    retry_on_timeout: true
    
    # Clustering
    cluster_enabled: false
    sentinel_enabled: false
  
  # MongoDB - Document storage
  mongodb:
    host: "${MONGODB_HOST}"
    port: 27017
    database: "${MONGODB_DB}"
    username: "${MONGODB_USER}"
    password: "${MONGODB_PASSWORD}"
    
    # Connection settings
    max_pool_size: 100
    min_pool_size: 10
    max_idle_time: 30000
    server_selection_timeout: 5000

# External APIs Configuration
external_apis:
  # Webull API and Browser Automation
  webull:
    username: "${WEBULL_USERNAME}"
    password: "${WEBULL_PASSWORD}"
    device_id: "${WEBULL_DEVICE_ID}"
    paper_trading: false

    # Rate limiting
    max_requests_per_minute: 60
    request_delay: 1.0

    # Browser automation settings (when API not available)
    browser_automation:
      enabled: true  # Enable browser automation
      headless: false  # Set to true for headless mode in production
      window_size: "1920,1080"
      user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      page_load_timeout: 30
      implicit_wait: 10
      explicit_wait: 10
      screenshot_on_error: true
      screenshot_dir: "./screenshots"
      cookies_file: "./data/cookies/webull_cookies.pkl"
      session_timeout: 3600  # 1 hour

      # Stealth settings to avoid detection
      disable_automation_flags: true
      disable_dev_shm_usage: true
      no_sandbox: false  # Set to true in Docker

      # Trading settings
      default_order_type: "market"
      max_position_size: 100  # shares
      stop_loss_percent: -2.0
      take_profit_percent: 5.0
      loop_interval: 60  # seconds
      min_confidence: 0.7

      # Watchlist for browser automation
      watchlist:
        - "AAPL"
        - "MSFT"
        - "GOOGL"
        - "TSLA"
        - "NVDA"
        - "AMZN"
        - "META"
        - "NFLX"

      # Integration with production components
      integration:
        use_enhanced_secrets: true
        mfa_required: true
        enable_metrics: true
        prometheus_port: 9090
        enable_compliance_checks: true
        pdt_monitoring: true
        wash_sale_detection: true
        enable_dead_mans_switch: true
        heartbeat_interval: 300  # 5 minutes
        enable_microstructure_analysis: true
        order_flow_analysis: true
        enable_performance_optimization: true
        optimization_level: "high"
    
    # Retry settings
    max_retries: 3
    retry_delay: 5
    backoff_factor: 2
  
  # News APIs
  news:
    alpha_vantage:
      api_key: "${ALPHA_VANTAGE_API_KEY}"
      rate_limit: 5  # requests per minute
    
    finnhub:
      api_key: "${FINNHUB_API_KEY}"
      rate_limit: 60  # requests per minute
  
  # Economic data
  economic:
    fred:
      api_key: "${FRED_API_KEY}"
      rate_limit: 120  # requests per minute

# Monitoring Configuration
monitoring:
  # Metrics
  metrics:
    enabled: true
    port: 9090
    path: "/metrics"
    update_interval: 15  # seconds
  
  # Health checks
  health:
    enabled: true
    interval: 30  # seconds
    timeout: 10  # seconds
    
    # Health check endpoints
    checks:
      - name: "database"
        type: "postgres"
        critical: true
      - name: "cache"
        type: "redis"
        critical: true
      - name: "api"
        type: "http"
        url: "http://localhost:8000/health"
        critical: true
  
  # Performance tracking
  performance:
    enabled: true
    update_interval: 300  # 5 minutes
    retention_days: 30
    
    # Alerts
    alerts:
      response_time_threshold: 1000  # milliseconds
      error_rate_threshold: 0.05  # 5%
      memory_threshold: 0.85  # 85%
      cpu_threshold: 0.80  # 80%

# Security Configuration
security:
  # Encryption
  encryption:
    algorithm: "AES-256-GCM"
    key_rotation_days: 90
  
  # Authentication
  authentication:
    session_timeout: 3600  # 1 hour
    max_login_attempts: 5
    lockout_duration: 900  # 15 minutes
    
    # Multi-factor authentication
    mfa_enabled: true
    mfa_issuer: "AI Trading Bot"
  
  # Network security
  network:
    allowed_ips:
      - "10.0.0.0/8"
      - "**********/12"
      - "***********/16"
    
    # Firewall rules
    firewall:
      allow_ports: [22, 80, 443, 8000, 9090]
      deny_all_others: true
  
  # Data protection
  data_protection:
    encryption_at_rest: true
    encryption_in_transit: true
    data_retention_days: 2555  # 7 years
    
    # PII handling
    pii_encryption: true
    pii_access_logging: true

# Backup Configuration
backup:
  # Database backups
  database:
    enabled: true
    schedule: "0 2 * * *"  # Daily at 2 AM UTC
    retention_days: 30
    compression: true
    encryption: true
    
    # Backup destinations
    destinations:
      - type: "s3"
        bucket: "trading-bot-backups"
        region: "us-east-1"
      - type: "local"
        path: "/backup/database"
  
  # Application backups
  application:
    enabled: true
    schedule: "0 3 * * *"  # Daily at 3 AM UTC
    retention_days: 7
    
    # Include/exclude patterns
    include:
      - "config/"
      - "models/"
      - "logs/"
    exclude:
      - "*.tmp"
      - "*.log"
      - "__pycache__/"

# Alerting Configuration
alerting:
  # Notification channels
  channels:
    email:
      enabled: true
      smtp_host: "${SMTP_HOST}"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      from_address: "<EMAIL>"
      to_addresses:
        - "<EMAIL>"
        - "<EMAIL>"
    
    slack:
      enabled: true
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channel: "#trading-alerts"
    
    pagerduty:
      enabled: true
      integration_key: "${PAGERDUTY_INTEGRATION_KEY}"
  
  # Alert rules
  rules:
    - name: "High Drawdown"
      condition: "current_drawdown > 0.03"
      severity: "critical"
      channels: ["email", "slack", "pagerduty"]
    
    - name: "System Error"
      condition: "error_rate > 0.05"
      severity: "warning"
      channels: ["email", "slack"]
    
    - name: "API Latency"
      condition: "api_response_time > 1000"
      severity: "warning"
      channels: ["slack"]

# Deployment Configuration
deployment:
  # Container settings
  container:
    image: "trading-bot:latest"
    registry: "your-registry.com"
    pull_policy: "Always"
    
    # Resource limits
    resources:
      requests:
        cpu: "1000m"
        memory: "2Gi"
      limits:
        cpu: "2000m"
        memory: "4Gi"
  
  # Scaling
  scaling:
    min_replicas: 2
    max_replicas: 5
    target_cpu_utilization: 70
    target_memory_utilization: 80
  
  # Rolling updates
  rolling_update:
    max_surge: 1
    max_unavailable: 0
