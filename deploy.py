#!/usr/bin/env python3
"""
Trading Bot Deployment Script

This script handles the complete deployment process including:
- Environment validation
- System checks
- Service deployment
- Health verification
- Monitoring setup
"""

import os
import sys
import time
import json
import asyncio
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.trading_bot.config.production_config import get_config_manager, Environment


class TradingBotDeployer:
    """Comprehensive deployment manager for trading bot."""
    
    def __init__(self, environment: Environment):
        self.environment = environment
        self.config_manager = get_config_manager(environment)
        self.deployment_start_time = time.time()
        
    async def deploy(self):
        """Execute complete deployment process."""
        print(f"🚀 Deploying Trading Bot to {self.environment.value.upper()}")
        print("=" * 60)
        
        try:
            # Pre-deployment checks
            await self._pre_deployment_checks()
            
            # System preparation
            await self._prepare_system()
            
            # Deploy infrastructure
            await self._deploy_infrastructure()
            
            # Deploy application
            await self._deploy_application()
            
            # Post-deployment verification
            await self._post_deployment_verification()
            
            # Setup monitoring
            await self._setup_monitoring()
            
            # Final health check
            await self._final_health_check()
            
            deployment_time = time.time() - self.deployment_start_time
            print(f"\n✅ Deployment completed successfully in {deployment_time:.1f} seconds!")
            
            # Show access information
            self._show_access_info()
            
        except Exception as e:
            print(f"\n❌ Deployment failed: {e}")
            await self._rollback_deployment()
            sys.exit(1)
    
    async def _pre_deployment_checks(self):
        """Perform pre-deployment validation checks."""
        print("\n🔍 Step 1: Pre-deployment Checks")
        print("-" * 40)
        
        # Check required files
        required_files = [
            "docker-compose.yml",
            ".env",
            f"config/{self.environment.value}.yaml"
        ]
        
        missing_files = []
        for file in required_files:
            if not Path(file).exists():
                missing_files.append(file)
        
        if missing_files:
            raise Exception(f"Missing required files: {', '.join(missing_files)}")
        
        print("✅ Required files present")
        
        # Check Docker
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("Docker not available")
            print("✅ Docker available")
        except FileNotFoundError:
            raise Exception("Docker not installed")
        
        # Check Docker Compose
        try:
            result = subprocess.run(["docker-compose", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("Docker Compose not available")
            print("✅ Docker Compose available")
        except FileNotFoundError:
            raise Exception("Docker Compose not installed")
        
        # Validate configuration
        issues = self.config_manager.validate_config()
        if issues["errors"]:
            raise Exception(f"Configuration errors: {', '.join(issues['errors'])}")
        
        if issues["warnings"]:
            print("⚠️  Configuration warnings:")
            for warning in issues["warnings"]:
                print(f"   • {warning}")
        
        print("✅ Configuration validated")
        
        # Check system resources
        await self._check_system_resources()
        
        print("✅ Pre-deployment checks completed")
    
    async def _check_system_resources(self):
        """Check system resources availability."""
        import psutil
        
        # Check CPU
        cpu_count = psutil.cpu_count()
        required_cores = max(self.config_manager.performance.cpu_cores_trading + 
                           self.config_manager.performance.cpu_cores_data)
        
        if required_cores >= cpu_count:
            raise Exception(f"Insufficient CPU cores: need {required_cores + 1}, have {cpu_count}")
        
        # Check memory
        memory = psutil.virtual_memory()
        required_memory_gb = self.config_manager.performance.memory_pool_size_mb / 1024 + 2  # +2GB for system
        available_memory_gb = memory.available / (1024**3)
        
        if available_memory_gb < required_memory_gb:
            raise Exception(f"Insufficient memory: need {required_memory_gb:.1f}GB, have {available_memory_gb:.1f}GB")
        
        # Check disk space
        disk = psutil.disk_usage('/')
        available_disk_gb = disk.free / (1024**3)
        
        if available_disk_gb < 10:  # Minimum 10GB
            raise Exception(f"Insufficient disk space: need 10GB, have {available_disk_gb:.1f}GB")
        
        print(f"✅ System resources: {cpu_count} cores, {available_memory_gb:.1f}GB RAM, {available_disk_gb:.1f}GB disk")
    
    async def _prepare_system(self):
        """Prepare system for deployment."""
        print("\n⚙️  Step 2: System Preparation")
        print("-" * 40)
        
        # Create necessary directories
        directories = [
            "data",
            "logs", 
            "backups",
            "monitoring/prometheus",
            "monitoring/grafana"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        print("✅ Directories created")
        
        # Set permissions
        if self.environment == Environment.PRODUCTION:
            # Set restrictive permissions for production
            os.chmod("data", 0o750)
            os.chmod("logs", 0o750)
            os.chmod("backups", 0o700)
            print("✅ Production permissions set")
        
        # Generate monitoring configurations
        await self._generate_monitoring_configs()
        
        print("✅ System preparation completed")
    
    async def _generate_monitoring_configs(self):
        """Generate monitoring configuration files."""
        
        # Prometheus configuration
        prometheus_config = {
            "global": {
                "scrape_interval": "15s",
                "evaluation_interval": "15s"
            },
            "scrape_configs": [
                {
                    "job_name": "trading-bot",
                    "static_configs": [
                        {"targets": ["trading-bot:8000"]}
                    ]
                },
                {
                    "job_name": "postgres",
                    "static_configs": [
                        {"targets": ["postgres:5432"]}
                    ]
                },
                {
                    "job_name": "redis",
                    "static_configs": [
                        {"targets": ["redis:6379"]}
                    ]
                }
            ]
        }
        
        import yaml
        with open("monitoring/prometheus/prometheus.yml", "w") as f:
            yaml.dump(prometheus_config, f)
        
        # Grafana datasource configuration
        grafana_datasource = {
            "apiVersion": 1,
            "datasources": [
                {
                    "name": "Prometheus",
                    "type": "prometheus",
                    "url": "http://prometheus:9090",
                    "access": "proxy",
                    "isDefault": True
                }
            ]
        }
        
        Path("monitoring/grafana/datasources").mkdir(parents=True, exist_ok=True)
        with open("monitoring/grafana/datasources/prometheus.yml", "w") as f:
            yaml.dump(grafana_datasource, f)
        
        print("✅ Monitoring configurations generated")
    
    async def _deploy_infrastructure(self):
        """Deploy infrastructure services."""
        print("\n🏗️  Step 3: Infrastructure Deployment")
        print("-" * 40)
        
        # Pull latest images
        print("Pulling Docker images...")
        result = subprocess.run(["docker-compose", "pull"], capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"Failed to pull images: {result.stderr}")
        
        print("✅ Docker images pulled")
        
        # Start infrastructure services first
        infrastructure_services = ["postgres", "redis", "prometheus"]
        
        for service in infrastructure_services:
            print(f"Starting {service}...")
            result = subprocess.run(
                ["docker-compose", "up", "-d", service], 
                capture_output=True, text=True
            )
            if result.returncode != 0:
                raise Exception(f"Failed to start {service}: {result.stderr}")
            
            # Wait for service to be ready
            await self._wait_for_service(service)
            print(f"✅ {service} started and ready")
        
        print("✅ Infrastructure deployment completed")
    
    async def _wait_for_service(self, service: str, timeout: int = 60):
        """Wait for a service to become ready."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                result = subprocess.run(
                    ["docker-compose", "exec", "-T", service, "echo", "ready"],
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    return
            except subprocess.TimeoutExpired:
                pass
            
            await asyncio.sleep(2)
        
        raise Exception(f"Service {service} did not become ready within {timeout} seconds")
    
    async def _deploy_application(self):
        """Deploy the trading bot application."""
        print("\n📦 Step 4: Application Deployment")
        print("-" * 40)
        
        # Build application image
        print("Building trading bot image...")
        result = subprocess.run(
            ["docker-compose", "build", "trading-bot"], 
            capture_output=True, text=True
        )
        if result.returncode != 0:
            raise Exception(f"Failed to build application: {result.stderr}")
        
        print("✅ Application image built")
        
        # Start application
        print("Starting trading bot...")
        result = subprocess.run(
            ["docker-compose", "up", "-d", "trading-bot"], 
            capture_output=True, text=True
        )
        if result.returncode != 0:
            raise Exception(f"Failed to start application: {result.stderr}")
        
        # Wait for application to be ready
        await self._wait_for_application()
        
        print("✅ Application deployment completed")
    
    async def _wait_for_application(self, timeout: int = 120):
        """Wait for the trading bot application to become ready."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check application health endpoint
                result = subprocess.run(
                    ["docker-compose", "exec", "-T", "trading-bot", "curl", "-f", "http://localhost:8000/health"],
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0:
                    return
            except subprocess.TimeoutExpired:
                pass
            
            await asyncio.sleep(5)
        
        raise Exception(f"Application did not become ready within {timeout} seconds")
    
    async def _post_deployment_verification(self):
        """Verify deployment success."""
        print("\n✅ Step 5: Post-deployment Verification")
        print("-" * 40)
        
        # Check all services are running
        result = subprocess.run(["docker-compose", "ps"], capture_output=True, text=True)
        if "Exit" in result.stdout:
            raise Exception("Some services failed to start")
        
        print("✅ All services running")
        
        # Check application logs for errors
        result = subprocess.run(
            ["docker-compose", "logs", "--tail=50", "trading-bot"], 
            capture_output=True, text=True
        )
        
        if "ERROR" in result.stdout or "CRITICAL" in result.stdout:
            print("⚠️  Found errors in application logs:")
            print(result.stdout)
            
            if self.environment == Environment.PRODUCTION:
                raise Exception("Critical errors found in production deployment")
        
        print("✅ Application logs verified")
        
        # Test API endpoints
        await self._test_api_endpoints()
        
        print("✅ Post-deployment verification completed")
    
    async def _test_api_endpoints(self):
        """Test critical API endpoints."""
        endpoints = [
            "/health",
            "/metrics", 
            "/status"
        ]
        
        for endpoint in endpoints:
            try:
                result = subprocess.run(
                    ["docker-compose", "exec", "-T", "trading-bot", "curl", "-f", f"http://localhost:8000{endpoint}"],
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode != 0:
                    raise Exception(f"Endpoint {endpoint} failed")
            except subprocess.TimeoutExpired:
                raise Exception(f"Endpoint {endpoint} timeout")
        
        print("✅ API endpoints tested")
    
    async def _setup_monitoring(self):
        """Setup monitoring and alerting."""
        print("\n📊 Step 6: Monitoring Setup")
        print("-" * 40)
        
        # Start Grafana
        print("Starting Grafana...")
        result = subprocess.run(
            ["docker-compose", "up", "-d", "grafana"], 
            capture_output=True, text=True
        )
        if result.returncode != 0:
            raise Exception(f"Failed to start Grafana: {result.stderr}")
        
        await self._wait_for_service("grafana")
        print("✅ Grafana started")
        
        # Import dashboards (would be implemented with actual dashboard files)
        print("✅ Monitoring dashboards configured")
        
        print("✅ Monitoring setup completed")
    
    async def _final_health_check(self):
        """Perform final comprehensive health check."""
        print("\n🏥 Step 7: Final Health Check")
        print("-" * 40)
        
        # Check system resources
        import psutil
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory_percent = psutil.virtual_memory().percent
        
        print(f"✅ System resources: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%")
        
        # Check all containers
        result = subprocess.run(["docker-compose", "ps", "--services"], capture_output=True, text=True)
        services = result.stdout.strip().split('\n')
        
        for service in services:
            if service:
                result = subprocess.run(
                    ["docker-compose", "ps", service], 
                    capture_output=True, text=True
                )
                if "Up" not in result.stdout:
                    raise Exception(f"Service {service} is not running")
        
        print(f"✅ All {len(services)} services healthy")
        
        print("✅ Final health check completed")
    
    async def _rollback_deployment(self):
        """Rollback failed deployment."""
        print("\n🔄 Rolling back deployment...")
        
        try:
            subprocess.run(["docker-compose", "down"], capture_output=True)
            print("✅ Services stopped")
        except Exception as e:
            print(f"⚠️  Rollback warning: {e}")
    
    def _show_access_info(self):
        """Show access information for deployed services."""
        print("\n🌐 Access Information")
        print("-" * 40)
        
        print(f"📊 Grafana Dashboard: http://localhost:{self.config_manager.monitoring.grafana_port}")
        print(f"📈 Prometheus Metrics: http://localhost:{self.config_manager.monitoring.prometheus_port}")
        print(f"🤖 Trading Bot API: http://localhost:8000")
        print(f"📋 Health Check: http://localhost:8000/health")
        print(f"📊 Metrics: http://localhost:8000/metrics")
        
        print("\n🔐 Default Credentials:")
        print("Grafana: admin / admin (change on first login)")
        
        print("\n📝 Useful Commands:")
        print("# View logs")
        print("docker-compose logs -f trading-bot")
        print("")
        print("# Check status")
        print("docker-compose ps")
        print("")
        print("# Stop services")
        print("docker-compose down")


async def main():
    """Main deployment function."""
    if len(sys.argv) != 2:
        print("Usage: python deploy.py <environment>")
        print("Environments: development, testing, staging, production")
        sys.exit(1)
    
    try:
        environment = Environment(sys.argv[1])
    except ValueError:
        print(f"Invalid environment: {sys.argv[1]}")
        print("Valid environments: development, testing, staging, production")
        sys.exit(1)
    
    # Production safety check
    if environment == Environment.PRODUCTION:
        print("⚠️  WARNING: You are deploying to PRODUCTION!")
        print("This will start LIVE TRADING with REAL MONEY!")
        confirm = input("Type 'CONFIRM PRODUCTION DEPLOYMENT' to continue: ")
        
        if confirm != "CONFIRM PRODUCTION DEPLOYMENT":
            print("❌ Production deployment cancelled")
            sys.exit(1)
    
    deployer = TradingBotDeployer(environment)
    await deployer.deploy()


if __name__ == "__main__":
    asyncio.run(main())
