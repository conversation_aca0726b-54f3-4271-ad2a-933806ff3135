# Emergency System Requirements
# Install these for the critical emergency components

# Core dependencies
asyncio
datetime
typing
dataclasses
enum
json
pathlib

# Security
cryptography>=3.4.8
keyring>=23.0.0

# Web dashboard
flask>=2.0.0

# Data processing
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# Optional: For production deployment
# gunicorn>=20.1.0  # Production WSGI server
# redis>=4.0.0      # For session storage
# celery>=5.2.0     # For background tasks
