"""Command-line interface for the trading bot."""

import asyncio
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table

from .core.config import settings
from .core.logger import get_logger
from .api.webull_client import WebullClient

app = typer.Typer(help="AI Trading Bot CLI")
console = Console()
logger = get_logger(__name__)


@app.command()
def info():
    """Display system information."""
    table = Table(title="AI Trading Bot Information")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("App Name", settings.app_name)
    table.add_row("Version", settings.app_version)
    table.add_row("Environment", settings.environment)
    table.add_row("Debug Mode", str(settings.debug))
    table.add_row("Trading Enabled", str(settings.trading_enabled))
    table.add_row("Paper Trading", str(settings.webull.paper_trading))
    table.add_row("Data Directory", str(settings.data_directory))
    table.add_row("Log Level", settings.log_level)
    
    console.print(table)


@app.command()
def test_webull():
    """Test Webull API connection."""
    async def _test():
        try:
            async with WebullClient() as client:
                console.print("Testing Webull API connection...")
                
                # Test basic connection
                quote = await client.get_quote("AAPL")
                console.print(f"✅ Successfully retrieved AAPL quote: ${quote.get('price', 'N/A')}")
                
                # Test search
                results = await client.search_stocks("TSLA")
                console.print(f"✅ Search results for TSLA: {len(results)} found")
                
                console.print("🎉 Webull API connection test successful!")
                
        except Exception as e:
            console.print(f"❌ Webull API test failed: {e}")
            raise typer.Exit(1)
    
    asyncio.run(_test())


@app.command()
def login(
    username: Optional[str] = typer.Option(None, "--username", "-u", help="Webull username"),
    password: Optional[str] = typer.Option(None, "--password", "-p", help="Webull password", hide_input=True),
):
    """Login to Webull."""
    if not username:
        username = typer.prompt("Webull username")
    if not password:
        password = typer.prompt("Webull password", hide_input=True)
    
    async def _login():
        try:
            async with WebullClient() as client:
                console.print("Logging in to Webull...")
                success = await client.login(username, password)
                
                if success:
                    console.print("✅ Successfully logged in to Webull!")
                    
                    # Get account info
                    account_info = await client.get_account_info()
                    console.print(f"Account ID: {account_info.get('accountId', 'N/A')}")
                    
                else:
                    console.print("❌ Login failed")
                    raise typer.Exit(1)
                    
        except Exception as e:
            console.print(f"❌ Login error: {e}")
            raise typer.Exit(1)
    
    asyncio.run(_login())


@app.command()
def positions():
    """Display current positions."""
    async def _positions():
        try:
            async with WebullClient() as client:
                positions = await client.get_positions()
                
                if not positions:
                    console.print("No positions found.")
                    return
                
                table = Table(title="Current Positions")
                table.add_column("Symbol", style="cyan")
                table.add_column("Quantity", style="green")
                table.add_column("Avg Price", style="yellow")
                table.add_column("Current Price", style="blue")
                table.add_column("P&L", style="red")
                
                for position in positions:
                    symbol = position.get("symbol", "N/A")
                    quantity = str(position.get("quantity", 0))
                    avg_price = f"${position.get('avgPrice', 0):.2f}"
                    current_price = f"${position.get('currentPrice', 0):.2f}"
                    pnl = f"${position.get('unrealizedPnL', 0):.2f}"
                    
                    table.add_row(symbol, quantity, avg_price, current_price, pnl)
                
                console.print(table)
                
        except Exception as e:
            console.print(f"❌ Error retrieving positions: {e}")
            raise typer.Exit(1)
    
    asyncio.run(_positions())


@app.command()
def quote(symbol: str):
    """Get real-time quote for a symbol."""
    async def _quote():
        try:
            async with WebullClient() as client:
                quote_data = await client.get_quote(symbol.upper())
                
                table = Table(title=f"Quote for {symbol.upper()}")
                table.add_column("Field", style="cyan")
                table.add_column("Value", style="green")
                
                table.add_row("Price", f"${quote_data.get('price', 'N/A')}")
                table.add_row("Change", f"${quote_data.get('change', 'N/A')}")
                table.add_row("Change %", f"{quote_data.get('changePercent', 'N/A')}%")
                table.add_row("Volume", str(quote_data.get('volume', 'N/A')))
                table.add_row("High", f"${quote_data.get('high', 'N/A')}")
                table.add_row("Low", f"${quote_data.get('low', 'N/A')}")
                table.add_row("Open", f"${quote_data.get('open', 'N/A')}")
                table.add_row("Prev Close", f"${quote_data.get('prevClose', 'N/A')}")
                
                console.print(table)
                
        except Exception as e:
            console.print(f"❌ Error retrieving quote: {e}")
            raise typer.Exit(1)
    
    asyncio.run(_quote())


@app.command()
def setup():
    """Set up the trading bot environment."""
    console.print("🚀 Setting up AI Trading Bot...")
    
    # Create directories
    directories = [
        settings.data_directory,
        settings.cache_directory,
        settings.model_directory,
        Path("logs"),
        Path("config"),
        Path("tests"),
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        console.print(f"✅ Created directory: {directory}")
    
    # Create .env template if it doesn't exist
    env_file = Path(".env")
    if not env_file.exists():
        env_template = """# AI Trading Bot Configuration

# Application Settings
APP_NAME=AI Trading Bot
APP_VERSION=0.1.0
DEBUG=false
ENVIRONMENT=development
LOG_LEVEL=INFO

# Trading Settings
TRADING_ENABLED=false
TRADING_HOURS_START=09:30
TRADING_HOURS_END=16:00
TIMEZONE=US/Eastern

# Webull API Settings
WEBULL_USERNAME=
WEBULL_PASSWORD=
WEBULL_PAPER_TRADING=true
WEBULL_MAX_REQUESTS_PER_MINUTE=60
WEBULL_REQUEST_DELAY=1.0

# Database Settings
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=trading_bot
POSTGRES_PASSWORD=
POSTGRES_DATABASE=trading_bot

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_USER=
MONGO_PASSWORD=
MONGO_DATABASE=trading_bot

# Risk Management
RISK_MAX_POSITION_SIZE=0.05
RISK_MAX_PORTFOLIO_RISK=0.02
RISK_MAX_DAILY_DRAWDOWN=0.02
RISK_DEFAULT_STOP_LOSS=0.02

# Machine Learning
ML_TRAIN_TEST_SPLIT=0.8
ML_VALIDATION_SPLIT=0.2
ML_RANDOM_SEED=42
ML_MIN_SHARPE_RATIO=1.5
ML_MIN_WIN_RATE=0.55
"""
        env_file.write_text(env_template)
        console.print(f"✅ Created .env template: {env_file}")
    
    console.print("\n🎉 Setup complete!")
    console.print("\n📝 Next steps:")
    console.print("1. Edit .env file with your configuration")
    console.print("2. Set up databases (PostgreSQL, Redis, MongoDB)")
    console.print("3. Install dependencies: pip install -e .")
    console.print("4. Test Webull connection: trading-bot test-webull")


@app.command()
def run():
    """Run the trading bot."""
    console.print("🤖 Starting AI Trading Bot...")
    
    if not settings.trading_enabled:
        console.print("⚠️  Trading is disabled. Enable in configuration to start trading.")
    
    # TODO: Implement main trading loop
    console.print("🚧 Trading bot implementation coming soon...")


def main():
    """Main CLI entry point."""
    app()


if __name__ == "__main__":
    main()
