# AI Trading Bot 🤖

An advanced AI-powered trading system for Webull with machine learning capabilities, sophisticated risk management, and automated execution.

## 🚀 Features

### Phase 1: Foundation & Infrastructure ✅
- **Unified API Framework**: Async-first architecture for Webull integration
- **Multi-Database Support**: PostgreSQL, Redis, and MongoDB
- **Comprehensive Configuration**: Environment-based settings management
- **Advanced Logging**: Structured logging with rotation and retention
- **CLI Interface**: Command-line tools for management and testing

### Planned Features
- **Machine Learning**: Ensemble models with LSTM, XGBoost, and Transformers
- **Risk Management**: Kelly Criterion, correlation tracking, dynamic stops
- **Multi-Source Data**: News, social sentiment, economic indicators
- **Advanced Strategies**: Momentum, mean reversion, market making
- **Real-time Execution**: Smart order routing and portfolio optimization
- **Continuous Learning**: Adaptive algorithms and strategy evolution

## 📋 Requirements

- Python 3.9+
- PostgreSQL 13+
- Redis 6+
- MongoDB 5+

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/imthebreezy247/Trading-bot.git
   cd Trading-bot
   ```

2. **Set up virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -e .
   ```

4. **Set up configuration**:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

5. **Initialize the project**:
   ```bash
   trading-bot setup
   ```

## ⚙️ Configuration

The bot uses environment variables for configuration. Copy `.env.example` to `.env` and configure:

### Essential Settings
- **Webull Credentials**: Username and password for API access
- **Database URLs**: PostgreSQL, Redis, and MongoDB connections
- **Risk Parameters**: Position sizing, stop losses, drawdown limits
- **Trading Settings**: Hours, timezone, paper trading mode

### Database Setup

#### PostgreSQL
```bash
# Install PostgreSQL and create database
createdb trading_bot
```

#### Redis
```bash
# Install and start Redis
redis-server
```

#### MongoDB
```bash
# Install and start MongoDB
mongod
```

## 🎯 Quick Start

1. **Test Webull connection**:
   ```bash
   trading-bot test-webull
   ```

2. **Login to Webull**:
   ```bash
   trading-bot login
   ```

3. **Get real-time quote**:
   ```bash
   trading-bot quote AAPL
   ```

4. **View positions**:
   ```bash
   trading-bot positions
   ```

5. **Run the bot** (when ready):
   ```bash
   trading-bot run
   ```

## 📊 System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │  ML Pipeline    │    │  Risk Manager   │
│                 │    │                 │    │                 │
│ • Webull API    │───▶│ • Feature Eng.  │───▶│ • Position Size │
│ • News APIs     │    │ • Model Training│    │ • Stop Losses   │
│ • Social Media  │    │ • Predictions   │    │ • Correlation   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Databases     │    │   Strategies    │    │   Execution     │
│                 │    │                 │    │                 │
│ • PostgreSQL    │    │ • Momentum      │    │ • Order Router  │
│ • Redis Cache   │◀───│ • Mean Rev.     │───▶│ • Portfolio Opt │
│ • MongoDB       │    │ • ML Signals    │    │ • Risk Controls │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Development

### Project Structure
```
src/trading_bot/
├── core/           # Core configuration and utilities
├── api/            # External API integrations
├── data/           # Data processing and storage
├── ml/             # Machine learning models
├── strategies/     # Trading strategies
├── risk/           # Risk management
├── execution/      # Order execution
└── monitoring/     # System monitoring
```

### Running Tests
```bash
pytest tests/
```

### Code Quality
```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Type checking
mypy src/

# Linting
flake8 src/ tests/
```

## 📈 Roadmap

See [ROADMAP.md](ROADMAP.md) for the complete 20-week development plan.

### Current Phase: Foundation & Infrastructure (Weeks 1-3)
- [x] Core architecture setup
- [x] Configuration management
- [x] Basic Webull API client
- [x] CLI interface
- [ ] Database schemas
- [ ] Data pipeline framework
- [ ] Risk management foundation

## ⚠️ Risk Disclaimer

This software is for educational and research purposes only. Trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. Use at your own risk.

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

- Create an issue for bug reports
- Join discussions for questions
- Check the wiki for documentation
