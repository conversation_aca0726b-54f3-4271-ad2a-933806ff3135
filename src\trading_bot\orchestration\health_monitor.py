"""Health Monitor - Comprehensive system health monitoring and alerting.

This module provides real-time monitoring of:
- System resources (CPU, memory, disk, network)
- Service health checks and availability
- Database connections and performance
- API rate limits and connectivity
- Model performance and drift detection
- Alert generation and notification
"""

import asyncio
import psutil
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
import aioredis
import asyncpg

from ..core.config import settings
from ..utils.logger import get_structured_logger
from ..data.database import get_postgres_session
from ..utils.cache import cache_manager

logger = get_structured_logger(__name__)


class HealthStatus(Enum):
    """Health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class HealthMetric:
    """Represents a health metric."""
    name: str
    value: float
    unit: str
    status: HealthStatus
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class HealthAlert:
    """Represents a health alert."""
    id: str
    component: str
    message: str
    severity: AlertSeverity
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SystemHealth:
    """Overall system health status."""
    status: HealthStatus
    timestamp: datetime
    metrics: Dict[str, HealthMetric]
    alerts: List[HealthAlert]
    uptime: float
    components: Dict[str, HealthStatus]


class HealthMonitor:
    """
    Comprehensive system health monitoring and alerting system.
    
    Monitors all aspects of the trading bot system and generates
    alerts when issues are detected.
    """
    
    def __init__(self):
        self.is_running = False
        self.start_time = datetime.utcnow()
        self.shutdown_event = asyncio.Event()
        
        # Health data storage
        self.current_metrics: Dict[str, HealthMetric] = {}
        self.active_alerts: Dict[str, HealthAlert] = {}
        self.alert_history: List[HealthAlert] = []
        
        # Monitoring tasks
        self.monitoring_tasks: List[asyncio.Task] = []
        
        # Alert callbacks
        self.alert_callbacks: List[Callable] = []
        
        # HTTP session for external checks
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Thresholds
        self.thresholds = {
            'cpu_usage': {'warning': 80.0, 'critical': 95.0},
            'memory_usage': {'warning': 85.0, 'critical': 95.0},
            'disk_usage': {'warning': 85.0, 'critical': 95.0},
            'api_response_time': {'warning': 1000.0, 'critical': 5000.0},  # ms
            'database_connections': {'warning': 80, 'critical': 95},
            'error_rate': {'warning': 5.0, 'critical': 10.0},  # percentage
        }
    
    async def initialize(self):
        """Initialize the health monitor."""
        logger.info("Initializing health monitor...")
        
        # Create HTTP session
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        # Start monitoring tasks
        await self._start_monitoring_tasks()
        
        self.is_running = True
        logger.info("Health monitor initialized")
    
    async def _start_monitoring_tasks(self):
        """Start all monitoring background tasks."""
        monitoring_tasks = [
            self._system_resources_monitor(),
            self._database_health_monitor(),
            self._api_health_monitor(),
            self._service_health_monitor(),
            self._model_performance_monitor(),
            self._alert_processor()
        ]
        
        for task_coro in monitoring_tasks:
            task = asyncio.create_task(task_coro)
            self.monitoring_tasks.append(task)
        
        logger.info(f"Started {len(self.monitoring_tasks)} monitoring tasks")
    
    async def _system_resources_monitor(self):
        """Monitor system resources (CPU, memory, disk)."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                await self._record_metric(
                    "cpu_usage", cpu_percent, "percent",
                    self.thresholds['cpu_usage']
                )
                
                # Memory usage
                memory = psutil.virtual_memory()
                await self._record_metric(
                    "memory_usage", memory.percent, "percent",
                    self.thresholds['memory_usage']
                )
                
                # Disk usage
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                await self._record_metric(
                    "disk_usage", disk_percent, "percent",
                    self.thresholds['disk_usage']
                )
                
                # Network I/O
                net_io = psutil.net_io_counters()
                await self._record_metric(
                    "network_bytes_sent", net_io.bytes_sent, "bytes"
                )
                await self._record_metric(
                    "network_bytes_recv", net_io.bytes_recv, "bytes"
                )
                
                # Process count
                process_count = len(psutil.pids())
                await self._record_metric(
                    "process_count", process_count, "count"
                )
                
                await asyncio.sleep(30)  # Check every 30 seconds
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in system resources monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _database_health_monitor(self):
        """Monitor database connections and performance."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # PostgreSQL health
                await self._check_postgres_health()
                
                # Redis health
                await self._check_redis_health()
                
                # MongoDB health (if configured)
                if hasattr(settings, 'mongodb') and settings.mongodb.enabled:
                    await self._check_mongodb_health()
                
                await asyncio.sleep(60)  # Check every minute
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in database health monitoring: {e}")
                await asyncio.sleep(120)
    
    async def _check_postgres_health(self):
        """Check PostgreSQL database health."""
        try:
            start_time = time.time()
            
            # Test connection and simple query
            async with get_postgres_session() as session:
                result = await session.execute("SELECT 1")
                await result.fetchone()
            
            response_time = (time.time() - start_time) * 1000  # ms
            
            await self._record_metric(
                "postgres_response_time", response_time, "ms",
                {'warning': 100.0, 'critical': 1000.0}
            )
            
            await self._record_metric(
                "postgres_status", 1.0, "boolean"  # 1 = healthy, 0 = unhealthy
            )
            
        except Exception as e:
            logger.error(f"PostgreSQL health check failed: {e}")
            await self._record_metric("postgres_status", 0.0, "boolean")
            await self._generate_alert(
                "postgres_connection",
                f"PostgreSQL connection failed: {e}",
                AlertSeverity.CRITICAL
            )
    
    async def _check_redis_health(self):
        """Check Redis cache health."""
        try:
            start_time = time.time()
            
            # Test Redis connection
            health_check = await cache_manager.health_check()
            
            response_time = (time.time() - start_time) * 1000  # ms
            
            await self._record_metric(
                "redis_response_time", response_time, "ms",
                {'warning': 50.0, 'critical': 500.0}
            )
            
            await self._record_metric(
                "redis_status", 1.0 if health_check else 0.0, "boolean"
            )
            
            if not health_check:
                await self._generate_alert(
                    "redis_connection",
                    "Redis health check failed",
                    AlertSeverity.CRITICAL
                )
        
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            await self._record_metric("redis_status", 0.0, "boolean")
            await self._generate_alert(
                "redis_connection",
                f"Redis connection failed: {e}",
                AlertSeverity.CRITICAL
            )
    
    async def _check_mongodb_health(self):
        """Check MongoDB health."""
        try:
            # This would be implemented when MongoDB client is available
            # For now, placeholder
            await self._record_metric("mongodb_status", 1.0, "boolean")
        
        except Exception as e:
            logger.error(f"MongoDB health check failed: {e}")
            await self._record_metric("mongodb_status", 0.0, "boolean")
    
    async def _api_health_monitor(self):
        """Monitor external API health and rate limits."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Check Webull API health
                await self._check_webull_api_health()
                
                # Check other external APIs
                await self._check_external_apis_health()
                
                await asyncio.sleep(120)  # Check every 2 minutes
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in API health monitoring: {e}")
                await asyncio.sleep(180)
    
    async def _check_webull_api_health(self):
        """Check Webull API connectivity and rate limits."""
        try:
            # This would integrate with the actual WebullAPI client
            # For now, placeholder implementation
            
            start_time = time.time()
            
            # Simulate API health check
            # In real implementation, this would call the actual API
            response_time = (time.time() - start_time) * 1000
            
            await self._record_metric(
                "webull_api_response_time", response_time, "ms",
                self.thresholds['api_response_time']
            )
            
            await self._record_metric("webull_api_status", 1.0, "boolean")
            
            # Check rate limits (placeholder)
            await self._record_metric("webull_api_rate_limit_remaining", 100.0, "count")
        
        except Exception as e:
            logger.error(f"Webull API health check failed: {e}")
            await self._record_metric("webull_api_status", 0.0, "boolean")
            await self._generate_alert(
                "webull_api",
                f"Webull API health check failed: {e}",
                AlertSeverity.CRITICAL
            )
    
    async def _check_external_apis_health(self):
        """Check other external APIs health."""
        external_apis = [
            {"name": "news_api", "url": "https://api.example.com/health"},
            {"name": "economic_data_api", "url": "https://api.example.com/health"}
        ]
        
        for api in external_apis:
            try:
                start_time = time.time()
                
                async with self.session.get(api["url"]) as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    await self._record_metric(
                        f"{api['name']}_response_time", response_time, "ms"
                    )
                    
                    status = 1.0 if response.status == 200 else 0.0
                    await self._record_metric(f"{api['name']}_status", status, "boolean")
            
            except Exception as e:
                logger.warning(f"{api['name']} health check failed: {e}")
                await self._record_metric(f"{api['name']}_status", 0.0, "boolean")

    async def _service_health_monitor(self):
        """Monitor internal service health."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # This would integrate with the ServiceManager
                # For now, placeholder implementation

                services = ["data_service", "ml_service", "strategy_service",
                           "execution_service", "risk_service", "analytics_service"]

                for service in services:
                    # Simulate service health check
                    await self._record_metric(f"{service}_status", 1.0, "boolean")

                await asyncio.sleep(60)  # Check every minute

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in service health monitoring: {e}")
                await asyncio.sleep(120)

    async def _model_performance_monitor(self):
        """Monitor ML model performance and drift."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # This would integrate with the ML pipeline
                # Monitor model accuracy, prediction latency, drift detection

                # Placeholder metrics
                await self._record_metric("model_accuracy", 0.85, "ratio")
                await self._record_metric("model_prediction_latency", 50.0, "ms")
                await self._record_metric("model_drift_score", 0.1, "score")

                await asyncio.sleep(300)  # Check every 5 minutes

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in model performance monitoring: {e}")
                await asyncio.sleep(600)

    async def _alert_processor(self):
        """Process and manage alerts."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Check for alert conditions
                await self._evaluate_alert_conditions()

                # Clean up resolved alerts
                await self._cleanup_resolved_alerts()

                await asyncio.sleep(30)  # Process every 30 seconds

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert processing: {e}")
                await asyncio.sleep(60)

    async def _record_metric(self, name: str, value: float, unit: str,
                           thresholds: Optional[Dict[str, float]] = None):
        """Record a health metric."""
        status = HealthStatus.HEALTHY

        if thresholds:
            if value >= thresholds.get('critical', float('inf')):
                status = HealthStatus.CRITICAL
            elif value >= thresholds.get('warning', float('inf')):
                status = HealthStatus.WARNING

        metric = HealthMetric(
            name=name,
            value=value,
            unit=unit,
            status=status,
            threshold_warning=thresholds.get('warning') if thresholds else None,
            threshold_critical=thresholds.get('critical') if thresholds else None
        )

        self.current_metrics[name] = metric

        # Generate alert if metric is critical
        if status == HealthStatus.CRITICAL:
            await self._generate_alert(
                f"metric_{name}",
                f"Critical threshold exceeded for {name}: {value} {unit}",
                AlertSeverity.CRITICAL,
                {"metric": name, "value": value, "unit": unit}
            )
        elif status == HealthStatus.WARNING:
            await self._generate_alert(
                f"metric_{name}",
                f"Warning threshold exceeded for {name}: {value} {unit}",
                AlertSeverity.WARNING,
                {"metric": name, "value": value, "unit": unit}
            )

    async def _generate_alert(self, component: str, message: str,
                            severity: AlertSeverity, metadata: Optional[Dict] = None):
        """Generate a new alert."""
        alert_id = f"{component}_{int(time.time())}"

        # Check if similar alert already exists
        existing_alert = None
        for alert in self.active_alerts.values():
            if alert.component == component and not alert.resolved:
                existing_alert = alert
                break

        if existing_alert:
            # Update existing alert
            existing_alert.timestamp = datetime.utcnow()
            existing_alert.message = message
            existing_alert.severity = severity
            if metadata:
                existing_alert.metadata.update(metadata)
        else:
            # Create new alert
            alert = HealthAlert(
                id=alert_id,
                component=component,
                message=message,
                severity=severity,
                timestamp=datetime.utcnow(),
                metadata=metadata or {}
            )

            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)

            # Notify alert callbacks
            for callback in self.alert_callbacks:
                try:
                    await callback(alert)
                except Exception as e:
                    logger.error(f"Error in alert callback: {e}")

            logger.warning(f"Alert generated: {severity.value} - {component}: {message}")

    async def _evaluate_alert_conditions(self):
        """Evaluate conditions that might trigger alerts."""
        # Check system uptime
        uptime = (datetime.utcnow() - self.start_time).total_seconds()
        if uptime < 300:  # Less than 5 minutes uptime
            await self._generate_alert(
                "system_uptime",
                f"System recently started (uptime: {uptime:.0f}s)",
                AlertSeverity.INFO
            )

        # Check for multiple critical metrics
        critical_metrics = [
            name for name, metric in self.current_metrics.items()
            if metric.status == HealthStatus.CRITICAL
        ]

        if len(critical_metrics) >= 3:
            await self._generate_alert(
                "system_health",
                f"Multiple critical metrics detected: {', '.join(critical_metrics)}",
                AlertSeverity.EMERGENCY
            )

    async def _cleanup_resolved_alerts(self):
        """Clean up old resolved alerts."""
        cutoff_time = datetime.utcnow() - timedelta(hours=24)

        # Remove resolved alerts older than 24 hours
        self.alert_history = [
            alert for alert in self.alert_history
            if not alert.resolved or alert.resolved_at > cutoff_time
        ]

        # Auto-resolve alerts for metrics that are now healthy
        for alert_id, alert in list(self.active_alerts.items()):
            if alert.component.startswith("metric_"):
                metric_name = alert.component.replace("metric_", "")
                current_metric = self.current_metrics.get(metric_name)

                if current_metric and current_metric.status == HealthStatus.HEALTHY:
                    await self.resolve_alert(alert_id)

    async def resolve_alert(self, alert_id: str):
        """Manually resolve an alert."""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.utcnow()

            del self.active_alerts[alert_id]

            logger.info(f"Alert resolved: {alert.component} - {alert.message}")

    def add_alert_callback(self, callback: Callable):
        """Add a callback function for alert notifications."""
        self.alert_callbacks.append(callback)

    async def perform_health_checks(self) -> SystemHealth:
        """Perform comprehensive health checks and return system status."""
        # Determine overall system status
        critical_count = sum(
            1 for metric in self.current_metrics.values()
            if metric.status == HealthStatus.CRITICAL
        )
        warning_count = sum(
            1 for metric in self.current_metrics.values()
            if metric.status == HealthStatus.WARNING
        )

        if critical_count > 0:
            overall_status = HealthStatus.CRITICAL
        elif warning_count > 0:
            overall_status = HealthStatus.WARNING
        else:
            overall_status = HealthStatus.HEALTHY

        # Calculate uptime
        uptime = (datetime.utcnow() - self.start_time).total_seconds()

        # Component status summary
        components = {
            "system_resources": self._get_component_status("cpu_usage", "memory_usage", "disk_usage"),
            "databases": self._get_component_status("postgres_status", "redis_status"),
            "apis": self._get_component_status("webull_api_status"),
            "services": self._get_component_status("data_service_status", "ml_service_status"),
            "models": self._get_component_status("model_accuracy", "model_drift_score")
        }

        return SystemHealth(
            status=overall_status,
            timestamp=datetime.utcnow(),
            metrics=self.current_metrics.copy(),
            alerts=list(self.active_alerts.values()),
            uptime=uptime,
            components=components
        )

    def _get_component_status(self, *metric_names: str) -> HealthStatus:
        """Get the worst status among a group of metrics."""
        statuses = []

        for metric_name in metric_names:
            metric = self.current_metrics.get(metric_name)
            if metric:
                statuses.append(metric.status)

        if not statuses:
            return HealthStatus.UNKNOWN

        # Return the worst status
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY

    async def get_system_health(self) -> Dict[str, Any]:
        """Get current system health as a dictionary."""
        health = await self.perform_health_checks()

        return {
            "status": health.status.value,
            "timestamp": health.timestamp.isoformat(),
            "uptime": health.uptime,
            "metrics_count": len(health.metrics),
            "active_alerts": len(health.alerts),
            "components": {k: v.value for k, v in health.components.items()},
            "critical_metrics": [
                name for name, metric in health.metrics.items()
                if metric.status == HealthStatus.CRITICAL
            ],
            "warning_metrics": [
                name for name, metric in health.metrics.items()
                if metric.status == HealthStatus.WARNING
            ]
        }

    def get_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get all current metrics."""
        return {
            name: {
                "value": metric.value,
                "unit": metric.unit,
                "status": metric.status.value,
                "timestamp": metric.timestamp.isoformat(),
                "threshold_warning": metric.threshold_warning,
                "threshold_critical": metric.threshold_critical
            }
            for name, metric in self.current_metrics.items()
        }

    def get_alerts(self, include_resolved: bool = False) -> List[Dict[str, Any]]:
        """Get current alerts."""
        alerts = list(self.active_alerts.values())

        if include_resolved:
            alerts.extend([a for a in self.alert_history if a.resolved])

        return [
            {
                "id": alert.id,
                "component": alert.component,
                "message": alert.message,
                "severity": alert.severity.value,
                "timestamp": alert.timestamp.isoformat(),
                "resolved": alert.resolved,
                "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None,
                "metadata": alert.metadata
            }
            for alert in alerts
        ]

    async def shutdown(self):
        """Graceful shutdown of the health monitor."""
        logger.info("Shutting down health monitor...")

        self.is_running = False
        self.shutdown_event.set()

        # Cancel all monitoring tasks
        for task in self.monitoring_tasks:
            task.cancel()

        if self.monitoring_tasks:
            await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)

        # Close HTTP session
        if self.session:
            await self.session.close()

        logger.info("Health monitor shutdown completed")
