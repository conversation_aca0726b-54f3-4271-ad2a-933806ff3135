# Staging Environment Configuration
# This file contains staging-specific settings for testing before production

security:
  hsm_provider: "mock"  # Use mock HSM for staging
  mfa_provider: "totp"
  secret_rotation_days: 7   # Faster rotation for testing
  audit_retention_days: 30  # Shorter retention for staging
  encryption_algorithm: "AES-256-GCM"
  key_derivation_iterations: 100000  # Lower for faster testing

broker:
  broker_type: "webull"
  api_endpoint: "https://api.webull.com"
  paper_trading: true  # ALWAYS paper trading in staging
  rate_limit_requests_per_second: 15  # Higher for testing
  timeout_seconds: 30
  retry_attempts: 3
  credentials_secret_name: "webull_staging_credentials"

monitoring:
  prometheus_port: 9091
  grafana_port: 3001
  alert_webhook_url: "https://hooks.slack.com/services/YOUR/STAGING/WEBHOOK"
  slack_webhook_url: "https://hooks.slack.com/services/YOUR/STAGING/WEBHOOK"
  email_smtp_server: "smtp.gmail.com"
  email_smtp_port: 587
  sms_provider: "mock"  # Mock SMS for staging
  phone_provider: "mock"

risk:
  max_daily_loss_percent: 1.0  # More conservative for testing
  max_position_percent: 3.0    # Smaller positions for testing
  max_sector_percent: 15.0     # Lower concentration for testing
  circuit_breaker_levels:
    level_1: 0.01  # 1% - More sensitive for testing
    level_2: 0.03  # 3% - Reduce positions
    level_3: 0.05  # 5% - Stop all trading
  pdt_enabled: true
  wash_sale_enabled: true
  position_limits_enabled: true

performance:
  optimization_level: "high"  # High performance but not real-time
  cpu_cores_trading: [0, 1]
  cpu_cores_data: [2, 3]
  memory_pool_size_mb: 64     # Smaller pool for staging
  huge_pages_enabled: false   # Disabled for easier testing
  numa_optimization: false    # Disabled for staging
  real_time_priority: 0       # No real-time priority

database:
  postgres_host: "staging-postgres.your-domain.com"
  postgres_port: 5432
  postgres_database: "trading_bot_staging"
  postgres_user: "trading_user"
  postgres_password_secret: "postgres_staging_password"
  redis_host: "staging-redis.your-domain.com"
  redis_port: 6379
  redis_password_secret: "redis_staging_password"
  connection_pool_size: 20
  query_timeout_seconds: 30

backup:
  backup_schedule_cron: "0 4 * * *"  # Daily at 4 AM UTC
  backup_retention_days: 7            # Shorter retention
  geographic_regions:
    - "us-east-1"      # Single region for staging
  s3_bucket: "your-trading-bot-backups-staging"
  encryption_enabled: true
  compression_enabled: true
  verification_enabled: true

# Staging-specific settings
environment_variables:
  LOG_LEVEL: "DEBUG"
  METRICS_ENABLED: "true"
  HEALTH_CHECK_ENABLED: "true"
  DEBUG_MODE: "true"
  PAPER_TRADING_OVERRIDE: "true"  # Force paper trading

# Relaxed thresholds for testing
monitoring_thresholds:
  slippage_bps:
    medium: 20
    high: 50
    critical: 100
  
  execution_time_ms:
    medium: 200
    high: 1000
    critical: 2000
  
  cpu_usage:
    medium: 80
    high: 90
    critical: 98
  
  memory_usage:
    medium: 80
    high: 95
    critical: 98
