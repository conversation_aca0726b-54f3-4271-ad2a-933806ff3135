"""
Audit trail system for AI Trading Bot production deployment.

This module provides comprehensive audit logging for compliance and
regulatory requirements in financial trading:

Audit Categories:
- Trading activities (orders, executions, cancellations)
- Risk management decisions (limit breaches, overrides)
- User actions (logins, configuration changes)
- System events (startups, shutdowns, errors)
- Data access (queries, exports, modifications)
- Security events (authentication, authorization)

Compliance Features:
- Immutable audit logs
- Cryptographic integrity verification
- Regulatory reporting formats
- Data retention policies
- Access controls and permissions
- Real-time monitoring and alerting
"""

import asyncio
import json
import hashlib
import hmac
import logging
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import aiofiles
from pathlib import Path
import sqlite3
import aiosqlite

from ....core.config import Config
from ....utils.logger import get_logger

logger = get_logger(__name__)


class AuditEventType(Enum):
    """Types of audit events."""
    TRADING_ORDER_PLACED = "trading.order.placed"
    TRADING_ORDER_EXECUTED = "trading.order.executed"
    TRADING_ORDER_CANCELLED = "trading.order.cancelled"
    TRADING_ORDER_REJECTED = "trading.order.rejected"
    
    RISK_LIMIT_BREACH = "risk.limit.breach"
    RISK_LIMIT_OVERRIDE = "risk.limit.override"
    RISK_POSITION_CLOSED = "risk.position.closed"
    
    USER_LOGIN = "user.login"
    USER_LOGOUT = "user.logout"
    USER_ACTION = "user.action"
    USER_CONFIG_CHANGE = "user.config.change"
    
    SYSTEM_STARTUP = "system.startup"
    SYSTEM_SHUTDOWN = "system.shutdown"
    SYSTEM_ERROR = "system.error"
    SYSTEM_CONFIG_CHANGE = "system.config.change"
    
    DATA_ACCESS = "data.access"
    DATA_EXPORT = "data.export"
    DATA_MODIFICATION = "data.modification"
    
    SECURITY_AUTH_SUCCESS = "security.auth.success"
    SECURITY_AUTH_FAILURE = "security.auth.failure"
    SECURITY_PERMISSION_DENIED = "security.permission.denied"
    SECURITY_SUSPICIOUS_ACTIVITY = "security.suspicious.activity"


class AuditSeverity(Enum):
    """Audit event severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Immutable audit event record."""
    event_id: str
    timestamp: datetime
    event_type: AuditEventType
    severity: AuditSeverity
    
    # Event details
    description: str
    component: str
    
    # User context
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    
    # Trading context
    account_id: Optional[str] = None
    order_id: Optional[str] = None
    symbol: Optional[str] = None
    quantity: Optional[float] = None
    price: Optional[float] = None
    
    # Risk context
    risk_metric: Optional[str] = None
    risk_value: Optional[float] = None
    risk_limit: Optional[float] = None
    
    # System context
    process_id: Optional[int] = None
    thread_id: Optional[int] = None
    
    # Additional data
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Integrity fields
    checksum: Optional[str] = None
    previous_checksum: Optional[str] = None
    
    def __post_init__(self):
        """Calculate checksum after initialization."""
        if not self.checksum:
            self.checksum = self._calculate_checksum()
    
    def _calculate_checksum(self) -> str:
        """Calculate SHA-256 checksum for integrity verification."""
        # Create a copy without checksum fields for hashing
        data = asdict(self)
        data.pop('checksum', None)
        data.pop('previous_checksum', None)
        
        # Convert to JSON string for consistent hashing
        json_str = json.dumps(data, sort_keys=True, default=str)
        
        # Calculate SHA-256 hash
        return hashlib.sha256(json_str.encode('utf-8')).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Verify event integrity using checksum."""
        expected_checksum = self._calculate_checksum()
        return self.checksum == expected_checksum


@dataclass
class AuditQuery:
    """Audit trail query parameters."""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    event_types: Optional[List[AuditEventType]] = None
    severity: Optional[AuditSeverity] = None
    user_id: Optional[str] = None
    component: Optional[str] = None
    order_id: Optional[str] = None
    symbol: Optional[str] = None
    limit: int = 1000
    offset: int = 0


class AuditTrail:
    """
    Comprehensive audit trail system for regulatory compliance.
    
    Provides immutable audit logging with cryptographic integrity
    verification and comprehensive querying capabilities.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.db_path = "data/audit_trail.db"
        self.secret_key = self._get_secret_key()
        self.last_checksum: Optional[str] = None
        
        # Audit settings
        self.retention_days = 2555  # 7 years for financial compliance
        self.max_memory_events = 1000
        self.batch_size = 100
        
        # In-memory cache for recent events
        self.recent_events: List[AuditEvent] = []
        
        # Event counters for monitoring
        self.event_counts: Dict[AuditEventType, int] = {}
        
    async def initialize(self):
        """Initialize audit trail system."""
        logger.info("Initializing audit trail system...")
        
        # Create data directory
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        await self._initialize_database()
        
        # Load last checksum for chain integrity
        await self._load_last_checksum()
        
        # Start background tasks
        asyncio.create_task(self._cleanup_loop())
        
        logger.info("Audit trail system initialized")
    
    async def log_event(self, 
                       event_type: AuditEventType,
                       description: str,
                       component: str,
                       severity: AuditSeverity = AuditSeverity.MEDIUM,
                       **kwargs) -> str:
        """Log an audit event."""
        # Generate unique event ID
        event_id = self._generate_event_id()
        
        # Create audit event
        event = AuditEvent(
            event_id=event_id,
            timestamp=datetime.utcnow(),
            event_type=event_type,
            severity=severity,
            description=description,
            component=component,
            previous_checksum=self.last_checksum,
            **kwargs
        )
        
        # Store event
        await self._store_event(event)
        
        # Update last checksum for chain integrity
        self.last_checksum = event.checksum
        
        # Add to memory cache
        self.recent_events.append(event)
        if len(self.recent_events) > self.max_memory_events:
            self.recent_events = self.recent_events[-self.max_memory_events:]
        
        # Update counters
        self.event_counts[event_type] = self.event_counts.get(event_type, 0) + 1
        
        logger.debug(f"Audit event logged: {event_type.value} - {event_id}")
        return event_id
    
    async def log_trading_order(self,
                               action: str,  # "placed", "executed", "cancelled", "rejected"
                               order_id: str,
                               symbol: str,
                               quantity: float,
                               price: Optional[float] = None,
                               user_id: Optional[str] = None,
                               **kwargs) -> str:
        """Log trading order event."""
        event_type_map = {
            "placed": AuditEventType.TRADING_ORDER_PLACED,
            "executed": AuditEventType.TRADING_ORDER_EXECUTED,
            "cancelled": AuditEventType.TRADING_ORDER_CANCELLED,
            "rejected": AuditEventType.TRADING_ORDER_REJECTED
        }
        
        event_type = event_type_map.get(action, AuditEventType.TRADING_ORDER_PLACED)
        severity = AuditSeverity.HIGH if action == "rejected" else AuditSeverity.MEDIUM
        
        return await self.log_event(
            event_type=event_type,
            description=f"Order {action}: {symbol} {quantity} @ {price}",
            component="trading_engine",
            severity=severity,
            order_id=order_id,
            symbol=symbol,
            quantity=quantity,
            price=price,
            user_id=user_id,
            **kwargs
        )
    
    async def log_risk_event(self,
                            event: str,  # "breach", "override", "position_closed"
                            risk_metric: str,
                            risk_value: float,
                            risk_limit: float,
                            user_id: Optional[str] = None,
                            **kwargs) -> str:
        """Log risk management event."""
        event_type_map = {
            "breach": AuditEventType.RISK_LIMIT_BREACH,
            "override": AuditEventType.RISK_LIMIT_OVERRIDE,
            "position_closed": AuditEventType.RISK_POSITION_CLOSED
        }
        
        event_type = event_type_map.get(event, AuditEventType.RISK_LIMIT_BREACH)
        severity = AuditSeverity.CRITICAL if event == "breach" else AuditSeverity.HIGH
        
        return await self.log_event(
            event_type=event_type,
            description=f"Risk {event}: {risk_metric} = {risk_value} (limit: {risk_limit})",
            component="risk_manager",
            severity=severity,
            risk_metric=risk_metric,
            risk_value=risk_value,
            risk_limit=risk_limit,
            user_id=user_id,
            **kwargs
        )
    
    async def log_user_action(self,
                             action: str,
                             user_id: str,
                             session_id: Optional[str] = None,
                             ip_address: Optional[str] = None,
                             **kwargs) -> str:
        """Log user action event."""
        return await self.log_event(
            event_type=AuditEventType.USER_ACTION,
            description=f"User action: {action}",
            component="user_interface",
            severity=AuditSeverity.LOW,
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
            **kwargs
        )
    
    async def log_security_event(self,
                                event: str,  # "auth_success", "auth_failure", "permission_denied", "suspicious"
                                user_id: Optional[str] = None,
                                ip_address: Optional[str] = None,
                                **kwargs) -> str:
        """Log security event."""
        event_type_map = {
            "auth_success": AuditEventType.SECURITY_AUTH_SUCCESS,
            "auth_failure": AuditEventType.SECURITY_AUTH_FAILURE,
            "permission_denied": AuditEventType.SECURITY_PERMISSION_DENIED,
            "suspicious": AuditEventType.SECURITY_SUSPICIOUS_ACTIVITY
        }
        
        event_type = event_type_map.get(event, AuditEventType.SECURITY_AUTH_FAILURE)
        severity_map = {
            "auth_success": AuditSeverity.LOW,
            "auth_failure": AuditSeverity.MEDIUM,
            "permission_denied": AuditSeverity.HIGH,
            "suspicious": AuditSeverity.CRITICAL
        }
        severity = severity_map.get(event, AuditSeverity.MEDIUM)
        
        return await self.log_event(
            event_type=event_type,
            description=f"Security event: {event}",
            component="security_manager",
            severity=severity,
            user_id=user_id,
            ip_address=ip_address,
            **kwargs
        )

    async def query_events(self, query: AuditQuery) -> List[AuditEvent]:
        """Query audit events with filters."""
        async with aiosqlite.connect(self.db_path) as db:
            # Build SQL query
            sql = "SELECT * FROM audit_events WHERE 1=1"
            params = []

            if query.start_time:
                sql += " AND timestamp >= ?"
                params.append(query.start_time.isoformat())

            if query.end_time:
                sql += " AND timestamp <= ?"
                params.append(query.end_time.isoformat())

            if query.event_types:
                placeholders = ",".join("?" * len(query.event_types))
                sql += f" AND event_type IN ({placeholders})"
                params.extend([et.value for et in query.event_types])

            if query.severity:
                sql += " AND severity = ?"
                params.append(query.severity.value)

            if query.user_id:
                sql += " AND user_id = ?"
                params.append(query.user_id)

            if query.component:
                sql += " AND component = ?"
                params.append(query.component)

            if query.order_id:
                sql += " AND order_id = ?"
                params.append(query.order_id)

            if query.symbol:
                sql += " AND symbol = ?"
                params.append(query.symbol)

            sql += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
            params.extend([query.limit, query.offset])

            # Execute query
            async with db.execute(sql, params) as cursor:
                rows = await cursor.fetchall()

            # Convert rows to AuditEvent objects
            events = []
            for row in rows:
                event_data = dict(row)

                # Parse JSON fields
                if event_data['metadata']:
                    event_data['metadata'] = json.loads(event_data['metadata'])
                else:
                    event_data['metadata'] = {}

                # Parse datetime
                event_data['timestamp'] = datetime.fromisoformat(event_data['timestamp'])

                # Parse enums
                event_data['event_type'] = AuditEventType(event_data['event_type'])
                event_data['severity'] = AuditSeverity(event_data['severity'])

                events.append(AuditEvent(**event_data))

            return events

    async def verify_chain_integrity(self, start_time: Optional[datetime] = None) -> bool:
        """Verify audit trail chain integrity."""
        query = AuditQuery(start_time=start_time, limit=10000)
        events = await self.query_events(query)

        if not events:
            return True

        # Sort by timestamp
        events.sort(key=lambda x: x.timestamp)

        # Verify each event's integrity
        for event in events:
            if not event.verify_integrity():
                logger.error(f"Integrity verification failed for event {event.event_id}")
                return False

        # Verify chain integrity (each event's previous_checksum matches previous event's checksum)
        for i in range(1, len(events)):
            current_event = events[i]
            previous_event = events[i-1]

            if current_event.previous_checksum != previous_event.checksum:
                logger.error(f"Chain integrity broken between events {previous_event.event_id} and {current_event.event_id}")
                return False

        logger.info(f"Chain integrity verified for {len(events)} events")
        return True

    async def export_audit_report(self,
                                 query: AuditQuery,
                                 format: str = "json") -> str:
        """Export audit report in specified format."""
        events = await self.query_events(query)

        if format.lower() == "json":
            return self._export_json(events)
        elif format.lower() == "csv":
            return self._export_csv(events)
        else:
            raise ValueError(f"Unsupported export format: {format}")

    def get_event_statistics(self) -> Dict[str, Any]:
        """Get audit event statistics."""
        return {
            "total_events": sum(self.event_counts.values()),
            "events_by_type": {et.value: count for et, count in self.event_counts.items()},
            "recent_events_count": len(self.recent_events),
            "last_event_time": self.recent_events[-1].timestamp.isoformat() if self.recent_events else None
        }

    async def _initialize_database(self):
        """Initialize SQLite database for audit storage."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS audit_events (
                    event_id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    description TEXT NOT NULL,
                    component TEXT NOT NULL,
                    user_id TEXT,
                    session_id TEXT,
                    ip_address TEXT,
                    account_id TEXT,
                    order_id TEXT,
                    symbol TEXT,
                    quantity REAL,
                    price REAL,
                    risk_metric TEXT,
                    risk_value REAL,
                    risk_limit REAL,
                    process_id INTEGER,
                    thread_id INTEGER,
                    metadata TEXT,
                    checksum TEXT NOT NULL,
                    previous_checksum TEXT
                )
            """)

            # Create indexes for common queries
            await db.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON audit_events(timestamp)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_event_type ON audit_events(event_type)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_user_id ON audit_events(user_id)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_order_id ON audit_events(order_id)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_symbol ON audit_events(symbol)")

            await db.commit()

    async def _store_event(self, event: AuditEvent):
        """Store audit event in database."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO audit_events (
                    event_id, timestamp, event_type, severity, description, component,
                    user_id, session_id, ip_address, account_id, order_id, symbol,
                    quantity, price, risk_metric, risk_value, risk_limit,
                    process_id, thread_id, metadata, checksum, previous_checksum
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                event.event_id,
                event.timestamp.isoformat(),
                event.event_type.value,
                event.severity.value,
                event.description,
                event.component,
                event.user_id,
                event.session_id,
                event.ip_address,
                event.account_id,
                event.order_id,
                event.symbol,
                event.quantity,
                event.price,
                event.risk_metric,
                event.risk_value,
                event.risk_limit,
                event.process_id,
                event.thread_id,
                json.dumps(event.metadata) if event.metadata else None,
                event.checksum,
                event.previous_checksum
            ))
            await db.commit()

    async def _load_last_checksum(self):
        """Load the last checksum for chain integrity."""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("SELECT checksum FROM audit_events ORDER BY timestamp DESC LIMIT 1") as cursor:
                row = await cursor.fetchone()
                if row:
                    self.last_checksum = row[0]

    def _generate_event_id(self) -> str:
        """Generate unique event ID."""
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S%f")
        return f"audit_{timestamp}"

    def _get_secret_key(self) -> str:
        """Get secret key for HMAC operations."""
        # In production, this should come from secure configuration
        return getattr(self.config, 'audit_secret_key', 'default_secret_key_change_in_production')

    def _export_json(self, events: List[AuditEvent]) -> str:
        """Export events as JSON."""
        data = []
        for event in events:
            event_dict = asdict(event)
            event_dict['timestamp'] = event.timestamp.isoformat()
            event_dict['event_type'] = event.event_type.value
            event_dict['severity'] = event.severity.value
            data.append(event_dict)

        return json.dumps(data, indent=2)

    def _export_csv(self, events: List[AuditEvent]) -> str:
        """Export events as CSV."""
        if not events:
            return ""

        # CSV header
        headers = [
            "event_id", "timestamp", "event_type", "severity", "description",
            "component", "user_id", "order_id", "symbol", "quantity", "price"
        ]

        lines = [",".join(headers)]

        # CSV data
        for event in events:
            row = [
                event.event_id,
                event.timestamp.isoformat(),
                event.event_type.value,
                event.severity.value,
                f'"{event.description}"',  # Quote description to handle commas
                event.component,
                event.user_id or "",
                event.order_id or "",
                event.symbol or "",
                str(event.quantity) if event.quantity else "",
                str(event.price) if event.price else ""
            ]
            lines.append(",".join(row))

        return "\n".join(lines)

    async def _cleanup_loop(self):
        """Clean up old audit records periodically."""
        while True:
            try:
                await asyncio.sleep(86400)  # Run daily
                await self._cleanup_old_records()
            except Exception as e:
                logger.error(f"Audit cleanup error: {e}")

    async def _cleanup_old_records(self):
        """Clean up audit records older than retention period."""
        cutoff_date = datetime.utcnow() - timedelta(days=self.retention_days)

        async with aiosqlite.connect(self.db_path) as db:
            # Count records to be deleted
            async with db.execute("SELECT COUNT(*) FROM audit_events WHERE timestamp < ?",
                                (cutoff_date.isoformat(),)) as cursor:
                count = (await cursor.fetchone())[0]

            if count > 0:
                # Archive before deletion (in production, export to long-term storage)
                logger.info(f"Archiving {count} old audit records...")

                # Delete old records
                await db.execute("DELETE FROM audit_events WHERE timestamp < ?",
                                (cutoff_date.isoformat(),))
                await db.commit()

                logger.info(f"Cleaned up {count} old audit records")

    async def shutdown(self):
        """Shutdown audit trail system."""
        logger.info("Shutting down audit trail system...")

        # Log shutdown event
        await self.log_event(
            event_type=AuditEventType.SYSTEM_SHUTDOWN,
            description="Audit trail system shutdown",
            component="audit_trail",
            severity=AuditSeverity.MEDIUM
        )

        logger.info("Audit trail system shutdown completed")
