"""Hyperparameter tuning using Bayesian optimization."""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import logging
from dataclasses import dataclass, field
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
import joblib
import json
from concurrent.futures import ThreadPoolExecutor
import warnings

logger = logging.getLogger(__name__)


@dataclass
class TuningConfig:
    """Configuration for hyperparameter tuning."""
    # Optimization settings
    n_trials: int = 100
    timeout: Optional[int] = None  # seconds
    n_jobs: int = 1
    
    # Optuna settings
    sampler: str = 'tpe'  # tpe, random, cmaes
    pruner: str = 'median'  # median, none
    direction: str = 'maximize'  # maximize, minimize
    
    # Cross-validation
    cv_folds: int = 5
    test_size: float = 0.2
    
    # Early stopping
    early_stopping_rounds: int = 20
    
    # Study settings
    study_name: Optional[str] = None
    storage: Optional[str] = None  # sqlite:///optuna.db
    load_if_exists: bool = True
    
    # Logging
    log_level: str = 'INFO'
    save_intermediate_results: bool = True


class HyperparameterSpace:
    """Define hyperparameter search spaces for different models."""
    
    @staticmethod
    def get_lstm_space() -> Dict[str, Any]:
        """Get LSTM hyperparameter space."""
        return {
            'hidden_size': ('int', 64, 512),
            'num_layers': ('int', 1, 5),
            'dropout': ('float', 0.0, 0.5),
            'learning_rate': ('loguniform', 1e-5, 1e-2),
            'sequence_length': ('int', 50, 300),
            'batch_size': ('categorical', [16, 32, 64, 128]),
            'bidirectional': ('categorical', [True, False]),
            'attention': ('categorical', [True, False])
        }
    
    @staticmethod
    def get_xgboost_space() -> Dict[str, Any]:
        """Get XGBoost hyperparameter space."""
        return {
            'n_estimators': ('int', 100, 1000),
            'max_depth': ('int', 3, 12),
            'learning_rate': ('loguniform', 0.01, 0.3),
            'subsample': ('float', 0.5, 1.0),
            'colsample_bytree': ('float', 0.5, 1.0),
            'reg_alpha': ('loguniform', 1e-8, 10.0),
            'reg_lambda': ('loguniform', 1e-8, 10.0),
            'min_child_weight': ('int', 1, 10),
            'gamma': ('loguniform', 1e-8, 10.0)
        }
    
    @staticmethod
    def get_transformer_space() -> Dict[str, Any]:
        """Get Transformer hyperparameter space."""
        return {
            'd_model': ('categorical', [128, 256, 512]),
            'nhead': ('categorical', [4, 8, 16]),
            'num_encoder_layers': ('int', 2, 8),
            'dim_feedforward': ('categorical', [256, 512, 1024, 2048]),
            'dropout': ('float', 0.0, 0.3),
            'learning_rate': ('loguniform', 1e-5, 1e-3),
            'sequence_length': ('int', 50, 300),
            'batch_size': ('categorical', [16, 32, 64]),
        }
    
    @staticmethod
    def get_rl_space() -> Dict[str, Any]:
        """Get RL hyperparameter space."""
        return {
            'learning_rate': ('loguniform', 1e-5, 1e-2),
            'gamma': ('float', 0.9, 0.999),
            'gae_lambda': ('float', 0.8, 0.99),
            'clip_epsilon': ('float', 0.1, 0.3),
            'value_coef': ('float', 0.1, 1.0),
            'entropy_coef': ('float', 0.001, 0.1),
            'hidden_dim': ('categorical', [128, 256, 512]),
            'n_hidden_layers': ('int', 2, 5),
            'batch_size': ('categorical', [32, 64, 128, 256]),
            'n_steps': ('categorical', [1024, 2048, 4096])
        }
    
    @staticmethod
    def get_ensemble_space() -> Dict[str, Any]:
        """Get Ensemble hyperparameter space."""
        return {
            'confidence_threshold': ('float', 0.5, 0.9),
            'agreement_threshold': ('float', 0.5, 0.9),
            'adaptation_rate': ('float', 0.01, 0.3),
            'min_model_weight': ('float', 0.01, 0.2),
            'max_model_weight': ('float', 0.3, 0.8),
            'ensemble_method': ('categorical', ['weighted_voting', 'stacking']),
            'conflict_resolution': ('categorical', ['highest_confidence', 'model_performance', 'abstain'])
        }
    
    @staticmethod
    def suggest_parameter(trial: optuna.Trial, name: str, param_config: Tuple) -> Any:
        """Suggest a parameter value based on its configuration."""
        param_type = param_config[0]
        
        if param_type == 'int':
            return trial.suggest_int(name, param_config[1], param_config[2])
        elif param_type == 'float':
            return trial.suggest_float(name, param_config[1], param_config[2])
        elif param_type == 'loguniform':
            return trial.suggest_loguniform(name, param_config[1], param_config[2])
        elif param_type == 'categorical':
            return trial.suggest_categorical(name, param_config[1])
        elif param_type == 'discrete_uniform':
            return trial.suggest_discrete_uniform(name, param_config[1], param_config[2], param_config[3])
        else:
            raise ValueError(f"Unknown parameter type: {param_type}")


class ModelObjective:
    """Base class for model objectives."""
    
    def __init__(self, 
                 model_type: str,
                 train_data: pd.DataFrame,
                 val_data: pd.DataFrame,
                 target_column: str,
                 config: TuningConfig):
        self.model_type = model_type
        self.train_data = train_data
        self.val_data = val_data
        self.target_column = target_column
        self.config = config
        
        # Get parameter space
        if model_type == 'lstm':
            self.param_space = HyperparameterSpace.get_lstm_space()
        elif model_type == 'xgboost':
            self.param_space = HyperparameterSpace.get_xgboost_space()
        elif model_type == 'transformer':
            self.param_space = HyperparameterSpace.get_transformer_space()
        elif model_type == 'rl':
            self.param_space = HyperparameterSpace.get_rl_space()
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    def __call__(self, trial: optuna.Trial) -> float:
        """Objective function for optimization."""
        try:
            # Suggest hyperparameters
            params = {}
            for name, param_config in self.param_space.items():
                params[name] = HyperparameterSpace.suggest_parameter(trial, name, param_config)
            
            # Train and evaluate model
            score = self._train_and_evaluate(params, trial)
            
            return score
            
        except Exception as e:
            logger.warning(f"Trial failed: {e}")
            # Return worst possible score
            return float('-inf') if self.config.direction == 'maximize' else float('inf')
    
    def _train_and_evaluate(self, params: Dict[str, Any], trial: optuna.Trial) -> float:
        """Train model with given parameters and return evaluation score."""
        
        if self.model_type == 'lstm':
            return self._evaluate_lstm(params, trial)
        elif self.model_type == 'xgboost':
            return self._evaluate_xgboost(params, trial)
        elif self.model_type == 'transformer':
            return self._evaluate_transformer(params, trial)
        elif self.model_type == 'rl':
            return self._evaluate_rl(params, trial)
    
    def _evaluate_lstm(self, params: Dict[str, Any], trial: optuna.Trial) -> float:
        """Evaluate LSTM with given parameters."""
        from ..models.lstm_predictor import LSTMTradingPredictor, LSTMConfig
        
        # Prepare data
        feature_cols = [col for col in self.train_data.columns 
                       if col != self.target_column and not col.startswith('target')]
        X_train = self.train_data[feature_cols].values
        X_val = self.val_data[feature_cols].values
        
        # Create config
        config = LSTMConfig(
            input_size=len(feature_cols),
            hidden_size=params['hidden_size'],
            num_layers=params['num_layers'],
            dropout=params['dropout'],
            sequence_length=params['sequence_length'],
            bidirectional=params['bidirectional'],
            attention=params['attention']
        )
        
        # Train model
        model = LSTMTradingPredictor(config)
        history = model.train(
            train_data=X_train,
            val_data=X_val,
            epochs=50,  # Reduced for hyperparameter tuning
            batch_size=params['batch_size'],
            learning_rate=params['learning_rate']
        )
        
        # Return validation loss (minimizing)
        if 'val_loss' in history and len(history['val_loss']) > 0:
            return -min(history['val_loss'])  # Negative because we want to maximize
        else:
            return -history['train_loss'][-1] if 'train_loss' in history else 0
    
    def _evaluate_xgboost(self, params: Dict[str, Any], trial: optuna.Trial) -> float:
        """Evaluate XGBoost with given parameters."""
        from ..models.xgboost_classifier import XGBoostClassifier, XGBoostConfig
        
        # Prepare data
        feature_cols = [col for col in self.train_data.columns 
                       if col != self.target_column and not col.startswith('target')]
        X_train = self.train_data[feature_cols]
        y_train = self.train_data[self.target_column].values
        
        # Create config
        config = XGBoostConfig(
            n_estimators=params['n_estimators'],
            max_depth=params['max_depth'],
            learning_rate=params['learning_rate'],
            subsample=params['subsample'],
            colsample_bytree=params['colsample_bytree'],
            reg_alpha=params.get('reg_alpha', 0),
            reg_lambda=params.get('reg_lambda', 1),
            min_child_weight=params.get('min_child_weight', 1),
            gamma=params.get('gamma', 0)
        )
        
        # Train model
        model = XGBoostClassifier(config)
        
        # Cross-validation
        cv_results = model.cross_validate(X_train, y_train, cv=3)  # Reduced folds for speed
        
        # Return mean accuracy
        return cv_results['accuracy']['mean']
    
    def _evaluate_transformer(self, params: Dict[str, Any], trial: optuna.Trial) -> float:
        """Evaluate Transformer with given parameters."""
        from ..models.transformer_model import PatternRecognitionTransformer, TransformerConfig
        
        # Prepare data
        feature_cols = [col for col in self.train_data.columns 
                       if col != self.target_column and not col.startswith('target')]
        X_train = self.train_data[feature_cols].values
        X_val = self.val_data[feature_cols].values
        
        # Create pattern labels (simplified)
        y_train = self.train_data[self.target_column].values
        y_val = self.val_data[self.target_column].values
        
        pattern_train = self._create_pattern_labels(y_train)
        pattern_val = self._create_pattern_labels(y_val)
        
        # Create config
        config = TransformerConfig(
            d_model=params['d_model'],
            nhead=params['nhead'],
            num_encoder_layers=params['num_encoder_layers'],
            dim_feedforward=params['dim_feedforward'],
            dropout=params['dropout'],
            sequence_length=params['sequence_length'],
            n_features=len(feature_cols)
        )
        
        # Train model
        model = PatternRecognitionTransformer(config)
        history = model.train(
            train_data=X_train,
            train_labels=pattern_train,
            val_data=X_val,
            val_labels=pattern_val,
            epochs=30,  # Reduced for hyperparameter tuning
            batch_size=params['batch_size']
        )
        
        # Return validation loss (minimizing)
        if 'val_loss' in history and len(history['val_loss']) > 0:
            return -min(history['val_loss'])
        else:
            return -history['train_loss'][-1] if 'train_loss' in history else 0
    
    def _evaluate_rl(self, params: Dict[str, Any], trial: optuna.Trial) -> float:
        """Evaluate RL with given parameters."""
        from ..models.reinforcement_agent import ReinforcementAgent, RLConfig
        
        # Prepare data
        feature_cols = [col for col in self.train_data.columns 
                       if col != self.target_column and not col.startswith('target')]
        X_train = self.train_data[feature_cols].values
        
        # Create config
        config = RLConfig(
            state_dim=len(feature_cols),
            learning_rate=params['learning_rate'],
            gamma=params['gamma'],
            gae_lambda=params['gae_lambda'],
            clip_epsilon=params['clip_epsilon'],
            value_coef=params['value_coef'],
            entropy_coef=params['entropy_coef'],
            hidden_dim=params['hidden_dim'],
            n_hidden_layers=params['n_hidden_layers'],
            batch_size=params['batch_size'],
            n_steps=params['n_steps']
        )
        
        # Train model
        model = ReinforcementAgent(config)
        history = model.train(
            train_data=X_train,
            n_episodes=50,  # Reduced for hyperparameter tuning
            max_steps_per_episode=500
        )
        
        # Return mean episode return
        if 'episode_returns' in history and len(history['episode_returns']) > 0:
            return np.mean(history['episode_returns'][-10:])  # Last 10 episodes
        else:
            return 0
    
    def _create_pattern_labels(self, targets: np.ndarray) -> np.ndarray:
        """Create pattern labels for transformer training."""
        patterns = np.zeros(len(targets), dtype=int)
        
        for i in range(len(targets)):
            if targets[i] > 0.02:
                patterns[i] = 0  # breakout_up
            elif targets[i] < -0.02:
                patterns[i] = 1  # breakout_down
            elif targets[i] > 0.005:
                patterns[i] = 5  # trend_up
            elif targets[i] < -0.005:
                patterns[i] = 6  # trend_down
            else:
                patterns[i] = 7  # sideways
        
        return patterns


class HyperparameterTuner:
    """Main hyperparameter tuning class."""
    
    def __init__(self, config: TuningConfig):
        self.config = config
        self.studies = {}
        self.best_params = {}
        
        # Configure Optuna logging
        optuna.logging.set_verbosity(getattr(optuna.logging, config.log_level))
        
        logger.info("Initialized hyperparameter tuner")
    
    def tune_model(self, 
                   model_type: str,
                   train_data: pd.DataFrame,
                   val_data: pd.DataFrame,
                   target_column: str = 'target') -> Dict[str, Any]:
        """Tune hyperparameters for a specific model."""
        
        logger.info(f"Starting hyperparameter tuning for {model_type}")
        
        # Create objective function
        objective = ModelObjective(
            model_type=model_type,
            train_data=train_data,
            val_data=val_data,
            target_column=target_column,
            config=self.config
        )
        
        # Create study
        study_name = self.config.study_name or f"{model_type}_tuning"
        
        # Setup sampler
        if self.config.sampler == 'tpe':
            sampler = TPESampler()
        elif self.config.sampler == 'random':
            sampler = optuna.samplers.RandomSampler()
        elif self.config.sampler == 'cmaes':
            sampler = optuna.samplers.CmaEsSampler()
        else:
            sampler = TPESampler()
        
        # Setup pruner
        if self.config.pruner == 'median':
            pruner = MedianPruner()
        else:
            pruner = optuna.pruners.NopPruner()
        
        # Create study
        study = optuna.create_study(
            study_name=study_name,
            direction=self.config.direction,
            sampler=sampler,
            pruner=pruner,
            storage=self.config.storage,
            load_if_exists=self.config.load_if_exists
        )
        
        # Add callback for intermediate results
        if self.config.save_intermediate_results:
            def callback(study, trial):
                logger.info(f"Trial {trial.number} completed with value {trial.value}")
        else:
            callback = None
        
        # Optimize
        try:
            study.optimize(
                objective,
                n_trials=self.config.n_trials,
                timeout=self.config.timeout,
                n_jobs=self.config.n_jobs,
                callbacks=[callback] if callback else None
            )
            
            # Store results
            self.studies[model_type] = study
            self.best_params[model_type] = study.best_params
            
            # Create results summary
            results = {
                'best_params': study.best_params,
                'best_value': study.best_value,
                'n_trials': len(study.trials),
                'study': study,
                'optimization_history': [(t.number, t.value) for t in study.trials if t.value is not None]
            }
            
            logger.info(f"Hyperparameter tuning completed for {model_type}")
            logger.info(f"Best value: {study.best_value}")
            logger.info(f"Best params: {study.best_params}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error during hyperparameter tuning: {e}")
            return {'error': str(e)}
    
    def tune_all_models(self, 
                       train_data: pd.DataFrame,
                       val_data: pd.DataFrame,
                       models: List[str] = ['lstm', 'xgboost', 'transformer'],
                       target_column: str = 'target') -> Dict[str, Dict[str, Any]]:
        """Tune hyperparameters for multiple models."""
        
        logger.info(f"Starting hyperparameter tuning for models: {models}")
        
        results = {}
        
        for model_type in models:
            try:
                model_results = self.tune_model(
                    model_type=model_type,
                    train_data=train_data,
                    val_data=val_data,
                    target_column=target_column
                )
                results[model_type] = model_results
                
            except Exception as e:
                logger.error(f"Error tuning {model_type}: {e}")
                results[model_type] = {'error': str(e)}
        
        return results
    
    def get_best_params(self, model_type: str) -> Optional[Dict[str, Any]]:
        """Get best parameters for a specific model."""
        return self.best_params.get(model_type)
    
    def plot_optimization_history(self, model_type: str, save_path: Optional[str] = None):
        """Plot optimization history for a model."""
        if model_type not in self.studies:
            logger.warning(f"No study found for {model_type}")
            return
        
        study = self.studies[model_type]
        
        try:
            import matplotlib.pyplot as plt
            
            # Plot optimization history
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # Optimization history
            optuna.visualization.matplotlib.plot_optimization_history(study, ax=ax1)
            ax1.set_title(f'{model_type} Optimization History')
            
            # Parameter importances
            try:
                optuna.visualization.matplotlib.plot_param_importances(study, ax=ax2)
                ax2.set_title(f'{model_type} Parameter Importances')
            except:
                ax2.text(0.5, 0.5, 'Parameter importances\nnot available', 
                        ha='center', va='center', transform=ax2.transAxes)
                ax2.set_title(f'{model_type} Parameter Importances')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
            plt.show()
            
        except ImportError:
            logger.warning("Matplotlib not available for plotting")
        except Exception as e:
            logger.error(f"Error plotting optimization history: {e}")
    
    def save_results(self, filepath: str):
        """Save tuning results to file."""
        results = {
            'config': self.config.__dict__,
            'best_params': self.best_params,
            'study_summaries': {}
        }
        
        # Add study summaries (without the full study objects)
        for model_type, study in self.studies.items():
            results['study_summaries'][model_type] = {
                'best_value': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials),
                'trials': [
                    {
                        'number': t.number,
                        'value': t.value,
                        'params': t.params,
                        'state': t.state.name
                    }
                    for t in study.trials
                ]
            }
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Tuning results saved to {filepath}")
    
    def load_results(self, filepath: str):
        """Load tuning results from file."""
        with open(filepath, 'r') as f:
            results = json.load(f)
        
        self.best_params = results.get('best_params', {})
        
        logger.info(f"Tuning results loaded from {filepath}")
        
        return results
    
    def get_tuning_summary(self) -> Dict[str, Any]:
        """Get summary of all tuning results."""
        summary = {
            'config': self.config.__dict__,
            'models_tuned': list(self.best_params.keys()),
            'best_params': self.best_params
        }
        
        # Add study statistics
        for model_type, study in self.studies.items():
            summary[f'{model_type}_stats'] = {
                'best_value': study.best_value,
                'n_trials': len(study.trials),
                'n_complete_trials': len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]),
                'n_failed_trials': len([t for t in study.trials if t.state == optuna.trial.TrialState.FAIL])
            }
        
        return summary