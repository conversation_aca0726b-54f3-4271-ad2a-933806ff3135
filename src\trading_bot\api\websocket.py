"""WebSocket client for real-time data streaming."""

import asyncio
import json
import time
import uuid
from typing import Any, Callable, Dict, List, Optional, Set

import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from ..models.enums import WebSocketMessageType
from ..models.market import Quote, Trade
from ..utils.exceptions import WebSocketError
from ..utils.logger import get_structured_logger
from .auth import AuthenticationManager

logger = get_structured_logger(__name__)


class WebSocketClient(AuthenticationManager):
    """WebSocket client for real-time market data and trading updates."""
    
    def __init__(self):
        super().__init__()
        
        # WebSocket connection
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.connection_id: str = str(uuid.uuid4())
        self.is_connected = False
        self.is_connecting = False
        
        # Connection management
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 5.0
        self.heartbeat_interval = 30.0
        self.last_heartbeat = 0.0
        
        # Subscriptions
        self.subscribed_symbols: Set[str] = set()
        self.subscription_callbacks: Dict[str, List[Callable]] = {}
        
        # Message handling
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.message_handlers: Dict[WebSocketMessageType, Callable] = {
            WebSocketMessageType.QUOTE: self._handle_quote,
            WebSocketMessageType.TRADE: self._handle_trade,
            WebSocketMessageType.ORDER_UPDATE: self._handle_order_update,
            WebSocketMessageType.POSITION_UPDATE: self._handle_position_update,
            WebSocketMessageType.HEARTBEAT: self._handle_heartbeat,
            WebSocketMessageType.ERROR: self._handle_error,
        }
        
        # Background tasks
        self.connection_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.message_processor_task: Optional[asyncio.Task] = None
    
    async def connect(self, auto_reconnect: bool = True) -> bool:
        """
        Connect to WebSocket server.
        
        Args:
            auto_reconnect: Whether to automatically reconnect on disconnection
            
        Returns:
            True if connection successful
        """
        if self.is_connected or self.is_connecting:
            return self.is_connected
        
        self.is_connecting = True
        
        try:
            await self.ensure_authenticated()
            
            # WebSocket URL with authentication
            ws_url = f"wss://quoteapi.webull.com/ws?access_token={self.access_token}&device_id={self.device_id}"
            
            # Connect to WebSocket
            self.websocket = await websockets.connect(
                ws_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10,
            )
            
            self.is_connected = True
            self.is_connecting = False
            self.reconnect_attempts = 0
            
            logger.websocket_event(
                "connected",
                connection_id=self.connection_id,
            )
            
            # Start background tasks
            await self._start_background_tasks()
            
            # Send initial heartbeat
            await self._send_heartbeat()
            
            # Resubscribe to symbols if any
            if self.subscribed_symbols:
                await self._resubscribe_all()
            
            return True
            
        except Exception as e:
            self.is_connected = False
            self.is_connecting = False
            
            logger.websocket_event(
                "connection_failed",
                connection_id=self.connection_id,
                error=str(e),
            )
            
            if auto_reconnect:
                await self._schedule_reconnect()
            
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from WebSocket server."""
        self.is_connected = False
        
        # Stop background tasks
        await self._stop_background_tasks()
        
        # Close WebSocket connection
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                logger.warning(f"Error closing WebSocket: {e}")
            finally:
                self.websocket = None
        
        # Clear subscriptions
        self.subscribed_symbols.clear()
        
        logger.websocket_event(
            "disconnected",
            connection_id=self.connection_id,
        )
    
    async def subscribe_quotes(self, symbols: List[str], callback: Optional[Callable] = None) -> bool:
        """
        Subscribe to real-time quotes for symbols.
        
        Args:
            symbols: List of symbols to subscribe to
            callback: Optional callback function for quote updates
            
        Returns:
            True if subscription successful
        """
        if not self.is_connected:
            raise WebSocketError(
                "Not connected to WebSocket",
                connection_state="disconnected",
            )
        
        try:
            # Validate symbols
            valid_symbols = [s.upper().strip() for s in symbols if s and isinstance(s, str)]
            
            if not valid_symbols:
                raise WebSocketError("No valid symbols provided")
            
            # Send subscription message
            message = {
                "action": "subscribe",
                "type": "quote",
                "symbols": valid_symbols,
                "id": str(uuid.uuid4()),
            }
            
            await self._send_message(message)
            
            # Add to subscribed symbols
            self.subscribed_symbols.update(valid_symbols)
            
            # Register callback if provided
            if callback:
                for symbol in valid_symbols:
                    if symbol not in self.subscription_callbacks:
                        self.subscription_callbacks[symbol] = []
                    self.subscription_callbacks[symbol].append(callback)
            
            logger.websocket_event(
                "subscribed",
                message_type="quote",
                symbol=",".join(valid_symbols),
            )
            
            return True
            
        except Exception as e:
            logger.websocket_event(
                "subscription_failed",
                message_type="quote",
                error=str(e),
            )
            raise
    
    async def unsubscribe_quotes(self, symbols: List[str]) -> bool:
        """
        Unsubscribe from real-time quotes.
        
        Args:
            symbols: List of symbols to unsubscribe from
            
        Returns:
            True if unsubscription successful
        """
        if not self.is_connected:
            return False
        
        try:
            valid_symbols = [s.upper().strip() for s in symbols if s and isinstance(s, str)]
            
            if not valid_symbols:
                return False
            
            # Send unsubscription message
            message = {
                "action": "unsubscribe",
                "type": "quote",
                "symbols": valid_symbols,
                "id": str(uuid.uuid4()),
            }
            
            await self._send_message(message)
            
            # Remove from subscribed symbols
            self.subscribed_symbols.difference_update(valid_symbols)
            
            # Remove callbacks
            for symbol in valid_symbols:
                if symbol in self.subscription_callbacks:
                    del self.subscription_callbacks[symbol]
            
            logger.websocket_event(
                "unsubscribed",
                message_type="quote",
                symbol=",".join(valid_symbols),
            )
            
            return True
            
        except Exception as e:
            logger.websocket_event(
                "unsubscription_failed",
                message_type="quote",
                error=str(e),
            )
            return False
    
    async def _send_message(self, message: Dict[str, Any]) -> None:
        """Send message to WebSocket server."""
        if not self.websocket or not self.is_connected:
            raise WebSocketError("WebSocket not connected")
        
        try:
            message_json = json.dumps(message)
            await self.websocket.send(message_json)
            
            logger.debug(
                f"Sent WebSocket message: {message.get('action', 'unknown')}",
                extra={"message_type": message.get("type"), "message_id": message.get("id")}
            )
            
        except Exception as e:
            logger.websocket_event(
                "send_failed",
                error=str(e),
            )
            raise WebSocketError(f"Failed to send message: {e}")

    async def _send_heartbeat(self) -> None:
        """Send heartbeat message to keep connection alive."""
        try:
            heartbeat_message = {
                "action": "heartbeat",
                "id": str(uuid.uuid4()),
                "timestamp": int(time.time() * 1000),
            }

            await self._send_message(heartbeat_message)
            self.last_heartbeat = time.time()

            logger.debug("Heartbeat sent")

        except Exception as e:
            logger.warning(f"Failed to send heartbeat: {e}")

    async def _handle_heartbeat_response(self, message: Dict[str, Any]) -> None:
        """Handle heartbeat response from server."""
        logger.debug("Heartbeat response received")

    async def _resubscribe_all(self) -> None:
        """Resubscribe to all previously subscribed symbols."""
        if not self.subscribed_symbols:
            return

        try:
            symbols_list = list(self.subscribed_symbols)
            await self.subscribe_quotes(symbols_list)

            logger.info(
                f"Resubscribed to {len(symbols_list)} symbols",
                extra={"symbols": symbols_list}
            )

        except Exception as e:
            logger.error(f"Failed to resubscribe to symbols: {e}")

    async def _start_background_tasks(self) -> None:
        """Start background tasks for connection management."""
        # Start message handler
        asyncio.create_task(self._message_handler())

        # Start heartbeat task
        asyncio.create_task(self._heartbeat_task())

        logger.debug("Background tasks started")

    async def _heartbeat_task(self) -> None:
        """Background task to send periodic heartbeats."""
        while self.is_connected:
            try:
                await asyncio.sleep(self.heartbeat_interval)

                if self.is_connected:
                    await self._send_heartbeat()

            except Exception as e:
                logger.error(f"Heartbeat task error: {e}")
                break

    async def _message_handler(self) -> None:
        """Handle incoming WebSocket messages."""
        while self.is_connected:
            try:
                if not self.websocket:
                    break

                # Receive message with timeout
                message_raw = await asyncio.wait_for(
                    self.websocket.recv(),
                    timeout=60.0
                )

                # Parse JSON message
                try:
                    message = json.loads(message_raw)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse WebSocket message: {e}")
                    continue

                # Process message
                await self._process_message(message)

            except asyncio.TimeoutError:
                logger.warning("WebSocket message timeout")
                continue

            except ConnectionClosed:
                logger.warning("WebSocket connection closed")
                self.is_connected = False
                break

            except Exception as e:
                logger.error(f"Message handler error: {e}")
                await asyncio.sleep(1)

        # Connection lost, attempt reconnection
        if not self.is_connecting:
            await self._handle_disconnection()

    async def _process_message(self, message: Dict[str, Any]) -> None:
        """Process incoming WebSocket message."""
        try:
            message_type = message.get("type")

            if message_type == "heartbeat":
                await self._handle_heartbeat_response(message)

            elif message_type == "quote":
                await self._handle_quote_message(message)

            elif message_type == "trade":
                await self._handle_trade_message(message)

            elif message_type == "error":
                await self._handle_error_message(message)

            elif message_type == "subscription_confirmed":
                await self._handle_subscription_confirmed(message)

            else:
                logger.debug(f"Unknown message type: {message_type}")

        except Exception as e:
            logger.error(f"Error processing message: {e}")

    async def _handle_quote_message(self, message: Dict[str, Any]) -> None:
        """Handle quote update message."""
        try:
            data = message.get("data", {})
            symbol = data.get("symbol")

            if not symbol:
                return

            # Create Quote object
            quote = Quote(
                symbol=symbol,
                price=float(data.get("price", 0)),
                bid=float(data.get("bid", 0)),
                ask=float(data.get("ask", 0)),
                bid_size=int(data.get("bid_size", 0)),
                ask_size=int(data.get("ask_size", 0)),
                volume=int(data.get("volume", 0)),
                timestamp=data.get("timestamp", int(time.time() * 1000)),
            )

            # Call registered callbacks
            if symbol in self.subscription_callbacks:
                for callback in self.subscription_callbacks[symbol]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(quote)
                        else:
                            callback(quote)
                    except Exception as e:
                        logger.error(f"Callback error for {symbol}: {e}")

            logger.debug(f"Quote update: {symbol} @ ${quote.price}")

        except Exception as e:
            logger.error(f"Error handling quote message: {e}")

    async def _handle_trade_message(self, message: Dict[str, Any]) -> None:
        """Handle trade update message."""
        try:
            data = message.get("data", {})
            symbol = data.get("symbol")

            if not symbol:
                return

            # Create Trade object
            trade = Trade(
                symbol=symbol,
                price=float(data.get("price", 0)),
                size=int(data.get("size", 0)),
                timestamp=data.get("timestamp", int(time.time() * 1000)),
                side=data.get("side", "unknown"),
            )

            logger.debug(f"Trade update: {symbol} {trade.size}@${trade.price}")

        except Exception as e:
            logger.error(f"Error handling trade message: {e}")

    async def _handle_error_message(self, message: Dict[str, Any]) -> None:
        """Handle error message from server."""
        error_code = message.get("code")
        error_msg = message.get("message", "Unknown error")

        logger.error(f"WebSocket error {error_code}: {error_msg}")

        # Handle specific error codes
        if error_code == "AUTH_FAILED":
            logger.error("WebSocket authentication failed")
            await self.disconnect()
        elif error_code == "RATE_LIMIT":
            logger.warning("WebSocket rate limit exceeded")

    async def _handle_subscription_confirmed(self, message: Dict[str, Any]) -> None:
        """Handle subscription confirmation."""
        symbols = message.get("symbols", [])
        logger.info(f"Subscription confirmed for symbols: {symbols}")

    async def _handle_disconnection(self) -> None:
        """Handle WebSocket disconnection and attempt reconnection."""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error("Max reconnection attempts reached, giving up")
            return

        self.reconnect_attempts += 1
        delay = min(self.reconnect_delay * (2 ** (self.reconnect_attempts - 1)), 300)

        logger.info(
            f"Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts} "
            f"in {delay} seconds"
        )

        await asyncio.sleep(delay)

        try:
            await self.connect()
        except Exception as e:
            logger.error(f"Reconnection attempt {self.reconnect_attempts} failed: {e}")
            await self._handle_disconnection()

    async def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status."""
        return {
            "connected": self.is_connected,
            "connecting": self.is_connecting,
            "connection_id": self.connection_id,
            "reconnect_attempts": self.reconnect_attempts,
            "subscribed_symbols": list(self.subscribed_symbols),
            "last_heartbeat": self.last_heartbeat,
        }
    
    async def _start_background_tasks(self) -> None:
        """Start background tasks for connection management."""
        # Message processor
        self.message_processor_task = asyncio.create_task(self._process_messages())
        
        # Heartbeat sender
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        
        # Connection monitor
        self.connection_task = asyncio.create_task(self._connection_monitor())
    
    async def _stop_background_tasks(self) -> None:
        """Stop background tasks."""
        tasks = [self.message_processor_task, self.heartbeat_task, self.connection_task]
        
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self.message_processor_task = None
        self.heartbeat_task = None
        self.connection_task = None
    
    async def _process_messages(self) -> None:
        """Process incoming WebSocket messages."""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.message_queue.put(data)
                    
                    # Handle message based on type
                    message_type = data.get("type")
                    if message_type:
                        handler = self.message_handlers.get(WebSocketMessageType(message_type))
                        if handler:
                            await handler(data)
                    
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON message: {message}")
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    
        except ConnectionClosed:
            logger.websocket_event("connection_closed", connection_id=self.connection_id)
            self.is_connected = False
            await self._schedule_reconnect()
        except Exception as e:
            logger.websocket_event("message_processing_error", error=str(e))
            self.is_connected = False
    
    async def _heartbeat_loop(self) -> None:
        """Send periodic heartbeat messages."""
        while self.is_connected:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                if self.is_connected:
                    await self._send_heartbeat()
            except Exception as e:
                logger.warning(f"Heartbeat error: {e}")
                break
    
    async def _send_heartbeat(self) -> None:
        """Send heartbeat message."""
        try:
            message = {
                "action": "ping",
                "timestamp": int(time.time() * 1000),
                "id": str(uuid.uuid4()),
            }
            
            await self._send_message(message)
            self.last_heartbeat = time.time()
            
        except Exception as e:
            logger.warning(f"Failed to send heartbeat: {e}")
    
    async def _connection_monitor(self) -> None:
        """Monitor connection health."""
        while self.is_connected:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                # Check if heartbeat is stale
                if time.time() - self.last_heartbeat > self.heartbeat_interval * 2:
                    logger.warning("Heartbeat timeout, reconnecting")
                    self.is_connected = False
                    await self._schedule_reconnect()
                    break
                    
            except Exception as e:
                logger.error(f"Connection monitor error: {e}")
                break
    
    async def _schedule_reconnect(self) -> None:
        """Schedule automatic reconnection."""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error("Max reconnection attempts reached")
            return
        
        self.reconnect_attempts += 1
        delay = self.reconnect_delay * (2 ** (self.reconnect_attempts - 1))  # Exponential backoff
        
        logger.info(f"Scheduling reconnection in {delay}s (attempt {self.reconnect_attempts})")
        
        await asyncio.sleep(delay)
        await self.connect(auto_reconnect=True)
    
    async def _resubscribe_all(self) -> None:
        """Resubscribe to all symbols after reconnection."""
        if self.subscribed_symbols:
            await self.subscribe_quotes(list(self.subscribed_symbols))
    
    # Message handlers
    async def _handle_quote(self, data: Dict[str, Any]) -> None:
        """Handle quote message."""
        try:
            quote = Quote.from_dict(data)
            
            # Call registered callbacks
            callbacks = self.subscription_callbacks.get(quote.symbol, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(quote)
                    else:
                        callback(quote)
                except Exception as e:
                    logger.error(f"Callback error for {quote.symbol}: {e}")
            
            logger.market_data_event(
                "quote_update",
                symbol=quote.symbol,
                price=float(quote.price),
                volume=quote.volume,
                data_source="websocket",
            )
            
        except Exception as e:
            logger.error(f"Error handling quote: {e}")
    
    async def _handle_trade(self, data: Dict[str, Any]) -> None:
        """Handle trade message."""
        try:
            trade = Trade.from_dict(data)
            logger.market_data_event(
                "trade_update",
                symbol=trade.symbol,
                price=float(trade.price),
                volume=trade.size,
                data_source="websocket",
            )
        except Exception as e:
            logger.error(f"Error handling trade: {e}")
    
    async def _handle_order_update(self, data: Dict[str, Any]) -> None:
        """Handle order update message."""
        logger.websocket_event("order_update", message_type="order_update")
    
    async def _handle_position_update(self, data: Dict[str, Any]) -> None:
        """Handle position update message."""
        logger.websocket_event("position_update", message_type="position_update")
    
    async def _handle_heartbeat(self, data: Dict[str, Any]) -> None:
        """Handle heartbeat response."""
        self.last_heartbeat = time.time()
    
    async def _handle_error(self, data: Dict[str, Any]) -> None:
        """Handle error message."""
        error_msg = data.get("message", "Unknown WebSocket error")
        logger.websocket_event("error_received", error=error_msg)
