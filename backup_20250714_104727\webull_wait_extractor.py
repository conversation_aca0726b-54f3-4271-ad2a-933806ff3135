# webull_wait_extractor.py
"""
Webull extractor that waits for elements to load
"""

import time
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import undetected_chromedriver as uc
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_with_wait(symbol='AAPL'):
    """Extract data with proper waiting"""
    driver = None
    
    try:
        # Initialize driver
        driver = uc.Chrome(version_main=None)
        wait = WebDriverWait(driver, 20)  # 20 second timeout
        
        # Navigate to page
        url = f"https://app.webull.com/stocks/{symbol}"
        logger.info(f"Loading {url}")
        driver.get(url)
        
        # Wait for any price-like element to appear
        logger.info("Waiting for price data to load...")
        
        # Try multiple wait strategies
        price_found = False
        
        # Strategy 1: Wait for any element with a dollar sign
        try:
            dollar_element = wait.until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), '$') and contains(text(), '.')]"))
            )
            logger.info(f"Found element with $: {dollar_element.text[:50]}")
            price_found = True
        except:
            logger.warning("Timeout waiting for dollar sign element")
        
        # Strategy 2: Wait for page to stabilize
        time.sleep(10)  # Sometimes dynamic content needs time
        
        # Get all text
        page_text = driver.find_element(By.TAG_NAME, 'body').text
        
        # Find all numbers that look like prices
        price_pattern = r'\$?(\d{1,4}\.\d{2})'
        all_prices = re.findall(price_pattern, page_text)
        
        # Filter for reasonable stock prices
        valid_prices = []
        for price_str in all_prices:
            try:
                price = float(price_str)
                if 0.01 <= price <= 5000:  # Reasonable stock price range
                    valid_prices.append(price)
            except:
                pass
        
        # Remove duplicates and sort
        valid_prices = sorted(list(set(valid_prices)), reverse=True)
        
        if valid_prices:
            logger.info(f"✅ Found {len(valid_prices)} potential prices")
            logger.info(f"Top 5 prices: {valid_prices[:5]}")
            
            # The actual stock price is often one of the larger values
            # but not the largest (which might be market cap)
            if len(valid_prices) >= 2:
                likely_price = valid_prices[1]  # Second largest often is the stock price
                logger.info(f"🎯 Most likely price for {symbol}: ${likely_price}")
        else:
            logger.warning("No valid prices found")
        
        # Take screenshot
        screenshot = f"wait_extract_{symbol}.png"
        driver.save_screenshot(screenshot)
        logger.info(f"Screenshot saved: {screenshot}")
        
        # Save page text for debugging
        with open(f"page_content_{symbol}.txt", 'w', encoding='utf-8') as f:
            f.write(page_text)
        
        return valid_prices
        
    except Exception as e:
        logger.error(f"Error: {e}")
        return []
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    print("\n🔍 Webull Wait Extractor")
    print("=" * 40)
    
    # Test with AAPL
    prices = extract_with_wait('AAPL')
    
    if prices:
        print(f"\n✅ Extraction successful!")
        print(f"Found {len(prices)} potential prices")
        print(f"Check the screenshot and page_content_AAPL.txt")
    else:
        print("\n❌ No prices found")