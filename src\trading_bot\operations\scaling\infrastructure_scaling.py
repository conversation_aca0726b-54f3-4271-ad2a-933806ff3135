"""
Infrastructure Scaling System

Manages automatic scaling of infrastructure resources based on
trading volume, performance requirements, and system load.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import psutil
import numpy as np

from ...core.logger import get_logger

logger = get_logger(__name__)

class InfrastructureComponent(Enum):
    """Infrastructure components that can be scaled"""
    COMPUTE = "compute"
    MEMORY = "memory"
    STORAGE = "storage"
    NETWORK = "network"
    DATABASE = "database"
    CACHE = "cache"
    MESSAGE_QUEUE = "message_queue"

class ScalingAction(Enum):
    """Scaling actions"""
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    SCALE_OUT = "scale_out"  # Add more instances
    SCALE_IN = "scale_in"    # Remove instances

@dataclass
class ResourceMetrics:
    """Resource utilization metrics"""
    component: InfrastructureComponent
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: float
    response_time: float
    throughput: float
    error_rate: float
    timestamp: datetime

@dataclass
class ScalingRule:
    """Rule for automatic scaling"""
    component: InfrastructureComponent
    metric_name: str
    threshold_up: float
    threshold_down: float
    action_up: ScalingAction
    action_down: ScalingAction
    cooldown_period: timedelta
    min_instances: int
    max_instances: int
    enabled: bool = True

@dataclass
class ScalingEvent:
    """Record of a scaling event"""
    timestamp: datetime
    component: InfrastructureComponent
    action: ScalingAction
    trigger_metric: str
    trigger_value: float
    old_capacity: int
    new_capacity: int
    reason: str

class InfrastructureScaler:
    """Manages infrastructure scaling for trading bot growth"""
    
    def __init__(self):
        self.current_capacity = {
            InfrastructureComponent.COMPUTE: 2,
            InfrastructureComponent.MEMORY: 8,  # GB
            InfrastructureComponent.STORAGE: 100,  # GB
            InfrastructureComponent.DATABASE: 1,
            InfrastructureComponent.CACHE: 1,
            InfrastructureComponent.MESSAGE_QUEUE: 1
        }
        
        self.scaling_rules = self._initialize_scaling_rules()
        self.scaling_history: List[ScalingEvent] = []
        self.last_scaling_action = {}
        self.metrics_history = []
        
    def _initialize_scaling_rules(self) -> List[ScalingRule]:
        """Initialize default scaling rules"""
        return [
            # CPU scaling
            ScalingRule(
                component=InfrastructureComponent.COMPUTE,
                metric_name='cpu_usage',
                threshold_up=0.8,  # 80% CPU
                threshold_down=0.3,  # 30% CPU
                action_up=ScalingAction.SCALE_OUT,
                action_down=ScalingAction.SCALE_IN,
                cooldown_period=timedelta(minutes=10),
                min_instances=1,
                max_instances=10
            ),
            
            # Memory scaling
            ScalingRule(
                component=InfrastructureComponent.MEMORY,
                metric_name='memory_usage',
                threshold_up=0.85,  # 85% memory
                threshold_down=0.4,  # 40% memory
                action_up=ScalingAction.SCALE_UP,
                action_down=ScalingAction.SCALE_DOWN,
                cooldown_period=timedelta(minutes=15),
                min_instances=4,  # GB
                max_instances=64  # GB
            ),
            
            # Database scaling
            ScalingRule(
                component=InfrastructureComponent.DATABASE,
                metric_name='response_time',
                threshold_up=100.0,  # 100ms response time
                threshold_down=20.0,  # 20ms response time
                action_up=ScalingAction.SCALE_OUT,
                action_down=ScalingAction.SCALE_IN,
                cooldown_period=timedelta(minutes=20),
                min_instances=1,
                max_instances=5
            ),
            
            # Cache scaling
            ScalingRule(
                component=InfrastructureComponent.CACHE,
                metric_name='memory_usage',
                threshold_up=0.9,  # 90% cache memory
                threshold_down=0.5,  # 50% cache memory
                action_up=ScalingAction.SCALE_UP,
                action_down=ScalingAction.SCALE_DOWN,
                cooldown_period=timedelta(minutes=10),
                min_instances=1,
                max_instances=8
            )
        ]
    
    async def monitor_and_scale(self):
        """Continuous monitoring and scaling loop"""
        logger.info("Starting infrastructure monitoring and scaling")
        
        while True:
            try:
                # Collect current metrics
                metrics = await self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Keep only last 24 hours of metrics
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.metrics_history = [
                    m for m in self.metrics_history 
                    if m.timestamp > cutoff_time
                ]
                
                # Evaluate scaling rules
                for rule in self.scaling_rules:
                    if rule.enabled:
                        await self._evaluate_scaling_rule(rule, metrics)
                
                # Predictive scaling based on trading patterns
                await self._predictive_scaling()
                
                # Sleep before next check
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in infrastructure monitoring: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _collect_metrics(self) -> Dict[InfrastructureComponent, ResourceMetrics]:
        """Collect current resource metrics"""
        metrics = {}
        
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        network = psutil.net_io_counters()
        
        # Compute metrics
        metrics[InfrastructureComponent.COMPUTE] = ResourceMetrics(
            component=InfrastructureComponent.COMPUTE,
            cpu_usage=cpu_percent / 100.0,
            memory_usage=memory.percent / 100.0,
            disk_usage=disk.percent / 100.0,
            network_io=network.bytes_sent + network.bytes_recv,
            response_time=await self._measure_response_time(),
            throughput=await self._measure_throughput(),
            error_rate=await self._measure_error_rate(),
            timestamp=datetime.now()
        )
        
        # Database metrics (simulated)
        metrics[InfrastructureComponent.DATABASE] = ResourceMetrics(
            component=InfrastructureComponent.DATABASE,
            cpu_usage=np.random.uniform(0.2, 0.8),
            memory_usage=np.random.uniform(0.3, 0.9),
            disk_usage=np.random.uniform(0.1, 0.7),
            network_io=np.random.uniform(1000, 10000),
            response_time=np.random.uniform(10, 150),  # ms
            throughput=np.random.uniform(100, 1000),   # queries/sec
            error_rate=np.random.uniform(0, 0.05),     # 0-5%
            timestamp=datetime.now()
        )
        
        # Cache metrics (simulated)
        metrics[InfrastructureComponent.CACHE] = ResourceMetrics(
            component=InfrastructureComponent.CACHE,
            cpu_usage=np.random.uniform(0.1, 0.6),
            memory_usage=np.random.uniform(0.4, 0.95),
            disk_usage=0.0,  # In-memory cache
            network_io=np.random.uniform(500, 5000),
            response_time=np.random.uniform(1, 10),    # ms
            throughput=np.random.uniform(1000, 10000), # ops/sec
            error_rate=np.random.uniform(0, 0.02),     # 0-2%
            timestamp=datetime.now()
        )
        
        return metrics
    
    async def _measure_response_time(self) -> float:
        """Measure system response time"""
        # This would measure actual API response times
        return np.random.uniform(10, 100)  # Simulated ms
    
    async def _measure_throughput(self) -> float:
        """Measure system throughput"""
        # This would measure actual requests/trades per second
        return np.random.uniform(50, 500)  # Simulated ops/sec
    
    async def _measure_error_rate(self) -> float:
        """Measure system error rate"""
        # This would measure actual error rates
        return np.random.uniform(0, 0.03)  # Simulated 0-3%
    
    async def _evaluate_scaling_rule(self, rule: ScalingRule, metrics: Dict):
        """Evaluate a scaling rule and take action if needed"""
        
        component_metrics = metrics.get(rule.component)
        if not component_metrics:
            return
        
        # Get the metric value
        metric_value = getattr(component_metrics, rule.metric_name, 0)
        
        # Check cooldown period
        last_action_time = self.last_scaling_action.get(rule.component)
        if last_action_time and datetime.now() - last_action_time < rule.cooldown_period:
            return
        
        current_capacity = self.current_capacity[rule.component]
        
        # Check for scale up
        if metric_value > rule.threshold_up and current_capacity < rule.max_instances:
            await self._execute_scaling_action(
                rule.component,
                rule.action_up,
                rule.metric_name,
                metric_value,
                f"Metric {rule.metric_name} ({metric_value:.2f}) exceeded threshold ({rule.threshold_up})"
            )
        
        # Check for scale down
        elif metric_value < rule.threshold_down and current_capacity > rule.min_instances:
            await self._execute_scaling_action(
                rule.component,
                rule.action_down,
                rule.metric_name,
                metric_value,
                f"Metric {rule.metric_name} ({metric_value:.2f}) below threshold ({rule.threshold_down})"
            )
    
    async def _execute_scaling_action(self,
                                    component: InfrastructureComponent,
                                    action: ScalingAction,
                                    trigger_metric: str,
                                    trigger_value: float,
                                    reason: str):
        """Execute a scaling action"""
        
        old_capacity = self.current_capacity[component]
        new_capacity = old_capacity
        
        # Calculate new capacity based on action
        if action == ScalingAction.SCALE_UP:
            new_capacity = min(old_capacity * 2, 64)  # Double capacity, max 64
        elif action == ScalingAction.SCALE_DOWN:
            new_capacity = max(old_capacity // 2, 1)  # Half capacity, min 1
        elif action == ScalingAction.SCALE_OUT:
            new_capacity = old_capacity + 1
        elif action == ScalingAction.SCALE_IN:
            new_capacity = max(old_capacity - 1, 1)
        
        if new_capacity == old_capacity:
            return  # No change needed
        
        # Execute the scaling action
        success = await self._perform_scaling(component, action, old_capacity, new_capacity)
        
        if success:
            self.current_capacity[component] = new_capacity
            self.last_scaling_action[component] = datetime.now()
            
            # Record the scaling event
            event = ScalingEvent(
                timestamp=datetime.now(),
                component=component,
                action=action,
                trigger_metric=trigger_metric,
                trigger_value=trigger_value,
                old_capacity=old_capacity,
                new_capacity=new_capacity,
                reason=reason
            )
            
            self.scaling_history.append(event)
            
            logger.info(f"Scaled {component.value}: {old_capacity} -> {new_capacity} ({action.value})")
        else:
            logger.error(f"Failed to scale {component.value}")
    
    async def _perform_scaling(self,
                             component: InfrastructureComponent,
                             action: ScalingAction,
                             old_capacity: int,
                             new_capacity: int) -> bool:
        """Perform the actual scaling operation"""
        
        try:
            if component == InfrastructureComponent.COMPUTE:
                return await self._scale_compute(action, old_capacity, new_capacity)
            elif component == InfrastructureComponent.MEMORY:
                return await self._scale_memory(action, old_capacity, new_capacity)
            elif component == InfrastructureComponent.DATABASE:
                return await self._scale_database(action, old_capacity, new_capacity)
            elif component == InfrastructureComponent.CACHE:
                return await self._scale_cache(action, old_capacity, new_capacity)
            else:
                logger.warning(f"Scaling not implemented for {component.value}")
                return False
                
        except Exception as e:
            logger.error(f"Error performing scaling for {component.value}: {e}")
            return False
    
    async def _scale_compute(self, action: ScalingAction, old_capacity: int, new_capacity: int) -> bool:
        """Scale compute resources"""
        # This would interface with cloud provider APIs or container orchestration
        logger.info(f"Scaling compute from {old_capacity} to {new_capacity} instances")
        
        # Simulate scaling delay
        await asyncio.sleep(2)
        
        return True
    
    async def _scale_memory(self, action: ScalingAction, old_capacity: int, new_capacity: int) -> bool:
        """Scale memory resources"""
        logger.info(f"Scaling memory from {old_capacity}GB to {new_capacity}GB")
        
        # Simulate scaling delay
        await asyncio.sleep(1)
        
        return True
    
    async def _scale_database(self, action: ScalingAction, old_capacity: int, new_capacity: int) -> bool:
        """Scale database resources"""
        logger.info(f"Scaling database from {old_capacity} to {new_capacity} instances")
        
        # This would scale database replicas or shards
        await asyncio.sleep(5)  # Database scaling takes longer
        
        return True
    
    async def _scale_cache(self, action: ScalingAction, old_capacity: int, new_capacity: int) -> bool:
        """Scale cache resources"""
        logger.info(f"Scaling cache from {old_capacity} to {new_capacity} instances")
        
        # This would scale Redis/Memcached instances
        await asyncio.sleep(1)
        
        return True
    
    async def _predictive_scaling(self):
        """Perform predictive scaling based on trading patterns"""
        
        # Analyze historical patterns
        if len(self.metrics_history) < 60:  # Need at least 1 hour of data
            return
        
        # Get current time info
        now = datetime.now()
        current_hour = now.hour
        current_day = now.weekday()
        
        # Predict load based on trading hours
        is_market_hours = 9 <= current_hour <= 16  # Market hours
        is_weekday = current_day < 5  # Monday-Friday
        
        if is_market_hours and is_weekday:
            # Scale up for market hours
            await self._preemptive_scale_up()
        elif not is_market_hours or not is_weekday:
            # Scale down for off-hours
            await self._preemptive_scale_down()
    
    async def _preemptive_scale_up(self):
        """Preemptively scale up for expected high load"""
        
        # Check if we're already scaled up
        compute_capacity = self.current_capacity[InfrastructureComponent.COMPUTE]
        
        if compute_capacity < 4:  # Scale up to at least 4 instances for market hours
            await self._execute_scaling_action(
                InfrastructureComponent.COMPUTE,
                ScalingAction.SCALE_OUT,
                'predictive',
                0.0,
                'Preemptive scaling for market hours'
            )
    
    async def _preemptive_scale_down(self):
        """Preemptively scale down for expected low load"""
        
        # Check if we can scale down
        compute_capacity = self.current_capacity[InfrastructureComponent.COMPUTE]
        
        if compute_capacity > 2:  # Scale down to 2 instances for off-hours
            await self._execute_scaling_action(
                InfrastructureComponent.COMPUTE,
                ScalingAction.SCALE_IN,
                'predictive',
                0.0,
                'Preemptive scaling for off-hours'
            )
    
    def get_scaling_summary(self) -> Dict[str, Any]:
        """Get summary of current infrastructure state"""
        
        recent_events = [
            event for event in self.scaling_history
            if event.timestamp > datetime.now() - timedelta(hours=24)
        ]
        
        return {
            'current_capacity': dict(self.current_capacity),
            'recent_scaling_events': len(recent_events),
            'total_scaling_events': len(self.scaling_history),
            'active_rules': len([rule for rule in self.scaling_rules if rule.enabled]),
            'last_24h_events': [
                {
                    'timestamp': event.timestamp.isoformat(),
                    'component': event.component.value,
                    'action': event.action.value,
                    'reason': event.reason
                }
                for event in recent_events[-10:]  # Last 10 events
            ]
        }
    
    async def add_scaling_rule(self, rule: ScalingRule):
        """Add a new scaling rule"""
        self.scaling_rules.append(rule)
        logger.info(f"Added scaling rule for {rule.component.value}")
    
    async def update_scaling_rule(self, component: InfrastructureComponent, **kwargs):
        """Update an existing scaling rule"""
        for rule in self.scaling_rules:
            if rule.component == component:
                for key, value in kwargs.items():
                    if hasattr(rule, key):
                        setattr(rule, key, value)
                logger.info(f"Updated scaling rule for {component.value}")
                return
        
        logger.warning(f"No scaling rule found for {component.value}")
    
    async def manual_scale(self,
                          component: InfrastructureComponent,
                          action: ScalingAction,
                          reason: str = "Manual scaling"):
        """Manually trigger scaling action"""
        
        await self._execute_scaling_action(
            component,
            action,
            'manual',
            0.0,
            reason
        )
