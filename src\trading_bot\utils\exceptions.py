"""Enhanced exceptions for the API framework."""

from typing import Any, Dict, Optional

from ..core.exceptions import TradingBotError


class APIConnectionError(TradingBotError):
    """Exception raised for API connection errors."""
    
    def __init__(
        self,
        message: str,
        endpoint: Optional[str] = None,
        retry_count: int = 0,
        error_code: Optional[str] = None,
    ):
        self.endpoint = endpoint
        self.retry_count = retry_count
        
        details = {"endpoint": endpoint, "retry_count": retry_count}
        super().__init__(message, error_code, details)


class APIRateLimitError(TradingBotError):
    """Exception raised when API rate limits are exceeded."""
    
    def __init__(
        self,
        message: str,
        rate_limit_type: Optional[str] = None,
        reset_time: Optional[float] = None,
        error_code: Optional[str] = None,
    ):
        self.rate_limit_type = rate_limit_type
        self.reset_time = reset_time
        
        details = {"rate_limit_type": rate_limit_type, "reset_time": reset_time}
        super().__init__(message, error_code, details)


class AuthenticationError(TradingBotError):
    """Exception raised for authentication failures."""
    
    def __init__(
        self,
        message: str,
        auth_type: Optional[str] = None,
        requires_reauth: bool = True,
        error_code: Optional[str] = None,
    ):
        self.auth_type = auth_type
        self.requires_reauth = requires_reauth
        
        details = {"auth_type": auth_type, "requires_reauth": requires_reauth}
        super().__init__(message, error_code, details)


class CircuitBreakerError(TradingBotError):
    """Exception raised when circuit breaker is open."""
    
    def __init__(
        self,
        message: str,
        service_name: Optional[str] = None,
        failure_count: int = 0,
        error_code: Optional[str] = None,
    ):
        self.service_name = service_name
        self.failure_count = failure_count
        
        details = {"service_name": service_name, "failure_count": failure_count}
        super().__init__(message, error_code, details)


class OrderValidationError(TradingBotError):
    """Exception raised for order validation failures."""
    
    def __init__(
        self,
        message: str,
        validation_type: Optional[str] = None,
        order_data: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
    ):
        self.validation_type = validation_type
        self.order_data = order_data or {}
        
        details = {"validation_type": validation_type, "order_data": order_data}
        super().__init__(message, error_code, details)


class WebSocketError(TradingBotError):
    """Exception raised for WebSocket connection errors."""
    
    def __init__(
        self,
        message: str,
        connection_state: Optional[str] = None,
        reconnect_attempts: int = 0,
        error_code: Optional[str] = None,
    ):
        self.connection_state = connection_state
        self.reconnect_attempts = reconnect_attempts
        
        details = {
            "connection_state": connection_state,
            "reconnect_attempts": reconnect_attempts,
        }
        super().__init__(message, error_code, details)


class DataValidationError(TradingBotError):
    """Exception raised for data validation errors."""
    
    def __init__(
        self,
        message: str,
        data_type: Optional[str] = None,
        validation_errors: Optional[Dict[str, str]] = None,
        error_code: Optional[str] = None,
    ):
        self.data_type = data_type
        self.validation_errors = validation_errors or {}
        
        details = {"data_type": data_type, "validation_errors": validation_errors}
        super().__init__(message, error_code, details)


class TimeoutError(TradingBotError):
    """Exception raised for timeout errors."""
    
    def __init__(
        self,
        message: str,
        timeout_duration: Optional[float] = None,
        operation: Optional[str] = None,
        error_code: Optional[str] = None,
    ):
        self.timeout_duration = timeout_duration
        self.operation = operation
        
        details = {"timeout_duration": timeout_duration, "operation": operation}
        super().__init__(message, error_code, details)
