"""Performance Tracker - Real-time performance metrics and analytics.

This module provides comprehensive performance tracking including:
- Trading performance metrics (P&L, Sharpe ratio, win rate)
- System performance metrics (latency, throughput, errors)
- Model performance tracking (accuracy, drift, inference time)
- Real-time dashboards and reporting
- Performance alerts and notifications
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import numpy as np
import pandas as pd

from ..core.config import settings
from ..utils.logger import get_structured_logger
from ..data.database import get_postgres_session

logger = get_structured_logger(__name__)


@dataclass
class TradingMetrics:
    """Trading performance metrics."""
    total_pnl: float = 0.0
    daily_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    max_drawdown: float = 0.0
    current_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    portfolio_value: float = 0.0
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class SystemMetrics:
    """System performance metrics."""
    api_response_time_avg: float = 0.0
    api_response_time_p95: float = 0.0
    order_execution_time_avg: float = 0.0
    order_execution_time_p95: float = 0.0
    data_processing_time_avg: float = 0.0
    ml_inference_time_avg: float = 0.0
    error_rate: float = 0.0
    throughput_orders_per_minute: float = 0.0
    throughput_data_points_per_second: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    active_connections: int = 0
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ModelMetrics:
    """ML model performance metrics."""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    auc_score: float = 0.0
    prediction_latency_ms: float = 0.0
    drift_score: float = 0.0
    confidence_avg: float = 0.0
    predictions_per_minute: float = 0.0
    model_version: str = ""
    last_training_date: Optional[datetime] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class PerformanceAlert:
    """Performance-based alert."""
    metric_name: str
    current_value: float
    threshold: float
    severity: str  # warning, critical
    message: str
    timestamp: datetime = field(default_factory=datetime.utcnow)


class PerformanceTracker:
    """
    Comprehensive performance tracking and analytics system.
    
    Tracks trading performance, system metrics, and model performance
    in real-time with alerting and reporting capabilities.
    """
    
    def __init__(self):
        self.is_running = False
        self.start_time = datetime.utcnow()
        
        # Current metrics
        self.trading_metrics = TradingMetrics()
        self.system_metrics = SystemMetrics()
        self.model_metrics = ModelMetrics()
        
        # Historical data (in-memory for real-time access)
        self.trading_history: deque = deque(maxlen=1440)  # 24 hours of minute data
        self.system_history: deque = deque(maxlen=1440)
        self.model_history: deque = deque(maxlen=1440)
        
        # Performance tracking
        self.order_times: deque = deque(maxlen=1000)  # Last 1000 orders
        self.api_response_times: deque = deque(maxlen=1000)
        self.ml_inference_times: deque = deque(maxlen=1000)
        self.error_counts: defaultdict = defaultdict(int)
        
        # Trade tracking
        self.trades_today: List[Dict] = []
        self.daily_pnl_history: deque = deque(maxlen=252)  # 1 year of trading days
        self.portfolio_value_history: deque = deque(maxlen=1440)
        
        # Alerts
        self.performance_alerts: List[PerformanceAlert] = []
        self.alert_thresholds = {
            'max_drawdown': 0.05,  # 5%
            'daily_loss': 0.02,    # 2%
            'error_rate': 0.05,    # 5%
            'api_latency': 1000,   # 1 second
            'order_latency': 500,  # 500ms
        }
        
        # Background tasks
        self.tracking_tasks: List[asyncio.Task] = []
        self.shutdown_event = asyncio.Event()
    
    async def initialize(self):
        """Initialize the performance tracker."""
        logger.info("Initializing performance tracker...")
        
        # Load historical data from database
        await self._load_historical_data()
        
        # Start tracking tasks
        await self._start_tracking_tasks()
        
        self.is_running = True
        logger.info("Performance tracker initialized")
    
    async def _load_historical_data(self):
        """Load historical performance data from database."""
        try:
            # Load recent trading metrics
            # This would query the database for recent performance data
            # For now, initialize with default values
            
            self.trading_metrics.portfolio_value = 100000.0  # Default starting value
            self.portfolio_value_history.append({
                'timestamp': datetime.utcnow(),
                'value': self.trading_metrics.portfolio_value
            })
            
            logger.info("Historical performance data loaded")
        
        except Exception as e:
            logger.error(f"Failed to load historical data: {e}")
    
    async def _start_tracking_tasks(self):
        """Start background tracking tasks."""
        tracking_tasks = [
            self._metrics_update_loop(),
            self._performance_calculation_loop(),
            self._alert_monitoring_loop(),
            self._data_persistence_loop()
        ]
        
        for task_coro in tracking_tasks:
            task = asyncio.create_task(task_coro)
            self.tracking_tasks.append(task)
        
        logger.info(f"Started {len(self.tracking_tasks)} tracking tasks")
    
    async def _metrics_update_loop(self):
        """Main metrics update loop."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                await self.update_metrics()
                await asyncio.sleep(60)  # Update every minute
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics update loop: {e}")
                await asyncio.sleep(60)
    
    async def _performance_calculation_loop(self):
        """Calculate advanced performance metrics."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                await self._calculate_advanced_metrics()
                await asyncio.sleep(300)  # Calculate every 5 minutes
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in performance calculation: {e}")
                await asyncio.sleep(300)
    
    async def _alert_monitoring_loop(self):
        """Monitor for performance alerts."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                await self._check_performance_alerts()
                await asyncio.sleep(30)  # Check every 30 seconds
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _data_persistence_loop(self):
        """Persist metrics to database."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                await self._persist_metrics()
                await asyncio.sleep(300)  # Persist every 5 minutes
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in data persistence: {e}")
                await asyncio.sleep(300)
    
    async def update_metrics(self):
        """Update all performance metrics."""
        current_time = datetime.utcnow()
        
        # Update trading metrics
        await self._update_trading_metrics()
        
        # Update system metrics
        await self._update_system_metrics()
        
        # Update model metrics
        await self._update_model_metrics()
        
        # Store historical snapshots
        self.trading_history.append({
            'timestamp': current_time,
            'metrics': self.trading_metrics.__dict__.copy()
        })
        
        self.system_history.append({
            'timestamp': current_time,
            'metrics': self.system_metrics.__dict__.copy()
        })
        
        self.model_history.append({
            'timestamp': current_time,
            'metrics': self.model_metrics.__dict__.copy()
        })
    
    async def _update_trading_metrics(self):
        """Update trading performance metrics."""
        try:
            # Calculate basic trading metrics
            if self.trades_today:
                total_pnl = sum(trade.get('pnl', 0) for trade in self.trades_today)
                winning_trades = len([t for t in self.trades_today if t.get('pnl', 0) > 0])
                losing_trades = len([t for t in self.trades_today if t.get('pnl', 0) < 0])
                
                self.trading_metrics.daily_pnl = total_pnl
                self.trading_metrics.total_trades = len(self.trades_today)
                self.trading_metrics.winning_trades = winning_trades
                self.trading_metrics.losing_trades = losing_trades
                
                if self.trading_metrics.total_trades > 0:
                    self.trading_metrics.win_rate = winning_trades / self.trading_metrics.total_trades
                
                # Calculate average win/loss
                wins = [t['pnl'] for t in self.trades_today if t.get('pnl', 0) > 0]
                losses = [t['pnl'] for t in self.trades_today if t.get('pnl', 0) < 0]
                
                self.trading_metrics.avg_win = np.mean(wins) if wins else 0.0
                self.trading_metrics.avg_loss = np.mean(losses) if losses else 0.0
            
            # Update portfolio value
            # This would integrate with actual portfolio tracking
            # For now, simulate based on daily P&L
            if self.portfolio_value_history:
                last_value = self.portfolio_value_history[-1]['value']
                self.trading_metrics.portfolio_value = last_value + self.trading_metrics.daily_pnl
            
            self.trading_metrics.timestamp = datetime.utcnow()
        
        except Exception as e:
            logger.error(f"Error updating trading metrics: {e}")
    
    async def _update_system_metrics(self):
        """Update system performance metrics."""
        try:
            # Calculate API response times
            if self.api_response_times:
                self.system_metrics.api_response_time_avg = np.mean(self.api_response_times)
                self.system_metrics.api_response_time_p95 = np.percentile(self.api_response_times, 95)
            
            # Calculate order execution times
            if self.order_times:
                self.system_metrics.order_execution_time_avg = np.mean(self.order_times)
                self.system_metrics.order_execution_time_p95 = np.percentile(self.order_times, 95)
            
            # Calculate ML inference times
            if self.ml_inference_times:
                self.system_metrics.ml_inference_time_avg = np.mean(self.ml_inference_times)
            
            # Calculate error rate
            total_requests = sum(self.error_counts.values()) if self.error_counts else 1
            errors = self.error_counts.get('error', 0)
            self.system_metrics.error_rate = errors / total_requests if total_requests > 0 else 0.0
            
            # Calculate throughput
            if self.trades_today:
                minutes_elapsed = max(1, (datetime.utcnow() - self.start_time).total_seconds() / 60)
                self.system_metrics.throughput_orders_per_minute = len(self.trades_today) / minutes_elapsed
            
            self.system_metrics.timestamp = datetime.utcnow()
        
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
    
    async def _update_model_metrics(self):
        """Update ML model performance metrics."""
        try:
            # This would integrate with the actual ML pipeline
            # For now, simulate some metrics
            
            if self.ml_inference_times:
                self.model_metrics.prediction_latency_ms = np.mean(self.ml_inference_times)
                self.model_metrics.predictions_per_minute = len(self.ml_inference_times)
            
            # Placeholder for actual model metrics
            # These would come from the ML pipeline
            self.model_metrics.accuracy = 0.75  # Placeholder
            self.model_metrics.drift_score = 0.1  # Placeholder
            
            self.model_metrics.timestamp = datetime.utcnow()
        
        except Exception as e:
            logger.error(f"Error updating model metrics: {e}")

    async def _calculate_advanced_metrics(self):
        """Calculate advanced performance metrics like Sharpe ratio, drawdown."""
        try:
            if len(self.daily_pnl_history) < 2:
                return

            # Convert to numpy array for calculations
            returns = np.array([day['pnl'] for day in self.daily_pnl_history])

            # Calculate Sharpe ratio (assuming 252 trading days per year)
            if len(returns) > 1 and np.std(returns) > 0:
                self.trading_metrics.sharpe_ratio = (np.mean(returns) * 252) / (np.std(returns) * np.sqrt(252))

            # Calculate Sortino ratio (downside deviation)
            negative_returns = returns[returns < 0]
            if len(negative_returns) > 0:
                downside_std = np.std(negative_returns)
                if downside_std > 0:
                    self.trading_metrics.sortino_ratio = (np.mean(returns) * 252) / (downside_std * np.sqrt(252))

            # Calculate maximum drawdown
            portfolio_values = [pv['value'] for pv in self.portfolio_value_history]
            if portfolio_values:
                peak = portfolio_values[0]
                max_dd = 0.0

                for value in portfolio_values:
                    if value > peak:
                        peak = value
                    drawdown = (peak - value) / peak
                    max_dd = max(max_dd, drawdown)

                self.trading_metrics.max_drawdown = max_dd

                # Current drawdown
                current_value = portfolio_values[-1]
                current_peak = max(portfolio_values)
                self.trading_metrics.current_drawdown = (current_peak - current_value) / current_peak

            # Calculate Calmar ratio
            if self.trading_metrics.max_drawdown > 0:
                annual_return = np.mean(returns) * 252
                self.trading_metrics.calmar_ratio = annual_return / self.trading_metrics.max_drawdown

        except Exception as e:
            logger.error(f"Error calculating advanced metrics: {e}")

    async def _check_performance_alerts(self):
        """Check for performance-based alerts."""
        alerts = []

        try:
            # Check drawdown alerts
            if self.trading_metrics.current_drawdown > self.alert_thresholds['max_drawdown']:
                alerts.append(PerformanceAlert(
                    metric_name="current_drawdown",
                    current_value=self.trading_metrics.current_drawdown,
                    threshold=self.alert_thresholds['max_drawdown'],
                    severity="critical",
                    message=f"Current drawdown {self.trading_metrics.current_drawdown:.2%} exceeds threshold"
                ))

            # Check daily loss alerts
            if self.trading_metrics.daily_pnl < -self.alert_thresholds['daily_loss'] * self.trading_metrics.portfolio_value:
                alerts.append(PerformanceAlert(
                    metric_name="daily_pnl",
                    current_value=self.trading_metrics.daily_pnl,
                    threshold=-self.alert_thresholds['daily_loss'] * self.trading_metrics.portfolio_value,
                    severity="warning",
                    message=f"Daily loss ${self.trading_metrics.daily_pnl:.2f} exceeds threshold"
                ))

            # Check system performance alerts
            if self.system_metrics.error_rate > self.alert_thresholds['error_rate']:
                alerts.append(PerformanceAlert(
                    metric_name="error_rate",
                    current_value=self.system_metrics.error_rate,
                    threshold=self.alert_thresholds['error_rate'],
                    severity="warning",
                    message=f"Error rate {self.system_metrics.error_rate:.2%} exceeds threshold"
                ))

            # Check API latency alerts
            if self.system_metrics.api_response_time_avg > self.alert_thresholds['api_latency']:
                alerts.append(PerformanceAlert(
                    metric_name="api_latency",
                    current_value=self.system_metrics.api_response_time_avg,
                    threshold=self.alert_thresholds['api_latency'],
                    severity="warning",
                    message=f"API latency {self.system_metrics.api_response_time_avg:.0f}ms exceeds threshold"
                ))

            # Add new alerts
            for alert in alerts:
                self.performance_alerts.append(alert)
                logger.warning(f"Performance alert: {alert.message}")

            # Clean up old alerts (keep last 100)
            if len(self.performance_alerts) > 100:
                self.performance_alerts = self.performance_alerts[-100:]

        except Exception as e:
            logger.error(f"Error checking performance alerts: {e}")

    async def _persist_metrics(self):
        """Persist metrics to database for long-term storage."""
        try:
            # This would save metrics to PostgreSQL
            # For now, just log the persistence
            logger.debug("Persisting performance metrics to database")

            # Example of what would be saved:
            # - Trading metrics with timestamp
            # - System metrics with timestamp
            # - Model metrics with timestamp
            # - Performance alerts

        except Exception as e:
            logger.error(f"Error persisting metrics: {e}")

    # Public methods for recording events

    async def record_order(self, order_data: Dict[str, Any]):
        """Record an order execution for performance tracking."""
        try:
            execution_time = order_data.get('execution_time_ms', 0)
            if execution_time > 0:
                self.order_times.append(execution_time)

            # Record trade if order is filled
            if order_data.get('status') == 'filled':
                trade = {
                    'symbol': order_data.get('symbol'),
                    'quantity': order_data.get('quantity'),
                    'price': order_data.get('fill_price'),
                    'side': order_data.get('side'),
                    'pnl': order_data.get('pnl', 0),
                    'timestamp': datetime.utcnow()
                }
                self.trades_today.append(trade)

        except Exception as e:
            logger.error(f"Error recording order: {e}")

    async def record_api_call(self, response_time_ms: float, success: bool = True):
        """Record API call performance."""
        try:
            self.api_response_times.append(response_time_ms)

            if success:
                self.error_counts['success'] += 1
            else:
                self.error_counts['error'] += 1

        except Exception as e:
            logger.error(f"Error recording API call: {e}")

    async def record_ml_inference(self, inference_time_ms: float, accuracy: Optional[float] = None):
        """Record ML inference performance."""
        try:
            self.ml_inference_times.append(inference_time_ms)

            if accuracy is not None:
                # Update running average of accuracy
                # This is a simplified approach
                self.model_metrics.accuracy = accuracy

        except Exception as e:
            logger.error(f"Error recording ML inference: {e}")

    async def record_portfolio_update(self, portfolio_value: float):
        """Record portfolio value update."""
        try:
            self.trading_metrics.portfolio_value = portfolio_value
            self.portfolio_value_history.append({
                'timestamp': datetime.utcnow(),
                'value': portfolio_value
            })

        except Exception as e:
            logger.error(f"Error recording portfolio update: {e}")

    # Reporting and dashboard methods

    def get_trading_performance(self) -> Dict[str, Any]:
        """Get current trading performance metrics."""
        return {
            'total_pnl': self.trading_metrics.total_pnl,
            'daily_pnl': self.trading_metrics.daily_pnl,
            'portfolio_value': self.trading_metrics.portfolio_value,
            'total_trades': self.trading_metrics.total_trades,
            'win_rate': self.trading_metrics.win_rate,
            'avg_win': self.trading_metrics.avg_win,
            'avg_loss': self.trading_metrics.avg_loss,
            'max_drawdown': self.trading_metrics.max_drawdown,
            'current_drawdown': self.trading_metrics.current_drawdown,
            'sharpe_ratio': self.trading_metrics.sharpe_ratio,
            'sortino_ratio': self.trading_metrics.sortino_ratio,
            'calmar_ratio': self.trading_metrics.calmar_ratio,
            'timestamp': self.trading_metrics.timestamp.isoformat()
        }

    def get_system_performance(self) -> Dict[str, Any]:
        """Get current system performance metrics."""
        return {
            'api_response_time_avg': self.system_metrics.api_response_time_avg,
            'api_response_time_p95': self.system_metrics.api_response_time_p95,
            'order_execution_time_avg': self.system_metrics.order_execution_time_avg,
            'order_execution_time_p95': self.system_metrics.order_execution_time_p95,
            'ml_inference_time_avg': self.system_metrics.ml_inference_time_avg,
            'error_rate': self.system_metrics.error_rate,
            'throughput_orders_per_minute': self.system_metrics.throughput_orders_per_minute,
            'memory_usage_mb': self.system_metrics.memory_usage_mb,
            'cpu_usage_percent': self.system_metrics.cpu_usage_percent,
            'timestamp': self.system_metrics.timestamp.isoformat()
        }

    def get_model_performance(self) -> Dict[str, Any]:
        """Get current model performance metrics."""
        return {
            'accuracy': self.model_metrics.accuracy,
            'precision': self.model_metrics.precision,
            'recall': self.model_metrics.recall,
            'f1_score': self.model_metrics.f1_score,
            'prediction_latency_ms': self.model_metrics.prediction_latency_ms,
            'drift_score': self.model_metrics.drift_score,
            'confidence_avg': self.model_metrics.confidence_avg,
            'predictions_per_minute': self.model_metrics.predictions_per_minute,
            'model_version': self.model_metrics.model_version,
            'timestamp': self.model_metrics.timestamp.isoformat()
        }

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        return {
            'trading': self.get_trading_performance(),
            'system': self.get_system_performance(),
            'models': self.get_model_performance(),
            'alerts': [
                {
                    'metric': alert.metric_name,
                    'value': alert.current_value,
                    'threshold': alert.threshold,
                    'severity': alert.severity,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat()
                }
                for alert in self.performance_alerts[-10:]  # Last 10 alerts
            ],
            'uptime': (datetime.utcnow() - self.start_time).total_seconds()
        }

    def get_historical_data(self, metric_type: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get historical performance data."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)

        if metric_type == 'trading':
            return [
                entry for entry in self.trading_history
                if entry['timestamp'] >= cutoff_time
            ]
        elif metric_type == 'system':
            return [
                entry for entry in self.system_history
                if entry['timestamp'] >= cutoff_time
            ]
        elif metric_type == 'models':
            return [
                entry for entry in self.model_history
                if entry['timestamp'] >= cutoff_time
            ]
        else:
            return []

    async def generate_performance_report(self, period: str = 'daily') -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        try:
            report = {
                'period': period,
                'generated_at': datetime.utcnow().isoformat(),
                'summary': self.get_performance_summary(),
                'trading_details': {
                    'trades_count': len(self.trades_today),
                    'best_trade': max(self.trades_today, key=lambda x: x.get('pnl', 0)) if self.trades_today else None,
                    'worst_trade': min(self.trades_today, key=lambda x: x.get('pnl', 0)) if self.trades_today else None,
                },
                'system_health': {
                    'uptime_hours': (datetime.utcnow() - self.start_time).total_seconds() / 3600,
                    'total_api_calls': sum(self.error_counts.values()),
                    'success_rate': self.error_counts.get('success', 0) / max(1, sum(self.error_counts.values())),
                },
                'recommendations': await self._generate_recommendations()
            }

            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {}

    async def _generate_recommendations(self) -> List[str]:
        """Generate performance improvement recommendations."""
        recommendations = []

        try:
            # Trading recommendations
            if self.trading_metrics.win_rate < 0.5:
                recommendations.append("Consider reviewing strategy parameters - win rate below 50%")

            if self.trading_metrics.current_drawdown > 0.03:
                recommendations.append("Current drawdown is high - consider reducing position sizes")

            # System recommendations
            if self.system_metrics.api_response_time_avg > 500:
                recommendations.append("API response times are high - consider optimizing requests")

            if self.system_metrics.error_rate > 0.02:
                recommendations.append("Error rate is elevated - review error logs")

            # Model recommendations
            if self.model_metrics.accuracy < 0.6:
                recommendations.append("Model accuracy is low - consider retraining")

            if self.model_metrics.drift_score > 0.3:
                recommendations.append("Model drift detected - schedule retraining")

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")

        return recommendations

    async def shutdown(self):
        """Graceful shutdown of the performance tracker."""
        logger.info("Shutting down performance tracker...")

        self.is_running = False
        self.shutdown_event.set()

        # Cancel all tracking tasks
        for task in self.tracking_tasks:
            task.cancel()

        if self.tracking_tasks:
            await asyncio.gather(*self.tracking_tasks, return_exceptions=True)

        # Final metrics persistence
        await self._persist_metrics()

        logger.info("Performance tracker shutdown completed")
