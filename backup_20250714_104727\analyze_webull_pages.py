# analyze_webull_pages.py
"""
Analyze Webull page structure to improve data extraction
"""

import time
import logging
from datetime import datetime
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import re
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebullPageAnalyzer:
    """Analyze Webull pages to find correct selectors"""
    
    def __init__(self):
        self.driver = None
        
    def initialize(self):
        """Initialize browser"""
        try:
            self.driver = uc.Chrome(version_main=None)
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("✅ Browser initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize: {e}")
            return False
    
    def analyze_stock_page(self, symbol):
        """Analyze a stock page structure"""
        try:
            url = f"https://app.webull.com/stocks/{symbol}"
            logger.info(f"Analyzing {symbol}...")
            self.driver.get(url)
            
            # Wait for page to fully load
            time.sleep(7)  # Longer wait for dynamic content
            
            # Save screenshot
            screenshot_name = f"analyze_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            self.driver.save_screenshot(screenshot_name)
            
            # Save page source
            page_source_file = f"page_source_{symbol}.html"
            with open(page_source_file, 'w', encoding='utf-8') as f:
                f.write(self.driver.page_source)
            logger.info(f"Page source saved to {page_source_file}")
            
            # Get page text
            page_text = self.driver.find_element(By.TAG_NAME, 'body').text
            
            # Save text content
            text_file = f"page_text_{symbol}.txt"
            with open(text_file, 'w', encoding='utf-8') as f:
                f.write(page_text)
            logger.info(f"Page text saved to {text_file}")
            
            # Try different methods to find price
            results = {
                'symbol': symbol,
                'url': url,
                'methods_tried': []
            }
            
            # Method 1: Look for elements containing dollar signs
            logger.info("Method 1: Finding elements with $ signs...")
            dollar_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '$')]")
            dollar_values = []
            for elem in dollar_elements[:10]:  # First 10 to avoid too many
                try:
                    text = elem.text.strip()
                    if '$' in text and len(text) < 20:  # Likely a price, not a paragraph
                        dollar_values.append(text)
                except:
                    pass
            results['dollar_values'] = dollar_values
            logger.info(f"Found {len(dollar_values)} dollar values: {dollar_values[:5]}")
            
            # Method 2: Look for specific class patterns
            logger.info("Method 2: Looking for price-related classes...")
            price_classes = []
            for pattern in ['price', 'quote', 'last', 'current']:
                elements = self.driver.find_elements(By.CSS_SELECTOR, f"[class*='{pattern}']")
                for elem in elements[:5]:
                    try:
                        class_name = elem.get_attribute('class')
                        text = elem.text.strip()
                        if text and len(text) < 50:
                            price_classes.append({
                                'class': class_name,
                                'text': text
                            })
                    except:
                        pass
            results['price_classes'] = price_classes[:10]
            
            # Method 3: Find largest numbers on page (likely prices)
            logger.info("Method 3: Finding large numbers...")
            numbers = re.findall(r'\d+\.\d{2}', page_text)
            unique_numbers = list(set(numbers))
            # Sort by value, assuming stock prices are typically between $1 and $5000
            valid_prices = [float(n) for n in unique_numbers if 1 <= float(n) <= 5000]
            valid_prices.sort(reverse=True)
            results['potential_prices'] = valid_prices[:10]
            
            # Method 4: Look for specific text patterns
            logger.info("Method 4: Text pattern matching...")
            patterns_found = {}
            
            # Price patterns
            price_patterns = [
                (r'Last Price[:\s]*\$?(\d+\.\d{2})', 'last_price'),
                (r'Current Price[:\s]*\$?(\d+\.\d{2})', 'current_price'),
                (r'Price[:\s]*\$?(\d+\.\d{2})', 'price'),
                (r'\$(\d+\.\d{2})\s*USD', 'usd_price'),
                (r'Quote[:\s]*\$?(\d+\.\d{2})', 'quote')
            ]
            
            for pattern, name in price_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    patterns_found[name] = match.group(1)
            
            results['pattern_matches'] = patterns_found
            
            # Save results
            results_file = f"analysis_{symbol}.json"
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Analysis saved to {results_file}")
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to analyze {symbol}: {e}")
            return None
    
    def find_interactive_elements(self, symbol):
        """Find clickable elements that might reveal data"""
        try:
            url = f"https://app.webull.com/stocks/{symbol}"
            self.driver.get(url)
            time.sleep(5)
            
            # Find all clickable elements
            clickable = self.driver.find_elements(By.CSS_SELECTOR, "button, a, [role='button'], [onclick]")
            logger.info(f"Found {len(clickable)} clickable elements")
            
            # Look for elements that might show more data
            for elem in clickable[:20]:
                try:
                    text = elem.text.strip()
                    if any(keyword in text.lower() for keyword in ['more', 'detail', 'quote', 'real-time', 'live']):
                        logger.info(f"Interesting element: {text}")
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"Error finding interactive elements: {e}")
    
    def cleanup(self):
        """Close browser"""
        if self.driver:
            self.driver.quit()

def main():
    """Run the analysis"""
    analyzer = WebullPageAnalyzer()
    
    try:
        if not analyzer.initialize():
            return
        
        # Analyze AAPL to understand the page structure
        symbol = 'AAPL'
        
        print(f"\n🔍 Analyzing {symbol} page structure...")
        print("=" * 50)
        
        results = analyzer.analyze_stock_page(symbol)
        
        if results:
            print(f"\n✅ Analysis complete for {symbol}")
            print(f"Check these files:")
            print(f"  - analyze_{symbol}_*.png (screenshot)")
            print(f"  - page_source_{symbol}.html (full HTML)")
            print(f"  - page_text_{symbol}.txt (visible text)")
            print(f"  - analysis_{symbol}.json (extracted data)")
            
            if results.get('potential_prices'):
                print(f"\n💰 Potential prices found: {results['potential_prices'][:5]}")
            
            if results.get('dollar_values'):
                print(f"\n💵 Dollar values found: {results['dollar_values'][:5]}")
        
        # Also check for interactive elements
        print(f"\n🔎 Looking for interactive elements...")
        analyzer.find_interactive_elements(symbol)
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
    finally:
        analyzer.cleanup()

if __name__ == "__main__":
    main()