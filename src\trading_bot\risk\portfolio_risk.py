"""Portfolio risk metrics calculator."""

import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from scipy import stats
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import Bar, Portfolio, Position, Symbol

logger = get_logger(__name__)


class PortfolioRiskCalculator:
    """Real-time portfolio risk metrics calculator."""
    
    def __init__(self):
        self.config = settings.risk
        self.risk_free_rate = 0.02  # 2% annual risk-free rate
        self.trading_days_per_year = 252
        
    async def calculate_var(
        self,
        positions: Dict[str, Dict],
        confidence_level: float = 0.95,
        lookback_days: int = 252
    ) -> Dict[str, float]:
        """
        Calculate Value at Risk (VaR) for the portfolio.
        
        Args:
            positions: Current portfolio positions
            confidence_level: Confidence level (0.95 for 95% VaR)
            lookback_days: Historical data lookback period
            
        Returns:
            Dictionary with VaR metrics
        """
        try:
            if not positions:
                return {"var_95": 0.0, "var_99": 0.0, "portfolio_value": 0.0}
            
            # Get historical returns for all positions
            returns_data = await self._get_portfolio_returns(positions, lookback_days)
            if returns_data.empty:
                return {"var_95": 0.0, "var_99": 0.0, "portfolio_value": 0.0}
            
            # Calculate portfolio value
            portfolio_value = sum(
                pos["quantity"] * pos["current_price"] 
                for pos in positions.values()
            )
            
            # Calculate portfolio returns
            portfolio_returns = returns_data.sum(axis=1)
            
            # Calculate VaR using historical simulation
            var_95 = np.percentile(portfolio_returns, (1 - 0.95) * 100) * portfolio_value
            var_99 = np.percentile(portfolio_returns, (1 - 0.99) * 100) * portfolio_value
            
            # Also calculate parametric VaR
            mean_return = np.mean(portfolio_returns)
            std_return = np.std(portfolio_returns)
            
            var_95_parametric = (mean_return - stats.norm.ppf(0.95) * std_return) * portfolio_value
            var_99_parametric = (mean_return - stats.norm.ppf(0.99) * std_return) * portfolio_value
            
            logger.info(
                f"VaR calculated - Historical 95%: ${var_95:,.2f}, 99%: ${var_99:,.2f}, "
                f"Parametric 95%: ${var_95_parametric:,.2f}, 99%: ${var_99_parametric:,.2f}"
            )
            
            return {
                "var_95": abs(var_95),
                "var_99": abs(var_99),
                "var_95_parametric": abs(var_95_parametric),
                "var_99_parametric": abs(var_99_parametric),
                "portfolio_value": portfolio_value,
                "mean_daily_return": mean_return,
                "daily_volatility": std_return
            }
            
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return {"var_95": 0.0, "var_99": 0.0, "portfolio_value": 0.0}
    
    async def calculate_cvar(
        self,
        positions: Dict[str, Dict],
        confidence_level: float = 0.95,
        lookback_days: int = 252
    ) -> float:
        """
        Calculate Conditional Value at Risk (CVaR/Expected Shortfall).
        
        Args:
            positions: Current portfolio positions
            confidence_level: Confidence level
            lookback_days: Historical data lookback period
            
        Returns:
            CVaR value
        """
        try:
            if not positions:
                return 0.0
            
            # Get historical returns
            returns_data = await self._get_portfolio_returns(positions, lookback_days)
            if returns_data.empty:
                return 0.0
            
            portfolio_value = sum(
                pos["quantity"] * pos["current_price"] 
                for pos in positions.values()
            )
            
            portfolio_returns = returns_data.sum(axis=1)
            
            # Calculate VaR threshold
            var_threshold = np.percentile(portfolio_returns, (1 - confidence_level) * 100)
            
            # Calculate CVaR as mean of returns below VaR threshold
            tail_returns = portfolio_returns[portfolio_returns <= var_threshold]
            cvar = np.mean(tail_returns) * portfolio_value if len(tail_returns) > 0 else 0.0
            
            logger.debug(f"CVaR calculated: ${abs(cvar):,.2f}")
            
            return abs(cvar)
            
        except Exception as e:
            logger.error(f"Error calculating CVaR: {e}")
            return 0.0
    
    async def calculate_sharpe_ratio(
        self,
        positions: Dict[str, Dict],
        lookback_days: int = 252
    ) -> float:
        """
        Calculate Sharpe ratio for the portfolio.
        
        Args:
            positions: Current portfolio positions
            lookback_days: Historical data lookback period
            
        Returns:
            Sharpe ratio
        """
        try:
            if not positions:
                return 0.0
            
            returns_data = await self._get_portfolio_returns(positions, lookback_days)
            if returns_data.empty:
                return 0.0
            
            portfolio_returns = returns_data.sum(axis=1)
            
            # Calculate annualized metrics
            mean_return = np.mean(portfolio_returns) * self.trading_days_per_year
            volatility = np.std(portfolio_returns) * np.sqrt(self.trading_days_per_year)
            
            if volatility == 0:
                return 0.0
            
            sharpe_ratio = (mean_return - self.risk_free_rate) / volatility
            
            logger.debug(f"Sharpe ratio calculated: {sharpe_ratio:.4f}")
            
            return sharpe_ratio
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    async def calculate_sortino_ratio(
        self,
        positions: Dict[str, Dict],
        lookback_days: int = 252
    ) -> float:
        """
        Calculate Sortino ratio for the portfolio.
        
        Args:
            positions: Current portfolio positions
            lookback_days: Historical data lookback period
            
        Returns:
            Sortino ratio
        """
        try:
            if not positions:
                return 0.0
            
            returns_data = await self._get_portfolio_returns(positions, lookback_days)
            if returns_data.empty:
                return 0.0
            
            portfolio_returns = returns_data.sum(axis=1)
            
            # Calculate annualized metrics
            mean_return = np.mean(portfolio_returns) * self.trading_days_per_year
            
            # Calculate downside deviation (only negative returns)
            negative_returns = portfolio_returns[portfolio_returns < 0]
            downside_deviation = np.std(negative_returns) * np.sqrt(self.trading_days_per_year) if len(negative_returns) > 0 else 0.0
            
            if downside_deviation == 0:
                return float('inf') if mean_return > self.risk_free_rate else 0.0
            
            sortino_ratio = (mean_return - self.risk_free_rate) / downside_deviation
            
            logger.debug(f"Sortino ratio calculated: {sortino_ratio:.4f}")
            
            return sortino_ratio
            
        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0.0
    
    async def calculate_beta(
        self,
        positions: Dict[str, Dict],
        benchmark_symbol: str = "SPY",
        lookback_days: int = 252
    ) -> float:
        """
        Calculate portfolio beta relative to benchmark.
        
        Args:
            positions: Current portfolio positions
            benchmark_symbol: Benchmark symbol (default SPY)
            lookback_days: Historical data lookback period
            
        Returns:
            Portfolio beta
        """
        try:
            if not positions:
                return 0.0
            
            # Get portfolio returns
            portfolio_returns_data = await self._get_portfolio_returns(positions, lookback_days)
            if portfolio_returns_data.empty:
                return 0.0
            
            portfolio_returns = portfolio_returns_data.sum(axis=1)
            
            # Get benchmark returns
            benchmark_returns = await self._get_symbol_returns(benchmark_symbol, lookback_days)
            if benchmark_returns.empty:
                return 0.0
            
            # Align dates
            common_dates = portfolio_returns.index.intersection(benchmark_returns.index)
            if len(common_dates) < 50:  # Need at least 50 data points
                return 0.0
            
            portfolio_aligned = portfolio_returns.loc[common_dates]
            benchmark_aligned = benchmark_returns.loc[common_dates]
            
            # Calculate beta using linear regression
            covariance = np.cov(portfolio_aligned, benchmark_aligned)[0, 1]
            benchmark_variance = np.var(benchmark_aligned)
            
            if benchmark_variance == 0:
                return 0.0
            
            beta = covariance / benchmark_variance
            
            logger.debug(f"Portfolio beta calculated: {beta:.4f}")
            
            return beta
            
        except Exception as e:
            logger.error(f"Error calculating beta: {e}")
            return 0.0
    
    async def calculate_maximum_drawdown(
        self,
        positions: Dict[str, Dict],
        lookback_days: int = 252
    ) -> Dict[str, float]:
        """
        Calculate maximum drawdown metrics.
        
        Args:
            positions: Current portfolio positions
            lookback_days: Historical data lookback period
            
        Returns:
            Dictionary with drawdown metrics
        """
        try:
            if not positions:
                return {"max_drawdown": 0.0, "current_drawdown": 0.0, "drawdown_duration": 0}
            
            returns_data = await self._get_portfolio_returns(positions, lookback_days)
            if returns_data.empty:
                return {"max_drawdown": 0.0, "current_drawdown": 0.0, "drawdown_duration": 0}
            
            portfolio_returns = returns_data.sum(axis=1)
            
            # Calculate cumulative returns
            cumulative_returns = (1 + portfolio_returns).cumprod()
            
            # Calculate running maximum
            running_max = cumulative_returns.expanding().max()
            
            # Calculate drawdown
            drawdown = (cumulative_returns - running_max) / running_max
            
            # Maximum drawdown
            max_drawdown = drawdown.min()
            
            # Current drawdown
            current_drawdown = drawdown.iloc[-1]
            
            # Drawdown duration (days since last peak)
            last_peak_idx = running_max.idxmax()
            current_date = cumulative_returns.index[-1]
            drawdown_duration = (current_date - last_peak_idx).days
            
            logger.debug(
                f"Drawdown metrics - Max: {max_drawdown:.4f}, Current: {current_drawdown:.4f}, "
                f"Duration: {drawdown_duration} days"
            )
            
            return {
                "max_drawdown": abs(max_drawdown),
                "current_drawdown": abs(current_drawdown),
                "drawdown_duration": drawdown_duration
            }
            
        except Exception as e:
            logger.error(f"Error calculating maximum drawdown: {e}")
            return {"max_drawdown": 0.0, "current_drawdown": 0.0, "drawdown_duration": 0}
    
    async def calculate_correlation_matrix(
        self,
        positions: Dict[str, Dict],
        lookback_days: int = 60
    ) -> pd.DataFrame:
        """
        Calculate correlation matrix for portfolio positions.
        
        Args:
            positions: Current portfolio positions
            lookback_days: Historical data lookback period
            
        Returns:
            Correlation matrix DataFrame
        """
        try:
            if len(positions) < 2:
                return pd.DataFrame()
            
            symbols = list(positions.keys())
            returns_data = {}
            
            for symbol in symbols:
                symbol_returns = await self._get_symbol_returns(symbol, lookback_days)
                if not symbol_returns.empty:
                    returns_data[symbol] = symbol_returns
            
            if len(returns_data) < 2:
                return pd.DataFrame()
            
            # Create DataFrame with aligned dates
            returns_df = pd.DataFrame(returns_data)
            returns_df = returns_df.dropna()
            
            if returns_df.empty:
                return pd.DataFrame()
            
            # Calculate correlation matrix
            correlation_matrix = returns_df.corr()
            
            logger.debug(f"Correlation matrix calculated for {len(symbols)} symbols")
            
            return correlation_matrix
            
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return pd.DataFrame()
    
    async def _get_portfolio_returns(
        self,
        positions: Dict[str, Dict],
        lookback_days: int
    ) -> pd.DataFrame:
        """Get historical returns for portfolio positions."""
        try:
            returns_data = {}
            total_value = sum(pos["quantity"] * pos["current_price"] for pos in positions.values())
            
            for symbol, position in positions.items():
                symbol_returns = await self._get_symbol_returns(symbol, lookback_days)
                if not symbol_returns.empty:
                    # Weight returns by position size
                    position_value = position["quantity"] * position["current_price"]
                    weight = position_value / total_value
                    returns_data[symbol] = symbol_returns * weight
            
            if not returns_data:
                return pd.DataFrame()
            
            # Create DataFrame with aligned dates
            returns_df = pd.DataFrame(returns_data)
            returns_df = returns_df.fillna(0)  # Fill missing values with 0
            
            return returns_df
            
        except Exception as e:
            logger.error(f"Error getting portfolio returns: {e}")
            return pd.DataFrame()
    
    async def _get_symbol_returns(self, symbol: str, lookback_days: int) -> pd.Series:
        """Get historical returns for a single symbol."""
        try:
            async with get_postgres_session() as session:
                # Get symbol ID
                stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                result = await session.execute(stmt)
                symbol_id = result.scalar_one_or_none()
                
                if not symbol_id:
                    return pd.Series()
                
                # Get historical prices
                cutoff_date = datetime.utcnow() - timedelta(days=lookback_days + 10)  # Extra buffer
                
                stmt = (
                    select(Bar.timestamp, Bar.close)
                    .where(
                        and_(
                            Bar.symbol_id == symbol_id,
                            Bar.timeframe == "1d",
                            Bar.timestamp >= cutoff_date
                        )
                    )
                    .order_by(Bar.timestamp.asc())
                )
                result = await session.execute(stmt)
                price_data = [(row[0], float(row[1])) for row in result.fetchall()]
                
                if len(price_data) < 10:
                    return pd.Series()
                
                # Create price series
                dates = [data[0] for data in price_data]
                prices = [data[1] for data in price_data]
                price_series = pd.Series(prices, index=dates)
                
                # Calculate returns
                returns = price_series.pct_change().dropna()
                
                return returns
                
        except Exception as e:
            logger.error(f"Error getting returns for {symbol}: {e}")
            return pd.Series()
