"""
ML Training Pipeline for Volatility Trading
Trains models to predict profitable volatility setups
"""
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix
import xgboost as xgb
import joblib
import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from src.trading_bot.ml.features.volatility_features import VolatilityFeatureEngineer

class VolatilityModelTrainer:
    def __init__(self):
        self.feature_engineer = VolatilityFeatureEngineer()
        self.models = {}
        self.model_dir = "models/volatility"
        os.makedirs(self.model_dir, exist_ok=True)
        
    async def train_models(self, symbols: list, lookback_days: int = 500):
        """Train ML models on volatility patterns"""
        print(f"🚀 Training volatility models on {len(symbols)} symbols...")
        
        # Collect training data
        all_features = []
        
        for symbol in symbols:
            try:
                # Get historical data
                ticker = yf.Ticker(symbol)
                df = ticker.history(period=f"{lookback_days}d", interval="1d")
                
                if len(df) < 100:
                    continue
                
                # Engineer features
                features = self.feature_engineer.create_features(df)
                features['symbol'] = symbol
                all_features.append(features)
                print(f"✓ Processed {symbol}: {len(features)} samples")
                
            except Exception as e:
                print(f"❌ Error processing {symbol}: {e}")
                continue
        
        if not all_features:
            print("❌ No training data collected!")
            return
        
        # Combine all data
        combined_data = pd.concat(all_features, ignore_index=True)
        print(f"📊 Total training samples: {len(combined_data)}")
        
        # Prepare features and targets
        feature_cols = [col for col in combined_data.columns 
                       if col not in ['target', 'symbol']]
        
        X = combined_data[feature_cols]
        # Create classification target (1 if profitable, 0 otherwise)
        y = (combined_data['target'] > 0.02).astype(int)  # 2% profit threshold
        
        print(f"Features: {len(feature_cols)}")
        print(f"Positive samples: {y.sum()} ({y.mean()*100:.1f}%)")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train models
        self._train_random_forest(X_train, y_train, X_test, y_test)
        self._train_xgboost(X_train, y_train, X_test, y_test)
        self._train_ensemble(X_train, y_train, X_test, y_test)
        
        # Save feature names
        joblib.dump(feature_cols, f"{self.model_dir}/feature_names.pkl")
        
        print("✅ Model training complete!")
        return self.models
    
    def _train_random_forest(self, X_train, y_train, X_test, y_test):
        """Train Random Forest model"""
        print("\n🌲 Training Random Forest...")
        
        rf = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=20,
            random_state=42,
            n_jobs=-1
        )
        
        rf.fit(X_train, y_train)
        
        # Evaluate
        train_score = rf.score(X_train, y_train)
        test_score = rf.score(X_test, y_test)
        
        print(f"Train accuracy: {train_score:.3f}")
        print(f"Test accuracy: {test_score:.3f}")
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': X_train.columns,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\nTop 10 features:")
        print(feature_importance.head(10))
        
        # Save model
        joblib.dump(rf, f"{self.model_dir}/random_forest.pkl")
        self.models['random_forest'] = rf
    
    def _train_xgboost(self, X_train, y_train, X_test, y_test):
        """Train XGBoost model"""
        print("\n🚀 Training XGBoost...")
        
        xgb_model = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            objective='binary:logistic',
            random_state=42
        )
        
        xgb_model.fit(
            X_train, y_train,
            eval_set=[(X_test, y_test)],
            early_stopping_rounds=10,
            verbose=False
        )
        
        # Evaluate
        train_score = xgb_model.score(X_train, y_train)
        test_score = xgb_model.score(X_test, y_test)
        
        print(f"Train accuracy: {train_score:.3f}")
        print(f"Test accuracy: {test_score:.3f}")
        
        # Save model
        joblib.dump(xgb_model, f"{self.model_dir}/xgboost.pkl")
        self.models['xgboost'] = xgb_model
    
    def _train_ensemble(self, X_train, y_train, X_test, y_test):
        """Create ensemble predictions"""
        print("\n🎯 Creating ensemble model...")
        
        # Get predictions from both models
        rf_pred = self.models['random_forest'].predict_proba(X_test)[:, 1]
        xgb_pred = self.models['xgboost'].predict_proba(X_test)[:, 1]
        
        # Weighted average
        ensemble_pred = 0.6 * rf_pred + 0.4 * xgb_pred
        ensemble_binary = (ensemble_pred > 0.5).astype(int)
        
        # Evaluate ensemble
        from sklearn.metrics import accuracy_score, precision_score, recall_score
        
        accuracy = accuracy_score(y_test, ensemble_binary)
        precision = precision_score(y_test, ensemble_binary)
        recall = recall_score(y_test, ensemble_binary)
        
        print(f"Ensemble accuracy: {accuracy:.3f}")
        print(f"Ensemble precision: {precision:.3f}")
        print(f"Ensemble recall: {recall:.3f}")
        
        # Save ensemble weights
        ensemble_config = {
            'weights': {'random_forest': 0.6, 'xgboost': 0.4},
            'threshold': 0.5,
            'performance': {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall
            }
        }
        
        joblib.dump(ensemble_config, f"{self.model_dir}/ensemble_config.pkl")

# Async wrapper for training
async def train_volatility_models():
    trainer = VolatilityModelTrainer()
    
    # High volatility symbols for training
    symbols = [
        'TQQQ', 'SQQQ', 'UVXY', 'SOXL', 'SOXS',
        'GME', 'AMC', 'TSLA', 'NVDA', 'RIOT',
        'MARA', 'COIN', 'MRNA', 'BNTX'
    ]
    
    await trainer.train_models(symbols)