# simple_working_extractor.py
"""
Simple working data extractor based on successful test
"""

import time
import logging
from datetime import datetime
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_stock_data_simple(symbol='AAPL'):
    """Simple extraction that works"""
    driver = None
    
    try:
        # Initialize driver (using working configuration)
        logger.info("Initializing browser...")
        driver = uc.Chrome(version_main=None)
        
        # Navigate to stock page
        url = f"https://app.webull.com/stocks/{symbol}"
        logger.info(f"Navigating to {url}")
        driver.get(url)
        
        # Wait for page load
        time.sleep(5)
        
        # Get page text
        page_text = driver.find_element(By.TAG_NAME, 'body').text
        
        # Extract price (simple regex)
        price_match = re.search(r'\$(\d+\.\d{2})', page_text)
        if price_match:
            price = float(price_match.group(1))
            logger.info(f"✅ {symbol} Price: ${price}")
        else:
            logger.info(f"❌ Could not find price for {symbol}")
        
        # Extract volume
        volume_match = re.search(r'Volume\s*([0-9,]+)', page_text, re.IGNORECASE)
        if volume_match:
            volume = volume_match.group(1)
            logger.info(f"✅ {symbol} Volume: {volume}")
        
        # Take screenshot
        screenshot = f"{symbol}_screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        driver.save_screenshot(screenshot)
        logger.info(f"📸 Screenshot saved: {screenshot}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error: {e}")
        return False
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    print("🚀 Simple Stock Data Extractor")
    print("=" * 40)
    
    symbols = ['AAPL', 'MSFT', 'TSLA']
    
    for symbol in symbols:
        print(f"\nTesting {symbol}...")
        success = extract_stock_data_simple(symbol)
        if success:
            print(f"✅ {symbol} extraction complete")
        else:
            print(f"❌ {symbol} extraction failed")
        time.sleep(3)