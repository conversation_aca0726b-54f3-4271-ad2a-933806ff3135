"""
Performance Analyzer for Continuous Improvement

Deep analysis of trading performance to identify patterns and improvement opportunities.
"""

from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
from dataclasses import dataclass
from collections import defaultdict
import logging

from ...core.logger import get_logger
from ...data.models import Trade, Position
from ...risk.manager import RiskManager

logger = get_logger(__name__)

@dataclass
class TradePattern:
    """Pattern analysis for trades"""
    win_rate: float
    avg_profit: float
    avg_loss: float
    avg_duration: timedelta
    best_times: List[int]  # Hours of day
    best_conditions: Dict[str, float]
    sample_size: int
    confidence_score: float

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    avg_trade_duration: timedelta
    total_trades: int
    profitable_months: int
    system_uptime: float

class PerformanceAnalyzer:
    """Deep analysis of trading performance for continuous improvement"""
    
    def __init__(self, db_manager, risk_manager: RiskManager):
        self.db_manager = db_manager
        self.risk_manager = risk_manager
        self.strategies = []
        self.analysis_cache = {}
        
    async def analyze_trade_patterns(self, lookback_days: int = 90) -> Dict[str, TradePattern]:
        """Analyze patterns in winning vs losing trades"""
        logger.info(f"Analyzing trade patterns for last {lookback_days} days")
        
        patterns = {}
        
        # Get all trades for analysis
        all_trades = await self._get_trades(lookback_days)
        
        if not all_trades:
            logger.warning("No trades found for pattern analysis")
            return patterns
        
        # Analyze by strategy
        strategy_trades = self._group_trades_by_strategy(all_trades)
        for strategy, trades in strategy_trades.items():
            if len(trades) >= 10:  # Minimum sample size
                patterns[f"strategy_{strategy}"] = await self._analyze_pattern(trades)
        
        # Analyze by time of day
        patterns['time_analysis'] = await self._analyze_time_patterns(all_trades)
        
        # Analyze by market conditions
        patterns['market_conditions'] = await self._analyze_market_conditions(all_trades)
        
        # Analyze by holding period
        patterns['duration_analysis'] = await self._analyze_duration_patterns(all_trades)
        
        # Analyze by volatility regime
        patterns['volatility_analysis'] = await self._analyze_volatility_patterns(all_trades)
        
        return patterns
    
    async def identify_improvement_areas(self) -> List[Dict]:
        """Find specific areas for improvement"""
        logger.info("Identifying improvement areas")
        
        improvements = []
        
        # Check for underperforming strategies
        strategy_performance = await self._analyze_strategy_performance()
        for strategy, metrics in strategy_performance.items():
            if metrics.sharpe_ratio < 1.0:
                improvements.append({
                    'area': 'strategy_performance',
                    'strategy': strategy,
                    'issue': f'Low Sharpe ratio: {metrics.sharpe_ratio:.2f}',
                    'recommendation': 'Consider parameter optimization or replacement',
                    'priority': 'high' if metrics.sharpe_ratio < 0.5 else 'medium'
                })
        
        # Check for high slippage patterns
        slippage_analysis = await self._analyze_slippage()
        if slippage_analysis['avg_slippage_bps'] > 5:
            improvements.append({
                'area': 'execution',
                'issue': f"High slippage: {slippage_analysis['avg_slippage_bps']:.1f} bps",
                'recommendation': 'Improve order execution algorithms',
                'priority': 'high'
            })
        
        # Check for risk concentration
        risk_concentration = await self._analyze_risk_concentration()
        for issue in risk_concentration:
            improvements.append(issue)
        
        # Check for timing inefficiencies
        timing_analysis = await self._analyze_timing_efficiency()
        if timing_analysis['efficiency_score'] < 0.7:
            improvements.append({
                'area': 'timing',
                'issue': f"Poor timing efficiency: {timing_analysis['efficiency_score']:.2f}",
                'recommendation': 'Optimize entry/exit timing algorithms',
                'priority': 'medium'
            })
            
        return improvements
    
    async def generate_optimization_recommendations(self) -> Dict:
        """Generate specific recommendations for system improvement"""
        logger.info("Generating optimization recommendations")
        
        return {
            'parameter_adjustments': await self._recommend_parameter_changes(),
            'strategy_modifications': await self._recommend_strategy_changes(),
            'risk_adjustments': await self._recommend_risk_changes(),
            'infrastructure_improvements': await self._recommend_infrastructure_changes(),
            'execution_improvements': await self._recommend_execution_improvements()
        }
    
    async def get_performance_metrics(self, days: int = 30) -> PerformanceMetrics:
        """Get comprehensive performance metrics"""
        trades = await self._get_trades(days)
        
        if not trades:
            return PerformanceMetrics(
                total_return=0.0, sharpe_ratio=0.0, max_drawdown=0.0,
                win_rate=0.0, profit_factor=0.0, avg_trade_duration=timedelta(0),
                total_trades=0, profitable_months=0, system_uptime=0.0
            )
        
        # Calculate metrics
        returns = [trade.profit_loss for trade in trades]
        winning_trades = [r for r in returns if r > 0]
        losing_trades = [r for r in returns if r < 0]
        
        total_return = sum(returns)
        win_rate = len(winning_trades) / len(trades) if trades else 0
        
        # Calculate Sharpe ratio (simplified)
        if len(returns) > 1:
            returns_std = np.std(returns)
            sharpe_ratio = (np.mean(returns) / returns_std) if returns_std > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Calculate max drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(min(drawdowns)) if len(drawdowns) > 0 else 0
        
        # Profit factor
        gross_profit = sum(winning_trades) if winning_trades else 0
        gross_loss = abs(sum(losing_trades)) if losing_trades else 1
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        
        # Average trade duration
        durations = [trade.exit_time - trade.entry_time for trade in trades if trade.exit_time]
        avg_duration = sum(durations, timedelta(0)) / len(durations) if durations else timedelta(0)
        
        return PerformanceMetrics(
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            avg_trade_duration=avg_duration,
            total_trades=len(trades),
            profitable_months=await self._count_profitable_months(days),
            system_uptime=await self._calculate_system_uptime(days)
        )

    # Private helper methods
    async def _get_trades(self, days: int) -> List[Trade]:
        """Get trades from the last N days"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # This would be implemented based on your database structure
            query = """
                SELECT * FROM trades
                WHERE entry_time >= %s AND entry_time <= %s
                ORDER BY entry_time DESC
            """

            # Placeholder - implement based on your database
            trades = []  # await self.db_manager.fetch_trades(start_date, end_date)
            return trades

        except Exception as e:
            logger.error(f"Error fetching trades: {e}")
            return []

    def _group_trades_by_strategy(self, trades: List[Trade]) -> Dict[str, List[Trade]]:
        """Group trades by strategy"""
        strategy_trades = defaultdict(list)
        for trade in trades:
            strategy_trades[trade.strategy].append(trade)
        return dict(strategy_trades)

    async def _analyze_pattern(self, trades: List[Trade]) -> TradePattern:
        """Analyze pattern for a group of trades"""
        if not trades:
            return TradePattern(0, 0, 0, timedelta(0), [], {}, 0, 0)

        winning_trades = [t for t in trades if t.profit_loss > 0]
        losing_trades = [t for t in trades if t.profit_loss < 0]

        win_rate = len(winning_trades) / len(trades)
        avg_profit = np.mean([t.profit_loss for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.profit_loss for t in losing_trades]) if losing_trades else 0

        # Calculate average duration
        durations = [t.exit_time - t.entry_time for t in trades if t.exit_time]
        avg_duration = sum(durations, timedelta(0)) / len(durations) if durations else timedelta(0)

        # Find best trading hours
        hours = [t.entry_time.hour for t in winning_trades]
        best_times = list(set(hours)) if hours else []

        # Best conditions (simplified)
        best_conditions = {
            'avg_volatility': np.mean([getattr(t, 'volatility', 0) for t in winning_trades]),
            'avg_volume': np.mean([getattr(t, 'volume', 0) for t in winning_trades])
        }

        confidence_score = min(len(trades) / 100, 1.0)  # Higher confidence with more trades

        return TradePattern(
            win_rate=win_rate,
            avg_profit=avg_profit,
            avg_loss=avg_loss,
            avg_duration=avg_duration,
            best_times=best_times,
            best_conditions=best_conditions,
            sample_size=len(trades),
            confidence_score=confidence_score
        )

    async def _analyze_time_patterns(self, trades: List[Trade]) -> TradePattern:
        """Analyze performance by time of day"""
        # Group trades by hour
        hourly_performance = defaultdict(list)
        for trade in trades:
            hour = trade.entry_time.hour
            hourly_performance[hour].append(trade.profit_loss)

        # Find best performing hours
        best_hours = []
        for hour, profits in hourly_performance.items():
            if len(profits) >= 5 and np.mean(profits) > 0:  # Minimum trades and profitable
                best_hours.append(hour)

        # Calculate overall time-based metrics
        all_profits = [trade.profit_loss for trade in trades]
        winning_trades = [p for p in all_profits if p > 0]
        losing_trades = [p for p in all_profits if p < 0]

        return TradePattern(
            win_rate=len(winning_trades) / len(all_profits) if all_profits else 0,
            avg_profit=np.mean(winning_trades) if winning_trades else 0,
            avg_loss=np.mean(losing_trades) if losing_trades else 0,
            avg_duration=timedelta(hours=4),  # Simplified
            best_times=sorted(best_hours),
            best_conditions={'time_based': True},
            sample_size=len(trades),
            confidence_score=min(len(trades) / 200, 1.0)
        )

    async def _analyze_market_conditions(self, trades: List[Trade]) -> TradePattern:
        """Analyze performance under different market conditions"""
        # This would analyze trades based on market volatility, trend, etc.
        # Simplified implementation
        return await self._analyze_pattern(trades)

    async def _analyze_duration_patterns(self, trades: List[Trade]) -> TradePattern:
        """Analyze performance by holding duration"""
        # Group by duration buckets
        short_term = [t for t in trades if self._get_duration(t) < timedelta(hours=1)]
        medium_term = [t for t in trades if timedelta(hours=1) <= self._get_duration(t) < timedelta(days=1)]
        long_term = [t for t in trades if self._get_duration(t) >= timedelta(days=1)]

        # Find best duration bucket
        buckets = {'short': short_term, 'medium': medium_term, 'long': long_term}
        best_bucket = max(buckets.items(),
                         key=lambda x: np.mean([t.profit_loss for t in x[1]]) if x[1] else -float('inf'))

        return await self._analyze_pattern(best_bucket[1])

    async def _analyze_volatility_patterns(self, trades: List[Trade]) -> TradePattern:
        """Analyze performance under different volatility regimes"""
        # Simplified - would use actual volatility data
        return await self._analyze_pattern(trades)

    def _get_duration(self, trade: Trade) -> timedelta:
        """Get trade duration"""
        if trade.exit_time and trade.entry_time:
            return trade.exit_time - trade.entry_time
        return timedelta(0)

    async def _analyze_strategy_performance(self) -> Dict[str, PerformanceMetrics]:
        """Analyze performance by strategy"""
        strategy_performance = {}

        for strategy in self.strategies:
            trades = await self._get_strategy_trades(strategy, 90)
            if trades:
                strategy_performance[strategy] = await self._calculate_strategy_metrics(trades)

        return strategy_performance

    async def _get_strategy_trades(self, strategy: str, days: int) -> List[Trade]:
        """Get trades for a specific strategy"""
        all_trades = await self._get_trades(days)
        return [trade for trade in all_trades if trade.strategy == strategy]

    async def _calculate_strategy_metrics(self, trades: List[Trade]) -> PerformanceMetrics:
        """Calculate metrics for a specific strategy"""
        # Reuse the main metrics calculation
        return await self.get_performance_metrics(90)  # Simplified

    async def _analyze_slippage(self) -> Dict[str, float]:
        """Analyze execution slippage"""
        trades = await self._get_trades(30)

        if not trades:
            return {'avg_slippage_bps': 0, 'max_slippage_bps': 0}

        slippages = []
        for trade in trades:
            # Calculate slippage (simplified)
            expected_price = getattr(trade, 'expected_price', trade.entry_price)
            actual_price = trade.entry_price
            slippage_bps = abs(actual_price - expected_price) / expected_price * 10000
            slippages.append(slippage_bps)

        return {
            'avg_slippage_bps': np.mean(slippages),
            'max_slippage_bps': max(slippages) if slippages else 0
        }

    async def _analyze_risk_concentration(self) -> List[Dict]:
        """Analyze risk concentration issues"""
        issues = []

        # Check sector concentration
        current_positions = await self._get_current_positions()
        sector_exposure = defaultdict(float)

        for position in current_positions:
            sector = getattr(position, 'sector', 'Unknown')
            sector_exposure[sector] += abs(position.market_value)

        total_exposure = sum(sector_exposure.values())

        for sector, exposure in sector_exposure.items():
            concentration = exposure / total_exposure if total_exposure > 0 else 0
            if concentration > 0.3:  # 30% threshold
                issues.append({
                    'area': 'risk_concentration',
                    'issue': f'High sector concentration in {sector}: {concentration:.1%}',
                    'recommendation': 'Diversify sector exposure',
                    'priority': 'high' if concentration > 0.5 else 'medium'
                })

        return issues

    async def _get_current_positions(self) -> List[Position]:
        """Get current positions"""
        # Placeholder - implement based on your position management
        return []

    async def _analyze_timing_efficiency(self) -> Dict[str, float]:
        """Analyze timing efficiency of entries and exits"""
        trades = await self._get_trades(30)

        if not trades:
            return {'efficiency_score': 0.0}

        # Simplified timing analysis
        # In reality, this would compare entry/exit prices to optimal prices
        efficiency_scores = []

        for trade in trades:
            # Calculate how close entry was to optimal (simplified)
            # This would use actual price data to determine optimal entry/exit points
            efficiency_score = 0.7  # Placeholder
            efficiency_scores.append(efficiency_score)

        return {
            'efficiency_score': np.mean(efficiency_scores),
            'entry_efficiency': np.mean(efficiency_scores),  # Simplified
            'exit_efficiency': np.mean(efficiency_scores)   # Simplified
        }

    async def _recommend_parameter_changes(self) -> List[Dict]:
        """Recommend parameter adjustments"""
        recommendations = []

        # Analyze current parameters vs performance
        patterns = await self.analyze_trade_patterns(90)

        for pattern_name, pattern in patterns.items():
            if pattern.win_rate < 0.5:
                recommendations.append({
                    'parameter': f'{pattern_name}_threshold',
                    'current_value': 'unknown',
                    'recommended_value': 'increase by 10%',
                    'reason': f'Low win rate: {pattern.win_rate:.2f}'
                })

        return recommendations

    async def _recommend_strategy_changes(self) -> List[Dict]:
        """Recommend strategy modifications"""
        recommendations = []

        strategy_performance = await self._analyze_strategy_performance()

        for strategy, metrics in strategy_performance.items():
            if metrics.sharpe_ratio < 0.5:
                recommendations.append({
                    'strategy': strategy,
                    'action': 'disable_or_modify',
                    'reason': f'Poor Sharpe ratio: {metrics.sharpe_ratio:.2f}',
                    'suggested_modification': 'Reduce position size or adjust parameters'
                })

        return recommendations

    async def _recommend_risk_changes(self) -> List[Dict]:
        """Recommend risk management adjustments"""
        recommendations = []

        metrics = await self.get_performance_metrics(90)

        if metrics.max_drawdown > 0.15:  # 15% threshold
            recommendations.append({
                'area': 'risk_management',
                'change': 'reduce_position_sizes',
                'reason': f'High max drawdown: {metrics.max_drawdown:.1%}',
                'target': 'Reduce to under 10%'
            })

        return recommendations

    async def _recommend_infrastructure_changes(self) -> List[Dict]:
        """Recommend infrastructure improvements"""
        recommendations = []

        uptime = await self._calculate_system_uptime(30)

        if uptime < 0.99:
            recommendations.append({
                'area': 'infrastructure',
                'change': 'improve_reliability',
                'reason': f'Low system uptime: {uptime:.2%}',
                'suggestion': 'Add redundancy and monitoring'
            })

        return recommendations

    async def _recommend_execution_improvements(self) -> List[Dict]:
        """Recommend execution improvements"""
        recommendations = []

        slippage = await self._analyze_slippage()

        if slippage['avg_slippage_bps'] > 3:
            recommendations.append({
                'area': 'execution',
                'change': 'improve_order_routing',
                'reason': f"High slippage: {slippage['avg_slippage_bps']:.1f} bps",
                'suggestion': 'Use TWAP/VWAP algorithms'
            })

        return recommendations

    async def _count_profitable_months(self, days: int) -> int:
        """Count profitable months in the period"""
        trades = await self._get_trades(days)

        monthly_pnl = defaultdict(float)
        for trade in trades:
            month_key = trade.entry_time.strftime('%Y-%m')
            monthly_pnl[month_key] += trade.profit_loss

        return sum(1 for pnl in monthly_pnl.values() if pnl > 0)

    async def _calculate_system_uptime(self, days: int) -> float:
        """Calculate system uptime percentage"""
        # This would be implemented based on your monitoring system
        # Placeholder implementation
        return 0.995  # 99.5% uptime
