"""
Feedback Loop for Continuous Learning

Learns from every trade and continuously improves the trading system
through pattern recognition and knowledge accumulation.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict
import numpy as np
import pandas as pd
import json
from pathlib import Path

from ...core.logger import get_logger
from ...data.models import Trade, Position
from ...ml.pipeline import MLPipeline

logger = get_logger(__name__)

@dataclass
class TradeInsight:
    """Insight extracted from a trade"""
    insight_type: str  # 'success_factor', 'failure_factor', 'pattern', 'anomaly'
    description: str
    confidence: float
    trade_id: str
    timestamp: datetime
    metadata: Dict[str, Any]
    impact_score: float  # How much this insight affects performance

@dataclass
class LearningPattern:
    """A learned pattern from multiple trades"""
    pattern_id: str
    pattern_type: str
    description: str
    conditions: Dict[str, Any]
    success_rate: float
    sample_size: int
    confidence: float
    last_updated: datetime
    performance_impact: float

class KnowledgeBase:
    """Stores and manages learned patterns and insights"""
    
    def __init__(self, storage_path: str = "knowledge_base.json"):
        self.storage_path = Path(storage_path)
        self.positive_patterns: Dict[str, LearningPattern] = {}
        self.negative_patterns: Dict[str, LearningPattern] = {}
        self.insights: List[TradeInsight] = []
        self.pattern_counter = 0
        
    async def add_positive_pattern(self, factors: Dict[str, Any]):
        """Add a positive pattern from successful trades"""
        pattern_id = f"positive_{self.pattern_counter}"
        self.pattern_counter += 1
        
        pattern = LearningPattern(
            pattern_id=pattern_id,
            pattern_type='positive',
            description=f"Success pattern: {factors.get('description', 'Unknown')}",
            conditions=factors,
            success_rate=factors.get('success_rate', 0.8),
            sample_size=factors.get('sample_size', 1),
            confidence=factors.get('confidence', 0.5),
            last_updated=datetime.now(),
            performance_impact=factors.get('impact', 0.1)
        )
        
        self.positive_patterns[pattern_id] = pattern
        await self._save_knowledge_base()
        
    async def add_negative_pattern(self, factors: Dict[str, Any]):
        """Add a negative pattern from failed trades"""
        pattern_id = f"negative_{self.pattern_counter}"
        self.pattern_counter += 1
        
        pattern = LearningPattern(
            pattern_id=pattern_id,
            pattern_type='negative',
            description=f"Failure pattern: {factors.get('description', 'Unknown')}",
            conditions=factors,
            success_rate=factors.get('success_rate', 0.2),
            sample_size=factors.get('sample_size', 1),
            confidence=factors.get('confidence', 0.5),
            last_updated=datetime.now(),
            performance_impact=factors.get('impact', -0.1)
        )
        
        self.negative_patterns[pattern_id] = pattern
        await self._save_knowledge_base()
        
    async def get_relevant_patterns(self, trade_context: Dict[str, Any]) -> List[LearningPattern]:
        """Get patterns relevant to current trade context"""
        relevant_patterns = []
        
        for pattern in list(self.positive_patterns.values()) + list(self.negative_patterns.values()):
            if await self._is_pattern_relevant(pattern, trade_context):
                relevant_patterns.append(pattern)
        
        # Sort by confidence and impact
        relevant_patterns.sort(key=lambda p: p.confidence * abs(p.performance_impact), reverse=True)
        
        return relevant_patterns
    
    async def _is_pattern_relevant(self, pattern: LearningPattern, context: Dict[str, Any]) -> bool:
        """Check if a pattern is relevant to the current context"""
        # Simple relevance check based on matching conditions
        matches = 0
        total_conditions = len(pattern.conditions)
        
        if total_conditions == 0:
            return False
        
        for condition_key, condition_value in pattern.conditions.items():
            if condition_key in context:
                context_value = context[condition_key]
                
                # Check for approximate matches
                if isinstance(condition_value, (int, float)) and isinstance(context_value, (int, float)):
                    if abs(condition_value - context_value) / max(abs(condition_value), 1) < 0.2:  # 20% tolerance
                        matches += 1
                elif condition_value == context_value:
                    matches += 1
        
        relevance_score = matches / total_conditions
        return relevance_score > 0.5  # 50% match threshold
    
    async def _save_knowledge_base(self):
        """Save knowledge base to file"""
        try:
            data = {
                'positive_patterns': {k: asdict(v) for k, v in self.positive_patterns.items()},
                'negative_patterns': {k: asdict(v) for k, v in self.negative_patterns.items()},
                'insights': [asdict(insight) for insight in self.insights[-1000:]],  # Keep last 1000
                'pattern_counter': self.pattern_counter,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error saving knowledge base: {e}")

class FeedbackLoop:
    """Learn from every trade and continuously improve"""
    
    def __init__(self, ml_pipeline: MLPipeline, db_manager):
        self.ml_pipeline = ml_pipeline
        self.db_manager = db_manager
        self.knowledge_base = KnowledgeBase()
        self.performance_db = {}  # Would be actual database
        self.learning_queue = asyncio.Queue()
        self.pattern_recognition_models = {}
        
    async def initialize(self):
        """Initialize the feedback loop"""
        await self.knowledge_base._load_knowledge_base()
        await self._start_learning_worker()
        
    async def process_completed_trade(self, trade: Trade):
        """Extract learnings from each trade"""
        logger.info(f"Processing completed trade: {trade.id}")
        
        try:
            # Analyze what worked or didn't work
            if trade.profit_loss > 0:
                positive_factors = await self._analyze_success_factors(trade)
                if positive_factors:
                    await self.knowledge_base.add_positive_pattern(positive_factors)
            else:
                negative_factors = await self._analyze_failure_factors(trade)
                if negative_factors:
                    await self.knowledge_base.add_negative_pattern(negative_factors)
            
            # Extract specific insights
            insights = await self._extract_insights(trade)
            for insight in insights:
                self.knowledge_base.insights.append(insight)
            
            # Apply insights to strategies
            if insights:
                await self.apply_insights_to_strategies(insights)
            
            # Add to ML training queue
            await self.ml_pipeline.add_to_training_queue(trade)
            
            # Performance attribution
            attribution = await self._attribute_performance(trade)
            await self._store_attribution(attribution)
            
        except Exception as e:
            logger.error(f"Error processing trade {trade.id}: {e}")
    
    async def _analyze_success_factors(self, trade: Trade) -> Dict[str, Any]:
        """Analyze factors that contributed to trade success"""
        factors = {
            'description': 'Successful trade analysis',
            'sample_size': 1,
            'confidence': 0.6
        }
        
        # Market conditions at entry
        factors['market_volatility'] = getattr(trade, 'entry_volatility', 0.2)
        factors['market_trend'] = getattr(trade, 'market_trend', 'neutral')
        factors['volume_ratio'] = getattr(trade, 'volume_ratio', 1.0)
        
        # Trade characteristics
        factors['holding_period'] = (trade.exit_time - trade.entry_time).total_seconds() / 3600  # hours
        factors['position_size'] = trade.quantity
        factors['entry_hour'] = trade.entry_time.hour
        factors['day_of_week'] = trade.entry_time.weekday()
        
        # Strategy specific
        factors['strategy'] = trade.strategy
        factors['signal_strength'] = getattr(trade, 'signal_strength', 0.5)
        
        # Performance metrics
        factors['profit_margin'] = trade.profit_loss / (trade.entry_price * trade.quantity)
        factors['success_rate'] = 1.0  # This trade was successful
        factors['impact'] = min(abs(trade.profit_loss) / 1000, 0.5)  # Normalize impact
        
        return factors
    
    async def _analyze_failure_factors(self, trade: Trade) -> Dict[str, Any]:
        """Analyze factors that contributed to trade failure"""
        factors = {
            'description': 'Failed trade analysis',
            'sample_size': 1,
            'confidence': 0.6
        }
        
        # Similar analysis as success factors but for failures
        factors['market_volatility'] = getattr(trade, 'entry_volatility', 0.2)
        factors['market_trend'] = getattr(trade, 'market_trend', 'neutral')
        factors['volume_ratio'] = getattr(trade, 'volume_ratio', 1.0)
        
        factors['holding_period'] = (trade.exit_time - trade.entry_time).total_seconds() / 3600
        factors['position_size'] = trade.quantity
        factors['entry_hour'] = trade.entry_time.hour
        factors['day_of_week'] = trade.entry_time.weekday()
        
        factors['strategy'] = trade.strategy
        factors['signal_strength'] = getattr(trade, 'signal_strength', 0.5)
        
        # Failure specific metrics
        factors['loss_margin'] = abs(trade.profit_loss) / (trade.entry_price * trade.quantity)
        factors['success_rate'] = 0.0  # This trade failed
        factors['impact'] = -min(abs(trade.profit_loss) / 1000, 0.5)  # Negative impact
        
        # Potential failure reasons
        factors['stop_loss_hit'] = getattr(trade, 'stop_loss_hit', False)
        factors['time_exit'] = getattr(trade, 'time_exit', False)
        factors['manual_exit'] = getattr(trade, 'manual_exit', False)
        
        return factors
    
    async def _extract_insights(self, trade: Trade) -> List[TradeInsight]:
        """Extract specific insights from a trade"""
        insights = []
        
        # Timing insight
        if trade.profit_loss > 0:
            insights.append(TradeInsight(
                insight_type='success_factor',
                description=f"Profitable trade at {trade.entry_time.hour}:00 on {trade.entry_time.strftime('%A')}",
                confidence=0.6,
                trade_id=trade.id,
                timestamp=datetime.now(),
                metadata={
                    'entry_hour': trade.entry_time.hour,
                    'day_of_week': trade.entry_time.weekday(),
                    'profit': trade.profit_loss
                },
                impact_score=min(abs(trade.profit_loss) / 1000, 1.0)
            ))
        
        # Strategy insight
        if hasattr(trade, 'signal_strength') and trade.signal_strength > 0.8:
            insight_type = 'success_factor' if trade.profit_loss > 0 else 'failure_factor'
            insights.append(TradeInsight(
                insight_type=insight_type,
                description=f"High signal strength ({trade.signal_strength:.2f}) trade",
                confidence=0.8,
                trade_id=trade.id,
                timestamp=datetime.now(),
                metadata={
                    'signal_strength': trade.signal_strength,
                    'strategy': trade.strategy,
                    'outcome': 'profit' if trade.profit_loss > 0 else 'loss'
                },
                impact_score=abs(trade.profit_loss) / 1000
            ))
        
        # Volatility insight
        if hasattr(trade, 'entry_volatility'):
            if trade.entry_volatility > 0.3:  # High volatility
                insights.append(TradeInsight(
                    insight_type='pattern',
                    description=f"High volatility trade ({trade.entry_volatility:.2f})",
                    confidence=0.7,
                    trade_id=trade.id,
                    timestamp=datetime.now(),
                    metadata={
                        'volatility': trade.entry_volatility,
                        'outcome': 'profit' if trade.profit_loss > 0 else 'loss'
                    },
                    impact_score=abs(trade.profit_loss) / 1000
                ))
        
        return insights
    
    async def apply_insights_to_strategies(self, insights: List[TradeInsight]):
        """Apply insights to improve strategies"""
        for insight in insights:
            try:
                if insight.confidence > 0.7 and abs(insight.impact_score) > 0.1:
                    # High confidence, high impact insights
                    await self._apply_high_impact_insight(insight)
                elif insight.confidence > 0.5:
                    # Medium confidence insights
                    await self._apply_medium_impact_insight(insight)
                    
            except Exception as e:
                logger.error(f"Error applying insight: {e}")
    
    async def _apply_high_impact_insight(self, insight: TradeInsight):
        """Apply high impact insights immediately"""
        logger.info(f"Applying high impact insight: {insight.description}")
        
        # This would modify strategy parameters based on the insight
        # For example, if certain times are consistently profitable,
        # increase position sizes during those times
        
        if 'entry_hour' in insight.metadata and insight.insight_type == 'success_factor':
            # Increase allocation for this hour
            profitable_hour = insight.metadata['entry_hour']
            logger.info(f"Increasing allocation for hour {profitable_hour}")
            
    async def _apply_medium_impact_insight(self, insight: TradeInsight):
        """Apply medium impact insights with caution"""
        logger.info(f"Noting medium impact insight: {insight.description}")
        
        # These insights would be accumulated and applied when there's
        # sufficient evidence from multiple trades
        
    async def _attribute_performance(self, trade: Trade) -> Dict[str, Any]:
        """Attribute trade performance to various factors"""
        attribution = {
            'trade_id': trade.id,
            'total_pnl': trade.profit_loss,
            'strategy_contribution': trade.profit_loss * 0.7,  # Simplified
            'market_contribution': trade.profit_loss * 0.2,
            'execution_contribution': trade.profit_loss * 0.1,
            'timestamp': datetime.now(),
            'factors': {
                'strategy': trade.strategy,
                'market_conditions': getattr(trade, 'market_trend', 'neutral'),
                'volatility': getattr(trade, 'entry_volatility', 0.2),
                'signal_strength': getattr(trade, 'signal_strength', 0.5)
            }
        }
        
        return attribution
    
    async def _store_attribution(self, attribution: Dict[str, Any]):
        """Store performance attribution"""
        # This would store in actual database
        trade_id = attribution['trade_id']
        self.performance_db[trade_id] = attribution
        
    async def _start_learning_worker(self):
        """Start background worker for continuous learning"""
        asyncio.create_task(self._learning_worker())
        
    async def _learning_worker(self):
        """Background worker that processes learning queue"""
        while True:
            try:
                # Process learning tasks
                await asyncio.sleep(60)  # Process every minute
                
                # Update pattern confidences based on new data
                await self._update_pattern_confidences()
                
                # Identify emerging patterns
                await self._identify_emerging_patterns()
                
            except Exception as e:
                logger.error(f"Error in learning worker: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _update_pattern_confidences(self):
        """Update confidence scores for existing patterns"""
        # This would analyze recent trades to update pattern confidences
        pass
        
    async def _identify_emerging_patterns(self):
        """Identify new patterns from recent insights"""
        # This would use clustering or other ML techniques to find new patterns
        pass
