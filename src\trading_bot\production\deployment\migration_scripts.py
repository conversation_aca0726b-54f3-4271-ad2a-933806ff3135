"""
Database migration management for AI Trading Bot.

This module provides comprehensive database migration capabilities
for safe production deployments with rollback support.
"""

import asyncio
import logging
import os
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
import asyncpg
import redis.asyncio as redis
from motor.motor_asyncio import AsyncIOMotorClient

from ...core.config import Config
from ...utils.logger import get_logger

logger = get_logger(__name__)


class MigrationStatus(Enum):
    """Migration execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


@dataclass
class Migration:
    """Database migration definition."""
    id: str
    name: str
    description: str
    version: str
    database_type: str  # postgres, redis, mongodb
    up_script: str
    down_script: str
    dependencies: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class MigrationResult:
    """Result of migration execution."""
    migration_id: str
    status: MigrationStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    execution_time: float = 0.0
    affected_records: int = 0


class MigrationManager:
    """
    Comprehensive database migration manager.
    
    Manages database schema and data migrations across:
    - PostgreSQL schema changes
    - Redis data structure updates
    - MongoDB collection modifications
    - Cross-database consistency
    - Rollback procedures
    - Migration dependencies
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.migrations: Dict[str, Migration] = {}
        self.migration_history: List[MigrationResult] = []
        
        # Database connections
        self.pg_pool = None
        self.redis_client = None
        self.mongo_client = None
        
        # Load migrations
        self._load_migrations()
    
    async def initialize(self):
        """Initialize database connections."""
        try:
            # Initialize PostgreSQL connection pool
            self.pg_pool = await asyncpg.create_pool(
                host=self.config.database.postgres.host,
                port=self.config.database.postgres.port,
                user=self.config.database.postgres.user,
                password=self.config.database.postgres.password,
                database=self.config.database.postgres.database,
                min_size=2,
                max_size=10
            )
            
            # Initialize Redis client
            self.redis_client = redis.Redis(
                host=self.config.database.redis.host,
                port=self.config.database.redis.port,
                password=self.config.database.redis.password,
                decode_responses=True
            )
            
            # Initialize MongoDB client
            self.mongo_client = AsyncIOMotorClient(
                f"mongodb://{self.config.database.mongodb.host}:{self.config.database.mongodb.port}"
            )
            
            # Ensure migration tracking tables exist
            await self._ensure_migration_tables()
            
            logger.info("Migration manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize migration manager: {e}")
            raise
    
    async def cleanup(self):
        """Clean up database connections."""
        try:
            if self.pg_pool:
                await self.pg_pool.close()
            if self.redis_client:
                await self.redis_client.close()
            if self.mongo_client:
                self.mongo_client.close()
            
            logger.info("Migration manager cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup migration manager: {e}")
    
    def _load_migrations(self):
        """Load migration definitions."""
        # PostgreSQL migrations
        self.migrations.update({
            "001_create_market_data_table": Migration(
                id="001_create_market_data_table",
                name="Create Market Data Table",
                description="Create table for storing real-time market data",
                version="1.0.0",
                database_type="postgres",
                up_script="""
                CREATE TABLE IF NOT EXISTS market_data (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(10) NOT NULL,
                    price DECIMAL(10,2) NOT NULL,
                    volume BIGINT NOT NULL,
                    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                    open_price DECIMAL(10,2),
                    high_price DECIMAL(10,2),
                    low_price DECIMAL(10,2),
                    close_price DECIMAL(10,2),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                
                CREATE INDEX idx_market_data_symbol_timestamp ON market_data (symbol, timestamp DESC);
                CREATE INDEX idx_market_data_timestamp ON market_data (timestamp DESC);
                """,
                down_script="DROP TABLE IF EXISTS market_data CASCADE;"
            ),
            
            "002_create_orders_table": Migration(
                id="002_create_orders_table",
                name="Create Orders Table",
                description="Create table for storing trading orders",
                version="1.0.0",
                database_type="postgres",
                up_script="""
                CREATE TABLE IF NOT EXISTS orders (
                    id SERIAL PRIMARY KEY,
                    order_id VARCHAR(50) UNIQUE NOT NULL,
                    symbol VARCHAR(10) NOT NULL,
                    action VARCHAR(10) NOT NULL CHECK (action IN ('BUY', 'SELL')),
                    quantity INTEGER NOT NULL CHECK (quantity > 0),
                    price DECIMAL(10,2),
                    order_type VARCHAR(20) NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
                    filled_quantity INTEGER DEFAULT 0,
                    avg_fill_price DECIMAL(10,2),
                    commission DECIMAL(10,2) DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    executed_at TIMESTAMP WITH TIME ZONE
                );
                
                CREATE INDEX idx_orders_symbol_status ON orders (symbol, status);
                CREATE INDEX idx_orders_status_timestamp ON orders (status, created_at DESC);
                CREATE INDEX idx_orders_created_at ON orders (created_at DESC);
                """,
                down_script="DROP TABLE IF EXISTS orders CASCADE;"
            ),
            
            "003_create_positions_table": Migration(
                id="003_create_positions_table",
                name="Create Positions Table",
                description="Create table for tracking current positions",
                version="1.0.0",
                database_type="postgres",
                up_script="""
                CREATE TABLE IF NOT EXISTS positions (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(10) NOT NULL,
                    quantity INTEGER NOT NULL,
                    avg_price DECIMAL(10,2) NOT NULL,
                    current_price DECIMAL(10,2),
                    unrealized_pnl DECIMAL(10,2) DEFAULT 0,
                    realized_pnl DECIMAL(10,2) DEFAULT 0,
                    is_active BOOLEAN DEFAULT TRUE,
                    opened_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    closed_at TIMESTAMP WITH TIME ZONE,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                
                CREATE INDEX idx_positions_symbol_active ON positions (symbol, is_active);
                CREATE UNIQUE INDEX idx_positions_active_symbol ON positions (symbol) WHERE is_active = TRUE;
                """,
                down_script="DROP TABLE IF EXISTS positions CASCADE;",
                dependencies=["002_create_orders_table"]
            ),
            
            "004_create_analytics_tables": Migration(
                id="004_create_analytics_tables",
                name="Create Analytics Tables",
                description="Create tables for trading analytics and performance tracking",
                version="1.0.0",
                database_type="postgres",
                up_script="""
                CREATE TABLE IF NOT EXISTS daily_pnl (
                    id SERIAL PRIMARY KEY,
                    date DATE NOT NULL,
                    symbol VARCHAR(10),
                    realized_pnl DECIMAL(10,2) DEFAULT 0,
                    unrealized_pnl DECIMAL(10,2) DEFAULT 0,
                    total_pnl DECIMAL(10,2) DEFAULT 0,
                    trade_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id SERIAL PRIMARY KEY,
                    strategy_name VARCHAR(50) NOT NULL,
                    date DATE NOT NULL,
                    trades_count INTEGER DEFAULT 0,
                    winning_trades INTEGER DEFAULT 0,
                    losing_trades INTEGER DEFAULT 0,
                    total_pnl DECIMAL(10,2) DEFAULT 0,
                    max_drawdown DECIMAL(10,4) DEFAULT 0,
                    sharpe_ratio DECIMAL(10,4) DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                
                CREATE INDEX idx_daily_pnl_date_symbol ON daily_pnl (date DESC, symbol);
                CREATE INDEX idx_strategy_performance_date ON strategy_performance (date DESC, strategy_name);
                """,
                down_script="""
                DROP TABLE IF EXISTS strategy_performance CASCADE;
                DROP TABLE IF EXISTS daily_pnl CASCADE;
                """,
                dependencies=["003_create_positions_table"]
            )
        })
        
        # Redis migrations
        self.migrations.update({
            "redis_001_setup_cache_structure": Migration(
                id="redis_001_setup_cache_structure",
                name="Setup Redis Cache Structure",
                description="Initialize Redis cache structure for market data and ML predictions",
                version="1.0.0",
                database_type="redis",
                up_script="redis_setup_cache",
                down_script="redis_cleanup_cache"
            )
        })
        
        # MongoDB migrations
        self.migrations.update({
            "mongo_001_create_collections": Migration(
                id="mongo_001_create_collections",
                name="Create MongoDB Collections",
                description="Create collections for ML models and analytics data",
                version="1.0.0",
                database_type="mongodb",
                up_script="mongo_create_collections",
                down_script="mongo_drop_collections"
            )
        })
    
    async def _ensure_migration_tables(self):
        """Ensure migration tracking tables exist."""
        if self.pg_pool:
            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS migration_history (
                        id SERIAL PRIMARY KEY,
                        migration_id VARCHAR(100) NOT NULL UNIQUE,
                        name VARCHAR(200) NOT NULL,
                        version VARCHAR(20) NOT NULL,
                        database_type VARCHAR(20) NOT NULL,
                        status VARCHAR(20) NOT NULL,
                        started_at TIMESTAMP WITH TIME ZONE NOT NULL,
                        completed_at TIMESTAMP WITH TIME ZONE,
                        error_message TEXT,
                        execution_time DECIMAL(10,3),
                        affected_records INTEGER DEFAULT 0
                    );
                """)
    
    async def get_pending_migrations(self) -> List[Migration]:
        """Get list of pending migrations."""
        if not self.pg_pool:
            return []
        
        try:
            async with self.pg_pool.acquire() as conn:
                # Get completed migrations
                completed = await conn.fetch("""
                    SELECT migration_id FROM migration_history 
                    WHERE status = 'completed'
                """)
                completed_ids = {row['migration_id'] for row in completed}
                
                # Filter pending migrations
                pending = []
                for migration in self.migrations.values():
                    if migration.id not in completed_ids:
                        # Check dependencies
                        deps_satisfied = all(
                            dep_id in completed_ids 
                            for dep_id in migration.dependencies
                        )
                        if deps_satisfied:
                            pending.append(migration)
                
                # Sort by dependencies
                return self._sort_migrations_by_dependencies(pending)
                
        except Exception as e:
            logger.error(f"Failed to get pending migrations: {e}")
            return []
    
    def _sort_migrations_by_dependencies(self, migrations: List[Migration]) -> List[Migration]:
        """Sort migrations by their dependencies."""
        sorted_migrations = []
        remaining = migrations.copy()
        
        while remaining:
            # Find migrations with no unresolved dependencies
            ready = []
            for migration in remaining:
                deps_satisfied = all(
                    any(m.id == dep_id for m in sorted_migrations)
                    for dep_id in migration.dependencies
                    if dep_id in [m.id for m in migrations]
                )
                if deps_satisfied:
                    ready.append(migration)
            
            if not ready:
                # Circular dependency or missing dependency
                logger.error("Circular dependency detected in migrations")
                break
            
            # Add ready migrations to sorted list
            for migration in ready:
                sorted_migrations.append(migration)
                remaining.remove(migration)
        
        return sorted_migrations
    
    async def run_migrations(self) -> List[MigrationResult]:
        """Run all pending migrations."""
        logger.info("Starting database migrations...")
        
        pending_migrations = await self.get_pending_migrations()
        if not pending_migrations:
            logger.info("No pending migrations found")
            return []
        
        results = []
        
        for migration in pending_migrations:
            logger.info(f"Running migration: {migration.name}")
            result = await self._execute_migration(migration)
            results.append(result)
            
            if result.status == MigrationStatus.FAILED:
                logger.error(f"Migration failed: {migration.name} - {result.error_message}")
                break
        
        logger.info(f"Completed {len(results)} migrations")
        return results
    
    async def _execute_migration(self, migration: Migration) -> MigrationResult:
        """Execute a single migration."""
        start_time = datetime.utcnow()
        
        # Record migration start
        await self._record_migration_start(migration, start_time)
        
        try:
            affected_records = 0
            
            if migration.database_type == "postgres":
                affected_records = await self._execute_postgres_migration(migration)
            elif migration.database_type == "redis":
                affected_records = await self._execute_redis_migration(migration)
            elif migration.database_type == "mongodb":
                affected_records = await self._execute_mongodb_migration(migration)
            else:
                raise ValueError(f"Unsupported database type: {migration.database_type}")
            
            # Record successful completion
            end_time = datetime.utcnow()
            execution_time = (end_time - start_time).total_seconds()
            
            result = MigrationResult(
                migration_id=migration.id,
                status=MigrationStatus.COMPLETED,
                started_at=start_time,
                completed_at=end_time,
                execution_time=execution_time,
                affected_records=affected_records
            )
            
            await self._record_migration_completion(migration, result)
            
            logger.info(f"Migration completed: {migration.name} ({execution_time:.2f}s)")
            return result
            
        except Exception as e:
            # Record failure
            end_time = datetime.utcnow()
            execution_time = (end_time - start_time).total_seconds()
            
            result = MigrationResult(
                migration_id=migration.id,
                status=MigrationStatus.FAILED,
                started_at=start_time,
                completed_at=end_time,
                execution_time=execution_time,
                error_message=str(e)
            )
            
            await self._record_migration_failure(migration, result)
            
            logger.error(f"Migration failed: {migration.name} - {str(e)}")
            return result

    async def _execute_postgres_migration(self, migration: Migration) -> int:
        """Execute PostgreSQL migration."""
        if not self.pg_pool:
            raise RuntimeError("PostgreSQL pool not initialized")

        async with self.pg_pool.acquire() as conn:
            # Execute migration script
            statements = migration.up_script.strip().split(';')
            affected_records = 0

            async with conn.transaction():
                for statement in statements:
                    statement = statement.strip()
                    if statement:
                        result = await conn.execute(statement)
                        # Extract affected rows count if available
                        if result.startswith('CREATE') or result.startswith('INSERT') or result.startswith('UPDATE'):
                            try:
                                affected_records += int(result.split()[-1])
                            except (ValueError, IndexError):
                                pass

            return affected_records

    async def _execute_redis_migration(self, migration: Migration) -> int:
        """Execute Redis migration."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        affected_records = 0

        if migration.up_script == "redis_setup_cache":
            # Setup cache structure
            await self.redis_client.hset("cache:config", mapping={
                "market_data_ttl": "300",  # 5 minutes
                "prediction_ttl": "60",    # 1 minute
                "analytics_ttl": "3600"    # 1 hour
            })

            # Create cache key patterns
            await self.redis_client.set("cache:patterns:market_data", "market:{symbol}:*")
            await self.redis_client.set("cache:patterns:predictions", "pred:{symbol}:*")
            await self.redis_client.set("cache:patterns:analytics", "analytics:*")

            affected_records = 3

        return affected_records

    async def _execute_mongodb_migration(self, migration: Migration) -> int:
        """Execute MongoDB migration."""
        if not self.mongo_client:
            raise RuntimeError("MongoDB client not initialized")

        db = self.mongo_client.trading_bot
        affected_records = 0

        if migration.up_script == "mongo_create_collections":
            # Create collections for ML models
            await db.create_collection("ml_models")
            await db.create_collection("model_predictions")
            await db.create_collection("feature_data")
            await db.create_collection("analytics_cache")

            # Create indexes
            await db.ml_models.create_index("model_name")
            await db.model_predictions.create_index([("symbol", 1), ("timestamp", -1)])
            await db.feature_data.create_index([("symbol", 1), ("timestamp", -1)])
            await db.analytics_cache.create_index("cache_key")

            affected_records = 4

        return affected_records

    async def _record_migration_start(self, migration: Migration, start_time: datetime):
        """Record migration start in database."""
        if not self.pg_pool:
            return

        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO migration_history
                (migration_id, name, version, database_type, status, started_at)
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (migration_id) DO UPDATE SET
                    status = EXCLUDED.status,
                    started_at = EXCLUDED.started_at
            """, migration.id, migration.name, migration.version,
                migration.database_type, MigrationStatus.RUNNING.value, start_time)

    async def _record_migration_completion(self, migration: Migration, result: MigrationResult):
        """Record successful migration completion."""
        if not self.pg_pool:
            return

        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                UPDATE migration_history SET
                    status = $1,
                    completed_at = $2,
                    execution_time = $3,
                    affected_records = $4
                WHERE migration_id = $5
            """, result.status.value, result.completed_at,
                result.execution_time, result.affected_records, migration.id)

    async def _record_migration_failure(self, migration: Migration, result: MigrationResult):
        """Record migration failure."""
        if not self.pg_pool:
            return

        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                UPDATE migration_history SET
                    status = $1,
                    completed_at = $2,
                    execution_time = $3,
                    error_message = $4
                WHERE migration_id = $5
            """, result.status.value, result.completed_at,
                result.execution_time, result.error_message, migration.id)

    async def rollback_migration(self, migration_id: str) -> MigrationResult:
        """Rollback a specific migration."""
        migration = self.migrations.get(migration_id)
        if not migration:
            raise ValueError(f"Migration not found: {migration_id}")

        logger.info(f"Rolling back migration: {migration.name}")
        start_time = datetime.utcnow()

        try:
            affected_records = 0

            if migration.database_type == "postgres":
                affected_records = await self._rollback_postgres_migration(migration)
            elif migration.database_type == "redis":
                affected_records = await self._rollback_redis_migration(migration)
            elif migration.database_type == "mongodb":
                affected_records = await self._rollback_mongodb_migration(migration)

            # Record rollback
            end_time = datetime.utcnow()
            execution_time = (end_time - start_time).total_seconds()

            result = MigrationResult(
                migration_id=migration.id,
                status=MigrationStatus.ROLLED_BACK,
                started_at=start_time,
                completed_at=end_time,
                execution_time=execution_time,
                affected_records=affected_records
            )

            await self._record_migration_rollback(migration, result)

            logger.info(f"Migration rolled back: {migration.name}")
            return result

        except Exception as e:
            logger.error(f"Migration rollback failed: {migration.name} - {str(e)}")
            raise

    async def _rollback_postgres_migration(self, migration: Migration) -> int:
        """Rollback PostgreSQL migration."""
        if not self.pg_pool:
            raise RuntimeError("PostgreSQL pool not initialized")

        async with self.pg_pool.acquire() as conn:
            statements = migration.down_script.strip().split(';')
            affected_records = 0

            async with conn.transaction():
                for statement in statements:
                    statement = statement.strip()
                    if statement:
                        result = await conn.execute(statement)
                        try:
                            affected_records += int(result.split()[-1])
                        except (ValueError, IndexError):
                            pass

            return affected_records

    async def _rollback_redis_migration(self, migration: Migration) -> int:
        """Rollback Redis migration."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        affected_records = 0

        if migration.down_script == "redis_cleanup_cache":
            # Clean up cache structure
            await self.redis_client.delete("cache:config")
            await self.redis_client.delete("cache:patterns:market_data")
            await self.redis_client.delete("cache:patterns:predictions")
            await self.redis_client.delete("cache:patterns:analytics")

            affected_records = 4

        return affected_records

    async def _rollback_mongodb_migration(self, migration: Migration) -> int:
        """Rollback MongoDB migration."""
        if not self.mongo_client:
            raise RuntimeError("MongoDB client not initialized")

        db = self.mongo_client.trading_bot
        affected_records = 0

        if migration.down_script == "mongo_drop_collections":
            # Drop collections
            await db.drop_collection("ml_models")
            await db.drop_collection("model_predictions")
            await db.drop_collection("feature_data")
            await db.drop_collection("analytics_cache")

            affected_records = 4

        return affected_records

    async def _record_migration_rollback(self, migration: Migration, result: MigrationResult):
        """Record migration rollback."""
        if not self.pg_pool:
            return

        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                UPDATE migration_history SET
                    status = $1,
                    completed_at = $2,
                    execution_time = $3
                WHERE migration_id = $4
            """, result.status.value, result.completed_at,
                result.execution_time, migration.id)

    async def get_migration_history(self) -> List[Dict[str, Any]]:
        """Get migration execution history."""
        if not self.pg_pool:
            return []

        async with self.pg_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT * FROM migration_history
                ORDER BY started_at DESC
            """)

            return [dict(row) for row in rows]

    async def validate_database_integrity(self) -> Dict[str, Any]:
        """Validate database integrity after migrations."""
        integrity_report = {
            "postgres": {"status": "unknown", "issues": []},
            "redis": {"status": "unknown", "issues": []},
            "mongodb": {"status": "unknown", "issues": []}
        }

        # Validate PostgreSQL
        if self.pg_pool:
            try:
                async with self.pg_pool.acquire() as conn:
                    # Check required tables exist
                    tables = await conn.fetch("""
                        SELECT table_name FROM information_schema.tables
                        WHERE table_schema = 'public'
                    """)
                    table_names = {row['table_name'] for row in tables}

                    required_tables = {'market_data', 'orders', 'positions', 'daily_pnl', 'strategy_performance'}
                    missing_tables = required_tables - table_names

                    if missing_tables:
                        integrity_report["postgres"]["issues"].append(f"Missing tables: {missing_tables}")
                        integrity_report["postgres"]["status"] = "error"
                    else:
                        integrity_report["postgres"]["status"] = "ok"

            except Exception as e:
                integrity_report["postgres"]["status"] = "error"
                integrity_report["postgres"]["issues"].append(str(e))

        # Validate Redis
        if self.redis_client:
            try:
                # Check cache configuration exists
                config_exists = await self.redis_client.exists("cache:config")
                if not config_exists:
                    integrity_report["redis"]["issues"].append("Cache configuration missing")
                    integrity_report["redis"]["status"] = "error"
                else:
                    integrity_report["redis"]["status"] = "ok"

            except Exception as e:
                integrity_report["redis"]["status"] = "error"
                integrity_report["redis"]["issues"].append(str(e))

        # Validate MongoDB
        if self.mongo_client:
            try:
                db = self.mongo_client.trading_bot
                collections = await db.list_collection_names()

                required_collections = {'ml_models', 'model_predictions', 'feature_data', 'analytics_cache'}
                missing_collections = required_collections - set(collections)

                if missing_collections:
                    integrity_report["mongodb"]["issues"].append(f"Missing collections: {missing_collections}")
                    integrity_report["mongodb"]["status"] = "error"
                else:
                    integrity_report["mongodb"]["status"] = "ok"

            except Exception as e:
                integrity_report["mongodb"]["status"] = "error"
                integrity_report["mongodb"]["issues"].append(str(e))

        return integrity_report
