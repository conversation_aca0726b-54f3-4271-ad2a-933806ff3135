"""Advanced rate limiting for API requests."""

import asyncio
import time
from collections import defaultdict, deque
from typing import Dict, Optional

from ..core.logger import get_logger
from ..models.enums import RateLimitType
from .exceptions import APIRateLimitError

logger = get_logger(__name__)


class RateLimiter:
    """Advanced rate limiter with different limits for different request types."""
    
    def __init__(self):
        # Rate limits per second for different types
        self.limits = {
            RateLimitType.DATA: 30,      # 30 requests/second for market data
            RateLimitType.TRADING: 3,    # 3 requests/second for trading
            RateLimitType.AUTH: 1,       # 1 request/second for authentication
        }
        
        # Request history for each type
        self.request_history: Dict[RateLimitType, deque] = defaultdict(lambda: deque())
        
        # Locks for thread safety
        self.locks: Dict[RateLimitType, asyncio.Lock] = {
            limit_type: asyncio.Lock() for limit_type in RateLimitType
        }
        
        # Burst allowance (can exceed limit briefly)
        self.burst_allowance = {
            RateLimitType.DATA: 10,      # Allow 10 extra requests
            RateLimitType.TRADING: 2,    # Allow 2 extra requests
            RateLimitType.AUTH: 0,       # No burst for auth
        }
        
        # Burst tracking
        self.burst_used: Dict[RateLimitType, int] = defaultdict(int)
        self.burst_reset_time: Dict[RateLimitType, float] = defaultdict(float)
    
    async def acquire(
        self,
        limit_type: RateLimitType = RateLimitType.DATA,
        endpoint: Optional[str] = None,
    ) -> None:
        """
        Acquire permission to make a request.
        
        Args:
            limit_type: Type of rate limit to apply
            endpoint: API endpoint (for logging)
            
        Raises:
            APIRateLimitError: If rate limit would be exceeded
        """
        async with self.locks[limit_type]:
            current_time = time.time()
            
            # Clean old requests (older than 1 second)
            self._cleanup_old_requests(limit_type, current_time)
            
            # Check if we can make the request
            if await self._can_make_request(limit_type, current_time):
                # Record the request
                self.request_history[limit_type].append(current_time)
                logger.debug(
                    f"Rate limit acquired",
                    extra={
                        "limit_type": limit_type.value,
                        "endpoint": endpoint,
                        "requests_in_window": len(self.request_history[limit_type]),
                        "limit": self.limits[limit_type],
                    }
                )
                return
            
            # Calculate wait time
            wait_time = await self._calculate_wait_time(limit_type, current_time)
            
            if wait_time > 10:  # Don't wait more than 10 seconds
                raise APIRateLimitError(
                    f"Rate limit exceeded for {limit_type.value}. Would need to wait {wait_time:.2f}s",
                    rate_limit_type=limit_type.value,
                    reset_time=current_time + wait_time,
                )
            
            logger.warning(
                f"Rate limit hit, waiting {wait_time:.2f}s",
                extra={
                    "limit_type": limit_type.value,
                    "endpoint": endpoint,
                    "wait_time": wait_time,
                }
            )
            
            # Wait and try again
            await asyncio.sleep(wait_time)
            self.request_history[limit_type].append(time.time())
    
    def _cleanup_old_requests(self, limit_type: RateLimitType, current_time: float) -> None:
        """Remove requests older than 1 second."""
        history = self.request_history[limit_type]
        while history and current_time - history[0] > 1.0:
            history.popleft()
    
    async def _can_make_request(self, limit_type: RateLimitType, current_time: float) -> bool:
        """Check if we can make a request without exceeding limits."""
        history = self.request_history[limit_type]
        limit = self.limits[limit_type]
        
        # Check basic rate limit
        if len(history) < limit:
            return True
        
        # Check burst allowance
        burst_limit = self.burst_allowance[limit_type]
        if burst_limit > 0:
            # Reset burst if enough time has passed
            if current_time - self.burst_reset_time[limit_type] > 5.0:
                self.burst_used[limit_type] = 0
                self.burst_reset_time[limit_type] = current_time
            
            # Check if we can use burst
            if self.burst_used[limit_type] < burst_limit:
                self.burst_used[limit_type] += 1
                return True
        
        return False
    
    async def _calculate_wait_time(self, limit_type: RateLimitType, current_time: float) -> float:
        """Calculate how long to wait before next request."""
        history = self.request_history[limit_type]
        if not history:
            return 0.0
        
        # Time until oldest request is 1 second old
        oldest_request = history[0]
        wait_time = 1.0 - (current_time - oldest_request)
        
        return max(0.0, wait_time)
    
    def get_status(self, limit_type: RateLimitType) -> Dict[str, any]:
        """Get current rate limit status."""
        current_time = time.time()
        self._cleanup_old_requests(limit_type, current_time)
        
        history = self.request_history[limit_type]
        limit = self.limits[limit_type]
        
        return {
            "limit_type": limit_type.value,
            "requests_in_window": len(history),
            "limit": limit,
            "remaining": max(0, limit - len(history)),
            "burst_used": self.burst_used[limit_type],
            "burst_available": self.burst_allowance[limit_type] - self.burst_used[limit_type],
            "reset_time": current_time + 1.0,
        }
    
    def reset_burst(self, limit_type: RateLimitType) -> None:
        """Reset burst allowance for a limit type."""
        self.burst_used[limit_type] = 0
        self.burst_reset_time[limit_type] = time.time()
    
    def set_limit(self, limit_type: RateLimitType, requests_per_second: int) -> None:
        """Update rate limit for a specific type."""
        self.limits[limit_type] = requests_per_second
        logger.info(
            f"Updated rate limit for {limit_type.value} to {requests_per_second} requests/second"
        )
    
    def clear_history(self, limit_type: Optional[RateLimitType] = None) -> None:
        """Clear request history."""
        if limit_type:
            self.request_history[limit_type].clear()
        else:
            for history in self.request_history.values():
                history.clear()


# Global rate limiter instance
rate_limiter = RateLimiter()
