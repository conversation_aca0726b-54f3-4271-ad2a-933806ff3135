"""
Emergency Kill Switch Dashboard

🚨 THE MOST CRITICAL COMPONENT 🚨

Big red button to instantly stop all trading, close positions, and lock the system.
Accessible from web, mobile, SMS, and phone hotline.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import json
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art

from ..core.logger import get_logger
from ..brokers.base_broker import BaseBroker
from ..strategies.strategy_manager import StrategyManager

logger = get_logger(__name__)

class EmergencyLevel(Enum):
    """Emergency levels"""
    STOP_TRADING = "stop_trading"           # Stop new trades only
    CLOSE_POSITIONS = "close_positions"     # Close all positions
    FULL_SHUTDOWN = "full_shutdown"         # Complete system shutdown
    SYSTEM_LOCK = "system_lock"             # Lock system completely

class EmergencyTrigger(Enum):
    """Emergency trigger sources"""
    WEB_DASHBOARD = "web_dashboard"
    MOBILE_APP = "mobile_app"
    SMS_COMMAND = "sms_command"
    PHONE_HOTLINE = "phone_hotline"
    AUTOMATED_RISK = "automated_risk"
    MANUAL_OVERRIDE = "manual_override"

class EmergencyKillSwitch:
    """Emergency system control with multiple access methods"""
    
    def __init__(self, broker: BaseBroker, strategy_manager: StrategyManager):
        self.broker = broker
        self.strategy_manager = strategy_manager
        self.is_emergency_active = False
        self.emergency_level = None
        self.emergency_log = []
        
        # Emergency contacts
        self.emergency_contacts = {
            'email': ['<EMAIL>'],
            'sms': ['+**********'],
            'phone': ['+**********']
        }
        
        # Emergency codes for SMS/phone access
        self.emergency_codes = {
            'KILL_ALL': EmergencyLevel.FULL_SHUTDOWN,
            'STOP_NOW': EmergencyLevel.STOP_TRADING,
            'CLOSE_ALL': EmergencyLevel.CLOSE_POSITIONS,
            'LOCK_SYS': EmergencyLevel.SYSTEM_LOCK
        }
        
        # Web server for dashboard
        self.web_server = None
        
    async def activate_emergency(self, 
                               level: EmergencyLevel,
                               trigger: EmergencyTrigger,
                               reason: str = "Manual activation",
                               user_id: str = "system") -> bool:
        """
        🚨 ACTIVATE EMERGENCY PROCEDURES 🚨
        
        This is the BIG RED BUTTON - use with extreme caution
        """
        
        logger.critical(f"🚨 EMERGENCY ACTIVATED: {level.value} via {trigger.value}")
        logger.critical(f"🚨 REASON: {reason}")
        logger.critical(f"🚨 USER: {user_id}")
        
        self.is_emergency_active = True
        self.emergency_level = level
        
        # Log emergency event
        emergency_event = {
            'timestamp': datetime.now().isoformat(),
            'level': level.value,
            'trigger': trigger.value,
            'reason': reason,
            'user_id': user_id,
            'actions_taken': []
        }
        
        try:
            # Execute emergency procedures based on level
            if level == EmergencyLevel.STOP_TRADING:
                await self._stop_all_trading(emergency_event)
            
            elif level == EmergencyLevel.CLOSE_POSITIONS:
                await self._stop_all_trading(emergency_event)
                await self._close_all_positions(emergency_event)
            
            elif level == EmergencyLevel.FULL_SHUTDOWN:
                await self._stop_all_trading(emergency_event)
                await self._close_all_positions(emergency_event)
                await self._shutdown_system(emergency_event)
            
            elif level == EmergencyLevel.SYSTEM_LOCK:
                await self._stop_all_trading(emergency_event)
                await self._close_all_positions(emergency_event)
                await self._shutdown_system(emergency_event)
                await self._lock_system(emergency_event)
            
            # Send emergency notifications
            await self._send_emergency_notifications(emergency_event)
            
            # Log the event
            self.emergency_log.append(emergency_event)
            
            logger.critical(f"🚨 EMERGENCY PROCEDURES COMPLETED: {level.value}")
            return True
            
        except Exception as e:
            logger.critical(f"🚨 EMERGENCY PROCEDURE FAILED: {e}")
            emergency_event['error'] = str(e)
            self.emergency_log.append(emergency_event)
            
            # Send failure notification
            await self._send_emergency_failure_notification(emergency_event)
            return False
    
    async def _stop_all_trading(self, event: Dict[str, Any]):
        """Stop all trading activities immediately"""
        
        logger.critical("🛑 STOPPING ALL TRADING")
        
        try:
            # Disable all strategies
            await self.strategy_manager.disable_all_strategies()
            event['actions_taken'].append('strategies_disabled')
            
            # Cancel all pending orders
            pending_orders = await self.broker.get_pending_orders()
            for order in pending_orders:
                try:
                    await self.broker.cancel_order(order['order_id'])
                    logger.info(f"Cancelled order: {order['order_id']}")
                except Exception as e:
                    logger.error(f"Failed to cancel order {order['order_id']}: {e}")
            
            event['actions_taken'].append(f'cancelled_{len(pending_orders)}_orders')
            
            # Set trading halt flag
            await self._set_trading_halt(True)
            event['actions_taken'].append('trading_halted')
            
            logger.critical("✅ ALL TRADING STOPPED")
            
        except Exception as e:
            logger.critical(f"❌ FAILED TO STOP TRADING: {e}")
            raise
    
    async def _close_all_positions(self, event: Dict[str, Any]):
        """Close all open positions at market prices"""
        
        logger.critical("🔴 CLOSING ALL POSITIONS")
        
        try:
            positions = await self.broker.get_positions()
            
            for position in positions:
                if position['quantity'] != 0:
                    try:
                        # Determine side for closing
                        side = 'sell' if position['quantity'] > 0 else 'buy'
                        quantity = abs(position['quantity'])
                        
                        # Place market order to close
                        close_order = await self.broker.place_order(
                            symbol=position['symbol'],
                            quantity=quantity,
                            side=side,
                            order_type='market',
                            time_in_force='IOC'  # Immediate or Cancel
                        )
                        
                        logger.critical(f"Closing position: {position['symbol']} {quantity} shares")
                        
                    except Exception as e:
                        logger.error(f"Failed to close position {position['symbol']}: {e}")
            
            event['actions_taken'].append(f'closed_{len(positions)}_positions')
            
            logger.critical("✅ ALL POSITIONS CLOSED")
            
        except Exception as e:
            logger.critical(f"❌ FAILED TO CLOSE POSITIONS: {e}")
            raise
    
    async def _shutdown_system(self, event: Dict[str, Any]):
        """Shutdown all system components"""
        
        logger.critical("⚡ SHUTTING DOWN SYSTEM")
        
        try:
            # Stop all background tasks
            await self.strategy_manager.stop_all_tasks()
            event['actions_taken'].append('background_tasks_stopped')
            
            # Disconnect from broker
            await self.broker.disconnect()
            event['actions_taken'].append('broker_disconnected')
            
            # Stop data feeds
            # await self.data_manager.stop_all_feeds()
            event['actions_taken'].append('data_feeds_stopped')
            
            # Save emergency state
            await self._save_emergency_state()
            event['actions_taken'].append('state_saved')
            
            logger.critical("✅ SYSTEM SHUTDOWN COMPLETE")
            
        except Exception as e:
            logger.critical(f"❌ FAILED TO SHUTDOWN SYSTEM: {e}")
            raise
    
    async def _lock_system(self, event: Dict[str, Any]):
        """Lock system to prevent any operations"""
        
        logger.critical("🔒 LOCKING SYSTEM")
        
        try:
            # Create lock file
            lock_file = "EMERGENCY_LOCK.txt"
            with open(lock_file, 'w') as f:
                f.write(f"EMERGENCY LOCK ACTIVATED\n")
                f.write(f"Timestamp: {datetime.now()}\n")
                f.write(f"Level: {self.emergency_level.value}\n")
                f.write(f"DO NOT DELETE THIS FILE\n")
            
            event['actions_taken'].append('system_locked')
            
            logger.critical("✅ SYSTEM LOCKED")
            
        except Exception as e:
            logger.critical(f"❌ FAILED TO LOCK SYSTEM: {e}")
            raise
    
    async def _send_emergency_notifications(self, event: Dict[str, Any]):
        """Send emergency notifications to all contacts"""
        
        message = f"""
🚨 TRADING BOT EMERGENCY ALERT 🚨

Level: {event['level']}
Trigger: {event['trigger']}
Time: {event['timestamp']}
Reason: {event['reason']}
User: {event['user_id']}

Actions Taken:
{chr(10).join('- ' + action for action in event['actions_taken'])}

System Status: EMERGENCY MODE ACTIVE

This is an automated alert. Do not reply.
        """
        
        # Send email notifications
        for email in self.emergency_contacts['email']:
            try:
                await self._send_email_alert(email, "🚨 TRADING BOT EMERGENCY", message)
            except Exception as e:
                logger.error(f"Failed to send email to {email}: {e}")
        
        # Send SMS notifications
        for phone in self.emergency_contacts['sms']:
            try:
                await self._send_sms_alert(phone, f"🚨 TRADING BOT EMERGENCY: {event['level']}")
            except Exception as e:
                logger.error(f"Failed to send SMS to {phone}: {e}")
        
        # Send push notifications
        try:
            await self._send_push_notification("🚨 TRADING BOT EMERGENCY", message)
        except Exception as e:
            logger.error(f"Failed to send push notification: {e}")
    
    async def _send_emergency_failure_notification(self, event: Dict[str, Any]):
        """Send notification when emergency procedures fail"""
        
        message = f"""
🚨🚨 CRITICAL: EMERGENCY PROCEDURE FAILED 🚨🚨

MANUAL INTERVENTION REQUIRED IMMEDIATELY

Failed Event: {event['level']}
Error: {event.get('error', 'Unknown error')}
Time: {event['timestamp']}

TAKE IMMEDIATE ACTION:
1. Check system status manually
2. Close positions manually if needed
3. Contact system administrator

This is a CRITICAL alert requiring immediate attention.
        """
        
        # Send to all contacts with high priority
        for email in self.emergency_contacts['email']:
            try:
                await self._send_email_alert(email, "🚨🚨 CRITICAL: EMERGENCY FAILED", message, priority='high')
            except:
                pass
        
        for phone in self.emergency_contacts['sms']:
            try:
                await self._send_sms_alert(phone, "🚨 CRITICAL: Emergency procedure failed. Manual intervention required.")
            except:
                pass
    
    async def _send_email_alert(self, email: str, subject: str, message: str, priority: str = 'normal'):
        """Send email alert"""
        
        try:
            # Configure your email settings
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
            sender_email = "<EMAIL>"
            sender_password = "your_app_password"  # Use app password for Gmail
            
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = email
            msg['Subject'] = subject
            
            if priority == 'high':
                msg['X-Priority'] = '1'
                msg['X-MSMail-Priority'] = 'High'
            
            msg.attach(MIMEText(message, 'plain'))
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Emergency email sent to {email}")
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
    
    async def _send_sms_alert(self, phone: str, message: str):
        """Send SMS alert using Twilio or similar service"""
        
        try:
            # Configure your SMS service (Twilio example)
            # You'll need to set up Twilio account and get credentials
            
            # Twilio example:
            # from twilio.rest import Client
            # client = Client(account_sid, auth_token)
            # client.messages.create(
            #     body=message,
            #     from_='+**********',  # Your Twilio number
            #     to=phone
            # )
            
            logger.info(f"Emergency SMS sent to {phone}")
            
        except Exception as e:
            logger.error(f"Failed to send SMS: {e}")
    
    async def _send_push_notification(self, title: str, message: str):
        """Send push notification"""
        
        try:
            # Configure push notification service
            # Example using Firebase or similar
            
            logger.info("Emergency push notification sent")
            
        except Exception as e:
            logger.error(f"Failed to send push notification: {e}")
    
    async def _set_trading_halt(self, halt: bool):
        """Set trading halt flag"""
        
        halt_file = "TRADING_HALT.flag"
        
        if halt:
            with open(halt_file, 'w') as f:
                f.write(f"TRADING HALTED\nTimestamp: {datetime.now()}\n")
        else:
            import os
            if os.path.exists(halt_file):
                os.remove(halt_file)
    
    async def _save_emergency_state(self):
        """Save emergency state for recovery"""
        
        state = {
            'emergency_active': self.is_emergency_active,
            'emergency_level': self.emergency_level.value if self.emergency_level else None,
            'emergency_log': self.emergency_log,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('emergency_state.json', 'w') as f:
            json.dump(state, f, indent=2)
    
    def check_emergency_codes(self, message: str) -> Optional[EmergencyLevel]:
        """Check if message contains emergency codes"""
        
        message_upper = message.upper().strip()
        
        for code, level in self.emergency_codes.items():
            if code in message_upper:
                return level
        
        return None
    
    async def process_sms_command(self, phone: str, message: str) -> bool:
        """Process SMS emergency command"""
        
        # Verify phone number is authorized
        if phone not in self.emergency_contacts['sms']:
            logger.warning(f"Unauthorized SMS emergency command from {phone}")
            return False
        
        # Check for emergency codes
        emergency_level = self.check_emergency_codes(message)
        
        if emergency_level:
            logger.critical(f"SMS emergency command received from {phone}: {emergency_level.value}")
            
            await self.activate_emergency(
                level=emergency_level,
                trigger=EmergencyTrigger.SMS_COMMAND,
                reason=f"SMS command: {message}",
                user_id=f"sms_{phone}"
            )
            
            return True
        
        return False
    
    def is_system_locked(self) -> bool:
        """Check if system is locked"""
        
        import os
        return os.path.exists("EMERGENCY_LOCK.txt")
    
    def is_trading_halted(self) -> bool:
        """Check if trading is halted"""
        
        import os
        return os.path.exists("TRADING_HALT.flag")
    
    async def deactivate_emergency(self, user_id: str = "system") -> bool:
        """Deactivate emergency mode (use with extreme caution)"""
        
        if not self.is_emergency_active:
            return True
        
        logger.warning(f"Deactivating emergency mode by {user_id}")
        
        self.is_emergency_active = False
        self.emergency_level = None
        
        # Remove halt flag
        await self._set_trading_halt(False)
        
        # Log deactivation
        self.emergency_log.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'emergency_deactivated',
            'user_id': user_id
        })
        
        return True
    
    def get_emergency_status(self) -> Dict[str, Any]:
        """Get current emergency status"""
        
        return {
            'emergency_active': self.is_emergency_active,
            'emergency_level': self.emergency_level.value if self.emergency_level else None,
            'system_locked': self.is_system_locked(),
            'trading_halted': self.is_trading_halted(),
            'last_emergency': self.emergency_log[-1] if self.emergency_log else None,
            'total_emergencies': len(self.emergency_log)
        }

# Global emergency instance
_emergency_kill_switch = None

def get_emergency_kill_switch(broker=None, strategy_manager=None) -> EmergencyKillSwitch:
    """Get global emergency kill switch instance"""
    global _emergency_kill_switch
    
    if _emergency_kill_switch is None and broker and strategy_manager:
        _emergency_kill_switch = EmergencyKillSwitch(broker, strategy_manager)
    
    return _emergency_kill_switch

# Emergency activation functions for different interfaces
async def emergency_stop_trading(reason: str = "Manual stop"):
    """Quick function to stop trading"""
    kill_switch = get_emergency_kill_switch()
    if kill_switch:
        return await kill_switch.activate_emergency(
            EmergencyLevel.STOP_TRADING,
            EmergencyTrigger.MANUAL_OVERRIDE,
            reason
        )
    return False

async def emergency_close_all(reason: str = "Manual close all"):
    """Quick function to close all positions"""
    kill_switch = get_emergency_kill_switch()
    if kill_switch:
        return await kill_switch.activate_emergency(
            EmergencyLevel.CLOSE_POSITIONS,
            EmergencyTrigger.MANUAL_OVERRIDE,
            reason
        )
    return False

async def emergency_full_shutdown(reason: str = "Manual shutdown"):
    """Quick function for full shutdown"""
    kill_switch = get_emergency_kill_switch()
    if kill_switch:
        return await kill_switch.activate_emergency(
            EmergencyLevel.FULL_SHUTDOWN,
            EmergencyTrigger.MANUAL_OVERRIDE,
            reason
        )
    return False
