"""
Paper Trading Engine - Standalone Implementation
Tracks virtual trades and calculates P&L for volatility hunting strategies
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import json
import logging
import yfinance as yf

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PaperTrade:
    """Represents a paper trade"""
    trade_id: str
    symbol: str
    strategy: str
    entry_time: datetime
    entry_price: float
    position_size: int
    target_price: float
    stop_loss: float
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    exit_reason: Optional[str] = None
    pnl: Optional[float] = None
    pnl_percent: Optional[float] = None
    is_closed: bool = False
    max_profit: float = 0.0
    max_loss: float = 0.0
    
    def __post_init__(self):
        if isinstance(self.entry_time, str):
            self.entry_time = datetime.fromisoformat(self.entry_time)
        if isinstance(self.exit_time, str) and self.exit_time:
            self.exit_time = datetime.fromisoformat(self.exit_time)

@dataclass
class Portfolio:
    """Paper trading portfolio"""
    initial_capital: float = 10000.0
    current_cash: float = 10000.0
    total_value: float = 10000.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    total_pnl: float = 0.0
    win_rate: float = 0.0
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0


class PaperTradingEngine:
    """
    Paper trading engine for testing volatility hunting strategies
    """
    
    def __init__(self, initial_capital: float = 10000.0):
        self.portfolio = Portfolio(initial_capital=initial_capital, current_cash=initial_capital)
        self.open_trades: Dict[str, PaperTrade] = {}
        self.closed_trades: List[PaperTrade] = []
        self.daily_values: List[Tuple[datetime, float]] = []
        self.trade_counter = 0
        
        # Risk management settings
        self.max_position_size = 0.05  # 5% max per trade
        self.max_total_exposure = 0.20  # 20% max total exposure
        self.max_daily_loss = 0.02  # 2% max daily loss
        
        # Performance tracking
        self.daily_pnl: List[float] = []
        self.peak_portfolio_value = initial_capital
        
    def place_order(self, symbol: str, strategy: str, entry_price: float,
                   target_price: float, stop_loss: float, confidence: float = 0.5) -> Optional[str]:
        """
        Place a paper trade order
        """
        try:
            # Calculate position size based on Kelly criterion and risk management
            position_size = self._calculate_position_size(entry_price, stop_loss, confidence)
            
            if position_size <= 0:
                logger.warning(f"Position size calculation returned {position_size}, skipping trade")
                return None
            
            # Check if we have enough cash
            required_capital = position_size * entry_price
            if required_capital > self.portfolio.current_cash:
                logger.warning(f"Insufficient cash for {symbol}: need ${required_capital:.2f}, have ${self.portfolio.current_cash:.2f}")
                return None
            
            # Check risk limits
            if not self._check_risk_limits(required_capital):
                logger.warning(f"Trade violates risk limits for {symbol}")
                return None
            
            # Create trade
            trade_id = f"{symbol}_{strategy}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.trade_counter}"
            self.trade_counter += 1
            
            trade = PaperTrade(
                trade_id=trade_id,
                symbol=symbol,
                strategy=strategy,
                entry_time=datetime.now(),
                entry_price=entry_price,
                position_size=position_size,
                target_price=target_price,
                stop_loss=stop_loss
            )
            
            # Execute trade
            self.open_trades[trade_id] = trade
            self.portfolio.current_cash -= required_capital
            
            logger.info(f"📈 PAPER TRADE OPENED: {symbol} ({strategy})")
            logger.info(f"   Entry: ${entry_price:.2f} x {position_size} shares = ${required_capital:.2f}")
            logger.info(f"   Target: ${target_price:.2f} ({((target_price/entry_price-1)*100):+.1f}%)")
            logger.info(f"   Stop: ${stop_loss:.2f} ({((stop_loss/entry_price-1)*100):+.1f}%)")
            logger.info(f"   Remaining cash: ${self.portfolio.current_cash:.2f}")
            
            return trade_id
            
        except Exception as e:
            logger.error(f"Error placing paper trade for {symbol}: {e}")
            return None
    
    def update_positions(self):
        """Update all open positions with current market prices"""
        if not self.open_trades:
            return
        
        symbols = list(set([trade.symbol for trade in self.open_trades.values()]))
        
        # Get current prices
        current_prices = {}
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
                if current_price > 0:
                    current_prices[symbol] = current_price
            except Exception as e:
                logger.error(f"Error getting price for {symbol}: {e}")
        
        # Update positions
        trades_to_close = []
        
        for trade_id, trade in self.open_trades.items():
            if trade.symbol not in current_prices:
                continue
                
            current_price = current_prices[trade.symbol]
            
            # Calculate current P&L
            current_pnl = (current_price - trade.entry_price) * trade.position_size
            current_pnl_percent = (current_price / trade.entry_price - 1) * 100
            
            # Update max profit/loss tracking
            trade.max_profit = max(trade.max_profit, current_pnl)
            trade.max_loss = min(trade.max_loss, current_pnl)
            
            # Check exit conditions
            exit_reason = None
            
            # Check target hit
            if trade.strategy in ['gap_fill', 'oversold_bounce']:
                if current_price >= trade.target_price:
                    exit_reason = "target_hit"
            elif trade.strategy == 'breakout_pullback':
                if current_price >= trade.target_price:
                    exit_reason = "target_hit"
            
            # Check stop loss
            if current_price <= trade.stop_loss:
                exit_reason = "stop_loss"
            
            # Check time-based exit (end of day for intraday strategies)
            if (datetime.now() - trade.entry_time).days >= 3:
                exit_reason = "time_exit"
            
            # Close trade if exit condition met
            if exit_reason:
                trades_to_close.append((trade_id, current_price, exit_reason))
        
        # Close trades
        for trade_id, exit_price, exit_reason in trades_to_close:
            self.close_trade(trade_id, exit_price, exit_reason)
        
        # Update portfolio value
        self._update_portfolio_value()
    
    def close_trade(self, trade_id: str, exit_price: float, exit_reason: str):
        """Close a paper trade"""
        if trade_id not in self.open_trades:
            return
        
        trade = self.open_trades[trade_id]
        
        # Calculate P&L
        pnl = (exit_price - trade.entry_price) * trade.position_size
        pnl_percent = (exit_price / trade.entry_price - 1) * 100
        
        # Update trade
        trade.exit_time = datetime.now()
        trade.exit_price = exit_price
        trade.exit_reason = exit_reason
        trade.pnl = pnl
        trade.pnl_percent = pnl_percent
        trade.is_closed = True
        
        # Return cash to portfolio
        self.portfolio.current_cash += trade.position_size * exit_price
        self.portfolio.realized_pnl += pnl
        
        # Move to closed trades
        self.closed_trades.append(trade)
        del self.open_trades[trade_id]
        
        # Update statistics
        self._update_portfolio_stats()
        
        # Log trade result
        logger.info(f"📊 PAPER TRADE CLOSED: {trade.symbol} ({trade.strategy})")
        logger.info(f"   Entry: ${trade.entry_price:.2f} → Exit: ${exit_price:.2f}")
        logger.info(f"   P&L: ${pnl:+.2f} ({pnl_percent:+.1f}%) - {exit_reason}")
        logger.info(f"   Duration: {(trade.exit_time - trade.entry_time).total_seconds()/3600:.1f} hours")
    
    def force_close_all(self):
        """Force close all open positions at current market price"""
        if not self.open_trades:
            return
        
        symbols = list(set([trade.symbol for trade in self.open_trades.values()]))
        
        # Get current prices
        current_prices = {}
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
                if current_price > 0:
                    current_prices[symbol] = current_price
            except Exception as e:
                logger.error(f"Error getting price for {symbol}: {e}")
        
        # Close all trades
        trades_to_close = list(self.open_trades.keys())
        for trade_id in trades_to_close:
            trade = self.open_trades[trade_id]
            if trade.symbol in current_prices:
                self.close_trade(trade_id, current_prices[trade.symbol], "force_close")
    
    def get_portfolio_summary(self) -> Dict:
        """Get current portfolio summary"""
        self._update_portfolio_value()
        
        return {
            'portfolio_value': self.portfolio.total_value,
            'cash': self.portfolio.current_cash,
            'realized_pnl': self.portfolio.realized_pnl,
            'unrealized_pnl': self.portfolio.unrealized_pnl,
            'total_pnl': self.portfolio.total_pnl,
            'total_return_pct': (self.portfolio.total_value / self.portfolio.initial_capital - 1) * 100,
            'open_positions': len(self.open_trades),
            'total_trades': self.portfolio.total_trades,
            'win_rate': self.portfolio.win_rate,
            'avg_win': self.portfolio.avg_win,
            'avg_loss': self.portfolio.avg_loss,
            'max_drawdown': self.portfolio.max_drawdown,
            'sharpe_ratio': self.portfolio.sharpe_ratio
        }
    
    def get_daily_summary(self) -> str:
        """Generate daily trading summary"""
        summary = self.get_portfolio_summary()
        
        output = f"\n📊 PAPER TRADING DAILY SUMMARY - {datetime.now().strftime('%Y-%m-%d')}\n"
        output += "=" * 60 + "\n"
        
        output += f"\n💰 PORTFOLIO PERFORMANCE:\n"
        output += f"   Total Value: ${summary['portfolio_value']:,.2f}\n"
        output += f"   Cash: ${summary['cash']:,.2f}\n"
        output += f"   Total Return: {summary['total_return_pct']:+.2f}%\n"
        output += f"   Realized P&L: ${summary['realized_pnl']:+,.2f}\n"
        output += f"   Unrealized P&L: ${summary['unrealized_pnl']:+,.2f}\n"
        
        output += f"\n📈 TRADING STATISTICS:\n"
        output += f"   Open Positions: {summary['open_positions']}\n"
        output += f"   Total Trades: {summary['total_trades']}\n"
        output += f"   Win Rate: {summary['win_rate']:.1f}%\n"
        output += f"   Avg Win: ${summary['avg_win']:.2f}\n"
        output += f"   Avg Loss: ${summary['avg_loss']:.2f}\n"
        output += f"   Max Drawdown: {summary['max_drawdown']:.2f}%\n"
        
        # Recent trades
        if self.closed_trades:
            output += f"\n🎯 RECENT TRADES (Last 5):\n"
            recent_trades = sorted(self.closed_trades, key=lambda x: x.exit_time or x.entry_time, reverse=True)[:5]
            for trade in recent_trades:
                output += f"   {trade.symbol} ({trade.strategy}): {trade.pnl_percent:+.1f}% - {trade.exit_reason}\n"
        
        # Open positions
        if self.open_trades:
            output += f"\n📋 OPEN POSITIONS:\n"
            for trade in self.open_trades.values():
                # Get current P&L estimate
                try:
                    ticker = yf.Ticker(trade.symbol)
                    current_price = ticker.info.get('currentPrice', trade.entry_price)
                    current_pnl_pct = (current_price / trade.entry_price - 1) * 100
                    output += f"   {trade.symbol} ({trade.strategy}): {current_pnl_pct:+.1f}% (${trade.position_size * trade.entry_price:.0f})\n"
                except:
                    output += f"   {trade.symbol} ({trade.strategy}): (${trade.position_size * trade.entry_price:.0f})\n"
        
        return output
    
    def save_results(self, filename: str = None):
        """Save trading results to JSON"""
        if filename is None:
            filename = f"paper_trading_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        results = {
            'portfolio': asdict(self.portfolio),
            'closed_trades': [asdict(trade) for trade in self.closed_trades],
            'open_trades': [asdict(trade) for trade in self.open_trades.values()],
            'daily_values': [(dt.isoformat(), val) for dt, val in self.daily_values]
        }
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"✅ Results saved to {filename}")
    
    def _calculate_position_size(self, entry_price: float, stop_loss: float, confidence: float) -> int:
        """Calculate position size using Kelly criterion with risk management"""
        # Risk per share
        risk_per_share = abs(entry_price - stop_loss)
        if risk_per_share <= 0:
            return 0
        
        # Maximum risk amount (2% of portfolio)
        max_risk_amount = self.portfolio.total_value * 0.02
        
        # Kelly criterion adjustment
        # Assume 60% base win rate, adjusted by confidence
        win_rate = 0.6 * confidence
        avg_win_rate = 0.03  # 3% average win
        avg_loss_rate = risk_per_share / entry_price
        
        # Kelly fraction
        kelly_fraction = (win_rate * avg_win_rate - (1 - win_rate) * avg_loss_rate) / avg_win_rate
        kelly_fraction = max(0, min(kelly_fraction * 0.25, 0.05))  # Conservative: 25% of Kelly, max 5%
        
        # Position size based on Kelly
        kelly_amount = self.portfolio.total_value * kelly_fraction
        kelly_position_size = int(kelly_amount / entry_price)
        
        # Position size based on risk
        risk_position_size = int(max_risk_amount / risk_per_share)
        
        # Take the smaller of the two
        position_size = min(kelly_position_size, risk_position_size)
        
        # Ensure minimum position size
        position_size = max(position_size, 1)
        
        # Check maximum position size (5% of portfolio)
        max_position_amount = self.portfolio.total_value * self.max_position_size
        max_position_size = int(max_position_amount / entry_price)
        position_size = min(position_size, max_position_size)
        
        return position_size
    
    def _check_risk_limits(self, required_capital: float) -> bool:
        """Check if trade violates risk management limits"""
        # Check total exposure
        current_exposure = sum(trade.position_size * trade.entry_price for trade in self.open_trades.values())
        total_exposure = (current_exposure + required_capital) / self.portfolio.total_value
        
        if total_exposure > self.max_total_exposure:
            return False
        
        # Check daily loss limit
        daily_loss = min(0, self.portfolio.total_pnl) / self.portfolio.initial_capital
        if abs(daily_loss) > self.max_daily_loss:
            return False
        
        return True
    
    def _update_portfolio_value(self):
        """Update portfolio value with current market prices"""
        if not self.open_trades:
            self.portfolio.unrealized_pnl = 0
            self.portfolio.total_value = self.portfolio.current_cash
            self.portfolio.total_pnl = self.portfolio.realized_pnl
            return
        
        symbols = list(set([trade.symbol for trade in self.open_trades.values()]))
        current_prices = {}
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
                if current_price > 0:
                    current_prices[symbol] = current_price
            except:
                pass
        
        # Calculate unrealized P&L
        unrealized_pnl = 0
        position_value = 0
        
        for trade in self.open_trades.values():
            if trade.symbol in current_prices:
                current_price = current_prices[trade.symbol]
                trade_pnl = (current_price - trade.entry_price) * trade.position_size
                unrealized_pnl += trade_pnl
                position_value += current_price * trade.position_size
            else:
                position_value += trade.entry_price * trade.position_size
        
        self.portfolio.unrealized_pnl = unrealized_pnl
        self.portfolio.total_value = self.portfolio.current_cash + position_value
        self.portfolio.total_pnl = self.portfolio.realized_pnl + unrealized_pnl
        
        # Track drawdown
        if self.portfolio.total_value > self.peak_portfolio_value:
            self.peak_portfolio_value = self.portfolio.total_value
        
        current_drawdown = (self.peak_portfolio_value - self.portfolio.total_value) / self.peak_portfolio_value * 100
        self.portfolio.max_drawdown = max(self.portfolio.max_drawdown, current_drawdown)
    
    def _update_portfolio_stats(self):
        """Update portfolio statistics"""
        if not self.closed_trades:
            return
        
        self.portfolio.total_trades = len(self.closed_trades)
        
        # Calculate win/loss stats
        wins = [trade for trade in self.closed_trades if trade.pnl > 0]
        losses = [trade for trade in self.closed_trades if trade.pnl <= 0]
        
        self.portfolio.winning_trades = len(wins)
        self.portfolio.losing_trades = len(losses)
        self.portfolio.win_rate = (len(wins) / len(self.closed_trades)) * 100 if self.closed_trades else 0
        
        self.portfolio.avg_win = np.mean([trade.pnl for trade in wins]) if wins else 0
        self.portfolio.avg_loss = np.mean([trade.pnl for trade in losses]) if losses else 0
        
        # Calculate Sharpe ratio (simplified)
        if len(self.closed_trades) > 5:
            returns = [trade.pnl_percent for trade in self.closed_trades]
            if np.std(returns) > 0:
                self.portfolio.sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)


def main():
    """Test the paper trading engine"""
    engine = PaperTradingEngine(initial_capital=10000)
    
    print("📈 Paper Trading Engine Test")
    print("=" * 40)
    
    # Simulate some trades
    test_trades = [
        ('TQQQ', 'gap_fill', 45.0, 47.0, 43.5, 0.8),
        ('GME', 'oversold_bounce', 25.0, 28.0, 23.0, 0.7),
        ('TSLA', 'breakout_pullback', 250.0, 265.0, 240.0, 0.6)
    ]
    
    for symbol, strategy, entry, target, stop, confidence in test_trades:
        trade_id = engine.place_order(symbol, strategy, entry, target, stop, confidence)
        if trade_id:
            print(f"✅ Placed paper trade: {trade_id}")
    
    # Update positions
    print("\n📊 Updating positions...")
    engine.update_positions()
    
    # Show summary
    print(engine.get_daily_summary())
    
    # Save results
    engine.save_results("test_paper_trading.json")


if __name__ == "__main__":
    main()