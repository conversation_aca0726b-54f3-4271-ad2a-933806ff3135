"""
Record Keeper System

Maintains comprehensive records for regulatory compliance,
audit trails, and long-term data retention requirements.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json
import sqlite3
import pandas as pd
from pathlib import Path
import hashlib
import gzip
import shutil

from ...core.logger import get_logger
from ...data.models import Trade, Position, Order

logger = get_logger(__name__)

class RecordType(Enum):
    """Types of records maintained"""
    TRADE_RECORD = "trade_record"
    ORDER_RECORD = "order_record"
    POSITION_RECORD = "position_record"
    RISK_REPORT = "risk_report"
    COMPLIANCE_REPORT = "compliance_report"
    SYSTEM_LOG = "system_log"
    MARKET_DATA = "market_data"
    CONFIGURATION = "configuration"
    AUDIT_TRAIL = "audit_trail"

class RetentionPolicy(Enum):
    """Data retention policies"""
    SEVEN_YEARS = "seven_years"      # 2555 days - regulatory requirement
    FIVE_YEARS = "five_years"        # 1825 days
    THREE_YEARS = "three_years"      # 1095 days
    ONE_YEAR = "one_year"            # 365 days
    SIX_MONTHS = "six_months"        # 180 days
    PERMANENT = "permanent"          # Never delete

@dataclass
class RecordMetadata:
    """Metadata for a record"""
    record_id: str
    record_type: RecordType
    created_date: datetime
    retention_policy: RetentionPolicy
    checksum: str
    file_path: str
    compressed: bool
    archived: bool
    size_bytes: int
    tags: List[str]

@dataclass
class RetentionRule:
    """Rule for data retention"""
    record_type: RecordType
    retention_period: timedelta
    archive_after: timedelta
    compress_after: timedelta
    delete_after: Optional[timedelta]

class RecordKeeper:
    """Maintains long-term records for compliance and audit purposes"""
    
    def __init__(self, storage_path: str = "records"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        # Create subdirectories
        (self.storage_path / "active").mkdir(exist_ok=True)
        (self.storage_path / "archived").mkdir(exist_ok=True)
        (self.storage_path / "compressed").mkdir(exist_ok=True)
        
        # Initialize database for metadata
        self.db_path = self.storage_path / "records_metadata.db"
        self._initialize_database()
        
        # Retention rules
        self.retention_rules = self._initialize_retention_rules()
        
        # Record statistics
        self.stats = {
            'total_records': 0,
            'total_size_bytes': 0,
            'records_by_type': {},
            'archived_records': 0,
            'compressed_records': 0
        }
        
    def _initialize_database(self):
        """Initialize SQLite database for record metadata"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS record_metadata (
                record_id TEXT PRIMARY KEY,
                record_type TEXT NOT NULL,
                created_date TEXT NOT NULL,
                retention_policy TEXT NOT NULL,
                checksum TEXT NOT NULL,
                file_path TEXT NOT NULL,
                compressed INTEGER DEFAULT 0,
                archived INTEGER DEFAULT 0,
                size_bytes INTEGER DEFAULT 0,
                tags TEXT DEFAULT '[]'
            )
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_record_type ON record_metadata(record_type)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_created_date ON record_metadata(created_date)
        ''')
        
        conn.commit()
        conn.close()
        
    def _initialize_retention_rules(self) -> Dict[RecordType, RetentionRule]:
        """Initialize data retention rules"""
        
        return {
            RecordType.TRADE_RECORD: RetentionRule(
                record_type=RecordType.TRADE_RECORD,
                retention_period=timedelta(days=2555),  # 7 years
                archive_after=timedelta(days=365),      # 1 year
                compress_after=timedelta(days=90),      # 3 months
                delete_after=None                       # Never delete
            ),
            RecordType.ORDER_RECORD: RetentionRule(
                record_type=RecordType.ORDER_RECORD,
                retention_period=timedelta(days=2555),  # 7 years
                archive_after=timedelta(days=365),
                compress_after=timedelta(days=90),
                delete_after=None
            ),
            RecordType.POSITION_RECORD: RetentionRule(
                record_type=RecordType.POSITION_RECORD,
                retention_period=timedelta(days=1825),  # 5 years
                archive_after=timedelta(days=365),
                compress_after=timedelta(days=90),
                delete_after=None
            ),
            RecordType.RISK_REPORT: RetentionRule(
                record_type=RecordType.RISK_REPORT,
                retention_period=timedelta(days=2555),  # 7 years
                archive_after=timedelta(days=365),
                compress_after=timedelta(days=30),
                delete_after=None
            ),
            RecordType.COMPLIANCE_REPORT: RetentionRule(
                record_type=RecordType.COMPLIANCE_REPORT,
                retention_period=timedelta(days=2555),  # 7 years
                archive_after=timedelta(days=365),
                compress_after=timedelta(days=30),
                delete_after=None
            ),
            RecordType.SYSTEM_LOG: RetentionRule(
                record_type=RecordType.SYSTEM_LOG,
                retention_period=timedelta(days=1095),  # 3 years
                archive_after=timedelta(days=90),
                compress_after=timedelta(days=7),
                delete_after=timedelta(days=1095)
            ),
            RecordType.MARKET_DATA: RetentionRule(
                record_type=RecordType.MARKET_DATA,
                retention_period=timedelta(days=365),   # 1 year
                archive_after=timedelta(days=30),
                compress_after=timedelta(days=7),
                delete_after=timedelta(days=365)
            ),
            RecordType.CONFIGURATION: RetentionRule(
                record_type=RecordType.CONFIGURATION,
                retention_period=timedelta(days=2555),  # 7 years
                archive_after=timedelta(days=365),
                compress_after=timedelta(days=30),
                delete_after=None
            ),
            RecordType.AUDIT_TRAIL: RetentionRule(
                record_type=RecordType.AUDIT_TRAIL,
                retention_period=timedelta(days=2555),  # 7 years
                archive_after=timedelta(days=365),
                compress_after=timedelta(days=30),
                delete_after=None
            )
        }
    
    async def store_record(self, 
                          record_type: RecordType,
                          data: Any,
                          tags: List[str] = None) -> str:
        """Store a record with proper metadata"""
        
        if tags is None:
            tags = []
        
        # Generate record ID
        record_id = f"{record_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # Serialize data
        if isinstance(data, (dict, list)):
            record_data = json.dumps(data, default=str, indent=2)
        elif hasattr(data, '__dict__'):
            record_data = json.dumps(asdict(data), default=str, indent=2)
        else:
            record_data = str(data)
        
        # Calculate checksum
        checksum = hashlib.sha256(record_data.encode()).hexdigest()
        
        # Determine file path
        file_name = f"{record_id}.json"
        file_path = self.storage_path / "active" / file_name
        
        # Write record to file
        with open(file_path, 'w') as f:
            f.write(record_data)
        
        # Get file size
        size_bytes = file_path.stat().st_size
        
        # Store metadata
        metadata = RecordMetadata(
            record_id=record_id,
            record_type=record_type,
            created_date=datetime.now(),
            retention_policy=self._get_retention_policy(record_type),
            checksum=checksum,
            file_path=str(file_path),
            compressed=False,
            archived=False,
            size_bytes=size_bytes,
            tags=tags
        )
        
        await self._store_metadata(metadata)
        
        # Update statistics
        self._update_stats(record_type, size_bytes)
        
        logger.debug(f"Stored record {record_id} ({record_type.value})")
        
        return record_id
    
    async def retrieve_record(self, record_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a record by ID"""
        
        metadata = await self._get_metadata(record_id)
        
        if not metadata:
            return None
        
        try:
            file_path = Path(metadata['file_path'])
            
            # Handle compressed files
            if metadata['compressed']:
                with gzip.open(file_path, 'rt') as f:
                    content = f.read()
            else:
                with open(file_path, 'r') as f:
                    content = f.read()
            
            # Parse JSON content
            record_data = json.loads(content)
            
            return {
                'record_id': record_id,
                'metadata': metadata,
                'data': record_data
            }
            
        except Exception as e:
            logger.error(f"Error retrieving record {record_id}: {e}")
            return None
    
    async def search_records(self, 
                           record_type: Optional[RecordType] = None,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None,
                           tags: Optional[List[str]] = None,
                           limit: int = 100) -> List[Dict[str, Any]]:
        """Search records based on criteria"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM record_metadata WHERE 1=1"
        params = []
        
        if record_type:
            query += " AND record_type = ?"
            params.append(record_type.value)
        
        if start_date:
            query += " AND created_date >= ?"
            params.append(start_date.isoformat())
        
        if end_date:
            query += " AND created_date <= ?"
            params.append(end_date.isoformat())
        
        if tags:
            for tag in tags:
                query += " AND tags LIKE ?"
                params.append(f'%"{tag}"%')
        
        query += " ORDER BY created_date DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        conn.close()
        
        # Convert to dictionaries
        columns = ['record_id', 'record_type', 'created_date', 'retention_policy',
                  'checksum', 'file_path', 'compressed', 'archived', 'size_bytes', 'tags']
        
        records = []
        for row in results:
            record_dict = dict(zip(columns, row))
            record_dict['tags'] = json.loads(record_dict['tags'])
            records.append(record_dict)
        
        return records
    
    async def start_maintenance(self):
        """Start background maintenance tasks"""
        logger.info("Starting record keeper maintenance")
        
        tasks = [
            self._compression_worker(),
            self._archival_worker(),
            self._cleanup_worker(),
            self._integrity_checker(),
            self._statistics_updater()
        ]
        
        await asyncio.gather(*tasks)
    
    async def _compression_worker(self):
        """Compress old records to save space"""
        
        while True:
            try:
                # Find records eligible for compression
                for record_type, rule in self.retention_rules.items():
                    cutoff_date = datetime.now() - rule.compress_after
                    
                    records = await self.search_records(
                        record_type=record_type,
                        end_date=cutoff_date,
                        limit=1000
                    )
                    
                    uncompressed_records = [r for r in records if not r['compressed']]
                    
                    for record in uncompressed_records:
                        await self._compress_record(record['record_id'])
                
                await asyncio.sleep(3600)  # Run hourly
                
            except Exception as e:
                logger.error(f"Error in compression worker: {e}")
                await asyncio.sleep(300)
    
    async def _archival_worker(self):
        """Archive old records"""
        
        while True:
            try:
                # Find records eligible for archival
                for record_type, rule in self.retention_rules.items():
                    cutoff_date = datetime.now() - rule.archive_after
                    
                    records = await self.search_records(
                        record_type=record_type,
                        end_date=cutoff_date,
                        limit=1000
                    )
                    
                    unarchived_records = [r for r in records if not r['archived']]
                    
                    for record in unarchived_records:
                        await self._archive_record(record['record_id'])
                
                await asyncio.sleep(86400)  # Run daily
                
            except Exception as e:
                logger.error(f"Error in archival worker: {e}")
                await asyncio.sleep(3600)
    
    async def _cleanup_worker(self):
        """Clean up expired records"""
        
        while True:
            try:
                # Find records eligible for deletion
                for record_type, rule in self.retention_rules.items():
                    if rule.delete_after:
                        cutoff_date = datetime.now() - rule.delete_after
                        
                        records = await self.search_records(
                            record_type=record_type,
                            end_date=cutoff_date,
                            limit=1000
                        )
                        
                        for record in records:
                            await self._delete_record(record['record_id'])
                
                await asyncio.sleep(86400)  # Run daily
                
            except Exception as e:
                logger.error(f"Error in cleanup worker: {e}")
                await asyncio.sleep(3600)
    
    async def _integrity_checker(self):
        """Check record integrity"""
        
        while True:
            try:
                # Check a sample of records for integrity
                recent_records = await self.search_records(
                    start_date=datetime.now() - timedelta(days=7),
                    limit=100
                )
                
                integrity_violations = []
                
                for record_meta in recent_records:
                    if not await self._verify_record_integrity(record_meta['record_id']):
                        integrity_violations.append(record_meta['record_id'])
                
                if integrity_violations:
                    logger.error(f"Record integrity violations: {integrity_violations}")
                
                await asyncio.sleep(86400)  # Check daily
                
            except Exception as e:
                logger.error(f"Error in integrity checker: {e}")
                await asyncio.sleep(3600)
    
    async def _statistics_updater(self):
        """Update record statistics"""
        
        while True:
            try:
                await self._calculate_statistics()
                await asyncio.sleep(3600)  # Update hourly
                
            except Exception as e:
                logger.error(f"Error updating statistics: {e}")
                await asyncio.sleep(300)
    
    async def _compress_record(self, record_id: str):
        """Compress a record to save space"""
        
        metadata = await self._get_metadata(record_id)
        if not metadata or metadata['compressed']:
            return
        
        try:
            file_path = Path(metadata['file_path'])
            compressed_path = self.storage_path / "compressed" / f"{record_id}.json.gz"
            
            # Compress the file
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Update metadata
            await self._update_metadata(record_id, {
                'compressed': True,
                'file_path': str(compressed_path),
                'size_bytes': compressed_path.stat().st_size
            })
            
            # Remove original file
            file_path.unlink()
            
            logger.debug(f"Compressed record {record_id}")
            
        except Exception as e:
            logger.error(f"Error compressing record {record_id}: {e}")
    
    async def _archive_record(self, record_id: str):
        """Archive a record"""
        
        metadata = await self._get_metadata(record_id)
        if not metadata or metadata['archived']:
            return
        
        try:
            current_path = Path(metadata['file_path'])
            
            # Determine archive path
            if metadata['compressed']:
                archive_path = self.storage_path / "archived" / f"{record_id}.json.gz"
            else:
                archive_path = self.storage_path / "archived" / f"{record_id}.json"
            
            # Move file to archive
            shutil.move(str(current_path), str(archive_path))
            
            # Update metadata
            await self._update_metadata(record_id, {
                'archived': True,
                'file_path': str(archive_path)
            })
            
            logger.debug(f"Archived record {record_id}")
            
        except Exception as e:
            logger.error(f"Error archiving record {record_id}: {e}")
    
    async def _delete_record(self, record_id: str):
        """Delete an expired record"""
        
        metadata = await self._get_metadata(record_id)
        if not metadata:
            return
        
        try:
            # Remove file
            file_path = Path(metadata['file_path'])
            if file_path.exists():
                file_path.unlink()
            
            # Remove metadata
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM record_metadata WHERE record_id = ?", (record_id,))
            conn.commit()
            conn.close()
            
            logger.debug(f"Deleted record {record_id}")
            
        except Exception as e:
            logger.error(f"Error deleting record {record_id}: {e}")
    
    async def _verify_record_integrity(self, record_id: str) -> bool:
        """Verify record integrity using checksum"""
        
        try:
            record = await self.retrieve_record(record_id)
            if not record:
                return False
            
            # Recalculate checksum
            record_data = json.dumps(record['data'], default=str, sort_keys=True)
            calculated_checksum = hashlib.sha256(record_data.encode()).hexdigest()
            
            return calculated_checksum == record['metadata']['checksum']
            
        except Exception as e:
            logger.error(f"Error verifying integrity for record {record_id}: {e}")
            return False
    
    async def _store_metadata(self, metadata: RecordMetadata):
        """Store record metadata in database"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO record_metadata 
            (record_id, record_type, created_date, retention_policy, checksum, 
             file_path, compressed, archived, size_bytes, tags)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            metadata.record_id,
            metadata.record_type.value,
            metadata.created_date.isoformat(),
            metadata.retention_policy.value,
            metadata.checksum,
            metadata.file_path,
            metadata.compressed,
            metadata.archived,
            metadata.size_bytes,
            json.dumps(metadata.tags)
        ))
        
        conn.commit()
        conn.close()
    
    async def _get_metadata(self, record_id: str) -> Optional[Dict[str, Any]]:
        """Get record metadata"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM record_metadata WHERE record_id = ?", (record_id,))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            columns = ['record_id', 'record_type', 'created_date', 'retention_policy',
                      'checksum', 'file_path', 'compressed', 'archived', 'size_bytes', 'tags']
            metadata = dict(zip(columns, result))
            metadata['tags'] = json.loads(metadata['tags'])
            return metadata
        
        return None
    
    async def _update_metadata(self, record_id: str, updates: Dict[str, Any]):
        """Update record metadata"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        set_clause = ", ".join([f"{key} = ?" for key in updates.keys()])
        values = list(updates.values()) + [record_id]
        
        cursor.execute(f"UPDATE record_metadata SET {set_clause} WHERE record_id = ?", values)
        conn.commit()
        conn.close()
    
    def _get_retention_policy(self, record_type: RecordType) -> RetentionPolicy:
        """Get retention policy for record type"""
        
        rule = self.retention_rules.get(record_type)
        if not rule:
            return RetentionPolicy.ONE_YEAR
        
        days = rule.retention_period.days
        
        if days >= 2555:
            return RetentionPolicy.SEVEN_YEARS
        elif days >= 1825:
            return RetentionPolicy.FIVE_YEARS
        elif days >= 1095:
            return RetentionPolicy.THREE_YEARS
        elif days >= 365:
            return RetentionPolicy.ONE_YEAR
        else:
            return RetentionPolicy.SIX_MONTHS
    
    def _update_stats(self, record_type: RecordType, size_bytes: int):
        """Update record statistics"""
        
        self.stats['total_records'] += 1
        self.stats['total_size_bytes'] += size_bytes
        
        type_key = record_type.value
        if type_key not in self.stats['records_by_type']:
            self.stats['records_by_type'][type_key] = 0
        self.stats['records_by_type'][type_key] += 1
    
    async def _calculate_statistics(self):
        """Calculate comprehensive statistics"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Total records
        cursor.execute("SELECT COUNT(*) FROM record_metadata")
        total_records = cursor.fetchone()[0]
        
        # Total size
        cursor.execute("SELECT SUM(size_bytes) FROM record_metadata")
        total_size = cursor.fetchone()[0] or 0
        
        # Records by type
        cursor.execute("SELECT record_type, COUNT(*) FROM record_metadata GROUP BY record_type")
        records_by_type = dict(cursor.fetchall())
        
        # Archived and compressed counts
        cursor.execute("SELECT COUNT(*) FROM record_metadata WHERE archived = 1")
        archived_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM record_metadata WHERE compressed = 1")
        compressed_count = cursor.fetchone()[0]
        
        conn.close()
        
        self.stats = {
            'total_records': total_records,
            'total_size_bytes': total_size,
            'records_by_type': records_by_type,
            'archived_records': archived_count,
            'compressed_records': compressed_count
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get current record keeping statistics"""
        
        return {
            'total_records': self.stats['total_records'],
            'total_size_mb': round(self.stats['total_size_bytes'] / (1024 * 1024), 2),
            'records_by_type': self.stats['records_by_type'],
            'archived_records': self.stats['archived_records'],
            'compressed_records': self.stats['compressed_records'],
            'retention_rules': {
                record_type.value: {
                    'retention_days': rule.retention_period.days,
                    'archive_after_days': rule.archive_after.days,
                    'compress_after_days': rule.compress_after.days
                }
                for record_type, rule in self.retention_rules.items()
            }
        }
