"""Options pricing models and Greeks calculation."""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from scipy.stats import norm
from scipy.optimize import brentq
import math

from ...core.config import settings
from ...core.logger import get_logger

logger = get_logger(__name__)


class OptionType(Enum):
    """Option type enumeration."""
    CALL = "call"
    PUT = "put"


@dataclass
class OptionContract:
    """Option contract data structure."""
    symbol: str
    underlying: str
    option_type: OptionType
    strike: float
    expiration: datetime
    price: float
    bid: float
    ask: float
    volume: int
    open_interest: int
    implied_volatility: Optional[float] = None


@dataclass
class Greeks:
    """Option Greeks data structure."""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float


@dataclass
class OptionPricing:
    """Option pricing results."""
    theoretical_price: float
    implied_volatility: float
    greeks: Greeks
    intrinsic_value: float
    time_value: float
    moneyness: float


class OptionsPricer:
    """Options pricing engine with Black-Scholes and advanced models."""
    
    def __init__(self):
        self.risk_free_rate = 0.05  # Default 5% risk-free rate
        
    def black_scholes_price(
        self,
        spot_price: float,
        strike_price: float,
        time_to_expiry: float,
        volatility: float,
        option_type: OptionType,
        risk_free_rate: float = None,
        dividend_yield: float = 0.0
    ) -> float:
        """
        Calculate option price using Black-Scholes model.
        
        Args:
            spot_price: Current price of underlying
            strike_price: Strike price of option
            time_to_expiry: Time to expiry in years
            volatility: Implied volatility (annualized)
            option_type: Call or Put
            risk_free_rate: Risk-free interest rate
            dividend_yield: Dividend yield of underlying
            
        Returns:
            Option price
        """
        try:
            if risk_free_rate is None:
                risk_free_rate = self.risk_free_rate
            
            if time_to_expiry <= 0:
                # Option has expired, return intrinsic value
                if option_type == OptionType.CALL:
                    return max(0, spot_price - strike_price)
                else:
                    return max(0, strike_price - spot_price)
            
            # Adjust spot price for dividends
            adjusted_spot = spot_price * np.exp(-dividend_yield * time_to_expiry)
            
            # Calculate d1 and d2
            d1 = (np.log(adjusted_spot / strike_price) + 
                  (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (
                  volatility * np.sqrt(time_to_expiry))
            
            d2 = d1 - volatility * np.sqrt(time_to_expiry)
            
            if option_type == OptionType.CALL:
                price = (adjusted_spot * norm.cdf(d1) - 
                        strike_price * np.exp(-risk_free_rate * time_to_expiry) * norm.cdf(d2))
            else:
                price = (strike_price * np.exp(-risk_free_rate * time_to_expiry) * norm.cdf(-d2) - 
                        adjusted_spot * norm.cdf(-d1))
            
            return max(0, price)
            
        except Exception as e:
            logger.error(f"Error calculating Black-Scholes price: {e}")
            return 0.0
    
    def calculate_greeks(
        self,
        spot_price: float,
        strike_price: float,
        time_to_expiry: float,
        volatility: float,
        option_type: OptionType,
        risk_free_rate: float = None,
        dividend_yield: float = 0.0
    ) -> Greeks:
        """
        Calculate option Greeks.
        
        Args:
            spot_price: Current price of underlying
            strike_price: Strike price of option
            time_to_expiry: Time to expiry in years
            volatility: Implied volatility
            option_type: Call or Put
            risk_free_rate: Risk-free interest rate
            dividend_yield: Dividend yield
            
        Returns:
            Greeks object
        """
        try:
            if risk_free_rate is None:
                risk_free_rate = self.risk_free_rate
            
            if time_to_expiry <= 0:
                return Greeks(0, 0, 0, 0, 0)
            
            # Adjust spot price for dividends
            adjusted_spot = spot_price * np.exp(-dividend_yield * time_to_expiry)
            
            # Calculate d1 and d2
            d1 = (np.log(adjusted_spot / strike_price) + 
                  (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (
                  volatility * np.sqrt(time_to_expiry))
            
            d2 = d1 - volatility * np.sqrt(time_to_expiry)
            
            # Delta
            if option_type == OptionType.CALL:
                delta = np.exp(-dividend_yield * time_to_expiry) * norm.cdf(d1)
            else:
                delta = -np.exp(-dividend_yield * time_to_expiry) * norm.cdf(-d1)
            
            # Gamma (same for calls and puts)
            gamma = (np.exp(-dividend_yield * time_to_expiry) * norm.pdf(d1)) / (
                adjusted_spot * volatility * np.sqrt(time_to_expiry))
            
            # Theta
            theta_part1 = -(adjusted_spot * norm.pdf(d1) * volatility * 
                           np.exp(-dividend_yield * time_to_expiry)) / (2 * np.sqrt(time_to_expiry))
            
            if option_type == OptionType.CALL:
                theta_part2 = (risk_free_rate * strike_price * 
                              np.exp(-risk_free_rate * time_to_expiry) * norm.cdf(d2))
                theta_part3 = (dividend_yield * adjusted_spot * 
                              np.exp(-dividend_yield * time_to_expiry) * norm.cdf(d1))
                theta = (theta_part1 - theta_part2 + theta_part3) / 365
            else:
                theta_part2 = (risk_free_rate * strike_price * 
                              np.exp(-risk_free_rate * time_to_expiry) * norm.cdf(-d2))
                theta_part3 = (dividend_yield * adjusted_spot * 
                              np.exp(-dividend_yield * time_to_expiry) * norm.cdf(-d1))
                theta = (theta_part1 + theta_part2 - theta_part3) / 365
            
            # Vega (same for calls and puts)
            vega = (adjusted_spot * np.sqrt(time_to_expiry) * norm.pdf(d1) * 
                   np.exp(-dividend_yield * time_to_expiry)) / 100
            
            # Rho
            if option_type == OptionType.CALL:
                rho = (strike_price * time_to_expiry * 
                      np.exp(-risk_free_rate * time_to_expiry) * norm.cdf(d2)) / 100
            else:
                rho = -(strike_price * time_to_expiry * 
                       np.exp(-risk_free_rate * time_to_expiry) * norm.cdf(-d2)) / 100
            
            return Greeks(
                delta=delta,
                gamma=gamma,
                theta=theta,
                vega=vega,
                rho=rho
            )
            
        except Exception as e:
            logger.error(f"Error calculating Greeks: {e}")
            return Greeks(0, 0, 0, 0, 0)
    
    def calculate_implied_volatility(
        self,
        market_price: float,
        spot_price: float,
        strike_price: float,
        time_to_expiry: float,
        option_type: OptionType,
        risk_free_rate: float = None,
        dividend_yield: float = 0.0
    ) -> float:
        """
        Calculate implied volatility using Newton-Raphson method.
        
        Args:
            market_price: Market price of option
            spot_price: Current price of underlying
            strike_price: Strike price of option
            time_to_expiry: Time to expiry in years
            option_type: Call or Put
            risk_free_rate: Risk-free interest rate
            dividend_yield: Dividend yield
            
        Returns:
            Implied volatility
        """
        try:
            if risk_free_rate is None:
                risk_free_rate = self.risk_free_rate
            
            if time_to_expiry <= 0:
                return 0.0
            
            # Check if option has intrinsic value
            if option_type == OptionType.CALL:
                intrinsic = max(0, spot_price - strike_price)
            else:
                intrinsic = max(0, strike_price - spot_price)
            
            if market_price <= intrinsic:
                return 0.0
            
            def objective_function(vol):
                try:
                    theoretical_price = self.black_scholes_price(
                        spot_price, strike_price, time_to_expiry, vol,
                        option_type, risk_free_rate, dividend_yield
                    )
                    return theoretical_price - market_price
                except:
                    return float('inf')
            
            # Use Brent's method to find implied volatility
            try:
                implied_vol = brentq(objective_function, 0.001, 5.0, xtol=1e-6)
                return max(0.001, min(5.0, implied_vol))
            except ValueError:
                # If Brent's method fails, use Newton-Raphson
                return self._newton_raphson_iv(
                    market_price, spot_price, strike_price, time_to_expiry,
                    option_type, risk_free_rate, dividend_yield
                )
            
        except Exception as e:
            logger.error(f"Error calculating implied volatility: {e}")
            return 0.2  # Default 20% volatility
    
    def _newton_raphson_iv(
        self,
        market_price: float,
        spot_price: float,
        strike_price: float,
        time_to_expiry: float,
        option_type: OptionType,
        risk_free_rate: float,
        dividend_yield: float,
        max_iterations: int = 100,
        tolerance: float = 1e-6
    ) -> float:
        """Newton-Raphson method for implied volatility."""
        try:
            # Initial guess
            vol = 0.2
            
            for i in range(max_iterations):
                # Calculate price and vega
                price = self.black_scholes_price(
                    spot_price, strike_price, time_to_expiry, vol,
                    option_type, risk_free_rate, dividend_yield
                )
                
                greeks = self.calculate_greeks(
                    spot_price, strike_price, time_to_expiry, vol,
                    option_type, risk_free_rate, dividend_yield
                )
                
                vega = greeks.vega * 100  # Convert to price change per 1% vol change
                
                if abs(vega) < 1e-10:
                    break
                
                # Newton-Raphson update
                price_diff = price - market_price
                vol_new = vol - price_diff / vega
                
                # Ensure volatility stays positive
                vol_new = max(0.001, min(5.0, vol_new))
                
                if abs(vol_new - vol) < tolerance:
                    return vol_new
                
                vol = vol_new
            
            return vol
            
        except Exception as e:
            logger.error(f"Error in Newton-Raphson IV calculation: {e}")
            return 0.2
    
    def binomial_tree_price(
        self,
        spot_price: float,
        strike_price: float,
        time_to_expiry: float,
        volatility: float,
        option_type: OptionType,
        steps: int = 100,
        american: bool = False,
        risk_free_rate: float = None,
        dividend_yield: float = 0.0
    ) -> float:
        """
        Calculate option price using binomial tree model.
        
        Args:
            spot_price: Current price of underlying
            strike_price: Strike price of option
            time_to_expiry: Time to expiry in years
            volatility: Volatility
            option_type: Call or Put
            steps: Number of time steps
            american: True for American option, False for European
            risk_free_rate: Risk-free rate
            dividend_yield: Dividend yield
            
        Returns:
            Option price
        """
        try:
            if risk_free_rate is None:
                risk_free_rate = self.risk_free_rate
            
            if time_to_expiry <= 0:
                if option_type == OptionType.CALL:
                    return max(0, spot_price - strike_price)
                else:
                    return max(0, strike_price - spot_price)
            
            # Calculate parameters
            dt = time_to_expiry / steps
            u = np.exp(volatility * np.sqrt(dt))
            d = 1 / u
            p = (np.exp((risk_free_rate - dividend_yield) * dt) - d) / (u - d)
            discount = np.exp(-risk_free_rate * dt)
            
            # Initialize asset prices at maturity
            asset_prices = np.zeros(steps + 1)
            for i in range(steps + 1):
                asset_prices[i] = spot_price * (u ** (steps - i)) * (d ** i)
            
            # Initialize option values at maturity
            option_values = np.zeros(steps + 1)
            for i in range(steps + 1):
                if option_type == OptionType.CALL:
                    option_values[i] = max(0, asset_prices[i] - strike_price)
                else:
                    option_values[i] = max(0, strike_price - asset_prices[i])
            
            # Work backwards through the tree
            for j in range(steps - 1, -1, -1):
                for i in range(j + 1):
                    # Calculate option value
                    option_values[i] = discount * (p * option_values[i] + (1 - p) * option_values[i + 1])
                    
                    # For American options, check early exercise
                    if american:
                        asset_price = spot_price * (u ** (j - i)) * (d ** i)
                        if option_type == OptionType.CALL:
                            exercise_value = max(0, asset_price - strike_price)
                        else:
                            exercise_value = max(0, strike_price - asset_price)
                        
                        option_values[i] = max(option_values[i], exercise_value)
            
            return option_values[0]
            
        except Exception as e:
            logger.error(f"Error calculating binomial tree price: {e}")
            return 0.0
    
    def monte_carlo_price(
        self,
        spot_price: float,
        strike_price: float,
        time_to_expiry: float,
        volatility: float,
        option_type: OptionType,
        simulations: int = 100000,
        risk_free_rate: float = None,
        dividend_yield: float = 0.0
    ) -> float:
        """
        Calculate option price using Monte Carlo simulation.
        
        Args:
            spot_price: Current price of underlying
            strike_price: Strike price of option
            time_to_expiry: Time to expiry in years
            volatility: Volatility
            option_type: Call or Put
            simulations: Number of Monte Carlo simulations
            risk_free_rate: Risk-free rate
            dividend_yield: Dividend yield
            
        Returns:
            Option price
        """
        try:
            if risk_free_rate is None:
                risk_free_rate = self.risk_free_rate
            
            if time_to_expiry <= 0:
                if option_type == OptionType.CALL:
                    return max(0, spot_price - strike_price)
                else:
                    return max(0, strike_price - spot_price)
            
            # Generate random numbers
            np.random.seed(42)  # For reproducibility
            z = np.random.standard_normal(simulations)
            
            # Calculate final stock prices
            final_prices = spot_price * np.exp(
                (risk_free_rate - dividend_yield - 0.5 * volatility**2) * time_to_expiry +
                volatility * np.sqrt(time_to_expiry) * z
            )
            
            # Calculate payoffs
            if option_type == OptionType.CALL:
                payoffs = np.maximum(final_prices - strike_price, 0)
            else:
                payoffs = np.maximum(strike_price - final_prices, 0)
            
            # Discount to present value
            option_price = np.exp(-risk_free_rate * time_to_expiry) * np.mean(payoffs)
            
            return option_price
            
        except Exception as e:
            logger.error(f"Error calculating Monte Carlo price: {e}")
            return 0.0
    
    def analyze_option(
        self,
        contract: OptionContract,
        spot_price: float,
        risk_free_rate: float = None,
        dividend_yield: float = 0.0
    ) -> OptionPricing:
        """
        Comprehensive option analysis.
        
        Args:
            contract: Option contract
            spot_price: Current underlying price
            risk_free_rate: Risk-free rate
            dividend_yield: Dividend yield
            
        Returns:
            Complete option pricing analysis
        """
        try:
            if risk_free_rate is None:
                risk_free_rate = self.risk_free_rate
            
            # Calculate time to expiry
            time_to_expiry = (contract.expiration - datetime.now()).total_seconds() / (365.25 * 24 * 3600)
            time_to_expiry = max(0, time_to_expiry)
            
            # Calculate implied volatility if not provided
            if contract.implied_volatility is None:
                mid_price = (contract.bid + contract.ask) / 2
                implied_vol = self.calculate_implied_volatility(
                    mid_price, spot_price, contract.strike, time_to_expiry,
                    contract.option_type, risk_free_rate, dividend_yield
                )
            else:
                implied_vol = contract.implied_volatility
            
            # Calculate theoretical price
            theoretical_price = self.black_scholes_price(
                spot_price, contract.strike, time_to_expiry, implied_vol,
                contract.option_type, risk_free_rate, dividend_yield
            )
            
            # Calculate Greeks
            greeks = self.calculate_greeks(
                spot_price, contract.strike, time_to_expiry, implied_vol,
                contract.option_type, risk_free_rate, dividend_yield
            )
            
            # Calculate intrinsic and time value
            if contract.option_type == OptionType.CALL:
                intrinsic_value = max(0, spot_price - contract.strike)
            else:
                intrinsic_value = max(0, contract.strike - spot_price)
            
            time_value = theoretical_price - intrinsic_value
            
            # Calculate moneyness
            moneyness = spot_price / contract.strike
            
            return OptionPricing(
                theoretical_price=theoretical_price,
                implied_volatility=implied_vol,
                greeks=greeks,
                intrinsic_value=intrinsic_value,
                time_value=time_value,
                moneyness=moneyness
            )
            
        except Exception as e:
            logger.error(f"Error analyzing option: {e}")
            return self._empty_pricing()
    
    def _empty_pricing(self) -> OptionPricing:
        """Return empty option pricing."""
        return OptionPricing(
            theoretical_price=0.0,
            implied_volatility=0.0,
            greeks=Greeks(0, 0, 0, 0, 0),
            intrinsic_value=0.0,
            time_value=0.0,
            moneyness=1.0
        )
