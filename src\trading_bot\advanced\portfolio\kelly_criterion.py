"""Kelly Criterion optimization for position sizing and portfolio allocation."""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from scipy.optimize import minimize, minimize_scalar
from scipy.stats import norm
import warnings

from ...core.config import settings
from ...core.logger import get_logger

logger = get_logger(__name__)


@dataclass
class KellyResult:
    """Kelly criterion optimization result."""
    optimal_fraction: float
    expected_growth_rate: float
    probability_of_loss: float
    max_drawdown_estimate: float
    confidence_interval: Tuple[float, float]
    risk_of_ruin: float


@dataclass
class PortfolioKellyResult:
    """Portfolio Kelly optimization result."""
    optimal_weights: Dict[str, float]
    expected_portfolio_growth: float
    portfolio_volatility: float
    sharpe_ratio: float
    max_leverage: float
    individual_kelly_fractions: Dict[str, float]


@dataclass
class KellyConstraints:
    """Constraints for Kelly optimization."""
    max_fraction: float = 1.0
    min_fraction: float = 0.0
    max_leverage: float = 1.0
    risk_of_ruin_limit: float = 0.01
    confidence_level: float = 0.95


class KellyCriterionOptimizer:
    """Kelly Criterion optimization for optimal position sizing."""
    
    def __init__(self):
        self.default_constraints = KellyConstraints()
        
    def calculate_kelly_fraction(
        self,
        expected_return: float,
        win_probability: float,
        win_loss_ratio: float,
        constraints: KellyConstraints = None
    ) -> KellyResult:
        """
        Calculate Kelly fraction for binary outcome betting.
        
        Args:
            expected_return: Expected return per period
            win_probability: Probability of winning
            win_loss_ratio: Ratio of win amount to loss amount
            constraints: Kelly constraints
            
        Returns:
            Kelly optimization result
        """
        try:
            if constraints is None:
                constraints = self.default_constraints
            
            # Classic Kelly formula: f* = (bp - q) / b
            # where b = win_loss_ratio, p = win_probability, q = 1 - p
            b = win_loss_ratio
            p = win_probability
            q = 1 - p
            
            # Calculate optimal fraction
            if b > 0:
                kelly_fraction = (b * p - q) / b
            else:
                kelly_fraction = 0.0
            
            # Apply constraints
            kelly_fraction = max(constraints.min_fraction, 
                               min(kelly_fraction, constraints.max_fraction))
            
            # Calculate expected growth rate
            if kelly_fraction > 0:
                expected_growth = (p * np.log(1 + b * kelly_fraction) + 
                                 q * np.log(1 - kelly_fraction))
            else:
                expected_growth = 0.0
            
            # Calculate risk metrics
            prob_loss = q
            max_dd_estimate = self._estimate_max_drawdown(kelly_fraction, p, b)
            confidence_interval = self._calculate_confidence_interval(
                kelly_fraction, p, b, constraints.confidence_level
            )
            risk_of_ruin = self._calculate_risk_of_ruin(kelly_fraction, p, b)
            
            return KellyResult(
                optimal_fraction=kelly_fraction,
                expected_growth_rate=expected_growth,
                probability_of_loss=prob_loss,
                max_drawdown_estimate=max_dd_estimate,
                confidence_interval=confidence_interval,
                risk_of_ruin=risk_of_ruin
            )
            
        except Exception as e:
            logger.error(f"Error calculating Kelly fraction: {e}")
            return self._empty_kelly_result()
    
    def calculate_continuous_kelly(
        self,
        returns_data: pd.Series,
        constraints: KellyConstraints = None
    ) -> KellyResult:
        """
        Calculate Kelly fraction for continuous returns distribution.
        
        Args:
            returns_data: Historical returns data
            constraints: Kelly constraints
            
        Returns:
            Kelly optimization result
        """
        try:
            if constraints is None:
                constraints = self.default_constraints
            
            if len(returns_data) < 30:
                logger.warning("Insufficient data for Kelly calculation")
                return self._empty_kelly_result()
            
            # Calculate statistics
            mean_return = returns_data.mean()
            variance = returns_data.var()
            
            if variance <= 0:
                return self._empty_kelly_result()
            
            # Kelly fraction for normal distribution: f* = μ / σ²
            kelly_fraction = mean_return / variance
            
            # Apply constraints
            kelly_fraction = max(constraints.min_fraction, 
                               min(kelly_fraction, constraints.max_fraction))
            
            # Calculate expected growth rate (continuous)
            expected_growth = kelly_fraction * mean_return - 0.5 * (kelly_fraction ** 2) * variance
            
            # Calculate risk metrics
            prob_loss = norm.cdf(0, mean_return, np.sqrt(variance))
            max_dd_estimate = self._estimate_continuous_max_drawdown(
                kelly_fraction, mean_return, variance
            )
            
            confidence_interval = self._calculate_continuous_confidence_interval(
                kelly_fraction, mean_return, variance, constraints.confidence_level
            )
            
            risk_of_ruin = self._calculate_continuous_risk_of_ruin(
                kelly_fraction, mean_return, variance
            )
            
            return KellyResult(
                optimal_fraction=kelly_fraction,
                expected_growth_rate=expected_growth,
                probability_of_loss=prob_loss,
                max_drawdown_estimate=max_dd_estimate,
                confidence_interval=confidence_interval,
                risk_of_ruin=risk_of_ruin
            )
            
        except Exception as e:
            logger.error(f"Error calculating continuous Kelly: {e}")
            return self._empty_kelly_result()
    
    def optimize_portfolio_kelly(
        self,
        returns_data: pd.DataFrame,
        correlation_matrix: pd.DataFrame = None,
        constraints: KellyConstraints = None
    ) -> PortfolioKellyResult:
        """
        Optimize portfolio using Kelly criterion for multiple assets.
        
        Args:
            returns_data: Returns data for multiple assets
            correlation_matrix: Asset correlation matrix
            constraints: Kelly constraints
            
        Returns:
            Portfolio Kelly optimization result
        """
        try:
            if constraints is None:
                constraints = self.default_constraints
            
            if len(returns_data) < 30:
                logger.warning("Insufficient data for portfolio Kelly optimization")
                return self._empty_portfolio_result()
            
            # Calculate expected returns and covariance matrix
            expected_returns = returns_data.mean().values
            
            if correlation_matrix is not None:
                # Use provided correlation matrix
                volatilities = returns_data.std().values
                cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix.values
            else:
                cov_matrix = returns_data.cov().values
            
            # Kelly optimization for portfolio
            n_assets = len(expected_returns)
            
            def kelly_objective(weights):
                """Objective function for portfolio Kelly optimization."""
                weights = np.array(weights)
                
                # Portfolio expected return and variance
                port_return = np.dot(weights, expected_returns)
                port_variance = np.dot(weights, np.dot(cov_matrix, weights))
                
                if port_variance <= 0:
                    return -np.inf
                
                # Kelly growth rate
                growth_rate = port_return - 0.5 * port_variance
                return -growth_rate  # Minimize negative growth rate
            
            # Constraints
            cons = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # Weights sum to 1
                {'type': 'ineq', 'fun': lambda x: constraints.max_leverage - np.sum(np.abs(x))}
            ]
            
            # Bounds
            bounds = [(constraints.min_fraction, constraints.max_fraction) 
                     for _ in range(n_assets)]
            
            # Initial guess (equal weights)
            x0 = np.ones(n_assets) / n_assets
            
            # Optimize
            result = minimize(
                kelly_objective,
                x0,
                method='SLSQP',
                bounds=bounds,
                constraints=cons
            )
            
            if result.success:
                optimal_weights = result.x
            else:
                # Fallback to individual Kelly fractions
                optimal_weights = self._calculate_individual_kelly_weights(
                    returns_data, constraints
                )
            
            # Calculate portfolio metrics
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_variance = np.dot(optimal_weights, np.dot(cov_matrix, optimal_weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            expected_growth = portfolio_return - 0.5 * portfolio_variance
            sharpe_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0
            
            # Calculate individual Kelly fractions
            individual_kelly = {}
            for i, asset in enumerate(returns_data.columns):
                asset_returns = returns_data[asset]
                kelly_result = self.calculate_continuous_kelly(asset_returns, constraints)
                individual_kelly[asset] = kelly_result.optimal_fraction
            
            # Convert weights to dictionary
            weights_dict = {asset: weight for asset, weight in 
                          zip(returns_data.columns, optimal_weights)}
            
            return PortfolioKellyResult(
                optimal_weights=weights_dict,
                expected_portfolio_growth=expected_growth,
                portfolio_volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                max_leverage=np.sum(np.abs(optimal_weights)),
                individual_kelly_fractions=individual_kelly
            )
            
        except Exception as e:
            logger.error(f"Error optimizing portfolio Kelly: {e}")
            return self._empty_portfolio_result()
    
    def calculate_fractional_kelly(
        self,
        kelly_fraction: float,
        fraction: float = 0.25
    ) -> float:
        """
        Calculate fractional Kelly (e.g., quarter Kelly).
        
        Args:
            kelly_fraction: Full Kelly fraction
            fraction: Fraction of Kelly to use (e.g., 0.25 for quarter Kelly)
            
        Returns:
            Fractional Kelly position size
        """
        return kelly_fraction * fraction
    
    def simulate_kelly_performance(
        self,
        kelly_fraction: float,
        returns_data: pd.Series,
        n_simulations: int = 1000,
        n_periods: int = 252
    ) -> Dict[str, Any]:
        """
        Simulate Kelly strategy performance.
        
        Args:
            kelly_fraction: Kelly fraction to use
            returns_data: Historical returns for simulation
            n_simulations: Number of Monte Carlo simulations
            n_periods: Number of periods to simulate
            
        Returns:
            Simulation results
        """
        try:
            # Bootstrap returns for simulation
            returns_sample = returns_data.dropna()
            
            if len(returns_sample) < 30:
                return {'error': 'Insufficient data for simulation'}
            
            final_values = []
            max_drawdowns = []
            
            for _ in range(n_simulations):
                # Sample returns with replacement
                simulated_returns = np.random.choice(returns_sample, n_periods)
                
                # Simulate portfolio value
                portfolio_value = 1.0
                peak_value = 1.0
                max_dd = 0.0
                
                for ret in simulated_returns:
                    # Kelly position sizing
                    position_return = kelly_fraction * ret
                    portfolio_value *= (1 + position_return)
                    
                    # Track drawdown
                    if portfolio_value > peak_value:
                        peak_value = portfolio_value
                    
                    current_dd = (peak_value - portfolio_value) / peak_value
                    max_dd = max(max_dd, current_dd)
                
                final_values.append(portfolio_value)
                max_drawdowns.append(max_dd)
            
            # Calculate statistics
            final_values = np.array(final_values)
            max_drawdowns = np.array(max_drawdowns)
            
            return {
                'mean_final_value': np.mean(final_values),
                'median_final_value': np.median(final_values),
                'std_final_value': np.std(final_values),
                'probability_of_loss': np.mean(final_values < 1.0),
                'mean_max_drawdown': np.mean(max_drawdowns),
                'worst_case_drawdown': np.max(max_drawdowns),
                'percentile_5': np.percentile(final_values, 5),
                'percentile_95': np.percentile(final_values, 95)
            }
            
        except Exception as e:
            logger.error(f"Error simulating Kelly performance: {e}")
            return {'error': str(e)}
    
    def _calculate_individual_kelly_weights(
        self,
        returns_data: pd.DataFrame,
        constraints: KellyConstraints
    ) -> np.ndarray:
        """Calculate weights based on individual Kelly fractions."""
        try:
            kelly_fractions = []
            
            for asset in returns_data.columns:
                asset_returns = returns_data[asset]
                kelly_result = self.calculate_continuous_kelly(asset_returns, constraints)
                kelly_fractions.append(max(0, kelly_result.optimal_fraction))
            
            kelly_fractions = np.array(kelly_fractions)
            
            # Normalize to sum to 1
            total_kelly = np.sum(kelly_fractions)
            if total_kelly > 0:
                weights = kelly_fractions / total_kelly
            else:
                weights = np.ones(len(kelly_fractions)) / len(kelly_fractions)
            
            return weights
            
        except Exception as e:
            logger.error(f"Error calculating individual Kelly weights: {e}")
            return np.ones(len(returns_data.columns)) / len(returns_data.columns)
    
    def _estimate_max_drawdown(
        self,
        kelly_fraction: float,
        win_prob: float,
        win_loss_ratio: float
    ) -> float:
        """Estimate maximum drawdown for binary Kelly."""
        try:
            # Simplified estimate based on Kelly fraction and probabilities
            if kelly_fraction <= 0:
                return 0.0
            
            # Worst case scenario estimate
            consecutive_losses = int(np.log(0.01) / np.log(1 - win_prob))  # 99% confidence
            max_dd = 1 - (1 - kelly_fraction) ** consecutive_losses
            
            return min(max_dd, 1.0)
            
        except Exception as e:
            logger.error(f"Error estimating max drawdown: {e}")
            return 0.0
    
    def _estimate_continuous_max_drawdown(
        self,
        kelly_fraction: float,
        mean_return: float,
        variance: float
    ) -> float:
        """Estimate maximum drawdown for continuous Kelly."""
        try:
            if kelly_fraction <= 0:
                return 0.0
            
            # Simplified estimate using normal distribution
            # Maximum drawdown approximation
            drift = kelly_fraction * mean_return - 0.5 * (kelly_fraction ** 2) * variance
            volatility = kelly_fraction * np.sqrt(variance)
            
            if drift >= 0:
                # Positive drift case
                max_dd = volatility * np.sqrt(2 * np.log(252))  # Annual estimate
            else:
                # Negative drift case (higher drawdown)
                max_dd = min(1.0, volatility * np.sqrt(2 * np.log(252)) - drift)
            
            return max_dd
            
        except Exception as e:
            logger.error(f"Error estimating continuous max drawdown: {e}")
            return 0.0
    
    def _calculate_confidence_interval(
        self,
        kelly_fraction: float,
        win_prob: float,
        win_loss_ratio: float,
        confidence_level: float
    ) -> Tuple[float, float]:
        """Calculate confidence interval for Kelly fraction."""
        try:
            # Simplified confidence interval
            # In practice, would use bootstrap or analytical methods
            
            margin = 0.1 * kelly_fraction  # 10% margin
            lower = max(0, kelly_fraction - margin)
            upper = min(1, kelly_fraction + margin)
            
            return (lower, upper)
            
        except Exception as e:
            logger.error(f"Error calculating confidence interval: {e}")
            return (0.0, 0.0)
    
    def _calculate_continuous_confidence_interval(
        self,
        kelly_fraction: float,
        mean_return: float,
        variance: float,
        confidence_level: float
    ) -> Tuple[float, float]:
        """Calculate confidence interval for continuous Kelly."""
        try:
            # Standard error approximation
            std_error = np.sqrt(variance) / np.sqrt(252)  # Assuming daily data
            
            z_score = norm.ppf((1 + confidence_level) / 2)
            margin = z_score * std_error
            
            lower = max(0, kelly_fraction - margin)
            upper = min(1, kelly_fraction + margin)
            
            return (lower, upper)
            
        except Exception as e:
            logger.error(f"Error calculating continuous confidence interval: {e}")
            return (0.0, 0.0)
    
    def _calculate_risk_of_ruin(
        self,
        kelly_fraction: float,
        win_prob: float,
        win_loss_ratio: float
    ) -> float:
        """Calculate risk of ruin for binary Kelly."""
        try:
            if kelly_fraction <= 0:
                return 1.0
            
            # Risk of ruin formula for Kelly betting
            if win_prob == 0.5 and win_loss_ratio == 1:
                return 1.0  # Fair game
            
            # Simplified calculation
            if kelly_fraction >= 1:
                return 1.0  # Certain ruin with full Kelly or more
            
            # Approximate risk of ruin
            edge = win_prob * win_loss_ratio - (1 - win_prob)
            if edge <= 0:
                return 1.0
            
            # Simplified formula
            risk_of_ruin = ((1 - win_prob) / win_prob) ** (1 / kelly_fraction)
            
            return min(risk_of_ruin, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating risk of ruin: {e}")
            return 0.5
    
    def _calculate_continuous_risk_of_ruin(
        self,
        kelly_fraction: float,
        mean_return: float,
        variance: float
    ) -> float:
        """Calculate risk of ruin for continuous Kelly."""
        try:
            if kelly_fraction <= 0:
                return 1.0
            
            # For continuous case with normal distribution
            drift = kelly_fraction * mean_return - 0.5 * (kelly_fraction ** 2) * variance
            
            if drift <= 0:
                return 1.0  # Negative or zero drift leads to certain ruin
            
            # Simplified risk of ruin approximation
            volatility = kelly_fraction * np.sqrt(variance)
            risk_of_ruin = np.exp(-2 * drift / (volatility ** 2))
            
            return min(risk_of_ruin, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating continuous risk of ruin: {e}")
            return 0.5
    
    def _empty_kelly_result(self) -> KellyResult:
        """Return empty Kelly result."""
        return KellyResult(
            optimal_fraction=0.0,
            expected_growth_rate=0.0,
            probability_of_loss=0.5,
            max_drawdown_estimate=0.0,
            confidence_interval=(0.0, 0.0),
            risk_of_ruin=1.0
        )
    
    def _empty_portfolio_result(self) -> PortfolioKellyResult:
        """Return empty portfolio Kelly result."""
        return PortfolioKellyResult(
            optimal_weights={},
            expected_portfolio_growth=0.0,
            portfolio_volatility=0.0,
            sharpe_ratio=0.0,
            max_leverage=0.0,
            individual_kelly_fractions={}
        )
