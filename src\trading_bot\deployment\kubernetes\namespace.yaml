apiVersion: v1
kind: Namespace
metadata:
  name: trading-bot
  labels:
    name: trading-bot
    environment: production
    app: ai-trading-bot
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: trading-bot-quota
  namespace: trading-bot
spec:
  hard:
    requests.cpu: "8"
    requests.memory: 16Gi
    limits.cpu: "16"
    limits.memory: 32Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: trading-bot-limits
  namespace: trading-bot
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
