"""
Market Regime Detection System

Identifies and adapts to changing market conditions using multiple
indicators and machine learning techniques.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import numpy as np
import pandas as pd
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import warnings
warnings.filterwarnings('ignore')

from ...core.logger import get_logger

logger = get_logger(__name__)

class MarketRegime(Enum):
    """Market regime types"""
    BULL_TRENDING = "bull_trending"
    BEAR_TRENDING = "bear_trending"
    SIDEWAYS_RANGING = "sideways_ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    CRISIS = "crisis"
    RECOVERY = "recovery"

@dataclass
class RegimeIndicators:
    """Market regime indicators"""
    trend_strength: float
    volatility_regime: float
    correlation_regime: float
    volume_regime: float
    sentiment_regime: float
    momentum: float
    mean_reversion: float
    timestamp: datetime

@dataclass
class RegimeDetection:
    """Market regime detection result"""
    current_regime: MarketRegime
    confidence: float
    regime_probabilities: Dict[MarketRegime, float]
    indicators: RegimeIndicators
    regime_duration: timedelta
    last_regime_change: datetime
    stability_score: float

@dataclass
class RegimeAdjustment:
    """Strategy adjustments for regime"""
    regime: MarketRegime
    position_sizing_multiplier: float
    risk_multiplier: float
    strategy_weights: Dict[str, float]
    recommended_actions: List[str]

class MarketRegimeDetector:
    """Identifies and adapts to changing market conditions"""
    
    def __init__(self, market_data_source=None):
        self.market_data_source = market_data_source
        self.current_regime = MarketRegime.SIDEWAYS_RANGING
        self.regime_history = []
        self.indicators_history = []
        self.regime_classifier = None
        self.scaler = StandardScaler()
        
        # Regime detection parameters
        self.lookback_periods = {
            'short': 5,    # 5 days
            'medium': 20,  # 20 days
            'long': 60     # 60 days
        }
        
        # Regime thresholds
        self.regime_thresholds = {
            'trend_strength': {
                'strong_bull': 0.7,
                'weak_bull': 0.3,
                'neutral': 0.0,
                'weak_bear': -0.3,
                'strong_bear': -0.7
            },
            'volatility': {
                'very_low': 0.1,
                'low': 0.15,
                'normal': 0.25,
                'high': 0.35,
                'very_high': 0.5
            },
            'correlation': {
                'low': 0.3,
                'medium': 0.6,
                'high': 0.8
            }
        }
        
        # Strategy adjustments by regime
        self.regime_adjustments = self._initialize_regime_adjustments()
        
    def _initialize_regime_adjustments(self) -> Dict[MarketRegime, RegimeAdjustment]:
        """Initialize strategy adjustments for each regime"""
        
        return {
            MarketRegime.BULL_TRENDING: RegimeAdjustment(
                regime=MarketRegime.BULL_TRENDING,
                position_sizing_multiplier=1.2,
                risk_multiplier=0.8,
                strategy_weights={'momentum': 0.6, 'breakout': 0.3, 'mean_reversion': 0.1},
                recommended_actions=[
                    'Increase momentum strategy allocation',
                    'Reduce hedging positions',
                    'Extend holding periods',
                    'Focus on growth sectors'
                ]
            ),
            MarketRegime.BEAR_TRENDING: RegimeAdjustment(
                regime=MarketRegime.BEAR_TRENDING,
                position_sizing_multiplier=0.6,
                risk_multiplier=1.5,
                strategy_weights={'momentum': 0.2, 'mean_reversion': 0.4, 'defensive': 0.4},
                recommended_actions=[
                    'Reduce position sizes',
                    'Increase cash allocation',
                    'Focus on defensive sectors',
                    'Implement hedging strategies'
                ]
            ),
            MarketRegime.SIDEWAYS_RANGING: RegimeAdjustment(
                regime=MarketRegime.SIDEWAYS_RANGING,
                position_sizing_multiplier=1.0,
                risk_multiplier=1.0,
                strategy_weights={'mean_reversion': 0.5, 'momentum': 0.3, 'arbitrage': 0.2},
                recommended_actions=[
                    'Focus on mean reversion strategies',
                    'Increase trading frequency',
                    'Target range-bound securities',
                    'Implement pairs trading'
                ]
            ),
            MarketRegime.HIGH_VOLATILITY: RegimeAdjustment(
                regime=MarketRegime.HIGH_VOLATILITY,
                position_sizing_multiplier=0.7,
                risk_multiplier=1.8,
                strategy_weights={'volatility': 0.4, 'momentum': 0.3, 'mean_reversion': 0.3},
                recommended_actions=[
                    'Reduce position sizes significantly',
                    'Implement volatility strategies',
                    'Tighten stop losses',
                    'Increase monitoring frequency'
                ]
            ),
            MarketRegime.LOW_VOLATILITY: RegimeAdjustment(
                regime=MarketRegime.LOW_VOLATILITY,
                position_sizing_multiplier=1.3,
                risk_multiplier=0.7,
                strategy_weights={'carry': 0.4, 'momentum': 0.4, 'arbitrage': 0.2},
                recommended_actions=[
                    'Increase position sizes',
                    'Focus on carry strategies',
                    'Extend holding periods',
                    'Reduce hedging costs'
                ]
            ),
            MarketRegime.CRISIS: RegimeAdjustment(
                regime=MarketRegime.CRISIS,
                position_sizing_multiplier=0.3,
                risk_multiplier=3.0,
                strategy_weights={'defensive': 0.7, 'cash': 0.3},
                recommended_actions=[
                    'Minimize risk exposure',
                    'Increase cash positions',
                    'Focus on liquidity',
                    'Implement crisis protocols'
                ]
            ),
            MarketRegime.RECOVERY: RegimeAdjustment(
                regime=MarketRegime.RECOVERY,
                position_sizing_multiplier=1.1,
                risk_multiplier=0.9,
                strategy_weights={'momentum': 0.4, 'value': 0.3, 'growth': 0.3},
                recommended_actions=[
                    'Gradually increase risk',
                    'Focus on recovery plays',
                    'Monitor for false signals',
                    'Diversify across sectors'
                ]
            )
        }
    
    async def detect_current_regime(self) -> RegimeDetection:
        """Detect current market regime using multiple indicators"""
        
        logger.info("Detecting current market regime")
        
        # Calculate regime indicators
        indicators = await self._calculate_regime_indicators()
        
        # Use ML model to classify regime if available
        if self.regime_classifier:
            regime_probabilities = await self._classify_regime_ml(indicators)
            current_regime = max(regime_probabilities.items(), key=lambda x: x[1])[0]
            confidence = regime_probabilities[current_regime]
        else:
            # Use rule-based classification
            current_regime, confidence, regime_probabilities = await self._classify_regime_rules(indicators)
        
        # Calculate regime duration and stability
        regime_duration, last_change, stability = await self._calculate_regime_stability(current_regime)
        
        # Update regime history
        detection = RegimeDetection(
            current_regime=current_regime,
            confidence=confidence,
            regime_probabilities=regime_probabilities,
            indicators=indicators,
            regime_duration=regime_duration,
            last_regime_change=last_change,
            stability_score=stability
        )
        
        self.regime_history.append(detection)
        self.indicators_history.append(indicators)
        
        # Keep only recent history
        if len(self.regime_history) > 1000:
            self.regime_history = self.regime_history[-1000:]
            self.indicators_history = self.indicators_history[-1000:]
        
        # Update current regime if confidence is high enough
        if confidence > 0.7 and current_regime != self.current_regime:
            logger.info(f"Market regime changed: {self.current_regime.value} -> {current_regime.value}")
            self.current_regime = current_regime
        
        return detection
    
    async def get_regime_adjustments(self, regime: Optional[MarketRegime] = None) -> RegimeAdjustment:
        """Get strategy adjustments for current or specified regime"""
        
        if regime is None:
            regime = self.current_regime
        
        return self.regime_adjustments.get(regime, self.regime_adjustments[MarketRegime.SIDEWAYS_RANGING])
    
    async def continuous_detection(self):
        """Continuously monitor and detect regime changes"""
        
        logger.info("Starting continuous market regime detection")
        
        while True:
            try:
                # Detect current regime
                detection = await self.detect_current_regime()
                
                # Log regime information
                logger.info(f"Current regime: {detection.current_regime.value} "
                           f"(confidence: {detection.confidence:.2f})")
                
                # Train/update ML model periodically
                if len(self.indicators_history) > 100 and len(self.indicators_history) % 50 == 0:
                    await self._train_regime_classifier()
                
                # Sleep before next detection
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in regime detection: {e}")
                await asyncio.sleep(60)
    
    async def _calculate_regime_indicators(self) -> RegimeIndicators:
        """Calculate all regime indicators"""
        
        # Get market data
        market_data = await self._get_market_data()
        
        if market_data is None or len(market_data) < self.lookback_periods['long']:
            # Return neutral indicators if insufficient data
            return RegimeIndicators(
                trend_strength=0.0,
                volatility_regime=0.2,
                correlation_regime=0.5,
                volume_regime=1.0,
                sentiment_regime=0.5,
                momentum=0.0,
                mean_reversion=0.5,
                timestamp=datetime.now()
            )
        
        # Calculate trend strength
        trend_strength = await self._calculate_trend_strength(market_data)
        
        # Calculate volatility regime
        volatility_regime = await self._calculate_volatility_regime(market_data)
        
        # Calculate correlation regime
        correlation_regime = await self._calculate_correlation_regime(market_data)
        
        # Calculate volume regime
        volume_regime = await self._calculate_volume_regime(market_data)
        
        # Calculate sentiment regime
        sentiment_regime = await self._calculate_sentiment_regime()
        
        # Calculate momentum
        momentum = await self._calculate_momentum(market_data)
        
        # Calculate mean reversion tendency
        mean_reversion = await self._calculate_mean_reversion(market_data)
        
        return RegimeIndicators(
            trend_strength=trend_strength,
            volatility_regime=volatility_regime,
            correlation_regime=correlation_regime,
            volume_regime=volume_regime,
            sentiment_regime=sentiment_regime,
            momentum=momentum,
            mean_reversion=mean_reversion,
            timestamp=datetime.now()
        )
    
    async def _get_market_data(self) -> Optional[pd.DataFrame]:
        """Get market data for analysis"""
        
        # This would fetch actual market data
        # For now, generate synthetic data
        
        dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
        
        # Generate synthetic price data with different regimes
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, 100)  # Daily returns
        
        # Add regime-specific patterns
        for i in range(len(returns)):
            if i < 30:  # Bull market
                returns[i] += 0.002
            elif i < 60:  # Bear market
                returns[i] -= 0.001
            else:  # Sideways
                returns[i] *= 0.5
        
        prices = 100 * np.exp(np.cumsum(returns))
        volumes = np.random.lognormal(15, 0.5, 100)
        
        return pd.DataFrame({
            'date': dates,
            'price': prices,
            'volume': volumes,
            'returns': returns
        })
    
    async def _calculate_trend_strength(self, data: pd.DataFrame) -> float:
        """Calculate trend strength indicator"""
        
        # Use multiple moving averages to determine trend
        short_ma = data['price'].rolling(self.lookback_periods['short']).mean()
        medium_ma = data['price'].rolling(self.lookback_periods['medium']).mean()
        long_ma = data['price'].rolling(self.lookback_periods['long']).mean()
        
        current_price = data['price'].iloc[-1]
        
        # Calculate trend score
        trend_score = 0
        
        if current_price > short_ma.iloc[-1]:
            trend_score += 0.3
        if short_ma.iloc[-1] > medium_ma.iloc[-1]:
            trend_score += 0.3
        if medium_ma.iloc[-1] > long_ma.iloc[-1]:
            trend_score += 0.4
        
        # Convert to range [-1, 1]
        trend_strength = (trend_score - 0.5) * 2
        
        return np.clip(trend_strength, -1, 1)
    
    async def _calculate_volatility_regime(self, data: pd.DataFrame) -> float:
        """Calculate volatility regime indicator"""
        
        # Calculate rolling volatility
        returns = data['returns']
        short_vol = returns.rolling(self.lookback_periods['short']).std() * np.sqrt(252)
        long_vol = returns.rolling(self.lookback_periods['long']).std() * np.sqrt(252)
        
        current_vol = short_vol.iloc[-1]
        avg_vol = long_vol.iloc[-1]
        
        # Normalize volatility
        vol_ratio = current_vol / avg_vol if avg_vol > 0 else 1
        
        return min(vol_ratio, 3.0)  # Cap at 3x average
    
    async def _calculate_correlation_regime(self, data: pd.DataFrame) -> float:
        """Calculate correlation regime indicator"""
        
        # This would calculate correlation between different assets
        # For now, simulate based on volatility
        
        vol_regime = await self._calculate_volatility_regime(data)
        
        # High volatility typically means high correlation
        correlation = min(0.3 + vol_regime * 0.3, 0.9)
        
        return correlation
    
    async def _calculate_volume_regime(self, data: pd.DataFrame) -> float:
        """Calculate volume regime indicator"""
        
        # Calculate volume trend
        short_vol = data['volume'].rolling(self.lookback_periods['short']).mean()
        long_vol = data['volume'].rolling(self.lookback_periods['long']).mean()
        
        current_vol = short_vol.iloc[-1]
        avg_vol = long_vol.iloc[-1]
        
        vol_ratio = current_vol / avg_vol if avg_vol > 0 else 1
        
        return min(vol_ratio, 3.0)
    
    async def _calculate_sentiment_regime(self) -> float:
        """Calculate sentiment regime indicator"""
        
        # This would integrate with sentiment data sources
        # For now, simulate sentiment
        
        # Random sentiment with some persistence
        if hasattr(self, '_last_sentiment'):
            sentiment = self._last_sentiment + np.random.normal(0, 0.1)
        else:
            sentiment = np.random.uniform(0, 1)
        
        sentiment = np.clip(sentiment, 0, 1)
        self._last_sentiment = sentiment
        
        return sentiment
    
    async def _calculate_momentum(self, data: pd.DataFrame) -> float:
        """Calculate momentum indicator"""
        
        # Price momentum over different periods
        short_momentum = (data['price'].iloc[-1] / data['price'].iloc[-self.lookback_periods['short']]) - 1
        medium_momentum = (data['price'].iloc[-1] / data['price'].iloc[-self.lookback_periods['medium']]) - 1
        
        # Weighted momentum
        momentum = 0.6 * short_momentum + 0.4 * medium_momentum
        
        return np.clip(momentum, -0.5, 0.5)
    
    async def _calculate_mean_reversion(self, data: pd.DataFrame) -> float:
        """Calculate mean reversion tendency"""
        
        # Calculate how often price reverts to mean
        returns = data['returns']
        
        # Count reversals
        reversals = 0
        total_periods = 0
        
        for i in range(2, len(returns)):
            if (returns.iloc[i-2] > 0 and returns.iloc[i-1] < 0 and returns.iloc[i] > 0) or \
               (returns.iloc[i-2] < 0 and returns.iloc[i-1] > 0 and returns.iloc[i] < 0):
                reversals += 1
            total_periods += 1
        
        mean_reversion_tendency = reversals / total_periods if total_periods > 0 else 0.5
        
        return mean_reversion_tendency
    
    async def _classify_regime_rules(self, indicators: RegimeIndicators) -> Tuple[MarketRegime, float, Dict[MarketRegime, float]]:
        """Classify regime using rule-based approach"""
        
        regime_scores = {}
        
        # Bull trending
        bull_score = 0
        if indicators.trend_strength > 0.3:
            bull_score += 0.4
        if indicators.momentum > 0.1:
            bull_score += 0.3
        if indicators.sentiment_regime > 0.6:
            bull_score += 0.3
        regime_scores[MarketRegime.BULL_TRENDING] = bull_score
        
        # Bear trending
        bear_score = 0
        if indicators.trend_strength < -0.3:
            bear_score += 0.4
        if indicators.momentum < -0.1:
            bear_score += 0.3
        if indicators.sentiment_regime < 0.4:
            bear_score += 0.3
        regime_scores[MarketRegime.BEAR_TRENDING] = bear_score
        
        # High volatility
        high_vol_score = 0
        if indicators.volatility_regime > 1.5:
            high_vol_score += 0.6
        if indicators.correlation_regime > 0.7:
            high_vol_score += 0.4
        regime_scores[MarketRegime.HIGH_VOLATILITY] = high_vol_score
        
        # Low volatility
        low_vol_score = 0
        if indicators.volatility_regime < 0.8:
            low_vol_score += 0.6
        if indicators.correlation_regime < 0.4:
            low_vol_score += 0.4
        regime_scores[MarketRegime.LOW_VOLATILITY] = low_vol_score
        
        # Sideways ranging (default)
        sideways_score = 0.3  # Base score
        if abs(indicators.trend_strength) < 0.2:
            sideways_score += 0.4
        if indicators.mean_reversion > 0.6:
            sideways_score += 0.3
        regime_scores[MarketRegime.SIDEWAYS_RANGING] = sideways_score
        
        # Crisis
        crisis_score = 0
        if indicators.volatility_regime > 2.0 and indicators.trend_strength < -0.5:
            crisis_score += 0.8
        if indicators.correlation_regime > 0.8:
            crisis_score += 0.2
        regime_scores[MarketRegime.CRISIS] = crisis_score
        
        # Recovery
        recovery_score = 0
        if indicators.trend_strength > 0.2 and indicators.volatility_regime > 1.2:
            recovery_score += 0.6
        if indicators.sentiment_regime > 0.5:
            recovery_score += 0.4
        regime_scores[MarketRegime.RECOVERY] = recovery_score
        
        # Normalize scores
        total_score = sum(regime_scores.values())
        if total_score > 0:
            regime_probabilities = {regime: score / total_score for regime, score in regime_scores.items()}
        else:
            regime_probabilities = {regime: 1.0 / len(regime_scores) for regime in regime_scores}
        
        # Get regime with highest probability
        best_regime = max(regime_probabilities.items(), key=lambda x: x[1])
        
        return best_regime[0], best_regime[1], regime_probabilities
    
    async def _classify_regime_ml(self, indicators: RegimeIndicators) -> Dict[MarketRegime, float]:
        """Classify regime using ML model"""
        
        # Prepare features
        features = np.array([[
            indicators.trend_strength,
            indicators.volatility_regime,
            indicators.correlation_regime,
            indicators.volume_regime,
            indicators.sentiment_regime,
            indicators.momentum,
            indicators.mean_reversion
        ]])
        
        # Scale features
        features_scaled = self.scaler.transform(features)
        
        # Get probabilities
        probabilities = self.regime_classifier.predict_proba(features_scaled)[0]
        
        # Map to regime enum
        regime_classes = list(MarketRegime)
        regime_probabilities = {
            regime_classes[i]: prob for i, prob in enumerate(probabilities)
        }
        
        return regime_probabilities
    
    async def _calculate_regime_stability(self, current_regime: MarketRegime) -> Tuple[timedelta, datetime, float]:
        """Calculate regime duration and stability"""
        
        if not self.regime_history:
            return timedelta(0), datetime.now(), 1.0
        
        # Find last regime change
        last_change = datetime.now()
        regime_duration = timedelta(0)
        
        for i in range(len(self.regime_history) - 1, -1, -1):
            if self.regime_history[i].current_regime != current_regime:
                last_change = self.regime_history[i].indicators.timestamp
                break
            regime_duration = datetime.now() - self.regime_history[i].indicators.timestamp
        
        # Calculate stability score
        recent_detections = self.regime_history[-20:] if len(self.regime_history) >= 20 else self.regime_history
        
        if recent_detections:
            same_regime_count = sum(1 for d in recent_detections if d.current_regime == current_regime)
            stability_score = same_regime_count / len(recent_detections)
        else:
            stability_score = 1.0
        
        return regime_duration, last_change, stability_score
    
    async def _train_regime_classifier(self):
        """Train ML classifier for regime detection"""
        
        if len(self.indicators_history) < 50:
            return
        
        # Prepare training data
        features = []
        labels = []
        
        for i, indicators in enumerate(self.indicators_history):
            if i < len(self.regime_history):
                features.append([
                    indicators.trend_strength,
                    indicators.volatility_regime,
                    indicators.correlation_regime,
                    indicators.volume_regime,
                    indicators.sentiment_regime,
                    indicators.momentum,
                    indicators.mean_reversion
                ])
                labels.append(self.regime_history[i].current_regime.value)
        
        if len(features) < 20:
            return
        
        # Convert to numpy arrays
        X = np.array(features)
        y = np.array(labels)
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train classifier
        self.regime_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        self.regime_classifier.fit(X_scaled, y)
        
        logger.info("Trained regime classifier with {} samples".format(len(features)))
    
    def get_regime_summary(self) -> Dict[str, Any]:
        """Get summary of current regime and recent history"""
        
        recent_regimes = self.regime_history[-10:] if len(self.regime_history) >= 10 else self.regime_history
        
        regime_counts = {}
        for detection in recent_regimes:
            regime = detection.current_regime.value
            regime_counts[regime] = regime_counts.get(regime, 0) + 1
        
        return {
            'current_regime': self.current_regime.value,
            'recent_regime_distribution': regime_counts,
            'total_detections': len(self.regime_history),
            'classifier_trained': self.regime_classifier is not None,
            'last_detection': self.regime_history[-1].indicators.timestamp.isoformat() if self.regime_history else None
        }
