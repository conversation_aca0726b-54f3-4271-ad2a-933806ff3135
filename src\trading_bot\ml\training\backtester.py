"""Backtesting framework for trading strategies."""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Callable
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings

logger = logging.getLogger(__name__)


@dataclass
class BacktestConfig:
    """Configuration for backtesting."""
    initial_capital: float = 100000.0
    commission: float = 0.001  # 0.1%
    slippage: float = 0.0005   # 0.05%
    max_position_size: float = 1.0  # 100% of portfolio
    min_position_size: float = 0.01  # 1% of portfolio
    
    # Risk management
    stop_loss: Optional[float] = None  # -5%
    take_profit: Optional[float] = None  # 10%
    max_drawdown_limit: Optional[float] = None  # 20%
    
    # Position sizing
    position_sizing_method: str = 'fixed'  # fixed, kelly, volatility_target
    volatility_target: float = 0.15  # 15% annual volatility target
    kelly_lookback: int = 252  # Days for Kelly calculation
    
    # Rebalancing
    rebalance_frequency: str = 'daily'  # daily, weekly, monthly
    
    # Transaction costs
    include_borrowing_costs: bool = False
    borrowing_rate: float = 0.03  # 3% annual
    
    # Market impact
    include_market_impact: bool = False
    market_impact_coefficient: float = 0.1


@dataclass
class Trade:
    """Individual trade record."""
    entry_date: datetime
    exit_date: Optional[datetime]
    symbol: str
    side: str  # 'long' or 'short'
    entry_price: float
    exit_price: Optional[float]
    quantity: float
    pnl: Optional[float] = None
    commission: float = 0.0
    slippage: float = 0.0
    signal_confidence: float = 0.0
    entry_reason: str = ""
    exit_reason: str = ""


@dataclass
class BacktestResults:
    """Comprehensive backtesting results."""
    # Performance metrics
    total_return: float = 0.0
    annual_return: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # Risk metrics
    max_drawdown: float = 0.0
    max_drawdown_duration: int = 0
    var_95: float = 0.0
    cvar_95: float = 0.0
    
    # Trade statistics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    
    # Portfolio statistics
    final_portfolio_value: float = 0.0
    peak_portfolio_value: float = 0.0
    total_commission: float = 0.0
    total_slippage: float = 0.0
    
    # Time series data
    portfolio_values: List[float] = field(default_factory=list)
    returns: List[float] = field(default_factory=list)
    positions: List[float] = field(default_factory=list)
    trades: List[Trade] = field(default_factory=list)
    
    # Benchmarking
    benchmark_return: Optional[float] = None
    alpha: Optional[float] = None
    beta: Optional[float] = None
    information_ratio: Optional[float] = None


class PortfolioManager:
    """Manages portfolio state during backtesting."""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.cash = config.initial_capital
        self.positions = {}  # symbol -> quantity
        self.portfolio_value = config.initial_capital
        self.peak_value = config.initial_capital
        self.current_drawdown = 0.0
        self.open_trades = {}  # symbol -> Trade
        
    def get_position_size(self, 
                         signal_strength: float,
                         current_price: float,
                         volatility: Optional[float] = None) -> float:
        """Calculate position size based on sizing method."""
        
        if self.config.position_sizing_method == 'fixed':
            # Fixed percentage of portfolio
            target_value = self.portfolio_value * signal_strength * self.config.max_position_size
            return target_value / current_price
            
        elif self.config.position_sizing_method == 'volatility_target':
            # Volatility targeting
            if volatility is None or volatility <= 0:
                volatility = 0.2  # Default 20% volatility
            
            # Scale position to achieve target volatility
            vol_scalar = self.config.volatility_target / volatility
            target_value = self.portfolio_value * signal_strength * vol_scalar
            target_value = min(target_value, self.portfolio_value * self.config.max_position_size)
            return target_value / current_price
            
        elif self.config.position_sizing_method == 'kelly':
            # Kelly criterion (simplified)
            # This would require expected return and variance estimates
            win_prob = 0.55  # Placeholder
            avg_win = 0.02   # Placeholder
            avg_loss = 0.015 # Placeholder
            
            kelly_fraction = (win_prob * avg_win - (1 - win_prob) * avg_loss) / avg_win
            kelly_fraction = max(0, min(kelly_fraction, self.config.max_position_size))
            
            target_value = self.portfolio_value * kelly_fraction * signal_strength
            return target_value / current_price
            
        else:
            raise ValueError(f"Unknown position sizing method: {self.config.position_sizing_method}")
    
    def enter_position(self, 
                      symbol: str,
                      side: str,
                      quantity: float,
                      price: float,
                      date: datetime,
                      signal_confidence: float = 0.0,
                      reason: str = "") -> Optional[Trade]:
        """Enter a new position."""
        
        # Calculate costs
        commission = abs(quantity * price) * self.config.commission
        slippage = abs(quantity * price) * self.config.slippage
        total_cost = abs(quantity * price) + commission + slippage
        
        # Check if we have enough cash
        if side == 'long' and total_cost > self.cash:
            logger.warning(f"Insufficient cash for {symbol} long position")
            return None
        
        # Execute trade
        if side == 'long':
            self.cash -= total_cost
            self.positions[symbol] = self.positions.get(symbol, 0) + quantity
        else:  # short
            self.cash += quantity * price - commission - slippage
            self.positions[symbol] = self.positions.get(symbol, 0) - quantity
        
        # Create trade record
        trade = Trade(
            entry_date=date,
            symbol=symbol,
            side=side,
            entry_price=price,
            quantity=quantity,
            commission=commission,
            slippage=slippage,
            signal_confidence=signal_confidence,
            entry_reason=reason
        )
        
        self.open_trades[symbol] = trade
        
        return trade
    
    def exit_position(self, 
                     symbol: str,
                     price: float,
                     date: datetime,
                     reason: str = "") -> Optional[Trade]:
        """Exit an existing position."""
        
        if symbol not in self.positions or self.positions[symbol] == 0:
            return None
        
        quantity = self.positions[symbol]
        
        # Calculate costs
        commission = abs(quantity * price) * self.config.commission
        slippage = abs(quantity * price) * self.config.slippage
        
        # Execute trade
        self.cash += quantity * price - commission - slippage
        
        # Calculate P&L
        if symbol in self.open_trades:
            trade = self.open_trades[symbol]
            trade.exit_date = date
            trade.exit_price = price
            trade.commission += commission
            trade.slippage += slippage
            trade.exit_reason = reason
            
            if trade.side == 'long':
                trade.pnl = quantity * (price - trade.entry_price) - trade.commission - trade.slippage
            else:  # short
                trade.pnl = quantity * (trade.entry_price - price) - trade.commission - trade.slippage
            
            # Remove from open trades
            del self.open_trades[symbol]
        
        # Update position
        self.positions[symbol] = 0
        
        return trade
    
    def update_portfolio_value(self, current_prices: Dict[str, float]):
        """Update portfolio value based on current prices."""
        position_value = 0
        
        for symbol, quantity in self.positions.items():
            if quantity != 0 and symbol in current_prices:
                position_value += quantity * current_prices[symbol]
        
        self.portfolio_value = self.cash + position_value
        
        # Update peak and drawdown
        if self.portfolio_value > self.peak_value:
            self.peak_value = self.portfolio_value
            self.current_drawdown = 0
        else:
            self.current_drawdown = (self.peak_value - self.portfolio_value) / self.peak_value
    
    def check_risk_limits(self) -> List[str]:
        """Check if any risk limits are breached."""
        violations = []
        
        if self.config.max_drawdown_limit and self.current_drawdown > self.config.max_drawdown_limit:
            violations.append(f"Max drawdown exceeded: {self.current_drawdown:.2%}")
        
        return violations


class Backtester:
    """Main backtesting engine."""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.portfolio_manager = PortfolioManager(config)
        self.results = BacktestResults()
        
    def run_backtest(self, 
                    data: pd.DataFrame,
                    strategy_signals: pd.DataFrame,
                    benchmark_data: Optional[pd.DataFrame] = None) -> BacktestResults:
        """Run complete backtest."""
        
        logger.info("Starting backtest...")
        
        # Validate inputs
        self._validate_inputs(data, strategy_signals)
        
        # Initialize tracking
        portfolio_values = []
        returns = []
        positions = []
        completed_trades = []
        
        # Main backtest loop
        for i, (date, row) in enumerate(data.iterrows()):
            # Get current signals
            if date in strategy_signals.index:
                signals = strategy_signals.loc[date]
            else:
                signals = pd.Series()  # No signals
            
            # Get current prices
            current_prices = {
                'symbol': row.get('close', row.get('price', 0))
            }
            
            # Process signals
            trades = self._process_signals(signals, current_prices, date, row)
            completed_trades.extend([t for t in trades if t and t.exit_date])
            
            # Update portfolio
            self.portfolio_manager.update_portfolio_value(current_prices)
            
            # Check risk limits
            violations = self.portfolio_manager.check_risk_limits()
            if violations:
                logger.warning(f"Risk violations on {date}: {violations}")
                # Force exit all positions if max drawdown exceeded
                if any('drawdown' in v.lower() for v in violations):
                    self._emergency_exit_all(current_prices, date)
            
            # Record state
            portfolio_values.append(self.portfolio_manager.portfolio_value)
            if i > 0:
                returns.append((self.portfolio_manager.portfolio_value / portfolio_values[i-1]) - 1)
            else:
                returns.append(0)
            
            # Record positions (simplified for single symbol)
            total_position_value = sum(
                qty * current_prices['symbol'] 
                for qty in self.portfolio_manager.positions.values()
            )
            positions.append(total_position_value / self.portfolio_manager.portfolio_value if self.portfolio_manager.portfolio_value > 0 else 0)
        
        # Close any remaining open positions
        final_prices = {'symbol': data.iloc[-1].get('close', data.iloc[-1].get('price', 0))}
        for symbol in list(self.portfolio_manager.positions.keys()):
            if self.portfolio_manager.positions[symbol] != 0:
                trade = self.portfolio_manager.exit_position(symbol, final_prices['symbol'], data.index[-1], "End of backtest")
                if trade:
                    completed_trades.append(trade)
        
        # Calculate results
        self.results = self._calculate_results(
            portfolio_values, 
            returns, 
            positions, 
            completed_trades,
            data.index,
            benchmark_data
        )
        
        logger.info("Backtest completed")
        return self.results
    
    def _validate_inputs(self, data: pd.DataFrame, strategy_signals: pd.DataFrame):
        """Validate input data."""
        if data.empty:
            raise ValueError("Data cannot be empty")
        
        required_cols = ['close']
        if not any(col in data.columns for col in required_cols + ['price']):
            raise ValueError("Data must contain 'close' or 'price' column")
    
    def _process_signals(self, 
                        signals: pd.Series,
                        current_prices: Dict[str, float],
                        date: datetime,
                        market_data: pd.Series) -> List[Optional[Trade]]:
        """Process trading signals."""
        
        trades = []
        
        if signals.empty:
            return trades
        
        # Simple signal processing for single symbol
        symbol = 'symbol'
        current_price = current_prices[symbol]
        
        # Get signal (assuming 'signal' column with BUY/SELL/HOLD)
        signal = signals.get('signal', 'HOLD')
        confidence = signals.get('confidence', 0.5)
        
        current_position = self.portfolio_manager.positions.get(symbol, 0)
        
        if signal == 'BUY' and current_position <= 0:
            # Enter long position
            volatility = market_data.get('volatility', 0.2)
            quantity = self.portfolio_manager.get_position_size(confidence, current_price, volatility)
            
            if quantity > 0:
                # Exit short first if exists
                if current_position < 0:
                    exit_trade = self.portfolio_manager.exit_position(symbol, current_price, date, "Signal reversal")
                    trades.append(exit_trade)
                
                # Enter long
                entry_trade = self.portfolio_manager.enter_position(
                    symbol, 'long', quantity, current_price, date, confidence, f"BUY signal"
                )
                trades.append(entry_trade)
                
        elif signal == 'SELL' and current_position >= 0:
            # Enter short position or exit long
            if current_position > 0:
                # Exit long position
                exit_trade = self.portfolio_manager.exit_position(symbol, current_price, date, "SELL signal")
                trades.append(exit_trade)
            
            # Optionally enter short (if allowed)
            # For simplicity, we'll just exit longs here
            
        elif signal == 'HOLD':
            # Check stop loss / take profit
            if current_position != 0 and symbol in self.portfolio_manager.open_trades:
                trade = self.portfolio_manager.open_trades[symbol]
                
                if trade.side == 'long':
                    pnl_pct = (current_price - trade.entry_price) / trade.entry_price
                else:
                    pnl_pct = (trade.entry_price - current_price) / trade.entry_price
                
                # Check stop loss
                if self.config.stop_loss and pnl_pct <= -abs(self.config.stop_loss):
                    exit_trade = self.portfolio_manager.exit_position(symbol, current_price, date, "Stop loss")
                    trades.append(exit_trade)
                
                # Check take profit
                elif self.config.take_profit and pnl_pct >= self.config.take_profit:
                    exit_trade = self.portfolio_manager.exit_position(symbol, current_price, date, "Take profit")
                    trades.append(exit_trade)
        
        return trades
    
    def _emergency_exit_all(self, current_prices: Dict[str, float], date: datetime):
        """Emergency exit all positions."""
        for symbol in list(self.portfolio_manager.positions.keys()):
            if self.portfolio_manager.positions[symbol] != 0:
                self.portfolio_manager.exit_position(symbol, current_prices[symbol], date, "Emergency exit")
    
    def _calculate_results(self, 
                          portfolio_values: List[float],
                          returns: List[float],
                          positions: List[float],
                          trades: List[Trade],
                          dates: pd.DatetimeIndex,
                          benchmark_data: Optional[pd.DataFrame] = None) -> BacktestResults:
        """Calculate comprehensive backtest results."""
        
        results = BacktestResults()
        
        # Basic performance metrics
        results.portfolio_values = portfolio_values
        results.returns = returns
        results.positions = positions
        results.trades = trades
        
        if len(portfolio_values) > 1:
            # Total return
            results.total_return = (portfolio_values[-1] / portfolio_values[0]) - 1
            
            # Annualized return
            years = len(portfolio_values) / 252  # Assuming daily data
            results.annual_return = ((portfolio_values[-1] / portfolio_values[0]) ** (1/years)) - 1
            
            # Volatility
            returns_array = np.array(returns[1:])  # Skip first zero return
            results.volatility = np.std(returns_array) * np.sqrt(252)
            
            # Sharpe ratio
            risk_free_rate = 0.02  # 2% risk-free rate
            excess_returns = returns_array - (risk_free_rate / 252)
            results.sharpe_ratio = np.mean(excess_returns) / (np.std(excess_returns) + 1e-8) * np.sqrt(252)
            
            # Sortino ratio
            negative_returns = returns_array[returns_array < 0]
            downside_deviation = np.std(negative_returns) if len(negative_returns) > 0 else 0
            results.sortino_ratio = np.mean(excess_returns) / (downside_deviation + 1e-8) * np.sqrt(252)
            
            # Maximum drawdown
            peak = portfolio_values[0]
            max_dd = 0
            max_dd_duration = 0
            current_dd_duration = 0
            
            for value in portfolio_values:
                if value > peak:
                    peak = value
                    current_dd_duration = 0
                else:
                    dd = (peak - value) / peak
                    max_dd = max(max_dd, dd)
                    current_dd_duration += 1
                    max_dd_duration = max(max_dd_duration, current_dd_duration)
            
            results.max_drawdown = max_dd
            results.max_drawdown_duration = max_dd_duration
            
            # Calmar ratio
            results.calmar_ratio = results.annual_return / (results.max_drawdown + 1e-8)
            
            # VaR and CVaR
            results.var_95 = np.percentile(returns_array, 5)
            results.cvar_95 = np.mean(returns_array[returns_array <= results.var_95])
        
        # Trade statistics
        if trades:
            completed_trades = [t for t in trades if t.pnl is not None]
            
            results.total_trades = len(completed_trades)
            
            if completed_trades:
                pnls = [t.pnl for t in completed_trades]
                winning_trades = [t for t in completed_trades if t.pnl > 0]
                losing_trades = [t for t in completed_trades if t.pnl < 0]
                
                results.winning_trades = len(winning_trades)
                results.losing_trades = len(losing_trades)
                results.win_rate = len(winning_trades) / len(completed_trades)
                
                if winning_trades:
                    results.avg_win = np.mean([t.pnl for t in winning_trades])
                if losing_trades:
                    results.avg_loss = np.mean([t.pnl for t in losing_trades])
                
                # Profit factor
                gross_profit = sum(t.pnl for t in winning_trades)
                gross_loss = abs(sum(t.pnl for t in losing_trades))
                results.profit_factor = gross_profit / (gross_loss + 1e-8)
                
                # Total costs
                results.total_commission = sum(t.commission for t in completed_trades)
                results.total_slippage = sum(t.slippage for t in completed_trades)
        
        # Portfolio metrics
        results.final_portfolio_value = portfolio_values[-1] if portfolio_values else self.config.initial_capital
        results.peak_portfolio_value = max(portfolio_values) if portfolio_values else self.config.initial_capital
        
        # Benchmark comparison
        if benchmark_data is not None:
            benchmark_returns = benchmark_data.pct_change().dropna()
            strategy_returns = pd.Series(returns[1:], index=dates[1:len(returns)])
            
            # Align dates
            common_dates = benchmark_returns.index.intersection(strategy_returns.index)
            if len(common_dates) > 0:
                bench_aligned = benchmark_returns.loc[common_dates]
                strat_aligned = strategy_returns.loc[common_dates]
                
                # Calculate benchmark metrics
                results.benchmark_return = (1 + bench_aligned).prod() - 1
                
                # Beta and Alpha
                if len(bench_aligned) > 1:
                    covariance = np.cov(strat_aligned, bench_aligned)[0, 1]
                    benchmark_variance = np.var(bench_aligned)
                    results.beta = covariance / (benchmark_variance + 1e-8)
                    
                    results.alpha = np.mean(strat_aligned) - results.beta * np.mean(bench_aligned)
                    
                    # Information ratio
                    active_returns = strat_aligned - bench_aligned
                    results.information_ratio = np.mean(active_returns) / (np.std(active_returns) + 1e-8)
        
        return results
    
    def generate_report(self, save_path: Optional[str] = None) -> Dict[str, Any]:
        """Generate comprehensive backtest report."""
        
        if not self.results:
            raise ValueError("No backtest results available. Run backtest first.")
        
        report = {
            'summary': {
                'Total Return': f"{self.results.total_return:.2%}",
                'Annual Return': f"{self.results.annual_return:.2%}",
                'Volatility': f"{self.results.volatility:.2%}",
                'Sharpe Ratio': f"{self.results.sharpe_ratio:.2f}",
                'Sortino Ratio': f"{self.results.sortino_ratio:.2f}",
                'Max Drawdown': f"{self.results.max_drawdown:.2%}",
                'Calmar Ratio': f"{self.results.calmar_ratio:.2f}",
            },
            'trade_stats': {
                'Total Trades': self.results.total_trades,
                'Win Rate': f"{self.results.win_rate:.2%}",
                'Avg Win': f"${self.results.avg_win:.2f}",
                'Avg Loss': f"${self.results.avg_loss:.2f}",
                'Profit Factor': f"{self.results.profit_factor:.2f}",
                'Total Commission': f"${self.results.total_commission:.2f}",
            },
            'risk_metrics': {
                'VaR (95%)': f"{self.results.var_95:.2%}",
                'CVaR (95%)': f"{self.results.cvar_95:.2%}",
                'Max DD Duration': f"{self.results.max_drawdown_duration} days",
            }
        }
        
        if self.results.benchmark_return is not None:
            report['benchmark_comparison'] = {
                'Benchmark Return': f"{self.results.benchmark_return:.2%}",
                'Alpha': f"{self.results.alpha:.4f}",
                'Beta': f"{self.results.beta:.2f}",
                'Information Ratio': f"{self.results.information_ratio:.2f}",
            }
        
        if save_path:
            import json
            with open(save_path, 'w') as f:
                json.dump(report, f, indent=2)
        
        return report
    
    def plot_results(self, save_path: Optional[str] = None):
        """Plot backtest results."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Portfolio value
        axes[0, 0].plot(self.results.portfolio_values)
        axes[0, 0].set_title('Portfolio Value')
        axes[0, 0].set_ylabel('Value ($)')
        
        # Returns
        axes[0, 1].plot(self.results.returns[1:])  # Skip first zero return
        axes[0, 1].set_title('Daily Returns')
        axes[0, 1].set_ylabel('Return')
        
        # Positions
        axes[1, 0].plot(self.results.positions)
        axes[1, 0].set_title('Position Size')
        axes[1, 0].set_ylabel('Position %')
        
        # Drawdown
        portfolio_values = np.array(self.results.portfolio_values)
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (peak - portfolio_values) / peak
        axes[1, 1].fill_between(range(len(drawdown)), drawdown, alpha=0.3, color='red')
        axes[1, 1].set_title('Drawdown')
        axes[1, 1].set_ylabel('Drawdown %')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
        
        return fig