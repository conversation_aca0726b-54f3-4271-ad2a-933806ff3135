"""
Rollback and disaster recovery manager for AI Trading Bot.

This module provides comprehensive rollback capabilities and disaster
recovery procedures for safe production operations.
"""

import asyncio
import json
import logging
import os
import shutil
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
import asyncpg
import redis.asyncio as redis
from motor.motor_asyncio import AsyncIOMotorClient

from ...core.config import Config
from ...utils.logger import get_logger

logger = get_logger(__name__)


class RollbackTrigger(Enum):
    """Rollback trigger types."""
    MANUAL = "manual"
    DAILY_LOSS_THRESHOLD = "daily_loss_threshold"
    ERROR_RATE_THRESHOLD = "error_rate_threshold"
    SYSTEM_FAILURE = "system_failure"
    DATA_CORRUPTION = "data_corruption"
    CRITICAL_ALERT = "critical_alert"


class RollbackStatus(Enum):
    """Rollback execution status."""
    INITIATED = "initiated"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL = "partial"


@dataclass
class RollbackSnapshot:
    """System state snapshot for rollback."""
    snapshot_id: str
    timestamp: datetime
    version: str
    database_backup_path: str
    config_backup_path: str
    model_backup_path: str
    positions_snapshot: Dict[str, Any]
    orders_snapshot: List[Dict[str, Any]]
    system_state: Dict[str, Any]
    description: str


@dataclass
class RollbackPlan:
    """Comprehensive rollback execution plan."""
    plan_id: str
    trigger: RollbackTrigger
    target_snapshot: RollbackSnapshot
    steps: List[str] = field(default_factory=list)
    estimated_duration: int = 300  # seconds
    risk_level: str = "medium"
    requires_approval: bool = True
    notification_channels: List[str] = field(default_factory=list)


@dataclass
class RollbackResult:
    """Result of rollback execution."""
    plan_id: str
    status: RollbackStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration: float = 0.0
    steps_completed: List[str] = field(default_factory=list)
    steps_failed: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    recovery_actions: List[str] = field(default_factory=list)


class RollbackManager:
    """
    Comprehensive rollback and disaster recovery manager.
    
    Provides automated rollback capabilities including:
    - Automated position closing
    - State snapshot restoration
    - Database rollback
    - Configuration reversion
    - Emergency contact notification
    - System health validation
    - Recovery time objectives enforcement
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.snapshots: Dict[str, RollbackSnapshot] = {}
        self.rollback_history: List[RollbackResult] = []
        
        # Database connections
        self.pg_pool = None
        self.redis_client = None
        self.mongo_client = None
        
        # Rollback configuration
        self.rollback_triggers = {
            RollbackTrigger.DAILY_LOSS_THRESHOLD: 0.05,  # 5% daily loss
            RollbackTrigger.ERROR_RATE_THRESHOLD: 0.10,  # 10% error rate
        }
        
        # Recovery time objectives (seconds)
        self.rto_targets = {
            "critical_failure": 300,    # 5 minutes
            "data_corruption": 1800,    # 30 minutes
            "complete_system": 7200     # 2 hours
        }
        
        # Backup paths
        self.backup_base_path = "/opt/trading-bot/backups"
        self.ensure_backup_directories()
    
    def ensure_backup_directories(self):
        """Ensure backup directories exist."""
        directories = [
            self.backup_base_path,
            f"{self.backup_base_path}/database",
            f"{self.backup_base_path}/config",
            f"{self.backup_base_path}/models",
            f"{self.backup_base_path}/snapshots"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    async def initialize(self):
        """Initialize rollback manager."""
        try:
            # Initialize database connections
            self.pg_pool = await asyncpg.create_pool(
                host=self.config.database.postgres.host,
                port=self.config.database.postgres.port,
                user=self.config.database.postgres.user,
                password=self.config.database.postgres.password,
                database=self.config.database.postgres.database,
                min_size=2,
                max_size=5
            )
            
            self.redis_client = redis.Redis(
                host=self.config.database.redis.host,
                port=self.config.database.redis.port,
                password=self.config.database.redis.password,
                decode_responses=True
            )
            
            self.mongo_client = AsyncIOMotorClient(
                f"mongodb://{self.config.database.mongodb.host}:{self.config.database.mongodb.port}"
            )
            
            # Load existing snapshots
            await self._load_existing_snapshots()
            
            logger.info("Rollback manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize rollback manager: {e}")
            raise
    
    async def cleanup(self):
        """Clean up rollback manager."""
        try:
            if self.pg_pool:
                await self.pg_pool.close()
            if self.redis_client:
                await self.redis_client.close()
            if self.mongo_client:
                self.mongo_client.close()
            
            logger.info("Rollback manager cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup rollback manager: {e}")
    
    async def create_snapshot(self, description: str = "Automated snapshot") -> RollbackSnapshot:
        """Create a complete system state snapshot."""
        snapshot_id = f"snapshot_{int(datetime.utcnow().timestamp())}"
        timestamp = datetime.utcnow()
        
        logger.info(f"Creating system snapshot: {snapshot_id}")
        
        try:
            # Create database backup
            db_backup_path = await self._backup_database(snapshot_id)
            
            # Create configuration backup
            config_backup_path = await self._backup_configuration(snapshot_id)
            
            # Create model backup
            model_backup_path = await self._backup_models(snapshot_id)
            
            # Capture current positions
            positions_snapshot = await self._capture_positions_snapshot()
            
            # Capture pending orders
            orders_snapshot = await self._capture_orders_snapshot()
            
            # Capture system state
            system_state = await self._capture_system_state()
            
            # Create snapshot object
            snapshot = RollbackSnapshot(
                snapshot_id=snapshot_id,
                timestamp=timestamp,
                version=self.config.app.version,
                database_backup_path=db_backup_path,
                config_backup_path=config_backup_path,
                model_backup_path=model_backup_path,
                positions_snapshot=positions_snapshot,
                orders_snapshot=orders_snapshot,
                system_state=system_state,
                description=description
            )
            
            # Save snapshot metadata
            await self._save_snapshot_metadata(snapshot)
            
            # Store in memory
            self.snapshots[snapshot_id] = snapshot
            
            logger.info(f"System snapshot created successfully: {snapshot_id}")
            return snapshot
            
        except Exception as e:
            logger.error(f"Failed to create snapshot: {e}")
            raise
    
    async def _backup_database(self, snapshot_id: str) -> str:
        """Create database backup."""
        backup_path = f"{self.backup_base_path}/database/{snapshot_id}"
        os.makedirs(backup_path, exist_ok=True)
        
        # PostgreSQL backup
        pg_backup_file = f"{backup_path}/postgres_backup.sql"
        # In production, use pg_dump command
        # For now, create a simple backup
        if self.pg_pool:
            async with self.pg_pool.acquire() as conn:
                # Export critical tables
                tables = ['market_data', 'orders', 'positions', 'daily_pnl']
                with open(pg_backup_file, 'w') as f:
                    for table in tables:
                        rows = await conn.fetch(f"SELECT * FROM {table}")
                        f.write(f"-- Backup of {table}\n")
                        for row in rows:
                            f.write(f"-- {dict(row)}\n")
        
        # Redis backup
        redis_backup_file = f"{backup_path}/redis_backup.json"
        if self.redis_client:
            redis_data = {}
            keys = await self.redis_client.keys("*")
            for key in keys[:100]:  # Limit to prevent memory issues
                value = await self.redis_client.get(key)
                redis_data[key] = value
            
            with open(redis_backup_file, 'w') as f:
                json.dump(redis_data, f, indent=2)
        
        return backup_path
    
    async def _backup_configuration(self, snapshot_id: str) -> str:
        """Create configuration backup."""
        backup_path = f"{self.backup_base_path}/config/{snapshot_id}"
        os.makedirs(backup_path, exist_ok=True)
        
        # Copy configuration files
        config_files = [
            "config/production.yaml",
            "config/production_deployment.yaml",
            ".env.production"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, backup_path)
        
        return backup_path
    
    async def _backup_models(self, snapshot_id: str) -> str:
        """Create ML models backup."""
        backup_path = f"{self.backup_base_path}/models/{snapshot_id}"
        os.makedirs(backup_path, exist_ok=True)
        
        # Copy model files
        model_directory = "models"
        if os.path.exists(model_directory):
            shutil.copytree(model_directory, f"{backup_path}/models", dirs_exist_ok=True)
        
        return backup_path
    
    async def _capture_positions_snapshot(self) -> Dict[str, Any]:
        """Capture current positions snapshot."""
        if not self.pg_pool:
            return {}
        
        async with self.pg_pool.acquire() as conn:
            positions = await conn.fetch("""
                SELECT symbol, quantity, avg_price, current_price, 
                       unrealized_pnl, is_active
                FROM positions 
                WHERE is_active = TRUE
            """)
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "positions": [dict(pos) for pos in positions],
                "total_positions": len(positions)
            }
    
    async def _capture_orders_snapshot(self) -> List[Dict[str, Any]]:
        """Capture pending orders snapshot."""
        if not self.pg_pool:
            return []
        
        async with self.pg_pool.acquire() as conn:
            orders = await conn.fetch("""
                SELECT order_id, symbol, action, quantity, price, 
                       order_type, status, created_at
                FROM orders 
                WHERE status IN ('PENDING', 'SUBMITTED', 'PARTIALLY_FILLED')
            """)
            
            return [dict(order) for order in orders]
    
    async def _capture_system_state(self) -> Dict[str, Any]:
        """Capture current system state."""
        import psutil
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "cpu_usage": psutil.cpu_percent(),
            "memory_usage": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "process_count": len(psutil.pids()),
            "network_connections": len(psutil.net_connections()),
        }
    
    async def _save_snapshot_metadata(self, snapshot: RollbackSnapshot):
        """Save snapshot metadata to file."""
        metadata_file = f"{self.backup_base_path}/snapshots/{snapshot.snapshot_id}.json"
        
        metadata = {
            "snapshot_id": snapshot.snapshot_id,
            "timestamp": snapshot.timestamp.isoformat(),
            "version": snapshot.version,
            "database_backup_path": snapshot.database_backup_path,
            "config_backup_path": snapshot.config_backup_path,
            "model_backup_path": snapshot.model_backup_path,
            "description": snapshot.description,
            "positions_count": len(snapshot.positions_snapshot.get("positions", [])),
            "orders_count": len(snapshot.orders_snapshot)
        }
        
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    async def _load_existing_snapshots(self):
        """Load existing snapshots from disk."""
        snapshots_dir = f"{self.backup_base_path}/snapshots"
        if not os.path.exists(snapshots_dir):
            return
        
        for filename in os.listdir(snapshots_dir):
            if filename.endswith('.json'):
                try:
                    with open(f"{snapshots_dir}/{filename}", 'r') as f:
                        metadata = json.load(f)
                    
                    # Reconstruct snapshot object (simplified)
                    snapshot = RollbackSnapshot(
                        snapshot_id=metadata["snapshot_id"],
                        timestamp=datetime.fromisoformat(metadata["timestamp"]),
                        version=metadata["version"],
                        database_backup_path=metadata["database_backup_path"],
                        config_backup_path=metadata["config_backup_path"],
                        model_backup_path=metadata["model_backup_path"],
                        positions_snapshot={},  # Would load from backup
                        orders_snapshot=[],     # Would load from backup
                        system_state={},        # Would load from backup
                        description=metadata["description"]
                    )
                    
                    self.snapshots[snapshot.snapshot_id] = snapshot
                    
                except Exception as e:
                    logger.error(f"Failed to load snapshot metadata {filename}: {e}")
    
    async def execute_emergency_rollback(self, trigger: RollbackTrigger, 
                                       target_snapshot_id: Optional[str] = None) -> RollbackResult:
        """Execute emergency rollback procedure."""
        logger.critical(f"Executing emergency rollback - Trigger: {trigger.value}")
        
        # Get target snapshot
        if target_snapshot_id:
            target_snapshot = self.snapshots.get(target_snapshot_id)
        else:
            # Use most recent snapshot
            target_snapshot = self._get_most_recent_snapshot()
        
        if not target_snapshot:
            raise RuntimeError("No suitable snapshot found for rollback")
        
        # Create rollback plan
        plan = RollbackPlan(
            plan_id=f"emergency_{int(datetime.utcnow().timestamp())}",
            trigger=trigger,
            target_snapshot=target_snapshot,
            steps=[
                "stop_all_trading",
                "close_open_positions",
                "cancel_pending_orders",
                "restore_database",
                "restore_configuration",
                "restart_services",
                "validate_system",
                "notify_stakeholders"
            ],
            estimated_duration=self.rto_targets.get("critical_failure", 300),
            risk_level="high",
            requires_approval=False,  # Emergency rollback
            notification_channels=["email", "telegram", "slack"]
        )
        
        # Execute rollback
        return await self._execute_rollback_plan(plan)
    
    def _get_most_recent_snapshot(self) -> Optional[RollbackSnapshot]:
        """Get the most recent snapshot."""
        if not self.snapshots:
            return None
        
        return max(self.snapshots.values(), key=lambda s: s.timestamp)
    
    async def _execute_rollback_plan(self, plan: RollbackPlan) -> RollbackResult:
        """Execute a rollback plan."""
        start_time = datetime.utcnow()
        
        result = RollbackResult(
            plan_id=plan.plan_id,
            status=RollbackStatus.IN_PROGRESS,
            started_at=start_time
        )
        
        try:
            logger.info(f"Executing rollback plan: {plan.plan_id}")
            
            # Execute each step
            for step in plan.steps:
                try:
                    await self._execute_rollback_step(step, plan.target_snapshot)
                    result.steps_completed.append(step)
                    logger.info(f"Rollback step completed: {step}")
                    
                except Exception as e:
                    result.steps_failed.append(step)
                    logger.error(f"Rollback step failed: {step} - {e}")
                    
                    if step in ["stop_all_trading", "close_open_positions"]:
                        # Critical steps - continue with rollback
                        continue
                    else:
                        # Non-critical step failure
                        result.recovery_actions.append(f"Manual intervention required for: {step}")
            
            # Determine final status
            if not result.steps_failed:
                result.status = RollbackStatus.COMPLETED
            elif len(result.steps_failed) < len(plan.steps) / 2:
                result.status = RollbackStatus.PARTIAL
            else:
                result.status = RollbackStatus.FAILED
            
            result.completed_at = datetime.utcnow()
            result.duration = (result.completed_at - start_time).total_seconds()
            
            logger.info(f"Rollback plan execution completed: {result.status.value}")
            
            # Store result
            self.rollback_history.append(result)
            
            return result
            
        except Exception as e:
            result.status = RollbackStatus.FAILED
            result.error_message = str(e)
            result.completed_at = datetime.utcnow()
            result.duration = (result.completed_at - start_time).total_seconds()
            
            logger.error(f"Rollback plan execution failed: {e}")
            
            self.rollback_history.append(result)
            return result

    async def _execute_rollback_step(self, step: str, target_snapshot: RollbackSnapshot):
        """Execute a single rollback step."""
        if step == "stop_all_trading":
            await self._stop_all_trading()
        elif step == "close_open_positions":
            await self._close_open_positions()
        elif step == "cancel_pending_orders":
            await self._cancel_pending_orders()
        elif step == "restore_database":
            await self._restore_database(target_snapshot)
        elif step == "restore_configuration":
            await self._restore_configuration(target_snapshot)
        elif step == "restart_services":
            await self._restart_services()
        elif step == "validate_system":
            await self._validate_system()
        elif step == "notify_stakeholders":
            await self._notify_stakeholders()
        else:
            logger.warning(f"Unknown rollback step: {step}")

    async def _stop_all_trading(self):
        """Stop all trading activities."""
        logger.info("Stopping all trading activities...")

        # This would integrate with the actual trading system
        # For now, simulate the action
        await asyncio.sleep(1)

        # Set trading halt flag in Redis
        if self.redis_client:
            await self.redis_client.set("trading:halt", "true")
            await self.redis_client.set("trading:halt_reason", "emergency_rollback")
            await self.redis_client.set("trading:halt_timestamp", datetime.utcnow().isoformat())

        logger.info("All trading activities stopped")

    async def _close_open_positions(self):
        """Close all open positions."""
        logger.info("Closing all open positions...")

        if not self.pg_pool:
            return

        async with self.pg_pool.acquire() as conn:
            # Get all active positions
            positions = await conn.fetch("""
                SELECT symbol, quantity, avg_price
                FROM positions
                WHERE is_active = TRUE
            """)

            # Create market sell orders for all positions
            for position in positions:
                symbol = position['symbol']
                quantity = position['quantity']

                # Insert emergency sell order
                await conn.execute("""
                    INSERT INTO orders
                    (order_id, symbol, action, quantity, order_type, status, created_at)
                    VALUES ($1, $2, 'SELL', $3, 'MARKET', 'EMERGENCY_CLOSE', NOW())
                """, f"emergency_close_{symbol}_{int(datetime.utcnow().timestamp())}",
                    symbol, quantity)

                # Mark position as closed
                await conn.execute("""
                    UPDATE positions
                    SET is_active = FALSE, closed_at = NOW()
                    WHERE symbol = $1 AND is_active = TRUE
                """, symbol)

        logger.info("All open positions closed")

    async def _cancel_pending_orders(self):
        """Cancel all pending orders."""
        logger.info("Cancelling all pending orders...")

        if not self.pg_pool:
            return

        async with self.pg_pool.acquire() as conn:
            # Cancel all pending orders
            await conn.execute("""
                UPDATE orders
                SET status = 'CANCELLED', updated_at = NOW()
                WHERE status IN ('PENDING', 'SUBMITTED', 'PARTIALLY_FILLED')
            """)

        logger.info("All pending orders cancelled")

    async def _restore_database(self, target_snapshot: RollbackSnapshot):
        """Restore database from snapshot."""
        logger.info(f"Restoring database from snapshot: {target_snapshot.snapshot_id}")

        # In production, this would restore from actual database backups
        # For now, simulate the restoration
        await asyncio.sleep(2)

        logger.info("Database restoration completed")

    async def _restore_configuration(self, target_snapshot: RollbackSnapshot):
        """Restore configuration from snapshot."""
        logger.info(f"Restoring configuration from snapshot: {target_snapshot.snapshot_id}")

        # Copy configuration files from backup
        if os.path.exists(target_snapshot.config_backup_path):
            config_files = os.listdir(target_snapshot.config_backup_path)
            for config_file in config_files:
                src = f"{target_snapshot.config_backup_path}/{config_file}"
                dst = f"config/{config_file}"
                if os.path.exists(src):
                    shutil.copy2(src, dst)

        logger.info("Configuration restoration completed")

    async def _restart_services(self):
        """Restart system services."""
        logger.info("Restarting system services...")

        # In production, this would restart actual services
        # For now, simulate the restart
        await asyncio.sleep(3)

        logger.info("System services restarted")

    async def _validate_system(self):
        """Validate system after rollback."""
        logger.info("Validating system after rollback...")

        validation_results = []

        # Check database connectivity
        if self.pg_pool:
            try:
                async with self.pg_pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")
                validation_results.append("Database: OK")
            except Exception as e:
                validation_results.append(f"Database: ERROR - {e}")

        # Check Redis connectivity
        if self.redis_client:
            try:
                await self.redis_client.ping()
                validation_results.append("Redis: OK")
            except Exception as e:
                validation_results.append(f"Redis: ERROR - {e}")

        # Check MongoDB connectivity
        if self.mongo_client:
            try:
                await self.mongo_client.admin.command('ping')
                validation_results.append("MongoDB: OK")
            except Exception as e:
                validation_results.append(f"MongoDB: ERROR - {e}")

        # Log validation results
        for result in validation_results:
            logger.info(f"Validation: {result}")

        # Check if any critical validations failed
        errors = [r for r in validation_results if "ERROR" in r]
        if errors:
            raise RuntimeError(f"System validation failed: {errors}")

        logger.info("System validation completed successfully")

    async def _notify_stakeholders(self):
        """Notify stakeholders about rollback."""
        logger.info("Notifying stakeholders about rollback...")

        # This would integrate with notification systems
        # For now, just log the notification
        notification_message = f"""
        EMERGENCY ROLLBACK EXECUTED

        Time: {datetime.utcnow().isoformat()}
        Status: Rollback completed

        All trading activities have been halted.
        System has been restored to previous stable state.

        Please review system status before resuming operations.
        """

        logger.critical(notification_message)

        # In production, send actual notifications via:
        # - Email
        # - Slack/Teams
        # - Telegram
        # - SMS
        # - Phone calls for critical alerts

    def get_available_snapshots(self) -> List[RollbackSnapshot]:
        """Get list of available snapshots for rollback."""
        return sorted(self.snapshots.values(), key=lambda s: s.timestamp, reverse=True)

    def get_rollback_history(self) -> List[RollbackResult]:
        """Get rollback execution history."""
        return sorted(self.rollback_history, key=lambda r: r.started_at, reverse=True)

    async def cleanup_old_snapshots(self, retention_days: int = 30):
        """Clean up old snapshots beyond retention period."""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)

        snapshots_to_remove = []
        for snapshot_id, snapshot in self.snapshots.items():
            if snapshot.timestamp < cutoff_date:
                snapshots_to_remove.append(snapshot_id)

        for snapshot_id in snapshots_to_remove:
            try:
                snapshot = self.snapshots[snapshot_id]

                # Remove backup files
                if os.path.exists(snapshot.database_backup_path):
                    shutil.rmtree(snapshot.database_backup_path)
                if os.path.exists(snapshot.config_backup_path):
                    shutil.rmtree(snapshot.config_backup_path)
                if os.path.exists(snapshot.model_backup_path):
                    shutil.rmtree(snapshot.model_backup_path)

                # Remove metadata file
                metadata_file = f"{self.backup_base_path}/snapshots/{snapshot_id}.json"
                if os.path.exists(metadata_file):
                    os.remove(metadata_file)

                # Remove from memory
                del self.snapshots[snapshot_id]

                logger.info(f"Cleaned up old snapshot: {snapshot_id}")

            except Exception as e:
                logger.error(f"Failed to cleanup snapshot {snapshot_id}: {e}")

    async def test_rollback_procedures(self) -> Dict[str, Any]:
        """Test rollback procedures without actual execution."""
        logger.info("Testing rollback procedures...")

        test_results = {
            "timestamp": datetime.utcnow().isoformat(),
            "tests": [],
            "overall_status": "passed"
        }

        # Test snapshot creation
        try:
            test_snapshot = await self.create_snapshot("Test snapshot")
            test_results["tests"].append({
                "test": "snapshot_creation",
                "status": "passed",
                "details": f"Created test snapshot: {test_snapshot.snapshot_id}"
            })
        except Exception as e:
            test_results["tests"].append({
                "test": "snapshot_creation",
                "status": "failed",
                "error": str(e)
            })
            test_results["overall_status"] = "failed"

        # Test database connectivity
        try:
            if self.pg_pool:
                async with self.pg_pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")
            test_results["tests"].append({
                "test": "database_connectivity",
                "status": "passed"
            })
        except Exception as e:
            test_results["tests"].append({
                "test": "database_connectivity",
                "status": "failed",
                "error": str(e)
            })
            test_results["overall_status"] = "failed"

        # Test backup directory access
        try:
            test_file = f"{self.backup_base_path}/test_access.txt"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            test_results["tests"].append({
                "test": "backup_directory_access",
                "status": "passed"
            })
        except Exception as e:
            test_results["tests"].append({
                "test": "backup_directory_access",
                "status": "failed",
                "error": str(e)
            })
            test_results["overall_status"] = "failed"

        logger.info(f"Rollback procedure test completed: {test_results['overall_status']}")
        return test_results
