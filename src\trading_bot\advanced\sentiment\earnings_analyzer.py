"""Earnings call analysis and sentiment extraction."""

import asyncio
import aiohttp
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from textblob import TextBlob
import pandas as pd
import numpy as np

from ...core.config import settings
from ...core.logger import get_logger
from ...utils.cache import cache_manager
from ...utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


@dataclass
class EarningsCall:
    """Earnings call data structure."""
    symbol: str
    quarter: str
    year: int
    date: datetime
    transcript: str
    management_tone: Optional[float] = None
    analyst_tone: Optional[float] = None
    guidance_sentiment: Optional[float] = None
    key_metrics: Optional[Dict[str, Any]] = None
    surprises: Optional[Dict[str, float]] = None


@dataclass
class EarningsAnalysis:
    """Earnings analysis results."""
    symbol: str
    overall_sentiment: float
    management_confidence: float
    analyst_sentiment: float
    guidance_direction: str  # 'positive', 'negative', 'neutral'
    key_themes: List[str]
    risk_factors: List[str]
    growth_indicators: List[str]
    financial_health_score: float


class EarningsAnalyzer:
    """Analyze earnings calls for sentiment and key insights."""
    
    def __init__(self):
        self.rate_limiter = RateLimiter(max_requests=60, time_window=60)
        self.financial_keywords = self._load_financial_keywords()
        self.guidance_keywords = self._load_guidance_keywords()
        
    async def analyze_earnings_sentiment(
        self,
        symbols: List[str],
        quarters_back: int = 4
    ) -> Dict[str, EarningsAnalysis]:
        """
        Analyze earnings sentiment for given symbols.
        
        Args:
            symbols: List of stock symbols
            quarters_back: Number of quarters to analyze
            
        Returns:
            Dictionary mapping symbols to earnings analysis
        """
        try:
            results = {}
            
            for symbol in symbols:
                # Fetch earnings calls
                earnings_calls = await self._fetch_earnings_calls(symbol, quarters_back)
                
                if not earnings_calls:
                    logger.warning(f"No earnings calls found for {symbol}")
                    continue
                
                # Analyze each call
                analyzed_calls = []
                for call in earnings_calls:
                    analysis = await self._analyze_earnings_call(call)
                    analyzed_calls.append(analysis)
                
                # Aggregate analysis
                aggregated = self._aggregate_earnings_analysis(analyzed_calls, symbol)
                results[symbol] = aggregated
            
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing earnings sentiment: {e}")
            return {}
    
    async def _fetch_earnings_calls(
        self,
        symbol: str,
        quarters_back: int
    ) -> List[EarningsCall]:
        """Fetch earnings call transcripts."""
        calls = []
        
        # Try multiple sources
        sources = [
            self._fetch_from_alpha_vantage,
            self._fetch_from_seeking_alpha,
            self._fetch_from_earnings_call_api
        ]
        
        for source_func in sources:
            try:
                source_calls = await source_func(symbol, quarters_back)
                calls.extend(source_calls)
            except Exception as e:
                logger.warning(f"Failed to fetch from source: {e}")
                continue
        
        # Remove duplicates and sort by date
        unique_calls = {}
        for call in calls:
            key = f"{call.symbol}_{call.quarter}_{call.year}"
            if key not in unique_calls or call.date > unique_calls[key].date:
                unique_calls[key] = call
        
        return sorted(unique_calls.values(), key=lambda x: x.date, reverse=True)
    
    async def _fetch_from_alpha_vantage(
        self,
        symbol: str,
        quarters_back: int
    ) -> List[EarningsCall]:
        """Fetch earnings data from Alpha Vantage."""
        if not hasattr(settings, 'alpha_vantage_api_key'):
            return []
        
        await self.rate_limiter.acquire()
        
        try:
            url = "https://www.alphavantage.co/query"
            params = {
                'function': 'EARNINGS',
                'symbol': symbol,
                'apikey': settings.alpha_vantage_api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    data = await response.json()
            
            calls = []
            if 'quarterlyEarnings' in data:
                for i, earnings in enumerate(data['quarterlyEarnings'][:quarters_back]):
                    # Note: Alpha Vantage doesn't provide transcripts
                    # This would need to be supplemented with other sources
                    call = EarningsCall(
                        symbol=symbol,
                        quarter=earnings.get('fiscalDateEnding', ''),
                        year=int(earnings.get('fiscalDateEnding', '2023')[:4]),
                        date=datetime.strptime(earnings.get('reportedDate', '2023-01-01'), '%Y-%m-%d'),
                        transcript='',  # Would need transcript source
                        key_metrics={
                            'reported_eps': float(earnings.get('reportedEPS', 0)),
                            'estimated_eps': float(earnings.get('estimatedEPS', 0)),
                            'surprise': float(earnings.get('surprise', 0)),
                            'surprise_percentage': float(earnings.get('surprisePercentage', 0))
                        }
                    )
                    calls.append(call)
            
            return calls
            
        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage earnings: {e}")
            return []
    
    async def _fetch_from_seeking_alpha(
        self,
        symbol: str,
        quarters_back: int
    ) -> List[EarningsCall]:
        """Fetch earnings transcripts from Seeking Alpha."""
        # This would require web scraping or API access
        # Placeholder implementation
        return []
    
    async def _fetch_from_earnings_call_api(
        self,
        symbol: str,
        quarters_back: int
    ) -> List[EarningsCall]:
        """Fetch from specialized earnings call API."""
        # This would require a specialized earnings transcript API
        # Placeholder implementation
        return []
    
    async def _analyze_earnings_call(self, call: EarningsCall) -> EarningsCall:
        """Analyze a single earnings call."""
        if not call.transcript:
            # If no transcript, analyze based on metrics only
            call.management_tone = self._analyze_metrics_sentiment(call.key_metrics)
            call.analyst_tone = 0.0
            call.guidance_sentiment = 0.0
            return call
        
        # Split transcript into sections
        sections = self._split_transcript(call.transcript)
        
        # Analyze management section
        if 'management' in sections:
            call.management_tone = await self._analyze_management_tone(sections['management'])
            call.guidance_sentiment = self._analyze_guidance_sentiment(sections['management'])
        
        # Analyze Q&A section
        if 'qa' in sections:
            call.analyst_tone = await self._analyze_analyst_tone(sections['qa'])
        
        return call
    
    def _split_transcript(self, transcript: str) -> Dict[str, str]:
        """Split earnings transcript into management and Q&A sections."""
        sections = {}
        
        # Common patterns to identify sections
        qa_patterns = [
            r'question.{0,20}answer',
            r'q&a',
            r'questions.{0,20}answers',
            r'analyst.{0,20}questions'
        ]
        
        # Find Q&A section start
        qa_start = len(transcript)
        for pattern in qa_patterns:
            match = re.search(pattern, transcript, re.IGNORECASE)
            if match:
                qa_start = min(qa_start, match.start())
        
        if qa_start < len(transcript):
            sections['management'] = transcript[:qa_start]
            sections['qa'] = transcript[qa_start:]
        else:
            sections['management'] = transcript
            sections['qa'] = ''
        
        return sections
    
    async def _analyze_management_tone(self, text: str) -> float:
        """Analyze management tone and confidence."""
        try:
            # Basic sentiment analysis
            blob = TextBlob(text)
            base_sentiment = blob.sentiment.polarity
            
            # Management-specific indicators
            confidence_indicators = [
                'confident', 'optimistic', 'strong', 'growth', 'positive',
                'excited', 'pleased', 'solid', 'robust', 'healthy'
            ]
            
            concern_indicators = [
                'challenging', 'difficult', 'uncertain', 'headwinds',
                'pressure', 'concerned', 'cautious', 'volatile'
            ]
            
            text_lower = text.lower()
            
            confidence_score = sum(1 for indicator in confidence_indicators if indicator in text_lower)
            concern_score = sum(1 for indicator in concern_indicators if indicator in text_lower)
            
            # Normalize and combine
            total_indicators = confidence_score + concern_score
            if total_indicators > 0:
                management_sentiment = (confidence_score - concern_score) / total_indicators
            else:
                management_sentiment = 0
            
            # Combine with base sentiment
            final_sentiment = (base_sentiment + management_sentiment) / 2
            
            return final_sentiment
            
        except Exception as e:
            logger.error(f"Error analyzing management tone: {e}")
            return 0.0
    
    def _analyze_guidance_sentiment(self, text: str) -> float:
        """Analyze forward guidance sentiment."""
        text_lower = text.lower()
        
        positive_guidance = [
            'raise', 'increase', 'upgrade', 'improve', 'higher',
            'above', 'exceed', 'outperform', 'accelerate'
        ]
        
        negative_guidance = [
            'lower', 'reduce', 'cut', 'decrease', 'below',
            'under', 'cautious', 'conservative', 'challenging'
        ]
        
        # Look for guidance-related sentences
        guidance_sentences = []
        for sentence in text.split('.'):
            if any(word in sentence.lower() for word in ['guidance', 'outlook', 'expect', 'forecast']):
                guidance_sentences.append(sentence)
        
        if not guidance_sentences:
            return 0.0
        
        guidance_text = ' '.join(guidance_sentences).lower()
        
        positive_score = sum(1 for indicator in positive_guidance if indicator in guidance_text)
        negative_score = sum(1 for indicator in negative_guidance if indicator in guidance_text)
        
        total_indicators = positive_score + negative_score
        if total_indicators > 0:
            return (positive_score - negative_score) / total_indicators
        
        return 0.0
    
    async def _analyze_analyst_tone(self, text: str) -> float:
        """Analyze analyst questions and tone."""
        try:
            # Extract questions (usually start with analyst name or "Question:")
            questions = re.findall(r'(?:question|analyst|q:)(.*?)(?:answer|a:|$)', text, re.IGNORECASE | re.DOTALL)
            
            if not questions:
                return 0.0
            
            total_sentiment = 0
            for question in questions:
                blob = TextBlob(question)
                total_sentiment += blob.sentiment.polarity
            
            return total_sentiment / len(questions)
            
        except Exception as e:
            logger.error(f"Error analyzing analyst tone: {e}")
            return 0.0
    
    def _analyze_metrics_sentiment(self, metrics: Optional[Dict[str, Any]]) -> float:
        """Analyze sentiment based on financial metrics."""
        if not metrics:
            return 0.0
        
        sentiment = 0.0
        
        # EPS surprise
        if 'surprise_percentage' in metrics:
            surprise_pct = metrics['surprise_percentage']
            if surprise_pct > 5:
                sentiment += 0.5
            elif surprise_pct > 0:
                sentiment += 0.2
            elif surprise_pct < -5:
                sentiment -= 0.5
            elif surprise_pct < 0:
                sentiment -= 0.2
        
        # Revenue growth (if available)
        if 'revenue_growth' in metrics:
            growth = metrics['revenue_growth']
            if growth > 0.1:  # 10% growth
                sentiment += 0.3
            elif growth > 0:
                sentiment += 0.1
            elif growth < -0.05:  # -5% decline
                sentiment -= 0.3
        
        return np.tanh(sentiment)  # Normalize to [-1, 1]
    
    def _aggregate_earnings_analysis(
        self,
        calls: List[EarningsCall],
        symbol: str
    ) -> EarningsAnalysis:
        """Aggregate analysis across multiple earnings calls."""
        if not calls:
            return self._empty_analysis(symbol)
        
        # Calculate averages
        management_tones = [c.management_tone for c in calls if c.management_tone is not None]
        analyst_tones = [c.analyst_tone for c in calls if c.analyst_tone is not None]
        guidance_sentiments = [c.guidance_sentiment for c in calls if c.guidance_sentiment is not None]
        
        avg_management = np.mean(management_tones) if management_tones else 0.0
        avg_analyst = np.mean(analyst_tones) if analyst_tones else 0.0
        avg_guidance = np.mean(guidance_sentiments) if guidance_sentiments else 0.0
        
        # Overall sentiment
        overall_sentiment = (avg_management + avg_analyst + avg_guidance) / 3
        
        # Determine guidance direction
        if avg_guidance > 0.1:
            guidance_direction = 'positive'
        elif avg_guidance < -0.1:
            guidance_direction = 'negative'
        else:
            guidance_direction = 'neutral'
        
        # Extract themes (simplified)
        key_themes = ['growth', 'profitability', 'market expansion']
        risk_factors = ['competition', 'regulation', 'supply chain']
        growth_indicators = ['new products', 'market share', 'efficiency']
        
        # Financial health score (based on metrics)
        financial_health = self._calculate_financial_health(calls)
        
        return EarningsAnalysis(
            symbol=symbol,
            overall_sentiment=overall_sentiment,
            management_confidence=avg_management,
            analyst_sentiment=avg_analyst,
            guidance_direction=guidance_direction,
            key_themes=key_themes,
            risk_factors=risk_factors,
            growth_indicators=growth_indicators,
            financial_health_score=financial_health
        )
    
    def _calculate_financial_health(self, calls: List[EarningsCall]) -> float:
        """Calculate financial health score from earnings metrics."""
        if not calls:
            return 0.5
        
        health_score = 0.5  # Neutral baseline
        
        # Analyze EPS trends
        eps_surprises = []
        for call in calls:
            if call.key_metrics and 'surprise_percentage' in call.key_metrics:
                eps_surprises.append(call.key_metrics['surprise_percentage'])
        
        if eps_surprises:
            avg_surprise = np.mean(eps_surprises)
            if avg_surprise > 5:
                health_score += 0.2
            elif avg_surprise > 0:
                health_score += 0.1
            elif avg_surprise < -5:
                health_score -= 0.2
        
        return max(0, min(1, health_score))
    
    def _load_financial_keywords(self) -> Dict[str, List[str]]:
        """Load financial keywords for analysis."""
        return {
            'positive': [
                'growth', 'profit', 'revenue', 'margin', 'efficiency',
                'expansion', 'innovation', 'market share', 'competitive advantage'
            ],
            'negative': [
                'loss', 'decline', 'pressure', 'challenge', 'headwind',
                'competition', 'disruption', 'uncertainty', 'volatility'
            ]
        }
    
    def _load_guidance_keywords(self) -> Dict[str, List[str]]:
        """Load guidance-related keywords."""
        return {
            'positive': ['raise', 'increase', 'upgrade', 'improve', 'exceed'],
            'negative': ['lower', 'reduce', 'cut', 'cautious', 'conservative']
        }
    
    def _empty_analysis(self, symbol: str) -> EarningsAnalysis:
        """Return empty analysis result."""
        return EarningsAnalysis(
            symbol=symbol,
            overall_sentiment=0.0,
            management_confidence=0.0,
            analyst_sentiment=0.0,
            guidance_direction='neutral',
            key_themes=[],
            risk_factors=[],
            growth_indicators=[],
            financial_health_score=0.5
        )
