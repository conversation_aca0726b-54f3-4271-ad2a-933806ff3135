#!/usr/bin/env python3
"""
Train Full Premium ML Models
Uses ALL premium trading targets for comprehensive ML training
"""
import asyncio
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
import xgboost as xgb
import joblib
import os

class FullPremiumTrainer:
    def __init__(self):
        # 🔥 ALL PREMIUM TARGETS - Comprehensive training data
        self.premium_symbols = {
            # Major ETFs - Predictable, high-volume
            'ETF_Majors': ['SPY', 'QQQ', 'IWM', 'DIA', 'VTI', 'XLF', 'XLK', 'XLE', 'XLV'],
            
            # FAANG + Mega Tech - News-driven, institutional 
            'Mega_Tech': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NFLX', 'CRM', 'ADBE'],
            
            # AI/Semiconductor - High growth, correlated
            'AI_Chips': ['NVDA', 'AMD', 'INTC', 'TSM', 'AVGO', 'QCOM', 'MU', 'AMAT'],
            
            # Financial Giants - Economic sensitivity
            'Financials': ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'BRK-B', 'V', 'MA'],
            
            # Healthcare/Biotech - Event-driven
            'Healthcare': ['JNJ', 'PFE', 'UNH', 'ABBV', 'LLY', 'MRNA', 'BNTX', 'GILD'],
            
            # Energy - Commodity correlation
            'Energy': ['XOM', 'CVX', 'COP', 'SLB', 'EOG', 'OXY', 'HAL'],
            
            # Leveraged ETFs - Amplified signals
            'Leveraged': ['TQQQ', 'SQQQ', 'SOXL', 'SOXS', 'UPRO', 'SPXU', 'TNA', 'TZA', 'LABU', 'LABD'],
            
            # Consumer & Other
            'Consumer': ['AMZN', 'DIS', 'NKE', 'SBUX', 'MCD', 'KO', 'PEP']
        }
        
        # Flatten all symbols and remove duplicates
        all_symbols_list = []
        for category, symbols in self.premium_symbols.items():
            all_symbols_list.extend(symbols)
        self.all_symbols = list(set(all_symbols_list))  # Remove duplicates
        
        self.model_dir = "models/full_premium"
        os.makedirs(self.model_dir, exist_ok=True)
        
        print(f"🔥 Full Premium Trainer initialized")
        print(f"📊 Total unique symbols: {len(self.all_symbols)}")
        print(f"📈 Categories: {list(self.premium_symbols.keys())}")

    async def train_comprehensive_models(self, lookback_days: int = 200):
        """Train comprehensive ML models on all premium targets"""
        print(f"\n🚀 COMPREHENSIVE PREMIUM ML TRAINING")
        print("="*60)
        print(f"📅 Lookback period: {lookback_days} days")
        print(f"📊 Training on {len(self.all_symbols)} premium symbols")
        
        # Collect training data
        all_features = []
        successful_symbols = 0
        
        for i, symbol in enumerate(self.all_symbols, 1):
            try:
                print(f"📈 [{i:2d}/{len(self.all_symbols)}] {symbol:6s}...", end=" ")
                
                # Get historical data
                ticker = yf.Ticker(symbol)
                df = ticker.history(period=f"{lookback_days}d", interval="1d")
                
                if len(df) < 50:
                    print("❌ Insufficient data")
                    continue
                
                # Determine category
                category = self._get_symbol_category(symbol)
                
                # Create features
                features = self._create_comprehensive_features(df, symbol, category)
                
                if len(features) > 30:  # Minimum samples
                    all_features.append(features)
                    successful_symbols += 1
                    print(f"✅ {len(features)} samples")
                else:
                    print("❌ Too few samples")
                    
            except Exception as e:
                print(f"❌ Error: {str(e)[:50]}")
                continue
        
        if not all_features:
            print("\n❌ No training data collected!")
            return False
        
        # Combine all data
        print(f"\n📊 Combining data from {successful_symbols} symbols...")
        combined_data = pd.concat(all_features, ignore_index=True)
        
        print(f"\n🎯 TRAINING DATA SUMMARY:")
        print(f"   Successful symbols: {successful_symbols}")
        print(f"   Total samples: {len(combined_data):,}")
        
        # Show category distribution
        if 'category' in combined_data.columns:
            category_counts = combined_data['category'].value_counts()
            print(f"   Category breakdown:")
            for cat, count in category_counts.items():
                print(f"     {cat}: {count:,} samples")
        
        # Prepare features and targets
        feature_cols = [col for col in combined_data.columns 
                       if col not in ['target_1pct', 'target_2pct', 'target_3pct', 'symbol', 'category', 'date']]
        
        X = combined_data[feature_cols]
        
        # Multiple profit targets
        targets = {}
        for pct in ['1pct', '2pct', '3pct']:
            if f'target_{pct}' in combined_data.columns:
                targets[pct] = combined_data[f'target_{pct}'].astype(int)
        
        print(f"\n🎯 TARGETS ANALYSIS:")
        print(f"   Features: {len(feature_cols)}")
        for pct, y in targets.items():
            print(f"   {pct} profit rate: {y.mean()*100:.1f}%")
        
        # Train models for each target
        results = {}
        for target_name, y in targets.items():
            print(f"\n🔧 Training models for {target_name} profit target...")
            
            if y.sum() < 10:  # Need minimum positive samples
                print(f"   ⚠️ Too few positive samples ({y.sum()}) for {target_name}")
                continue
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Train ensemble
            models = self._train_model_ensemble(X_train, y_train, X_test, y_test, target_name)
            results[target_name] = models
        
        # Save everything
        if results:
            self._save_comprehensive_artifacts(feature_cols, results, combined_data)
            print(f"\n✅ COMPREHENSIVE TRAINING COMPLETE!")
            return True
        else:
            print(f"\n❌ No models successfully trained")
            return False
    
    def _get_symbol_category(self, symbol: str) -> str:
        """Determine category for a symbol"""
        for category, symbols in self.premium_symbols.items():
            if symbol in symbols:
                return category
        return 'Other'
    
    def _create_comprehensive_features(self, df: pd.DataFrame, symbol: str, category: str) -> pd.DataFrame:
        """Create comprehensive features for ML training"""
        
        # Price-based features
        df['returns_1d'] = df['Close'].pct_change()
        df['returns_3d'] = df['Close'].pct_change(3)
        df['returns_5d'] = df['Close'].pct_change(5)
        df['returns_10d'] = df['Close'].pct_change(10)
        
        # Gap analysis
        df['gap'] = (df['Open'] - df['Close'].shift(1)) / df['Close'].shift(1)
        df['gap_abs'] = df['gap'].abs()
        
        # Intraday patterns
        df['intraday_range'] = (df['High'] - df['Low']) / df['Open']
        df['open_to_close'] = (df['Close'] - df['Open']) / df['Open']
        
        # Volume analysis
        df['volume_sma_10'] = df['Volume'].rolling(10).mean()
        df['volume_sma_20'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma_20']
        df['volume_trend'] = df['volume_sma_10'] / df['volume_sma_20']
        
        # RSI multi-timeframe
        for period in [7, 14, 21]:
            df[f'rsi_{period}'] = self._calculate_rsi(df['Close'], period)
        
        # Moving averages
        for period in [5, 10, 20]:
            df[f'sma_{period}'] = df['Close'].rolling(period).mean()
            df[f'price_vs_sma_{period}'] = df['Close'] / df[f'sma_{period}'] - 1
        
        # Bollinger Bands
        sma_20 = df['Close'].rolling(20).mean()
        std_20 = df['Close'].rolling(20).std()
        df['bb_upper'] = sma_20 + 2 * std_20
        df['bb_lower'] = sma_20 - 2 * std_20
        df['bb_position'] = (df['Close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Volatility measures
        df['volatility_10d'] = df['returns_1d'].rolling(10).std()
        df['volatility_20d'] = df['returns_1d'].rolling(20).std()
        
        # ATR
        df['atr_14'] = self._calculate_atr(df, 14)
        df['atr_ratio'] = df['atr_14'] / df['Close']
        
        # Support/Resistance
        df['resistance_20'] = df['High'].rolling(20).max()
        df['support_20'] = df['Low'].rolling(20).min()
        df['support_distance'] = (df['Close'] - df['support_20']) / df['Close']
        df['resistance_distance'] = (df['resistance_20'] - df['Close']) / df['Close']
        
        # Category features
        df['is_etf'] = 1 if category in ['ETF_Majors', 'Leveraged'] else 0
        df['is_tech'] = 1 if category in ['Mega_Tech', 'AI_Chips'] else 0
        df['is_leveraged'] = 1 if category == 'Leveraged' else 0
        df['is_financial'] = 1 if category == 'Financials' else 0
        df['is_healthcare'] = 1 if category == 'Healthcare' else 0
        
        # Market regime (using volatility as proxy)
        df['high_vol_regime'] = (df['volatility_20d'] > df['volatility_20d'].rolling(50).quantile(0.8)).astype(int)
        df['low_vol_regime'] = (df['volatility_20d'] < df['volatility_20d'].rolling(50).quantile(0.2)).astype(int)
        
        # Pattern features
        df['doji'] = (df['open_to_close'].abs() < 0.005).astype(int)
        df['large_range'] = (df['intraday_range'] > 0.03).astype(int)
        df['volume_spike'] = (df['volume_ratio'] > 2.0).astype(int)
        
        # Target variables (next day returns)
        df['next_day_return'] = df['Close'].shift(-1) / df['Close'] - 1
        df['target_1pct'] = (df['next_day_return'] > 0.01).astype(int)
        df['target_2pct'] = (df['next_day_return'] > 0.02).astype(int)
        df['target_3pct'] = (df['next_day_return'] > 0.03).astype(int)
        
        # Add metadata
        df['symbol'] = symbol
        df['category'] = category
        df['date'] = df.index
        
        # Clean and return
        features_df = df.dropna()
        return features_df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        high_low = df['High'] - df['Low']
        high_close = abs(df['High'] - df['Close'].shift())
        low_close = abs(df['Low'] - df['Close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(period).mean()
    
    def _train_model_ensemble(self, X_train, y_train, X_test, y_test, target_name):
        """Train ensemble of models"""
        models = {}
        
        print(f"   🌲 Training Random Forest for {target_name}...")
        # Random Forest
        rf = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=20,
            min_samples_leaf=10,
            random_state=42,
            n_jobs=-1
        )
        rf.fit(X_train, y_train)
        
        rf_train_score = rf.score(X_train, y_train)
        rf_test_score = rf.score(X_test, y_test)
        print(f"      Random Forest - Train: {rf_train_score:.3f} | Test: {rf_test_score:.3f}")
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': X_train.columns,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"      Top 5 features: {', '.join(feature_importance.head(5)['feature'].tolist())}")
        
        models['random_forest'] = rf
        models['feature_importance'] = feature_importance
        
        # XGBoost
        print(f"   🚀 Training XGBoost for {target_name}...")
        xgb_model = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42,
            eval_metric='logloss'
        )
        xgb_model.fit(X_train, y_train)
        
        xgb_train_score = xgb_model.score(X_train, y_train)
        xgb_test_score = xgb_model.score(X_test, y_test)
        print(f"      XGBoost - Train: {xgb_train_score:.3f} | Test: {xgb_test_score:.3f}")
        
        models['xgboost'] = xgb_model
        
        # Ensemble
        rf_pred = rf.predict_proba(X_test)[:, 1]
        xgb_pred = xgb_model.predict_proba(X_test)[:, 1]
        ensemble_pred = 0.6 * rf_pred + 0.4 * xgb_pred
        ensemble_binary = (ensemble_pred > 0.5).astype(int)
        
        ensemble_accuracy = accuracy_score(y_test, ensemble_binary)
        print(f"   🎯 Ensemble accuracy: {ensemble_accuracy:.3f}")
        
        models['ensemble_weights'] = {'rf': 0.6, 'xgb': 0.4}
        models['performance'] = {
            'rf_test': rf_test_score,
            'xgb_test': xgb_test_score,
            'ensemble_test': ensemble_accuracy
        }
        
        return models
    
    def _save_comprehensive_artifacts(self, feature_cols, results, training_data):
        """Save all training artifacts"""
        print(f"\n💾 Saving comprehensive training artifacts...")
        
        # Save feature names
        joblib.dump(feature_cols, f"{self.model_dir}/feature_names.pkl")
        print(f"   ✅ Feature names saved ({len(feature_cols)} features)")
        
        # Save models for each target
        for target_name, models in results.items():
            target_dir = f"{self.model_dir}/{target_name}"
            os.makedirs(target_dir, exist_ok=True)
            
            # Save models
            joblib.dump(models['random_forest'], f"{target_dir}/random_forest.pkl")
            joblib.dump(models['xgboost'], f"{target_dir}/xgboost.pkl")
            joblib.dump(models['ensemble_weights'], f"{target_dir}/ensemble_weights.pkl")
            joblib.dump(models['performance'], f"{target_dir}/performance.pkl")
            
            # Save feature importance
            models['feature_importance'].to_csv(f"{target_dir}/feature_importance.csv", index=False)
            
            print(f"   ✅ {target_name} models saved to {target_dir}")
        
        # Save summary
        summary = {
            'training_date': datetime.now().isoformat(),
            'total_samples': len(training_data),
            'symbols_processed': len(training_data['symbol'].unique()),
            'feature_count': len(feature_cols),
            'models_trained': list(results.keys()),
            'categories': training_data['category'].value_counts().to_dict() if 'category' in training_data.columns else {}
        }
        
        joblib.dump(summary, f"{self.model_dir}/training_summary.pkl")
        
        # Save best model for easy loading
        best_target = max(results.keys(), key=lambda x: results[x]['performance']['ensemble_test'])
        best_rf = results[best_target]['random_forest']
        joblib.dump(best_rf, f"{self.model_dir}/best_model.pkl")
        
        print(f"   ✅ Best model ({best_target}) saved as best_model.pkl")
        print(f"   ✅ Training summary saved")
        print(f"\n🎯 All artifacts saved to: {self.model_dir}")

async def main():
    print("🔥 COMPREHENSIVE PREMIUM ML TRAINING")
    print("="*70)
    print("Training on ALL premium targets for maximum ML performance")
    
    trainer = FullPremiumTrainer()
    
    # Train comprehensive models
    success = await trainer.train_comprehensive_models(lookback_days=150)
    
    if success:
        print(f"\n🎯 COMPREHENSIVE TRAINING COMPLETE!")
        print(f"✅ Models saved to: {trainer.model_dir}")
        print(f"✅ Ready for production trading")
        
        # Show what was created
        print(f"\n📁 Created files:")
        for root, dirs, files in os.walk(trainer.model_dir):
            for file in files:
                print(f"   {os.path.relpath(os.path.join(root, file), trainer.model_dir)}")
    else:
        print(f"\n❌ Training failed - check data connectivity")

if __name__ == "__main__":
    asyncio.run(main())