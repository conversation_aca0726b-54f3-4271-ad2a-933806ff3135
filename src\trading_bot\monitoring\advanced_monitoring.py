"""
Advanced Monitoring with ML Anomaly Detection

This module provides sophisticated monitoring capabilities including:
- ML-powered anomaly detection
- Real-time slippage analysis
- Execution quality metrics
- Market manipulation detection
- Performance degradation alerts
"""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import aiofiles

from ..core.logger import get_logger
from ..core.config import settings
from ..data.models import Order, Trade, Position

logger = get_logger(__name__)


class AnomalyType(Enum):
    """Types of anomalies that can be detected."""
    EXECUTION_LATENCY = "execution_latency"
    SLIPPAGE_SPIKE = "slippage_spike"
    VOLUME_ANOMALY = "volume_anomaly"
    PRICE_MANIPULATION = "price_manipulation"
    SYSTEM_PERFORMANCE = "system_performance"
    TRADING_PATTERN = "trading_pattern"
    RISK_BREACH = "risk_breach"


class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AnomalyAlert:
    """Anomaly detection alert."""
    alert_id: str
    timestamp: datetime
    anomaly_type: AnomalyType
    severity: AlertSeverity
    description: str
    metrics: Dict[str, float]
    threshold_breached: float
    current_value: float
    confidence_score: float
    affected_symbols: List[str]
    recommended_actions: List[str]
    resolved: bool = False
    resolution_time: Optional[datetime] = None


@dataclass
class ExecutionMetrics:
    """Execution quality metrics."""
    symbol: str
    timestamp: datetime
    order_id: str
    intended_price: float
    executed_price: float
    slippage_bps: float
    execution_time_ms: float
    market_impact_bps: float
    fill_ratio: float
    venue: str
    order_size: float
    market_volume: float


@dataclass
class PerformanceMetrics:
    """System performance metrics."""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_io_rate: float
    network_latency_ms: float
    database_response_time_ms: float
    api_response_time_ms: float
    active_connections: int
    queue_depth: int


class AdvancedMonitoring:
    """Advanced monitoring with ML anomaly detection."""
    
    def __init__(self):
        self.is_monitoring = False
        self.anomaly_models: Dict[str, IsolationForest] = {}
        self.scalers: Dict[str, StandardScaler] = {}
        self.execution_history: List[ExecutionMetrics] = []
        self.performance_history: List[PerformanceMetrics] = []
        self.active_alerts: List[AnomalyAlert] = []
        self.alert_history: List[AnomalyAlert] = []
        
        # Monitoring configuration
        self.config = {
            'execution_window_minutes': 60,
            'performance_window_minutes': 30,
            'anomaly_threshold': 0.1,  # 10% outliers
            'min_samples_for_training': 100,
            'model_retrain_interval_hours': 24,
            'alert_cooldown_minutes': 15
        }
        
        # Alert thresholds
        self.thresholds = {
            'slippage_bps': {'medium': 10, 'high': 25, 'critical': 50},
            'execution_time_ms': {'medium': 100, 'high': 500, 'critical': 1000},
            'cpu_usage': {'medium': 70, 'high': 85, 'critical': 95},
            'memory_usage': {'medium': 75, 'high': 90, 'critical': 95},
            'database_response_ms': {'medium': 100, 'high': 500, 'critical': 1000}
        }
        
        # Last alert times for cooldown
        self.last_alert_times: Dict[str, datetime] = {}
    
    async def start_monitoring(self):
        """Start advanced monitoring."""
        if self.is_monitoring:
            logger.warning("Advanced monitoring already started")
            return
        
        self.is_monitoring = True
        logger.info("Starting advanced monitoring with ML anomaly detection...")
        
        # Start monitoring tasks
        monitoring_tasks = [
            asyncio.create_task(self._monitor_execution_quality()),
            asyncio.create_task(self._monitor_system_performance()),
            asyncio.create_task(self._detect_trading_anomalies()),
            asyncio.create_task(self._detect_market_manipulation()),
            asyncio.create_task(self._model_maintenance()),
            asyncio.create_task(self._alert_processor())
        ]
        
        logger.info("Advanced monitoring started successfully")
        
        try:
            await asyncio.gather(*monitoring_tasks)
        except Exception as e:
            logger.error(f"Advanced monitoring error: {e}")
        finally:
            self.is_monitoring = False
    
    async def stop_monitoring(self):
        """Stop advanced monitoring."""
        self.is_monitoring = False
        logger.info("Advanced monitoring stopped")
    
    async def record_execution(self, execution: ExecutionMetrics):
        """Record execution metrics for analysis."""
        self.execution_history.append(execution)
        
        # Keep only recent history
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        self.execution_history = [
            e for e in self.execution_history 
            if e.timestamp > cutoff_time
        ]
        
        # Immediate anomaly check for critical metrics
        await self._check_execution_anomalies(execution)
    
    async def record_performance(self, performance: PerformanceMetrics):
        """Record system performance metrics."""
        self.performance_history.append(performance)
        
        # Keep only recent history
        cutoff_time = datetime.utcnow() - timedelta(hours=12)
        self.performance_history = [
            p for p in self.performance_history 
            if p.timestamp > cutoff_time
        ]
        
        # Immediate performance check
        await self._check_performance_anomalies(performance)
    
    async def _monitor_execution_quality(self):
        """Monitor execution quality metrics."""
        while self.is_monitoring:
            try:
                # Analyze recent executions
                recent_executions = self._get_recent_executions(minutes=self.config['execution_window_minutes'])
                
                if len(recent_executions) >= 10:  # Need minimum data
                    await self._analyze_execution_quality(recent_executions)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in execution quality monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_system_performance(self):
        """Monitor system performance metrics."""
        while self.is_monitoring:
            try:
                # Collect current performance metrics
                # This would integrate with actual system monitoring
                current_metrics = await self._collect_system_metrics()
                await self.record_performance(current_metrics)
                
                await asyncio.sleep(15)  # Check every 15 seconds
                
            except Exception as e:
                logger.error(f"Error in system performance monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _detect_trading_anomalies(self):
        """Detect trading pattern anomalies using ML."""
        while self.is_monitoring:
            try:
                if len(self.execution_history) >= self.config['min_samples_for_training']:
                    await self._run_anomaly_detection()
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in trading anomaly detection: {e}")
                await asyncio.sleep(300)
    
    async def _detect_market_manipulation(self):
        """Detect potential market manipulation patterns."""
        while self.is_monitoring:
            try:
                # Analyze for suspicious patterns
                await self._analyze_market_patterns()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in market manipulation detection: {e}")
                await asyncio.sleep(300)
    
    async def _model_maintenance(self):
        """Maintain and retrain ML models."""
        while self.is_monitoring:
            try:
                await self._retrain_models()
                
                # Wait for next retrain cycle
                await asyncio.sleep(self.config['model_retrain_interval_hours'] * 3600)
                
            except Exception as e:
                logger.error(f"Error in model maintenance: {e}")
                await asyncio.sleep(3600)  # Retry in 1 hour
    
    async def _alert_processor(self):
        """Process and manage alerts."""
        while self.is_monitoring:
            try:
                await self._process_active_alerts()
                await self._cleanup_resolved_alerts()
                
                await asyncio.sleep(30)  # Process every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in alert processing: {e}")
                await asyncio.sleep(60)

    def _get_recent_executions(self, minutes: int) -> List[ExecutionMetrics]:
        """Get executions from the last N minutes."""
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        return [e for e in self.execution_history if e.timestamp > cutoff_time]

    async def _analyze_execution_quality(self, executions: List[ExecutionMetrics]):
        """Analyze execution quality and detect issues."""
        if not executions:
            return

        # Calculate aggregate metrics
        avg_slippage = np.mean([e.slippage_bps for e in executions])
        max_slippage = np.max([e.slippage_bps for e in executions])
        avg_execution_time = np.mean([e.execution_time_ms for e in executions])
        max_execution_time = np.max([e.execution_time_ms for e in executions])

        # Check for slippage anomalies
        if max_slippage > self.thresholds['slippage_bps']['critical']:
            await self._create_alert(
                anomaly_type=AnomalyType.SLIPPAGE_SPIKE,
                severity=AlertSeverity.CRITICAL,
                description=f"Critical slippage detected: {max_slippage:.2f} bps",
                current_value=max_slippage,
                threshold_breached=self.thresholds['slippage_bps']['critical'],
                affected_symbols=[e.symbol for e in executions if e.slippage_bps > 25]
            )
        elif avg_slippage > self.thresholds['slippage_bps']['high']:
            await self._create_alert(
                anomaly_type=AnomalyType.SLIPPAGE_SPIKE,
                severity=AlertSeverity.HIGH,
                description=f"High average slippage: {avg_slippage:.2f} bps",
                current_value=avg_slippage,
                threshold_breached=self.thresholds['slippage_bps']['high']
            )

        # Check for execution time anomalies
        if max_execution_time > self.thresholds['execution_time_ms']['critical']:
            await self._create_alert(
                anomaly_type=AnomalyType.EXECUTION_LATENCY,
                severity=AlertSeverity.CRITICAL,
                description=f"Critical execution latency: {max_execution_time:.0f}ms",
                current_value=max_execution_time,
                threshold_breached=self.thresholds['execution_time_ms']['critical']
            )

    async def _check_execution_anomalies(self, execution: ExecutionMetrics):
        """Check individual execution for immediate anomalies."""

        # Critical slippage check
        if execution.slippage_bps > self.thresholds['slippage_bps']['critical']:
            await self._create_alert(
                anomaly_type=AnomalyType.SLIPPAGE_SPIKE,
                severity=AlertSeverity.CRITICAL,
                description=f"Extreme slippage on {execution.symbol}: {execution.slippage_bps:.2f} bps",
                current_value=execution.slippage_bps,
                threshold_breached=self.thresholds['slippage_bps']['critical'],
                affected_symbols=[execution.symbol],
                recommended_actions=[
                    "Review order routing",
                    "Check market conditions",
                    "Consider reducing order size",
                    "Investigate venue performance"
                ]
            )

        # Critical execution time check
        if execution.execution_time_ms > self.thresholds['execution_time_ms']['critical']:
            await self._create_alert(
                anomaly_type=AnomalyType.EXECUTION_LATENCY,
                severity=AlertSeverity.CRITICAL,
                description=f"Extreme execution latency on {execution.symbol}: {execution.execution_time_ms:.0f}ms",
                current_value=execution.execution_time_ms,
                threshold_breached=self.thresholds['execution_time_ms']['critical'],
                affected_symbols=[execution.symbol],
                recommended_actions=[
                    "Check network connectivity",
                    "Review system performance",
                    "Investigate broker API status",
                    "Consider circuit breaker activation"
                ]
            )

    async def _check_performance_anomalies(self, performance: PerformanceMetrics):
        """Check system performance for anomalies."""

        # CPU usage check
        if performance.cpu_usage > self.thresholds['cpu_usage']['critical']:
            await self._create_alert(
                anomaly_type=AnomalyType.SYSTEM_PERFORMANCE,
                severity=AlertSeverity.CRITICAL,
                description=f"Critical CPU usage: {performance.cpu_usage:.1f}%",
                current_value=performance.cpu_usage,
                threshold_breached=self.thresholds['cpu_usage']['critical'],
                recommended_actions=[
                    "Scale up compute resources",
                    "Optimize algorithms",
                    "Reduce trading frequency",
                    "Investigate CPU-intensive processes"
                ]
            )

        # Memory usage check
        if performance.memory_usage > self.thresholds['memory_usage']['critical']:
            await self._create_alert(
                anomaly_type=AnomalyType.SYSTEM_PERFORMANCE,
                severity=AlertSeverity.CRITICAL,
                description=f"Critical memory usage: {performance.memory_usage:.1f}%",
                current_value=performance.memory_usage,
                threshold_breached=self.thresholds['memory_usage']['critical'],
                recommended_actions=[
                    "Scale up memory",
                    "Optimize data structures",
                    "Clear unnecessary caches",
                    "Investigate memory leaks"
                ]
            )

        # Database response time check
        if performance.database_response_time_ms > self.thresholds['database_response_ms']['critical']:
            await self._create_alert(
                anomaly_type=AnomalyType.SYSTEM_PERFORMANCE,
                severity=AlertSeverity.CRITICAL,
                description=f"Critical database latency: {performance.database_response_time_ms:.0f}ms",
                current_value=performance.database_response_time_ms,
                threshold_breached=self.thresholds['database_response_ms']['critical'],
                recommended_actions=[
                    "Optimize database queries",
                    "Scale database resources",
                    "Check database connections",
                    "Review indexing strategy"
                ]
            )

    async def _collect_system_metrics(self) -> PerformanceMetrics:
        """Collect current system performance metrics."""
        import psutil

        # Get system metrics
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk_io = psutil.disk_io_counters()

        # Simulate network and database metrics (would be real in production)
        network_latency = 5.0  # ms
        db_response_time = 10.0  # ms
        api_response_time = 15.0  # ms
        active_connections = 25
        queue_depth = 5

        return PerformanceMetrics(
            timestamp=datetime.utcnow(),
            cpu_usage=cpu_usage,
            memory_usage=memory.percent,
            disk_io_rate=disk_io.read_bytes + disk_io.write_bytes if disk_io else 0,
            network_latency_ms=network_latency,
            database_response_time_ms=db_response_time,
            api_response_time_ms=api_response_time,
            active_connections=active_connections,
            queue_depth=queue_depth
        )
