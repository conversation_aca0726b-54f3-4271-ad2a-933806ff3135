"""Legacy Webull API client - DEPRECATED.

This module is deprecated. Use the new unified API framework instead:

from trading_bot.api import WebullAPI

# Old way (deprecated)
# client = WebullClient()

# New way (recommended)
api = WebullAPI()
"""

import warnings
from typing import Any, Dict, List, Optional

from .webull_api import WebullAPI

# Issue deprecation warning
warnings.warn(
    "WebullClient is deprecated. Use WebullAPI instead.",
    DeprecationWarning,
    stacklevel=2
)


class WebullClient:
    """
    Legacy Webull API client - DEPRECATED.

    This class is maintained for backward compatibility but is deprecated.
    Use WebullAPI instead for new code.
    """

    def __init__(self):
        """Initialize legacy client with new unified API."""
        warnings.warn(
            "WebullClient is deprecated. Use WebullAPI instead.",
            DeprecationWarning,
            stacklevel=2
        )
        self._api = WebullAPI()
        self.session = None  # For compatibility
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self._api.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._api.close()

    async def start_session(self):
        """Start the HTTP session - compatibility method."""
        await self._api.initialize()

    async def close_session(self):
        """Close the HTTP session - compatibility method."""
        await self._api.close()
    
    # Legacy compatibility methods
    async def login(self, username: str, password: str) -> bool:
        """Login to Webull - compatibility method."""
        return await self._api.login(username, password)

    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information - compatibility method."""
        account_summary = await self._api.get_account_summary()
        return {
            "accountId": account_summary.account.id,
            "totalValue": float(account_summary.balance.total_value),
            "cash": float(account_summary.balance.cash),
            "buyingPower": float(account_summary.balance.buying_power),
        }

    async def get_positions(self) -> List[Dict[str, Any]]:
        """Get current positions - compatibility method."""
        positions = await self._api.get_positions()
        return [
            {
                "symbol": pos.symbol,
                "quantity": pos.quantity,
                "avgPrice": float(pos.avg_price),
                "currentPrice": float(pos.current_price) if pos.current_price else None,
                "unrealizedPnl": float(pos.unrealized_pnl) if pos.unrealized_pnl else None,
            }
            for pos in positions
        ]
    
    async def get_orders(self, status: str = "all") -> List[Dict[str, Any]]:
        """Get orders - compatibility method."""
        from ..models.enums import OrderStatus

        order_status = None
        if status != "all":
            try:
                order_status = OrderStatus(status.upper())
            except ValueError:
                pass

        orders = await self._api.get_orders(order_status)
        return [
            {
                "id": order.id,
                "symbol": order.symbol,
                "side": order.side.value,
                "orderType": order.order_type.value,
                "quantity": order.quantity,
                "price": float(order.price) if order.price else None,
                "status": order.status.value,
                "filledQuantity": order.filled_quantity,
            }
            for order in orders
        ]

    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get real-time quote - compatibility method."""
        quote = await self._api.get_quote(symbol)
        return {
            "symbol": quote.symbol,
            "price": float(quote.price),
            "bid": float(quote.bid) if quote.bid else None,
            "ask": float(quote.ask) if quote.ask else None,
            "volume": quote.volume,
        }
    
    async def get_bars(
        self,
        symbol: str,
        interval: str = "1m",
        count: int = 100,
        extend_trading: bool = False,
    ) -> List[Dict[str, Any]]:
        """Get historical bars - compatibility method."""
        from ..models.enums import Interval

        try:
            interval_enum = Interval(interval)
        except ValueError:
            interval_enum = Interval.ONE_MINUTE

        bars = await self._api.get_bars(symbol, interval_enum, count, extend_trading)
        return [
            {
                "timestamp": bar.timestamp.timestamp(),
                "open": float(bar.open),
                "high": float(bar.high),
                "low": float(bar.low),
                "close": float(bar.close),
                "volume": bar.volume,
            }
            for bar in bars
        ]
    
    async def place_order(
        self,
        symbol: str,
        side: str,  # "BUY" or "SELL"
        order_type: str,  # "MKT", "LMT", "STP", "STP_LMT"
        quantity: int,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        time_in_force: str = "DAY",
    ) -> Dict[str, Any]:
        """Place an order - compatibility method."""
        from ..models.enums import OrderSide, OrderType, TimeInForce
        from ..models.orders import OrderRequest
        from decimal import Decimal

        # Convert to new enums
        order_side = OrderSide(side.upper())
        order_type_enum = OrderType(order_type.upper())
        tif = TimeInForce(time_in_force.upper())

        # Create order request
        order_request = OrderRequest(
            symbol=symbol,
            side=order_side,
            order_type=order_type_enum,
            quantity=quantity,
            price=Decimal(str(price)) if price else None,
            stop_price=Decimal(str(stop_price)) if stop_price else None,
            time_in_force=tif,
        )

        order = await self._api.place_order(order_request)
        return order.to_dict()

    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order - compatibility method."""
        success = await self._api.cancel_order(order_id)
        return {"success": success}

    async def search_stocks(self, query: str) -> List[Dict[str, Any]]:
        """Search for stocks - compatibility method."""
        symbols = await self._api.search_stocks(query)
        return [
            {
                "symbol": symbol.symbol,
                "name": symbol.name,
                "exchange": symbol.exchange,
            }
            for symbol in symbols
        ]
    
    async def get_watchlist(self) -> List[Dict[str, Any]]:
        """Get watchlist - compatibility method."""
        # This would need to be implemented in the new API
        # For now, return empty list
        return []

    @property
    def access_token(self) -> Optional[str]:
        """Get access token - compatibility property."""
        return self._api.client.access_token

    @property
    def is_authenticated(self) -> bool:
        """Check if authenticated - compatibility property."""
        return self._api.is_authenticated
