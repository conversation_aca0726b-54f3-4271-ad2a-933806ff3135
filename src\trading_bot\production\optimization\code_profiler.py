"""
Code profiling and performance analysis for AI Trading Bot.

This module provides comprehensive performance profiling to identify
bottlenecks and optimize code for meeting strict latency requirements.
"""

import asyncio
import cProfile
import io
import logging
import pstats
import time
import tracemalloc
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
import psutil
import line_profiler

from ...utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class FunctionProfile:
    """Profile data for a single function."""
    function_name: str
    total_time: float
    cumulative_time: float
    call_count: int
    time_per_call: float
    memory_usage: float
    filename: str
    line_number: int


@dataclass
class ProfileResult:
    """Comprehensive profiling result."""
    profile_name: str
    total_duration: float
    peak_memory_mb: float
    function_profiles: List[FunctionProfile] = field(default_factory=list)
    hotspots: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    @property
    def top_time_consumers(self) -> List[FunctionProfile]:
        """Get top 10 time-consuming functions."""
        return sorted(self.function_profiles, key=lambda x: x.total_time, reverse=True)[:10]
    
    @property
    def top_memory_consumers(self) -> List[FunctionProfile]:
        """Get top 10 memory-consuming functions."""
        return sorted(self.function_profiles, key=lambda x: x.memory_usage, reverse=True)[:10]


class CodeProfiler:
    """
    Comprehensive code profiler for trading bot optimization.
    
    Provides multiple profiling capabilities:
    - CPU profiling with cProfile
    - Memory profiling with tracemalloc
    - Line-by-line profiling
    - Async operation profiling
    - Real-time performance monitoring
    - Bottleneck identification
    - Optimization recommendations
    """
    
    def __init__(self):
        self.profiler = None
        self.line_profiler = None
        self.memory_tracker = None
        self.active_profiles = {}
        
        # Performance targets (in milliseconds)
        self.performance_targets = {
            'market_data_processing': 5,
            'feature_calculation': 10,
            'ml_inference': 50,
            'order_execution': 100,
            'risk_checks': 20,
            'total_loop_time': 200
        }
    
    @contextmanager
    def profile_function(self, function_name: str):
        """Context manager for profiling a specific function."""
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        # Start memory tracking
        tracemalloc.start()
        
        try:
            yield
        finally:
            # Stop memory tracking
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            duration = (end_time - start_time) * 1000  # Convert to milliseconds
            memory_delta = end_memory - start_memory
            
            # Log performance
            target = self.performance_targets.get(function_name, 1000)
            if duration > target:
                logger.warning(f"{function_name} took {duration:.2f}ms (target: {target}ms)")
            else:
                logger.debug(f"{function_name} took {duration:.2f}ms")
            
            # Store profile data
            self.active_profiles[function_name] = {
                'duration': duration,
                'memory_delta': memory_delta,
                'peak_memory': peak / 1024 / 1024,  # Convert to MB
                'timestamp': datetime.utcnow()
            }
    
    async def profile_async_function(self, func: Callable, *args, **kwargs) -> Tuple[Any, Dict[str, float]]:
        """Profile an async function and return result with metrics."""
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        # Start memory tracking
        tracemalloc.start()
        
        try:
            result = await func(*args, **kwargs)
            
            # Stop memory tracking
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            metrics = {
                'duration_ms': (end_time - start_time) * 1000,
                'memory_delta_mb': (end_memory - start_memory) / 1024 / 1024,
                'peak_memory_mb': peak / 1024 / 1024
            }
            
            return result, metrics
            
        except Exception as e:
            tracemalloc.stop()
            raise e
    
    def start_comprehensive_profiling(self):
        """Start comprehensive CPU and memory profiling."""
        # Start CPU profiling
        self.profiler = cProfile.Profile()
        self.profiler.enable()
        
        # Start line profiling
        self.line_profiler = line_profiler.LineProfiler()
        
        # Start memory tracking
        tracemalloc.start()
        
        logger.info("Comprehensive profiling started")
    
    def stop_comprehensive_profiling(self) -> ProfileResult:
        """Stop profiling and generate comprehensive report."""
        if not self.profiler:
            raise RuntimeError("Profiling not started")
        
        # Stop CPU profiling
        self.profiler.disable()
        
        # Stop memory tracking
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # Generate profile report
        profile_result = self._generate_profile_report(peak)
        
        # Reset profilers
        self.profiler = None
        self.line_profiler = None
        
        logger.info("Comprehensive profiling stopped")
        return profile_result
    
    def profile_trading_loop(self, loop_function: Callable) -> ProfileResult:
        """Profile the main trading loop for optimization."""
        logger.info("Starting trading loop profiling...")
        
        # Start profiling
        self.start_comprehensive_profiling()
        
        start_time = time.time()
        
        try:
            # Run the trading loop
            if asyncio.iscoroutinefunction(loop_function):
                asyncio.run(loop_function())
            else:
                loop_function()
            
            duration = time.time() - start_time
            
            # Stop profiling and get results
            result = self.stop_comprehensive_profiling()
            result.profile_name = "Trading Loop Profile"
            result.total_duration = duration
            
            # Add trading-specific analysis
            self._analyze_trading_performance(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Trading loop profiling failed: {e}")
            if self.profiler:
                self.stop_comprehensive_profiling()
            raise
    
    def profile_component_performance(self, component_name: str, 
                                    operations: List[Callable]) -> ProfileResult:
        """Profile specific component performance."""
        logger.info(f"Profiling {component_name} performance...")
        
        function_profiles = []
        total_duration = 0
        peak_memory = 0
        
        for operation in operations:
            with self.profile_function(operation.__name__):
                if asyncio.iscoroutinefunction(operation):
                    asyncio.run(operation())
                else:
                    operation()
            
            # Get profile data
            profile_data = self.active_profiles.get(operation.__name__, {})
            duration = profile_data.get('duration', 0)
            memory = profile_data.get('peak_memory', 0)
            
            function_profiles.append(FunctionProfile(
                function_name=operation.__name__,
                total_time=duration / 1000,  # Convert to seconds
                cumulative_time=duration / 1000,
                call_count=1,
                time_per_call=duration / 1000,
                memory_usage=memory,
                filename="",
                line_number=0
            ))
            
            total_duration += duration / 1000
            peak_memory = max(peak_memory, memory)
        
        result = ProfileResult(
            profile_name=f"{component_name} Performance Profile",
            total_duration=total_duration,
            peak_memory_mb=peak_memory,
            function_profiles=function_profiles
        )
        
        # Add component-specific recommendations
        self._add_component_recommendations(result, component_name)
        
        return result
    
    def _generate_profile_report(self, peak_memory: int) -> ProfileResult:
        """Generate comprehensive profile report from cProfile data."""
        # Capture profile stats
        s = io.StringIO()
        ps = pstats.Stats(self.profiler, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats()
        
        # Parse profile data
        function_profiles = self._parse_profile_stats(ps)
        
        # Identify hotspots
        hotspots = self._identify_hotspots(function_profiles)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(function_profiles, hotspots)
        
        return ProfileResult(
            profile_name="Comprehensive Profile",
            total_duration=0,  # Will be set by caller
            peak_memory_mb=peak_memory / 1024 / 1024,
            function_profiles=function_profiles,
            hotspots=hotspots,
            recommendations=recommendations
        )
    
    def _parse_profile_stats(self, ps: pstats.Stats) -> List[FunctionProfile]:
        """Parse cProfile stats into FunctionProfile objects."""
        function_profiles = []
        
        for func_key, (call_count, total_time, cumulative_time, callers) in ps.stats.items():
            filename, line_number, function_name = func_key
            
            function_profiles.append(FunctionProfile(
                function_name=function_name,
                total_time=total_time,
                cumulative_time=cumulative_time,
                call_count=call_count,
                time_per_call=total_time / call_count if call_count > 0 else 0,
                memory_usage=0,  # Not available from cProfile
                filename=filename,
                line_number=line_number
            ))
        
        return function_profiles
    
    def _identify_hotspots(self, function_profiles: List[FunctionProfile]) -> List[str]:
        """Identify performance hotspots."""
        hotspots = []
        
        # Sort by total time
        sorted_profiles = sorted(function_profiles, key=lambda x: x.total_time, reverse=True)
        
        # Top 5 time consumers
        for profile in sorted_profiles[:5]:
            if profile.total_time > 0.1:  # More than 100ms
                hotspots.append(f"{profile.function_name}: {profile.total_time:.3f}s")
        
        # Functions called many times
        frequent_calls = [p for p in function_profiles if p.call_count > 1000]
        for profile in frequent_calls:
            hotspots.append(f"{profile.function_name}: {profile.call_count} calls")
        
        return hotspots
    
    def _generate_recommendations(self, function_profiles: List[FunctionProfile], 
                                hotspots: List[str]) -> List[str]:
        """Generate optimization recommendations."""
        recommendations = []
        
        # Check for slow functions
        slow_functions = [p for p in function_profiles if p.time_per_call > 0.01]
        if slow_functions:
            recommendations.append("Optimize slow functions: " + 
                                 ", ".join([f.function_name for f in slow_functions[:3]]))
        
        # Check for frequent calls
        frequent_functions = [p for p in function_profiles if p.call_count > 1000]
        if frequent_functions:
            recommendations.append("Consider caching for frequently called functions: " + 
                                 ", ".join([f.function_name for f in frequent_functions[:3]]))
        
        # Check for I/O operations
        io_functions = [p for p in function_profiles if 'read' in p.function_name.lower() or 
                       'write' in p.function_name.lower() or 'request' in p.function_name.lower()]
        if io_functions:
            recommendations.append("Optimize I/O operations with async/await or connection pooling")
        
        # Memory recommendations
        recommendations.append("Consider implementing object pooling for frequently created objects")
        recommendations.append("Use generators instead of lists for large datasets")
        
        return recommendations
    
    def _analyze_trading_performance(self, result: ProfileResult):
        """Add trading-specific performance analysis."""
        # Check against performance targets
        for profile in result.function_profiles:
            func_name = profile.function_name.lower()
            
            if 'market_data' in func_name and profile.time_per_call > 0.005:
                result.recommendations.append(
                    f"Market data processing too slow: {profile.time_per_call*1000:.1f}ms (target: 5ms)"
                )
            
            if 'predict' in func_name and profile.time_per_call > 0.05:
                result.recommendations.append(
                    f"ML inference too slow: {profile.time_per_call*1000:.1f}ms (target: 50ms)"
                )
            
            if 'order' in func_name and profile.time_per_call > 0.1:
                result.recommendations.append(
                    f"Order execution too slow: {profile.time_per_call*1000:.1f}ms (target: 100ms)"
                )
    
    def _add_component_recommendations(self, result: ProfileResult, component_name: str):
        """Add component-specific optimization recommendations."""
        if component_name.lower() == 'market_data':
            result.recommendations.extend([
                "Use WebSocket connections for real-time data",
                "Implement data compression for network efficiency",
                "Cache frequently accessed market data"
            ])
        
        elif component_name.lower() == 'ml':
            result.recommendations.extend([
                "Use GPU acceleration for model inference",
                "Implement model quantization for faster inference",
                "Batch predictions when possible"
            ])
        
        elif component_name.lower() == 'database':
            result.recommendations.extend([
                "Use connection pooling",
                "Implement query result caching",
                "Optimize database indexes"
            ])
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in bytes."""
        process = psutil.Process()
        return process.memory_info().rss
