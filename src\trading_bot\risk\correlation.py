"""Asset correlation tracking and analysis."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import Bar, Symbol

logger = get_logger(__name__)


class CorrelationTracker:
    """Real-time asset correlation tracking and analysis."""
    
    def __init__(self):
        self.config = settings.risk
        self.correlation_matrix = pd.DataFrame()
        self.correlation_cache = {}
        self.last_update = None
        self.update_frequency = timedelta(hours=1)  # Update every hour
        
    async def initialize(self):
        """Initialize correlation tracker."""
        await self.update_correlation_matrix()
        logger.info("Correlation tracker initialized")
    
    async def update_correlation_matrix(
        self,
        symbols: Optional[List[str]] = None,
        lookback_days: int = 60
    ) -> pd.DataFrame:
        """
        Update correlation matrix for given symbols or all active symbols.
        
        Args:
            symbols: List of symbols to calculate correlations for
            lookback_days: Historical data lookback period
            
        Returns:
            Updated correlation matrix
        """
        try:
            if symbols is None:
                symbols = await self._get_active_symbols()
            
            if len(symbols) < 2:
                logger.warning("Need at least 2 symbols for correlation calculation")
                return pd.DataFrame()
            
            # Get returns data for all symbols
            returns_data = {}
            
            for symbol in symbols:
                symbol_returns = await self._get_symbol_returns(symbol, lookback_days)
                if not symbol_returns.empty and len(symbol_returns) >= 20:  # Minimum data points
                    returns_data[symbol] = symbol_returns
            
            if len(returns_data) < 2:
                logger.warning("Insufficient data for correlation calculation")
                return pd.DataFrame()
            
            # Create aligned DataFrame
            returns_df = pd.DataFrame(returns_data)
            returns_df = returns_df.dropna()
            
            if returns_df.empty or len(returns_df) < 20:
                logger.warning("Insufficient aligned data for correlation calculation")
                return pd.DataFrame()
            
            # Calculate correlation matrix
            self.correlation_matrix = returns_df.corr()
            self.last_update = datetime.utcnow()
            
            # Update cache
            for i, symbol1 in enumerate(self.correlation_matrix.index):
                for j, symbol2 in enumerate(self.correlation_matrix.columns):
                    if i != j:  # Don't cache self-correlation
                        key = tuple(sorted([symbol1, symbol2]))
                        self.correlation_cache[key] = self.correlation_matrix.loc[symbol1, symbol2]
            
            logger.info(f"Correlation matrix updated for {len(symbols)} symbols")
            
            return self.correlation_matrix
            
        except Exception as e:
            logger.error(f"Error updating correlation matrix: {e}")
            return pd.DataFrame()
    
    async def get_correlation(self, symbol1: str, symbol2: str, use_cache: bool = True) -> Optional[float]:
        """
        Get correlation between two symbols.
        
        Args:
            symbol1: First symbol
            symbol2: Second symbol
            use_cache: Whether to use cached correlation
            
        Returns:
            Correlation coefficient (-1 to 1)
        """
        try:
            if symbol1 == symbol2:
                return 1.0
            
            key = tuple(sorted([symbol1, symbol2]))
            
            # Check cache first
            if use_cache and key in self.correlation_cache:
                # Check if cache is recent enough
                if (self.last_update and 
                    datetime.utcnow() - self.last_update < self.update_frequency):
                    return self.correlation_cache[key]
            
            # Calculate fresh correlation
            correlation = await self._calculate_pairwise_correlation(symbol1, symbol2)
            
            if correlation is not None:
                self.correlation_cache[key] = correlation
            
            return correlation
            
        except Exception as e:
            logger.error(f"Error getting correlation for {symbol1}-{symbol2}: {e}")
            return None
    
    async def _calculate_pairwise_correlation(
        self,
        symbol1: str,
        symbol2: str,
        lookback_days: int = 60
    ) -> Optional[float]:
        """Calculate correlation between two symbols."""
        try:
            # Get returns for both symbols
            returns1 = await self._get_symbol_returns(symbol1, lookback_days)
            returns2 = await self._get_symbol_returns(symbol2, lookback_days)
            
            if returns1.empty or returns2.empty:
                return None
            
            # Align the data
            common_dates = returns1.index.intersection(returns2.index)
            if len(common_dates) < 20:  # Need at least 20 data points
                return None
            
            aligned_returns1 = returns1.loc[common_dates]
            aligned_returns2 = returns2.loc[common_dates]
            
            # Calculate correlation
            correlation = np.corrcoef(aligned_returns1, aligned_returns2)[0, 1]
            
            return float(correlation) if not np.isnan(correlation) else None
            
        except Exception as e:
            logger.error(f"Error calculating pairwise correlation: {e}")
            return None
    
    async def get_portfolio_correlations(
        self,
        positions: Dict[str, Dict],
        threshold: float = 0.7
    ) -> Dict[str, any]:
        """
        Get correlation analysis for current portfolio.
        
        Args:
            positions: Current portfolio positions
            threshold: Correlation threshold for high correlation warning
            
        Returns:
            Dictionary with correlation analysis
        """
        try:
            symbols = list(positions.keys())
            
            if len(symbols) < 2:
                return {
                    "high_correlations": [],
                    "avg_correlation": 0.0,
                    "max_correlation": 0.0,
                    "correlation_risk_score": 0.0
                }
            
            # Get all pairwise correlations
            correlations = []
            high_correlations = []
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols[i+1:], i+1):
                    correlation = await self.get_correlation(symbol1, symbol2)
                    
                    if correlation is not None:
                        correlations.append(abs(correlation))
                        
                        if abs(correlation) >= threshold:
                            high_correlations.append({
                                "symbol1": symbol1,
                                "symbol2": symbol2,
                                "correlation": correlation,
                                "position1_value": positions[symbol1]["quantity"] * positions[symbol1]["current_price"],
                                "position2_value": positions[symbol2]["quantity"] * positions[symbol2]["current_price"]
                            })
            
            # Calculate metrics
            avg_correlation = np.mean(correlations) if correlations else 0.0
            max_correlation = max(correlations) if correlations else 0.0
            
            # Calculate correlation risk score (0-1, higher is riskier)
            correlation_risk_score = self._calculate_correlation_risk_score(
                correlations, positions, high_correlations
            )
            
            return {
                "high_correlations": high_correlations,
                "avg_correlation": avg_correlation,
                "max_correlation": max_correlation,
                "correlation_risk_score": correlation_risk_score,
                "total_pairs": len(correlations)
            }
            
        except Exception as e:
            logger.error(f"Error getting portfolio correlations: {e}")
            return {
                "high_correlations": [],
                "avg_correlation": 0.0,
                "max_correlation": 0.0,
                "correlation_risk_score": 0.0
            }
    
    def _calculate_correlation_risk_score(
        self,
        correlations: List[float],
        positions: Dict[str, Dict],
        high_correlations: List[Dict]
    ) -> float:
        """Calculate overall correlation risk score for portfolio."""
        try:
            if not correlations:
                return 0.0
            
            # Base score from average correlation
            avg_correlation = np.mean(correlations)
            base_score = min(avg_correlation / 0.7, 1.0)  # Normalize to 0.7 threshold
            
            # Penalty for high correlations weighted by position sizes
            total_portfolio_value = sum(
                pos["quantity"] * pos["current_price"] 
                for pos in positions.values()
            )
            
            high_corr_penalty = 0.0
            for high_corr in high_correlations:
                # Weight by combined position size
                combined_value = high_corr["position1_value"] + high_corr["position2_value"]
                weight = combined_value / total_portfolio_value
                correlation_strength = abs(high_corr["correlation"])
                
                high_corr_penalty += weight * correlation_strength
            
            # Combine base score and penalty
            risk_score = min(base_score + high_corr_penalty, 1.0)
            
            return risk_score
            
        except Exception as e:
            logger.error(f"Error calculating correlation risk score: {e}")
            return 0.0
    
    async def get_diversification_suggestions(
        self,
        positions: Dict[str, Dict],
        target_correlation: float = 0.3
    ) -> List[Dict]:
        """
        Get suggestions for improving portfolio diversification.
        
        Args:
            positions: Current portfolio positions
            target_correlation: Target average correlation
            
        Returns:
            List of diversification suggestions
        """
        try:
            suggestions = []
            
            # Get current correlation analysis
            correlation_analysis = await self.get_portfolio_correlations(positions)
            
            if correlation_analysis["avg_correlation"] <= target_correlation:
                return suggestions  # Already well diversified
            
            # Identify highly correlated clusters
            high_correlations = correlation_analysis["high_correlations"]
            
            # Group symbols by high correlation
            correlation_clusters = self._identify_correlation_clusters(high_correlations)
            
            for cluster in correlation_clusters:
                if len(cluster) > 2:  # Only suggest for clusters with 3+ symbols
                    # Calculate total cluster value
                    cluster_value = sum(
                        positions[symbol]["quantity"] * positions[symbol]["current_price"]
                        for symbol in cluster
                    )
                    
                    # Suggest reducing largest positions in cluster
                    cluster_positions = [
                        (symbol, positions[symbol]["quantity"] * positions[symbol]["current_price"])
                        for symbol in cluster
                    ]
                    cluster_positions.sort(key=lambda x: x[1], reverse=True)
                    
                    suggestions.append({
                        "type": "reduce_correlation_cluster",
                        "cluster": cluster,
                        "cluster_value": cluster_value,
                        "suggestion": f"Consider reducing positions in highly correlated cluster: {', '.join(cluster)}",
                        "primary_reduction_target": cluster_positions[0][0],
                        "secondary_reduction_target": cluster_positions[1][0] if len(cluster_positions) > 1 else None
                    })
            
            # Suggest sector diversification
            sector_suggestions = await self._get_sector_diversification_suggestions(positions)
            suggestions.extend(sector_suggestions)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting diversification suggestions: {e}")
            return []
    
    def _identify_correlation_clusters(self, high_correlations: List[Dict]) -> List[List[str]]:
        """Identify clusters of highly correlated symbols."""
        try:
            # Build adjacency list
            adjacency = {}
            for corr in high_correlations:
                symbol1, symbol2 = corr["symbol1"], corr["symbol2"]
                
                if symbol1 not in adjacency:
                    adjacency[symbol1] = set()
                if symbol2 not in adjacency:
                    adjacency[symbol2] = set()
                
                adjacency[symbol1].add(symbol2)
                adjacency[symbol2].add(symbol1)
            
            # Find connected components (clusters)
            visited = set()
            clusters = []
            
            for symbol in adjacency:
                if symbol not in visited:
                    cluster = []
                    stack = [symbol]
                    
                    while stack:
                        current = stack.pop()
                        if current not in visited:
                            visited.add(current)
                            cluster.append(current)
                            stack.extend(adjacency[current] - visited)
                    
                    if len(cluster) > 1:
                        clusters.append(cluster)
            
            return clusters
            
        except Exception as e:
            logger.error(f"Error identifying correlation clusters: {e}")
            return []
    
    async def _get_sector_diversification_suggestions(self, positions: Dict[str, Dict]) -> List[Dict]:
        """Get sector-based diversification suggestions."""
        try:
            suggestions = []
            
            # Get sector exposure
            sector_exposures = {}
            total_portfolio_value = sum(
                pos["quantity"] * pos["current_price"] 
                for pos in positions.values()
            )
            
            async with get_postgres_session() as session:
                for symbol, position in positions.items():
                    stmt = select(Symbol.sector).where(Symbol.symbol == symbol)
                    result = await session.execute(stmt)
                    sector = result.scalar_one_or_none()
                    
                    if sector:
                        position_value = position["quantity"] * position["current_price"]
                        if sector not in sector_exposures:
                            sector_exposures[sector] = 0.0
                        sector_exposures[sector] += position_value
            
            # Check for over-concentration
            for sector, exposure in sector_exposures.items():
                exposure_pct = exposure / total_portfolio_value
                if exposure_pct > 0.4:  # More than 40% in one sector
                    suggestions.append({
                        "type": "reduce_sector_concentration",
                        "sector": sector,
                        "exposure_percentage": exposure_pct,
                        "suggestion": f"Consider reducing exposure to {sector} sector ({exposure_pct:.1%} of portfolio)"
                    })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting sector diversification suggestions: {e}")
            return []
    
    async def _get_active_symbols(self) -> List[str]:
        """Get list of active symbols from current positions."""
        try:
            async with get_postgres_session() as session:
                stmt = (
                    select(Symbol.symbol)
                    .join(Position)
                    .where(Position.status == "OPEN")
                    .distinct()
                )
                result = await session.execute(stmt)
                symbols = [row[0] for row in result.fetchall()]
                
                return symbols
                
        except Exception as e:
            logger.error(f"Error getting active symbols: {e}")
            return []
    
    async def _get_symbol_returns(self, symbol: str, lookback_days: int) -> pd.Series:
        """Get historical returns for a symbol."""
        try:
            async with get_postgres_session() as session:
                # Get symbol ID
                stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                result = await session.execute(stmt)
                symbol_id = result.scalar_one_or_none()
                
                if not symbol_id:
                    return pd.Series()
                
                # Get historical prices
                cutoff_date = datetime.utcnow() - timedelta(days=lookback_days + 10)
                
                stmt = (
                    select(Bar.timestamp, Bar.close)
                    .where(
                        and_(
                            Bar.symbol_id == symbol_id,
                            Bar.timeframe == "1d",
                            Bar.timestamp >= cutoff_date
                        )
                    )
                    .order_by(Bar.timestamp.asc())
                )
                result = await session.execute(stmt)
                price_data = [(row[0], float(row[1])) for row in result.fetchall()]
                
                if len(price_data) < 10:
                    return pd.Series()
                
                # Create price series and calculate returns
                dates = [data[0] for data in price_data]
                prices = [data[1] for data in price_data]
                price_series = pd.Series(prices, index=dates)
                
                returns = price_series.pct_change().dropna()
                
                return returns
                
        except Exception as e:
            logger.error(f"Error getting returns for {symbol}: {e}")
            return pd.Series()
