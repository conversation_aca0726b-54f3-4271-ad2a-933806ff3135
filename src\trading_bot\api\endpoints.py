"""Webull API endpoints configuration."""

from typing import Dict

from ..models.enums import APIEndpointType, RateLimitType


class WebullEndpoints:
    """Webull API endpoints configuration."""
    
    # Base URLs
    QUOTE_BASE = "https://quoteapi.webull.com/api"
    TRADE_BASE = "https://tradeapi.webull.com/api"
    USER_BASE = "https://userapi.webull.com/api"
    PASSPORT_BASE = "https://passport.webull.com/api"
    
    # WebSocket URLs
    WS_QUOTE = "wss://quoteapi.webull.com/ws"
    WS_TRADE = "wss://tradeapi.webull.com/ws"
    
    # Authentication endpoints
    AUTH_ENDPOINTS = {
        "login_challenge": "/passport/login/v5/account",
        "login_submit": "/passport/login/v5/account",
        "refresh_token": "/passport/refreshToken",
        "logout": "/passport/logout",
        "verify_pin": "/passport/verifyPin",
        "get_mfa": "/passport/getMfa",
        "verify_mfa": "/passport/verifyMfa",
    }
    
    # Quote/Market data endpoints
    QUOTE_ENDPOINTS = {
        "get_quote": "/quote/getQuote",
        "get_quotes": "/quote/getQuotes",
        "get_bars": "/quote/getBars",
        "get_option_quotes": "/quote/getOptionQuotes",
        "get_option_chain": "/quote/getOptionChain",
        "search_stocks": "/search/stocks",
        "get_market_hours": "/quote/getMarketHours",
        "get_screener": "/screener/getScreener",
        "get_movers": "/quote/getMovers",
        "get_earnings": "/quote/getEarnings",
        "get_dividends": "/quote/getDividends",
        "get_splits": "/quote/getSplits",
        "get_news": "/quote/getNews",
    }
    
    # Trading endpoints
    TRADE_ENDPOINTS = {
        "get_account": "/account/getAccountInfo",
        "get_positions": "/account/getPositions",
        "get_orders": "/account/getOrders",
        "get_order": "/account/getOrder",
        "place_order": "/trade/placeOrder",
        "modify_order": "/trade/modifyOrder",
        "cancel_order": "/trade/cancelOrder",
        "cancel_all_orders": "/trade/cancelAllOrders",
        "get_buying_power": "/account/getBuyingPower",
        "get_day_trades": "/account/getDayTrades",
        "get_portfolio": "/account/getPortfolio",
        "get_performance": "/account/getPerformance",
    }
    
    # Paper trading endpoints
    PAPER_ENDPOINTS = {
        "get_account": "/paper/account/getAccountInfo",
        "get_positions": "/paper/account/getPositions",
        "get_orders": "/paper/account/getOrders",
        "place_order": "/paper/trade/placeOrder",
        "modify_order": "/paper/trade/modifyOrder",
        "cancel_order": "/paper/trade/cancelOrder",
        "reset_account": "/paper/account/reset",
    }
    
    # User endpoints
    USER_ENDPOINTS = {
        "get_profile": "/user/getProfile",
        "get_watchlists": "/user/getWatchlists",
        "create_watchlist": "/user/createWatchlist",
        "update_watchlist": "/user/updateWatchlist",
        "delete_watchlist": "/user/deleteWatchlist",
        "get_alerts": "/user/getAlerts",
        "create_alert": "/user/createAlert",
        "delete_alert": "/user/deleteAlert",
    }
    
    @classmethod
    def get_base_url(cls, endpoint_type: APIEndpointType) -> str:
        """Get base URL for endpoint type."""
        mapping = {
            APIEndpointType.QUOTE: cls.QUOTE_BASE,
            APIEndpointType.TRADE: cls.TRADE_BASE,
            APIEndpointType.PAPER: cls.TRADE_BASE,
            APIEndpointType.USER: cls.USER_BASE,
            APIEndpointType.PASSPORT: cls.PASSPORT_BASE,
        }
        return mapping.get(endpoint_type, cls.QUOTE_BASE)
    
    @classmethod
    def get_endpoint(cls, endpoint_type: APIEndpointType, endpoint_name: str) -> str:
        """Get full endpoint URL."""
        base_url = cls.get_base_url(endpoint_type)
        
        endpoint_mapping = {
            APIEndpointType.QUOTE: cls.QUOTE_ENDPOINTS,
            APIEndpointType.TRADE: cls.TRADE_ENDPOINTS,
            APIEndpointType.PAPER: cls.PAPER_ENDPOINTS,
            APIEndpointType.USER: cls.USER_ENDPOINTS,
            APIEndpointType.PASSPORT: cls.AUTH_ENDPOINTS,
        }
        
        endpoints = endpoint_mapping.get(endpoint_type, {})
        endpoint_path = endpoints.get(endpoint_name)
        
        if not endpoint_path:
            raise ValueError(f"Unknown endpoint: {endpoint_type}.{endpoint_name}")
        
        return f"{base_url}{endpoint_path}"
    
    @classmethod
    def get_rate_limit_type(cls, endpoint_type: APIEndpointType, endpoint_name: str) -> RateLimitType:
        """Get rate limit type for endpoint."""
        # Trading operations have stricter limits
        if endpoint_type in [APIEndpointType.TRADE, APIEndpointType.PAPER]:
            if endpoint_name in ["place_order", "modify_order", "cancel_order"]:
                return RateLimitType.TRADING
        
        # Authentication has strict limits
        if endpoint_type == APIEndpointType.PASSPORT:
            return RateLimitType.AUTH
        
        # Default to data rate limit
        return RateLimitType.DATA


# Endpoint metadata for validation and documentation
ENDPOINT_METADATA = {
    # Quote endpoints
    ("QUOTE", "get_quote"): {
        "method": "GET",
        "params": ["symbol"],
        "auth_required": False,
        "cache_ttl": 5,
    },
    ("QUOTE", "get_bars"): {
        "method": "GET", 
        "params": ["symbol", "type", "count", "extendTrading"],
        "auth_required": False,
        "cache_ttl": 60,
    },
    ("QUOTE", "search_stocks"): {
        "method": "GET",
        "params": ["keyword"],
        "auth_required": False,
        "cache_ttl": 300,
    },
    
    # Trading endpoints
    ("TRADE", "place_order"): {
        "method": "POST",
        "params": ["symbol", "side", "orderType", "quantity"],
        "optional_params": ["price", "stopPrice", "timeInForce"],
        "auth_required": True,
        "cache_ttl": 0,
    },
    ("TRADE", "get_positions"): {
        "method": "GET",
        "params": [],
        "auth_required": True,
        "cache_ttl": 10,
    },
    ("TRADE", "get_orders"): {
        "method": "GET",
        "params": [],
        "optional_params": ["status", "startDate", "endDate"],
        "auth_required": True,
        "cache_ttl": 5,
    },
    
    # Paper trading endpoints
    ("PAPER", "place_order"): {
        "method": "POST",
        "params": ["symbol", "side", "orderType", "quantity"],
        "optional_params": ["price", "stopPrice", "timeInForce"],
        "auth_required": True,
        "cache_ttl": 0,
    },
    
    # Authentication endpoints
    ("PASSPORT", "login_challenge"): {
        "method": "POST",
        "params": ["account", "accountType", "deviceId"],
        "auth_required": False,
        "cache_ttl": 0,
    },
    ("PASSPORT", "login_submit"): {
        "method": "POST",
        "params": ["account", "accountType", "deviceId", "pwd"],
        "auth_required": False,
        "cache_ttl": 0,
    },
}
