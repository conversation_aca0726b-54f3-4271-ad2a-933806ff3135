# Yahoo Finance Integration for Trading Bot

This integration replaces Webull browser automation with reliable Yahoo Finance data while maintaining compatibility with all existing ML models and strategies.

## 🚀 Quick Start

### 1. Test Basic Functionality
```bash
# Test if Yahoo Finance works on your system
python test_yahoo_simple.py
```

### 2. Install Dependencies
```bash
# Install Yahoo Finance and required packages
pip install yfinance pandas numpy
```

### 3. Install Full Integration
```bash
# Run the automated installer
python scripts/install_yahoo_integration.py
```

### 4. Train ML Models
```bash
# Train your ML models with Yahoo Finance data
python scripts/train_models_yahoo.py
```

### 5. Start Trading Bot
```bash
# Start the bot with Yahoo Finance data
python src/trading_bot/main_yahoo.py
```

## 📊 What This Integration Provides

### Reliable Data Sources
- **Real-time quotes** - Current price, bid/ask, volume
- **Historical data** - OHLCV bars with multiple timeframes
- **Company information** - Market cap, sector, industry, ratios
- **Market hours** - Trading session information

### IP Protection Features
- **Rate limiting** - Automatic delays between requests
- **Human behavior simulation** - Random pauses and patterns
- **Market hours awareness** - Reduced activity outside trading hours
- **Burst protection** - Prevents overwhelming the API

### ML Integration
- **Feature engineering** - Technical indicators, market microstructure
- **Model compatibility** - Works with LSTM, XGBoost, Transformer models
- **Real-time inference** - Live predictions during trading
- **Backtesting support** - Historical data for strategy validation

## 🔧 Configuration

### Yahoo Finance Settings
```yaml
data_sources:
  yahoo_finance:
    enabled: true
    rate_limit:
      requests_per_minute: 30
      burst_protection: true
      market_hours_only: true
    
    ip_protection:
      human_behavior: true
      random_delays: true
      weekend_enabled: false
      night_mode: true
```

### Trading Configuration
```yaml
trading:
  data_source: "yahoo_finance"
  watchlist:
    - "AAPL"
    - "MSFT"
    - "GOOGL"
    - "TSLA"
    - "NVDA"
```

## 📈 Supported Features

### Time Intervals
- `1m`, `5m`, `15m`, `30m` - Intraday data
- `1h` - Hourly data
- `1d` - Daily data
- `1wk`, `1mo` - Weekly and monthly data

### Time Periods
- `1d`, `5d` - Short term
- `1mo`, `3mo`, `6mo` - Medium term
- `1y`, `2y`, `5y`, `10y` - Long term
- `ytd`, `max` - Year-to-date and maximum available

### Data Types
- **Quotes** - Real-time price information
- **Bars** - OHLCV historical data
- **Company Info** - Fundamental data
- **Market Hours** - Trading session status

## 🛡️ IP Protection

### Automatic Protection
- **Rate limiting** - 2-10 second delays between requests
- **Market hours** - Reduced activity outside 9:30 AM - 4:00 PM ET
- **Weekend mode** - Minimal requests on weekends
- **Burst detection** - Increased delays if too many requests

### Manual Controls
```python
# Check protection status
stats = data_source.ip_protection.get_protection_stats()
print(f"Requests last minute: {stats['requests_last_minute']}")

# Reset protection (if needed)
data_source.ip_protection.reset_protection()
```

## 🤖 ML Model Training

### Training Process
1. **Data Collection** - Fetch 2 years of historical data
2. **Feature Engineering** - Create technical indicators and features
3. **Model Training** - Train LSTM, XGBoost, and Transformer models
4. **Evaluation** - Test on recent data
5. **Model Saving** - Save trained models for production use

### Example Training
```python
# Train models for specific symbols
symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
results = await trainer.train_models(symbols, "2022-01-01", "2024-01-01")

# Results include accuracy, Sharpe ratio, max drawdown
for model, metrics in results.items():
    print(f"{model}: Accuracy {metrics['accuracy']:.2%}")
```

## 🔄 Migration from Webull

### What Changes
- **Data source** - Yahoo Finance instead of browser automation
- **Reliability** - No more browser crashes or detection issues
- **Speed** - Faster data retrieval
- **Maintenance** - No browser updates or captcha issues

### What Stays the Same
- **ML models** - All existing models work unchanged
- **Strategies** - All trading strategies remain compatible
- **Risk management** - Same risk controls and limits
- **Database** - Same data storage and caching
- **API** - Same REST and WebSocket interfaces

## 📋 Troubleshooting

### Common Issues

#### "No data available"
```bash
# Check if symbol is valid
python -c "import yfinance as yf; print(yf.Ticker('AAPL').info.get('symbol'))"
```

#### "Rate limit exceeded"
- Increase delays in IP protection settings
- Enable market hours only mode
- Reduce number of concurrent requests

#### "Connection timeout"
- Check internet connection
- Try different symbols
- Increase request timeout settings

### Debug Mode
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Test specific functionality
quote = await data_source.get_realtime_quote("AAPL")
print(f"Quote: {quote}")
```

## 🎯 Performance Optimization

### Caching Strategy
- **Quotes** - 5 second cache
- **Historical bars** - 5 minute cache
- **Company info** - 1 hour cache

### Request Optimization
- **Batch requests** - Group multiple symbols
- **Smart caching** - Avoid redundant API calls
- **Async processing** - Non-blocking data retrieval

## 📊 Monitoring

### Built-in Metrics
- Request count and timing
- Cache hit rates
- Error rates and types
- IP protection status

### Integration with Existing Monitoring
- Prometheus metrics
- Grafana dashboards
- Alert notifications
- Performance tracking

## 🔮 Future Enhancements

### Planned Features
- **Multiple data sources** - Combine Yahoo with other providers
- **Real-time WebSocket** - Live streaming data
- **Options data** - Options chains and Greeks
- **Crypto support** - Cryptocurrency data
- **International markets** - Global stock exchanges

### Contributing
- Report issues on GitHub
- Submit feature requests
- Contribute code improvements
- Share trading strategies

---

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Run the test script: `python test_yahoo_simple.py`
3. Check logs in the `logs/` directory
4. Review configuration in `config/yahoo_finance.yaml`

**Happy Trading! 🚀📈**
