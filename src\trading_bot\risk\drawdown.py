"""Drawdown monitoring and analysis system."""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import Portfolio

logger = get_logger(__name__)


class DrawdownMonitor:
    """Real-time drawdown monitoring and analysis."""
    
    def __init__(self):
        self.config = settings.risk
        self.current_peak = 0.0
        self.current_drawdown = 0.0
        self.max_drawdown = 0.0
        self.drawdown_start_date = None
        self.drawdown_duration = 0
        self.recovery_target = 0.0
        self.drawdown_history = []
        
    async def initialize(self, initial_portfolio_value: float):
        """Initialize drawdown monitor with starting portfolio value."""
        try:
            self.current_peak = initial_portfolio_value
            self.recovery_target = initial_portfolio_value
            
            # Load historical drawdown data
            await self._load_historical_data()
            
            logger.info(f"Drawdown monitor initialized with peak: ${self.current_peak:,.2f}")
            
        except Exception as e:
            logger.error(f"Error initializing drawdown monitor: {e}")
            raise RiskError(f"Drawdown monitor initialization failed: {e}")
    
    async def _load_historical_data(self):
        """Load historical portfolio data to calculate past drawdowns."""
        try:
            async with get_postgres_session() as session:
                # Get last 252 trading days of portfolio data
                cutoff_date = datetime.utcnow() - timedelta(days=365)
                
                stmt = (
                    select(Portfolio.timestamp, Portfolio.total_value, Portfolio.max_drawdown)
                    .where(Portfolio.timestamp >= cutoff_date)
                    .order_by(Portfolio.timestamp.asc())
                )
                result = await session.execute(stmt)
                historical_data = [(row[0], float(row[1]), float(row[2] or 0)) for row in result.fetchall()]
                
                if historical_data:
                    # Find the actual peak and max drawdown from historical data
                    values = [data[1] for data in historical_data]
                    max_historical_value = max(values)
                    
                    if max_historical_value > self.current_peak:
                        self.current_peak = max_historical_value
                    
                    # Get the latest max drawdown from database
                    latest_max_dd = historical_data[-1][2] if historical_data else 0.0
                    if latest_max_dd > self.max_drawdown:
                        self.max_drawdown = latest_max_dd
                    
                    logger.info(f"Loaded historical data: Peak=${max_historical_value:,.2f}, Max DD={latest_max_dd:.2%}")
                
        except Exception as e:
            logger.error(f"Error loading historical drawdown data: {e}")
    
    def update_drawdown(self, current_portfolio_value: float) -> Dict[str, any]:
        """
        Update drawdown metrics with current portfolio value.
        
        Args:
            current_portfolio_value: Current portfolio value
            
        Returns:
            Dictionary with updated drawdown metrics
        """
        try:
            # Update peak if new high
            if current_portfolio_value > self.current_peak:
                # New peak reached - end any current drawdown
                if self.drawdown_start_date:
                    self._record_drawdown_recovery()
                
                self.current_peak = current_portfolio_value
                self.current_drawdown = 0.0
                self.drawdown_start_date = None
                self.drawdown_duration = 0
                self.recovery_target = current_portfolio_value
                
                logger.info(f"New portfolio peak reached: ${current_portfolio_value:,.2f}")
            
            else:
                # Calculate current drawdown
                self.current_drawdown = (self.current_peak - current_portfolio_value) / self.current_peak
                
                # Start tracking drawdown if this is the beginning
                if self.drawdown_start_date is None and self.current_drawdown > 0.001:  # 0.1% threshold
                    self.drawdown_start_date = datetime.utcnow()
                    logger.info(f"Drawdown period started: {self.current_drawdown:.2%}")
                
                # Update duration if in drawdown
                if self.drawdown_start_date:
                    self.drawdown_duration = (datetime.utcnow() - self.drawdown_start_date).days
                
                # Update max drawdown if current is worse
                if self.current_drawdown > self.max_drawdown:
                    self.max_drawdown = self.current_drawdown
                    logger.warning(f"New maximum drawdown: {self.max_drawdown:.2%}")
            
            return self.get_drawdown_metrics()
            
        except Exception as e:
            logger.error(f"Error updating drawdown: {e}")
            return self.get_drawdown_metrics()
    
    def get_drawdown_metrics(self) -> Dict[str, any]:
        """Get current drawdown metrics."""
        return {
            "current_peak": self.current_peak,
            "current_drawdown": self.current_drawdown,
            "max_drawdown": self.max_drawdown,
            "drawdown_duration": self.drawdown_duration,
            "is_in_drawdown": self.drawdown_start_date is not None,
            "drawdown_start_date": self.drawdown_start_date,
            "recovery_target": self.recovery_target,
            "recovery_percentage": self._calculate_recovery_percentage()
        }
    
    def _calculate_recovery_percentage(self) -> float:
        """Calculate how much recovery is needed to reach new peak."""
        if self.current_drawdown == 0:
            return 0.0
        
        # Recovery percentage needed = drawdown / (1 - drawdown)
        recovery_needed = self.current_drawdown / (1 - self.current_drawdown)
        return recovery_needed
    
    def check_drawdown_alerts(self) -> List[Dict]:
        """
        Check for drawdown alert conditions.
        
        Returns:
            List of alert dictionaries
        """
        alerts = []
        
        try:
            # Daily drawdown alert
            if self.current_drawdown >= self.config.max_daily_drawdown:
                alerts.append({
                    "type": "daily_drawdown_breach",
                    "severity": "HIGH",
                    "message": f"Daily drawdown limit breached: {self.current_drawdown:.2%} >= {self.config.max_daily_drawdown:.2%}",
                    "current_value": self.current_drawdown,
                    "limit": self.config.max_daily_drawdown,
                    "action_required": True
                })
            
            # Weekly drawdown alert
            if self.current_drawdown >= self.config.max_weekly_drawdown:
                alerts.append({
                    "type": "weekly_drawdown_breach",
                    "severity": "CRITICAL",
                    "message": f"Weekly drawdown limit breached: {self.current_drawdown:.2%} >= {self.config.max_weekly_drawdown:.2%}",
                    "current_value": self.current_drawdown,
                    "limit": self.config.max_weekly_drawdown,
                    "action_required": True
                })
            
            # Monthly drawdown alert
            if self.current_drawdown >= self.config.max_monthly_drawdown:
                alerts.append({
                    "type": "monthly_drawdown_breach",
                    "severity": "CRITICAL",
                    "message": f"Monthly drawdown limit breached: {self.current_drawdown:.2%} >= {self.config.max_monthly_drawdown:.2%}",
                    "current_value": self.current_drawdown,
                    "limit": self.config.max_monthly_drawdown,
                    "action_required": True
                })
            
            # Duration alerts
            if self.drawdown_duration > 30:  # More than 30 days in drawdown
                alerts.append({
                    "type": "prolonged_drawdown",
                    "severity": "MEDIUM",
                    "message": f"Prolonged drawdown: {self.drawdown_duration} days in drawdown",
                    "current_value": self.drawdown_duration,
                    "limit": 30,
                    "action_required": False
                })
            
            # Warning alerts (approaching limits)
            warning_threshold = 0.8  # 80% of limit
            
            if (self.current_drawdown >= self.config.max_daily_drawdown * warning_threshold and
                self.current_drawdown < self.config.max_daily_drawdown):
                alerts.append({
                    "type": "drawdown_warning",
                    "severity": "LOW",
                    "message": f"Approaching daily drawdown limit: {self.current_drawdown:.2%}",
                    "current_value": self.current_drawdown,
                    "limit": self.config.max_daily_drawdown,
                    "action_required": False
                })
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error checking drawdown alerts: {e}")
            return []
    
    def _record_drawdown_recovery(self):
        """Record a completed drawdown period."""
        try:
            if self.drawdown_start_date:
                recovery_record = {
                    "start_date": self.drawdown_start_date,
                    "end_date": datetime.utcnow(),
                    "duration_days": self.drawdown_duration,
                    "max_drawdown": self.current_drawdown,
                    "peak_before": self.current_peak,
                    "trough_value": self.current_peak * (1 - self.current_drawdown)
                }
                
                self.drawdown_history.append(recovery_record)
                
                # Keep only last 50 drawdown periods
                if len(self.drawdown_history) > 50:
                    self.drawdown_history = self.drawdown_history[-50:]
                
                logger.info(f"Drawdown recovery recorded: {self.drawdown_duration} days, {self.current_drawdown:.2%} max")
                
        except Exception as e:
            logger.error(f"Error recording drawdown recovery: {e}")
    
    async def get_drawdown_statistics(self, lookback_days: int = 252) -> Dict[str, any]:
        """
        Get comprehensive drawdown statistics.
        
        Args:
            lookback_days: Number of days to look back for statistics
            
        Returns:
            Dictionary with drawdown statistics
        """
        try:
            # Get historical portfolio data
            async with get_postgres_session() as session:
                cutoff_date = datetime.utcnow() - timedelta(days=lookback_days)
                
                stmt = (
                    select(Portfolio.timestamp, Portfolio.total_value)
                    .where(Portfolio.timestamp >= cutoff_date)
                    .order_by(Portfolio.timestamp.asc())
                )
                result = await session.execute(stmt)
                historical_data = [(row[0], float(row[1])) for row in result.fetchall()]
                
                if len(historical_data) < 10:
                    return {"error": "Insufficient historical data"}
                
                # Calculate historical drawdowns
                values = [data[1] for data in historical_data]
                dates = [data[0] for data in historical_data]
                
                # Calculate running maximum and drawdowns
                running_max = pd.Series(values).expanding().max()
                drawdowns = (pd.Series(values) - running_max) / running_max
                
                # Find drawdown periods
                drawdown_periods = self._identify_drawdown_periods(drawdowns, dates)
                
                # Calculate statistics
                stats = {
                    "current_drawdown": self.current_drawdown,
                    "max_drawdown": abs(drawdowns.min()),
                    "avg_drawdown": abs(drawdowns[drawdowns < 0].mean()) if len(drawdowns[drawdowns < 0]) > 0 else 0.0,
                    "drawdown_frequency": len(drawdown_periods),
                    "avg_drawdown_duration": np.mean([period["duration"] for period in drawdown_periods]) if drawdown_periods else 0,
                    "max_drawdown_duration": max([period["duration"] for period in drawdown_periods]) if drawdown_periods else 0,
                    "recovery_factor": self._calculate_recovery_factor(drawdown_periods),
                    "pain_index": self._calculate_pain_index(drawdowns),
                    "ulcer_index": self._calculate_ulcer_index(drawdowns),
                    "drawdown_periods": drawdown_periods[-10:]  # Last 10 periods
                }
                
                return stats
                
        except Exception as e:
            logger.error(f"Error getting drawdown statistics: {e}")
            return {"error": str(e)}
    
    def _identify_drawdown_periods(self, drawdowns: pd.Series, dates: List[datetime]) -> List[Dict]:
        """Identify distinct drawdown periods from drawdown series."""
        periods = []
        in_drawdown = False
        start_idx = None
        
        for i, dd in enumerate(drawdowns):
            if dd < -0.001 and not in_drawdown:  # Start of drawdown (0.1% threshold)
                in_drawdown = True
                start_idx = i
            elif dd >= -0.001 and in_drawdown:  # End of drawdown
                in_drawdown = False
                if start_idx is not None:
                    period_drawdowns = drawdowns[start_idx:i+1]
                    periods.append({
                        "start_date": dates[start_idx],
                        "end_date": dates[i],
                        "duration": (dates[i] - dates[start_idx]).days,
                        "max_drawdown": abs(period_drawdowns.min()),
                        "start_index": start_idx,
                        "end_index": i
                    })
        
        # Handle case where we're still in drawdown
        if in_drawdown and start_idx is not None:
            period_drawdowns = drawdowns[start_idx:]
            periods.append({
                "start_date": dates[start_idx],
                "end_date": dates[-1],
                "duration": (dates[-1] - dates[start_idx]).days,
                "max_drawdown": abs(period_drawdowns.min()),
                "start_index": start_idx,
                "end_index": len(drawdowns) - 1,
                "ongoing": True
            })
        
        return periods
    
    def _calculate_recovery_factor(self, drawdown_periods: List[Dict]) -> float:
        """Calculate average recovery factor (how quickly drawdowns are recovered)."""
        if not drawdown_periods:
            return 0.0
        
        recovery_factors = []
        for period in drawdown_periods:
            if not period.get("ongoing", False):
                # Recovery factor = max_drawdown / duration
                if period["duration"] > 0:
                    recovery_factors.append(period["max_drawdown"] / period["duration"])
        
        return np.mean(recovery_factors) if recovery_factors else 0.0
    
    def _calculate_pain_index(self, drawdowns: pd.Series) -> float:
        """Calculate Pain Index (average of all drawdowns)."""
        negative_drawdowns = drawdowns[drawdowns < 0]
        return abs(negative_drawdowns.mean()) if len(negative_drawdowns) > 0 else 0.0
    
    def _calculate_ulcer_index(self, drawdowns: pd.Series) -> float:
        """Calculate Ulcer Index (RMS of drawdowns)."""
        negative_drawdowns = drawdowns[drawdowns < 0]
        if len(negative_drawdowns) == 0:
            return 0.0
        
        squared_drawdowns = negative_drawdowns ** 2
        mean_squared = squared_drawdowns.mean()
        return np.sqrt(mean_squared)
    
    async def save_drawdown_snapshot(self, portfolio_value: float):
        """Save current drawdown state to database."""
        try:
            async with get_postgres_session() as session:
                # Update the latest portfolio record with current drawdown info
                stmt = (
                    select(Portfolio)
                    .order_by(Portfolio.timestamp.desc())
                    .limit(1)
                )
                result = await session.execute(stmt)
                latest_portfolio = result.scalar_one_or_none()
                
                if latest_portfolio:
                    latest_portfolio.drawdown = self.current_drawdown
                    latest_portfolio.max_drawdown = self.max_drawdown
                    await session.commit()
                    
                    logger.debug(f"Drawdown snapshot saved: Current={self.current_drawdown:.2%}, Max={self.max_drawdown:.2%}")
                
        except Exception as e:
            logger.error(f"Error saving drawdown snapshot: {e}")
    
    def get_recovery_plan(self) -> Dict[str, any]:
        """Generate a recovery plan based on current drawdown."""
        if self.current_drawdown == 0:
            return {"message": "No recovery needed - at peak performance"}
        
        recovery_percentage = self._calculate_recovery_percentage()
        
        plan = {
            "current_drawdown": self.current_drawdown,
            "recovery_needed": recovery_percentage,
            "target_value": self.current_peak,
            "recommendations": []
        }
        
        # Add recommendations based on drawdown severity
        if self.current_drawdown < 0.05:  # Less than 5%
            plan["recommendations"] = [
                "Monitor positions closely",
                "Consider tightening stop losses",
                "Review recent trades for lessons"
            ]
        elif self.current_drawdown < 0.10:  # 5-10%
            plan["recommendations"] = [
                "Reduce position sizes by 25%",
                "Focus on high-probability setups only",
                "Implement stricter risk management",
                "Consider taking a trading break"
            ]
        else:  # More than 10%
            plan["recommendations"] = [
                "Reduce position sizes by 50%",
                "Stop opening new positions temporarily",
                "Review and revise trading strategy",
                "Consider professional consultation",
                "Focus on capital preservation"
            ]
        
        return plan
