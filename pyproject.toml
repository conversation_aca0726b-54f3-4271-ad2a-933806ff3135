[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-trading-bot"
version = "0.1.0"
description = "Advanced AI-powered trading system for Webull"
authors = [
    {name = "Trading Bot Developer", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business :: Financial :: Investment",
]

dependencies = [
    # Core dependencies
    "asyncio",
    "aiohttp>=3.8.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    
    # Data processing
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "polars>=0.18.0",
    "pyarrow>=12.0.0",
    "yfinance>=0.2.18",  # Yahoo Finance data
    
    # Database
    "asyncpg>=0.28.0",
    "redis>=4.5.0",
    "motor>=3.2.0",  # MongoDB async driver
    "sqlalchemy>=2.0.0",
    "alembic>=1.11.0",
    
    # Machine Learning
    "scikit-learn>=1.3.0",
    "xgboost>=1.7.0",
    "lightgbm>=4.0.0",
    "torch>=2.0.0",
    "transformers>=4.30.0",
    "stable-baselines3>=2.0.0",
    "gym>=0.26.0",
    "gymnasium>=0.28.0",
    "optuna>=3.3.0",
    "mlflow>=2.5.0",
    "joblib>=1.3.0",
    "shap>=0.42.0",
    "imbalanced-learn>=0.11.0",
    "scipy>=1.11.0",
    "textblob>=0.17.0",
    
    # Technical Analysis
    "ta-lib>=0.4.0",
    "pandas-ta>=0.3.14b",
    "yfinance>=0.2.0",
    
    # API and Web
    "fastapi>=0.100.0",
    "uvicorn>=0.22.0",
    "websockets>=11.0.0",
    "httpx>=0.24.0",
    
    # Message Queue
    "celery>=5.3.0",
    "redis>=4.5.0",
    "kombu>=5.3.0",
    
    # Monitoring and Metrics
    "prometheus-client>=0.17.0",
    "psutil>=5.9.0",
    
    # Utilities
    "click>=8.1.0",
    "rich>=13.4.0",
    "typer>=0.9.0",
    "schedule>=1.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.4.0",
    "pre-commit>=3.3.0",
]

backtest = [
    "backtrader>=1.9.76",
    "zipline-reloaded>=3.0.0",
    "vectorbt>=0.25.0",
]

visualization = [
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.15.0",
    "dash>=2.11.0",
    "streamlit>=1.25.0",
]

quantum = [
    "qiskit>=0.43.0",
    "pennylane>=0.31.0",
    "cirq>=1.1.0",
]

[project.urls]
Homepage = "https://github.com/imthebreezy247/Trading-bot"
Repository = "https://github.com/imthebreezy247/Trading-bot"
Issues = "https://github.com/imthebreezy247/Trading-bot/issues"

[project.scripts]
trading-bot = "trading_bot.cli:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["trading_bot"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "ta",
    "talib",
    "pandas_ta",
    "backtrader",
    "vectorbt",
    "yfinance",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=trading_bot",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "live: marks tests that require live market data",
]

[tool.coverage.run]
source = ["src/trading_bot"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
