"""
Strategy Scaling System

Manages the progressive addition and scaling of trading strategies
based on performance and risk diversification requirements.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import numpy as np

from ...core.logger import get_logger
from ...strategies.base_strategy import BaseStrategy
from ..continuous_improvement.performance_analyzer import PerformanceAnalyzer

logger = get_logger(__name__)

class StrategyStatus(Enum):
    """Strategy status in the scaling system"""
    DEVELOPMENT = "development"
    PAPER_TESTING = "paper_testing"
    LIVE_TESTING = "live_testing"
    PRODUCTION = "production"
    PAUSED = "paused"
    RETIRED = "retired"

@dataclass
class StrategyConfig:
    """Configuration for a strategy in the scaling system"""
    strategy_id: str
    strategy_class: str
    status: StrategyStatus
    allocation_percentage: float
    max_allocation: float
    min_performance_period: int  # days
    performance_threshold: float
    risk_budget: float
    correlation_limit: float
    added_date: datetime
    last_review_date: datetime

@dataclass
class StrategyPerformance:
    """Performance metrics for a strategy"""
    strategy_id: str
    sharpe_ratio: float
    total_return: float
    max_drawdown: float
    win_rate: float
    correlation_with_portfolio: float
    risk_contribution: float
    profit_contribution: float
    trade_count: int
    last_updated: datetime

class StrategyScaler:
    """Manages progressive strategy scaling and diversification"""
    
    def __init__(self, performance_analyzer: PerformanceAnalyzer):
        self.performance_analyzer = performance_analyzer
        self.strategies: Dict[str, StrategyConfig] = {}
        self.strategy_performance: Dict[str, StrategyPerformance] = {}
        self.total_allocation = 0.0
        self.max_strategies = 10
        self.correlation_matrix = {}
        
        # Strategy scaling thresholds
        self.scaling_thresholds = {
            StrategyStatus.PAPER_TESTING: {
                'min_days': 30,
                'min_sharpe': 1.0,
                'max_drawdown': 0.15,
                'min_trades': 20
            },
            StrategyStatus.LIVE_TESTING: {
                'min_days': 60,
                'min_sharpe': 1.2,
                'max_drawdown': 0.12,
                'min_trades': 50
            },
            StrategyStatus.PRODUCTION: {
                'min_days': 90,
                'min_sharpe': 1.5,
                'max_drawdown': 0.10,
                'min_trades': 100
            }
        }
        
    async def add_strategy(self, 
                          strategy_id: str,
                          strategy_class: str,
                          initial_allocation: float = 0.05,
                          max_allocation: float = 0.2) -> bool:
        """Add a new strategy to the scaling system"""
        
        if strategy_id in self.strategies:
            logger.warning(f"Strategy {strategy_id} already exists")
            return False
        
        if len(self.strategies) >= self.max_strategies:
            logger.warning(f"Maximum number of strategies ({self.max_strategies}) reached")
            return False
        
        # Create strategy configuration
        config = StrategyConfig(
            strategy_id=strategy_id,
            strategy_class=strategy_class,
            status=StrategyStatus.PAPER_TESTING,
            allocation_percentage=0.0,  # Start with 0 allocation
            max_allocation=max_allocation,
            min_performance_period=30,
            performance_threshold=1.0,
            risk_budget=0.1,
            correlation_limit=0.7,
            added_date=datetime.now(),
            last_review_date=datetime.now()
        )
        
        self.strategies[strategy_id] = config
        
        logger.info(f"Added strategy {strategy_id} in {StrategyStatus.PAPER_TESTING.value} mode")
        return True
    
    async def evaluate_strategy_scaling(self, strategy_id: str) -> Dict[str, Any]:
        """Evaluate if a strategy is ready for scaling"""
        
        if strategy_id not in self.strategies:
            return {'error': f'Strategy {strategy_id} not found'}
        
        config = self.strategies[strategy_id]
        current_status = config.status
        
        # Get strategy performance
        performance = await self._get_strategy_performance(strategy_id)
        
        if not performance:
            return {
                'strategy_id': strategy_id,
                'current_status': current_status.value,
                'ready_to_scale': False,
                'reason': 'Insufficient performance data'
            }
        
        # Check if ready for next stage
        next_status = self._get_next_status(current_status)
        
        if not next_status:
            return {
                'strategy_id': strategy_id,
                'current_status': current_status.value,
                'ready_to_scale': False,
                'reason': 'Already at maximum status'
            }
        
        # Evaluate criteria for next status
        criteria_met = await self._evaluate_scaling_criteria(strategy_id, next_status, performance)
        
        return {
            'strategy_id': strategy_id,
            'current_status': current_status.value,
            'next_status': next_status.value,
            'ready_to_scale': criteria_met['all_met'],
            'criteria_met': criteria_met,
            'performance': {
                'sharpe_ratio': performance.sharpe_ratio,
                'total_return': performance.total_return,
                'max_drawdown': performance.max_drawdown,
                'win_rate': performance.win_rate,
                'trade_count': performance.trade_count
            }
        }
    
    async def scale_strategy(self, strategy_id: str, force: bool = False) -> bool:
        """Scale a strategy to the next level"""
        
        evaluation = await self.evaluate_strategy_scaling(strategy_id)
        
        if 'error' in evaluation:
            logger.error(evaluation['error'])
            return False
        
        if not evaluation['ready_to_scale'] and not force:
            logger.warning(f"Strategy {strategy_id} not ready to scale: {evaluation}")
            return False
        
        config = self.strategies[strategy_id]
        old_status = config.status
        new_status = self._get_next_status(old_status)
        
        if not new_status:
            logger.warning(f"Strategy {strategy_id} already at maximum status")
            return False
        
        # Update strategy status
        config.status = new_status
        config.last_review_date = datetime.now()
        
        # Adjust allocation based on new status
        await self._adjust_strategy_allocation(strategy_id, new_status)
        
        logger.info(f"Scaled strategy {strategy_id} from {old_status.value} to {new_status.value}")
        
        # Log scaling event
        await self._log_strategy_scaling_event(strategy_id, old_status, new_status, evaluation)
        
        return True
    
    async def rebalance_allocations(self) -> Dict[str, float]:
        """Rebalance strategy allocations based on performance"""
        
        # Get current performance for all strategies
        strategy_performances = {}
        for strategy_id in self.strategies:
            performance = await self._get_strategy_performance(strategy_id)
            if performance:
                strategy_performances[strategy_id] = performance
        
        if not strategy_performances:
            logger.warning("No strategy performance data available for rebalancing")
            return {}
        
        # Calculate new allocations
        new_allocations = await self._calculate_optimal_allocations(strategy_performances)
        
        # Apply new allocations
        for strategy_id, new_allocation in new_allocations.items():
            config = self.strategies[strategy_id]
            old_allocation = config.allocation_percentage
            config.allocation_percentage = new_allocation
            
            logger.info(f"Rebalanced {strategy_id}: {old_allocation:.1%} -> {new_allocation:.1%}")
        
        return new_allocations
    
    async def monitor_strategy_performance(self):
        """Continuously monitor strategy performance"""
        
        while True:
            try:
                # Update performance metrics
                await self._update_all_strategy_performance()
                
                # Check for scaling opportunities
                for strategy_id in self.strategies:
                    evaluation = await self.evaluate_strategy_scaling(strategy_id)
                    
                    if evaluation.get('ready_to_scale', False):
                        await self.scale_strategy(strategy_id)
                
                # Check for underperforming strategies
                await self._check_underperforming_strategies()
                
                # Rebalance if needed
                if await self._should_rebalance():
                    await self.rebalance_allocations()
                
                # Sleep before next check
                await asyncio.sleep(3600)  # Check every hour
                
            except Exception as e:
                logger.error(f"Error in strategy performance monitoring: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _get_strategy_performance(self, strategy_id: str) -> Optional[StrategyPerformance]:
        """Get performance metrics for a strategy"""
        
        # This would query actual strategy performance data
        # For now, return simulated performance
        
        config = self.strategies.get(strategy_id)
        if not config:
            return None
        
        days_active = (datetime.now() - config.added_date).days
        
        if days_active < 7:  # Need at least a week of data
            return None
        
        # Simulate performance based on strategy status
        base_performance = {
            StrategyStatus.PAPER_TESTING: {'sharpe': 0.8, 'return': 0.05, 'drawdown': 0.12},
            StrategyStatus.LIVE_TESTING: {'sharpe': 1.1, 'return': 0.08, 'drawdown': 0.10},
            StrategyStatus.PRODUCTION: {'sharpe': 1.4, 'return': 0.12, 'drawdown': 0.08}
        }.get(config.status, {'sharpe': 0.5, 'return': 0.02, 'drawdown': 0.15})
        
        # Add some randomness
        noise_factor = 0.2
        
        performance = StrategyPerformance(
            strategy_id=strategy_id,
            sharpe_ratio=base_performance['sharpe'] * (1 + np.random.normal(0, noise_factor)),
            total_return=base_performance['return'] * (1 + np.random.normal(0, noise_factor)),
            max_drawdown=base_performance['drawdown'] * (1 + np.random.normal(0, noise_factor * 0.5)),
            win_rate=0.55 + np.random.normal(0, 0.1),
            correlation_with_portfolio=np.random.uniform(0.1, 0.6),
            risk_contribution=config.allocation_percentage * 0.8,
            profit_contribution=config.allocation_percentage * 1.2,
            trade_count=max(int(days_active * 2 + np.random.normal(0, 10)), 0),
            last_updated=datetime.now()
        )
        
        self.strategy_performance[strategy_id] = performance
        return performance
    
    def _get_next_status(self, current_status: StrategyStatus) -> Optional[StrategyStatus]:
        """Get the next status in the scaling progression"""
        
        progression = [
            StrategyStatus.DEVELOPMENT,
            StrategyStatus.PAPER_TESTING,
            StrategyStatus.LIVE_TESTING,
            StrategyStatus.PRODUCTION
        ]
        
        try:
            current_index = progression.index(current_status)
            if current_index < len(progression) - 1:
                return progression[current_index + 1]
        except ValueError:
            pass
        
        return None
    
    async def _evaluate_scaling_criteria(self, 
                                       strategy_id: str, 
                                       target_status: StrategyStatus,
                                       performance: StrategyPerformance) -> Dict[str, Any]:
        """Evaluate if strategy meets criteria for target status"""
        
        thresholds = self.scaling_thresholds.get(target_status, {})
        config = self.strategies[strategy_id]
        
        days_active = (datetime.now() - config.added_date).days
        
        criteria = {
            'min_days': days_active >= thresholds.get('min_days', 0),
            'min_sharpe': performance.sharpe_ratio >= thresholds.get('min_sharpe', 0),
            'max_drawdown': performance.max_drawdown <= thresholds.get('max_drawdown', 1.0),
            'min_trades': performance.trade_count >= thresholds.get('min_trades', 0),
            'correlation_check': performance.correlation_with_portfolio <= config.correlation_limit
        }
        
        criteria['all_met'] = all(criteria.values())
        
        return criteria
    
    async def _adjust_strategy_allocation(self, strategy_id: str, new_status: StrategyStatus):
        """Adjust strategy allocation based on new status"""
        
        config = self.strategies[strategy_id]
        
        # Define allocation by status
        status_allocations = {
            StrategyStatus.PAPER_TESTING: 0.0,
            StrategyStatus.LIVE_TESTING: 0.05,  # 5%
            StrategyStatus.PRODUCTION: 0.15     # 15%
        }
        
        new_allocation = min(
            status_allocations.get(new_status, 0.0),
            config.max_allocation
        )
        
        config.allocation_percentage = new_allocation
        
        # Ensure total allocation doesn't exceed 100%
        await self._normalize_allocations()
    
    async def _normalize_allocations(self):
        """Ensure total allocations don't exceed 100%"""
        
        total_allocation = sum(config.allocation_percentage for config in self.strategies.values())
        
        if total_allocation > 1.0:
            # Scale down all allocations proportionally
            scale_factor = 0.95 / total_allocation  # Leave 5% buffer
            
            for config in self.strategies.values():
                config.allocation_percentage *= scale_factor
    
    async def _calculate_optimal_allocations(self, 
                                           strategy_performances: Dict[str, StrategyPerformance]) -> Dict[str, float]:
        """Calculate optimal allocations using risk-adjusted returns"""
        
        # Simple allocation based on Sharpe ratios
        sharpe_ratios = {sid: perf.sharpe_ratio for sid, perf in strategy_performances.items()}
        
        # Only consider strategies with positive Sharpe ratios
        positive_sharpe = {sid: max(sharpe, 0.1) for sid, sharpe in sharpe_ratios.items() if sharpe > 0}
        
        if not positive_sharpe:
            return {sid: 0.0 for sid in strategy_performances}
        
        # Calculate weights based on Sharpe ratios
        total_sharpe = sum(positive_sharpe.values())
        raw_weights = {sid: sharpe / total_sharpe for sid, sharpe in positive_sharpe.items()}
        
        # Apply constraints
        final_allocations = {}
        
        for strategy_id in strategy_performances:
            config = self.strategies[strategy_id]
            
            if strategy_id in raw_weights:
                # Apply max allocation constraint
                allocation = min(raw_weights[strategy_id] * 0.8, config.max_allocation)  # 80% of total
                
                # Apply minimum allocation for production strategies
                if config.status == StrategyStatus.PRODUCTION:
                    allocation = max(allocation, 0.05)  # Minimum 5%
                
                final_allocations[strategy_id] = allocation
            else:
                final_allocations[strategy_id] = 0.0
        
        return final_allocations
    
    async def _update_all_strategy_performance(self):
        """Update performance metrics for all strategies"""
        
        for strategy_id in self.strategies:
            await self._get_strategy_performance(strategy_id)
    
    async def _check_underperforming_strategies(self):
        """Check for strategies that should be paused or retired"""
        
        for strategy_id, config in self.strategies.items():
            performance = self.strategy_performance.get(strategy_id)
            
            if not performance:
                continue
            
            # Check for poor performance
            if (performance.sharpe_ratio < 0.3 or 
                performance.max_drawdown > 0.25 or
                performance.win_rate < 0.35):
                
                if config.status != StrategyStatus.PAUSED:
                    logger.warning(f"Pausing underperforming strategy {strategy_id}")
                    config.status = StrategyStatus.PAUSED
                    config.allocation_percentage = 0.0
    
    async def _should_rebalance(self) -> bool:
        """Check if rebalancing is needed"""
        
        # Check if any strategy allocation is significantly off target
        for strategy_id, config in self.strategies.items():
            performance = self.strategy_performance.get(strategy_id)
            
            if performance and config.status == StrategyStatus.PRODUCTION:
                # If a production strategy has very high Sharpe, consider increasing allocation
                if performance.sharpe_ratio > 2.0 and config.allocation_percentage < config.max_allocation * 0.8:
                    return True
        
        return False
    
    async def _log_strategy_scaling_event(self, 
                                        strategy_id: str,
                                        old_status: StrategyStatus,
                                        new_status: StrategyStatus,
                                        evaluation: Dict):
        """Log strategy scaling events"""
        
        event = {
            'timestamp': datetime.now(),
            'strategy_id': strategy_id,
            'old_status': old_status.value,
            'new_status': new_status.value,
            'evaluation': evaluation
        }
        
        logger.info(f"Strategy scaling event: {event}")
    
    def get_strategy_summary(self) -> Dict[str, Any]:
        """Get summary of all strategies"""
        
        summary = {
            'total_strategies': len(self.strategies),
            'total_allocation': sum(config.allocation_percentage for config in self.strategies.values()),
            'strategies_by_status': {},
            'top_performers': []
        }
        
        # Count by status
        for config in self.strategies.values():
            status = config.status.value
            summary['strategies_by_status'][status] = summary['strategies_by_status'].get(status, 0) + 1
        
        # Top performers
        if self.strategy_performance:
            sorted_strategies = sorted(
                self.strategy_performance.items(),
                key=lambda x: x[1].sharpe_ratio,
                reverse=True
            )
            
            summary['top_performers'] = [
                {
                    'strategy_id': sid,
                    'sharpe_ratio': perf.sharpe_ratio,
                    'allocation': self.strategies[sid].allocation_percentage
                }
                for sid, perf in sorted_strategies[:5]
            ]
        
        return summary
