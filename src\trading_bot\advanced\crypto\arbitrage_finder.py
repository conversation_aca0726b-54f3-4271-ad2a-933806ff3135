"""Cross-exchange arbitrage detection and execution."""

import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import json

from ...core.config import settings
from ...core.logger import get_logger
from ...utils.cache import cache_manager
from ...utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


class ExchangeType(Enum):
    """Exchange types."""
    CEX = "centralized"  # Centralized Exchange
    DEX = "decentralized"  # Decentralized Exchange


@dataclass
class ExchangePrice:
    """Price data from an exchange."""
    exchange: str
    exchange_type: ExchangeType
    symbol: str
    bid: float
    ask: float
    volume_24h: float
    timestamp: datetime
    fees: float  # Trading fees as percentage


@dataclass
class ArbitrageOpportunity:
    """Arbitrage opportunity between exchanges."""
    token: str
    buy_exchange: str
    sell_exchange: str
    buy_price: float
    sell_price: float
    profit_percentage: float
    profit_usd: float
    volume_limit: float
    execution_time_estimate: int  # seconds
    gas_cost: Optional[float] = None  # For DEX arbitrage
    confidence: float = 0.0


@dataclass
class TriangularArbitrage:
    """Triangular arbitrage opportunity."""
    exchange: str
    path: List[str]  # e.g., ['BTC', 'ETH', 'USDT', 'BTC']
    profit_percentage: float
    starting_amount: float
    final_amount: float
    execution_complexity: int  # 1-10 scale


class ArbitrageFinder:
    """Find and analyze arbitrage opportunities across exchanges."""
    
    def __init__(self):
        self.rate_limiter = RateLimiter(max_requests=200, time_window=60)
        self.exchanges = self._load_exchange_configs()
        self.price_cache = {}
        self.min_profit_threshold = 0.005  # 0.5% minimum profit
        
    async def find_cross_exchange_arbitrage(
        self,
        tokens: List[str],
        min_profit: float = None,
        max_execution_time: int = 300  # 5 minutes
    ) -> List[ArbitrageOpportunity]:
        """
        Find arbitrage opportunities across different exchanges.
        
        Args:
            tokens: List of tokens to analyze
            min_profit: Minimum profit percentage
            max_execution_time: Maximum execution time in seconds
            
        Returns:
            List of arbitrage opportunities
        """
        try:
            if min_profit is None:
                min_profit = self.min_profit_threshold
            
            opportunities = []
            
            # Fetch prices from all exchanges
            all_prices = await self._fetch_all_exchange_prices(tokens)
            
            # Find arbitrage opportunities
            for token in tokens:
                token_prices = all_prices.get(token, [])
                
                if len(token_prices) < 2:
                    continue
                
                # Compare all exchange pairs
                for i, buy_price_data in enumerate(token_prices):
                    for j, sell_price_data in enumerate(token_prices):
                        if i == j:
                            continue
                        
                        opportunity = self._calculate_arbitrage_opportunity(
                            token, buy_price_data, sell_price_data
                        )
                        
                        if (opportunity and 
                            opportunity.profit_percentage >= min_profit and
                            opportunity.execution_time_estimate <= max_execution_time):
                            opportunities.append(opportunity)
            
            # Sort by profit percentage
            opportunities.sort(key=lambda x: x.profit_percentage, reverse=True)
            
            return opportunities[:20]  # Top 20 opportunities
            
        except Exception as e:
            logger.error(f"Error finding cross-exchange arbitrage: {e}")
            return []
    
    async def find_triangular_arbitrage(
        self,
        exchange: str,
        base_currencies: List[str] = None
    ) -> List[TriangularArbitrage]:
        """
        Find triangular arbitrage opportunities on a single exchange.
        
        Args:
            exchange: Exchange to analyze
            base_currencies: Base currencies to start with
            
        Returns:
            List of triangular arbitrage opportunities
        """
        try:
            if base_currencies is None:
                base_currencies = ['BTC', 'ETH', 'USDT', 'USDC']
            
            opportunities = []
            
            # Get all trading pairs for the exchange
            trading_pairs = await self._get_trading_pairs(exchange)
            
            # Build currency graph
            currency_graph = self._build_currency_graph(trading_pairs)
            
            # Find triangular paths
            for base_currency in base_currencies:
                triangular_paths = self._find_triangular_paths(
                    currency_graph, base_currency
                )
                
                for path in triangular_paths:
                    opportunity = await self._calculate_triangular_opportunity(
                        exchange, path
                    )
                    
                    if opportunity and opportunity.profit_percentage > 0.001:  # 0.1% minimum
                        opportunities.append(opportunity)
            
            # Sort by profit percentage
            opportunities.sort(key=lambda x: x.profit_percentage, reverse=True)
            
            return opportunities[:10]  # Top 10 opportunities
            
        except Exception as e:
            logger.error(f"Error finding triangular arbitrage: {e}")
            return []
    
    async def monitor_arbitrage_execution(
        self,
        opportunity: ArbitrageOpportunity,
        execution_amount: float
    ) -> Dict[str, Any]:
        """
        Monitor arbitrage execution and track performance.
        
        Args:
            opportunity: Arbitrage opportunity to execute
            execution_amount: Amount to trade
            
        Returns:
            Execution monitoring data
        """
        try:
            execution_log = {
                'opportunity': opportunity,
                'execution_amount': execution_amount,
                'start_time': datetime.now(),
                'steps': [],
                'final_profit': 0.0,
                'success': False
            }
            
            # Step 1: Buy on cheaper exchange
            buy_step = await self._simulate_trade_execution(
                opportunity.buy_exchange,
                opportunity.token,
                'buy',
                execution_amount,
                opportunity.buy_price
            )
            execution_log['steps'].append(buy_step)
            
            # Step 2: Transfer (if needed)
            if opportunity.buy_exchange != opportunity.sell_exchange:
                transfer_step = await self._simulate_transfer(
                    opportunity.token,
                    opportunity.buy_exchange,
                    opportunity.sell_exchange,
                    execution_amount
                )
                execution_log['steps'].append(transfer_step)
            
            # Step 3: Sell on more expensive exchange
            sell_step = await self._simulate_trade_execution(
                opportunity.sell_exchange,
                opportunity.token,
                'sell',
                execution_amount,
                opportunity.sell_price
            )
            execution_log['steps'].append(sell_step)
            
            # Calculate final profit
            total_costs = sum(step.get('cost', 0) for step in execution_log['steps'])
            gross_profit = execution_amount * (opportunity.sell_price - opportunity.buy_price)
            net_profit = gross_profit - total_costs
            
            execution_log['final_profit'] = net_profit
            execution_log['success'] = net_profit > 0
            execution_log['end_time'] = datetime.now()
            execution_log['execution_time'] = (
                execution_log['end_time'] - execution_log['start_time']
            ).total_seconds()
            
            return execution_log
            
        except Exception as e:
            logger.error(f"Error monitoring arbitrage execution: {e}")
            return {'error': str(e)}
    
    async def _fetch_all_exchange_prices(
        self,
        tokens: List[str]
    ) -> Dict[str, List[ExchangePrice]]:
        """Fetch prices from all configured exchanges."""
        try:
            all_prices = {}
            
            # Create tasks for all exchange/token combinations
            tasks = []
            for exchange_name, exchange_config in self.exchanges.items():
                for token in tokens:
                    tasks.append(
                        self._fetch_exchange_price(exchange_name, token, exchange_config)
                    )
            
            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Organize results by token
            for result in results:
                if isinstance(result, ExchangePrice):
                    token = result.symbol
                    if token not in all_prices:
                        all_prices[token] = []
                    all_prices[token].append(result)
                elif isinstance(result, Exception):
                    logger.warning(f"Price fetch failed: {result}")
            
            return all_prices
            
        except Exception as e:
            logger.error(f"Error fetching all exchange prices: {e}")
            return {}
    
    async def _fetch_exchange_price(
        self,
        exchange_name: str,
        token: str,
        exchange_config: Dict[str, Any]
    ) -> Optional[ExchangePrice]:
        """Fetch price from a specific exchange."""
        try:
            await self.rate_limiter.acquire()
            
            # Build API URL based on exchange type
            if exchange_config['type'] == 'binance':
                url = f"https://api.binance.com/api/v3/ticker/bookTicker?symbol={token}USDT"
            elif exchange_config['type'] == 'coinbase':
                url = f"https://api.exchange.coinbase.com/products/{token}-USD/ticker"
            elif exchange_config['type'] == 'kraken':
                url = f"https://api.kraken.com/0/public/Ticker?pair={token}USD"
            else:
                return None
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    data = await response.json()
            
            # Parse response based on exchange format
            if exchange_config['type'] == 'binance':
                return ExchangePrice(
                    exchange=exchange_name,
                    exchange_type=ExchangeType.CEX,
                    symbol=token,
                    bid=float(data['bidPrice']),
                    ask=float(data['askPrice']),
                    volume_24h=0,  # Would need separate API call
                    timestamp=datetime.now(),
                    fees=exchange_config.get('fees', 0.001)
                )
            elif exchange_config['type'] == 'coinbase':
                return ExchangePrice(
                    exchange=exchange_name,
                    exchange_type=ExchangeType.CEX,
                    symbol=token,
                    bid=float(data['bid']),
                    ask=float(data['ask']),
                    volume_24h=float(data.get('volume', 0)),
                    timestamp=datetime.now(),
                    fees=exchange_config.get('fees', 0.005)
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error fetching price from {exchange_name}: {e}")
            return None
    
    def _calculate_arbitrage_opportunity(
        self,
        token: str,
        buy_price_data: ExchangePrice,
        sell_price_data: ExchangePrice
    ) -> Optional[ArbitrageOpportunity]:
        """Calculate arbitrage opportunity between two exchanges."""
        try:
            # Use ask price for buying, bid price for selling
            buy_price = buy_price_data.ask
            sell_price = sell_price_data.bid
            
            if sell_price <= buy_price:
                return None
            
            # Calculate gross profit percentage
            gross_profit_pct = (sell_price - buy_price) / buy_price
            
            # Subtract trading fees
            total_fees = buy_price_data.fees + sell_price_data.fees
            net_profit_pct = gross_profit_pct - total_fees
            
            if net_profit_pct <= 0:
                return None
            
            # Estimate volume limit (based on smaller exchange volume)
            volume_limit = min(
                buy_price_data.volume_24h * 0.01,  # 1% of daily volume
                sell_price_data.volume_24h * 0.01
            )
            
            # Estimate execution time
            execution_time = self._estimate_execution_time(
                buy_price_data.exchange_type,
                sell_price_data.exchange_type
            )
            
            # Calculate confidence based on volume and spread
            spread = (sell_price - buy_price) / ((sell_price + buy_price) / 2)
            confidence = min(spread * 100, 1.0)  # Higher spread = higher confidence
            
            return ArbitrageOpportunity(
                token=token,
                buy_exchange=buy_price_data.exchange,
                sell_exchange=sell_price_data.exchange,
                buy_price=buy_price,
                sell_price=sell_price,
                profit_percentage=net_profit_pct,
                profit_usd=net_profit_pct * buy_price * 1000,  # Assume $1000 trade
                volume_limit=volume_limit,
                execution_time_estimate=execution_time,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error calculating arbitrage opportunity: {e}")
            return None
    
    def _estimate_execution_time(
        self,
        buy_exchange_type: ExchangeType,
        sell_exchange_type: ExchangeType
    ) -> int:
        """Estimate execution time in seconds."""
        base_time = 30  # Base execution time
        
        # Add time for transfers between exchanges
        if buy_exchange_type != sell_exchange_type:
            base_time += 600  # 10 minutes for transfer
        
        # DEX transactions take longer
        if buy_exchange_type == ExchangeType.DEX:
            base_time += 60
        if sell_exchange_type == ExchangeType.DEX:
            base_time += 60
        
        return base_time
    
    async def _get_trading_pairs(self, exchange: str) -> List[Tuple[str, str]]:
        """Get all trading pairs for an exchange."""
        # This would fetch from exchange API
        # For now, return common pairs
        return [
            ('BTC', 'USDT'), ('ETH', 'USDT'), ('BTC', 'ETH'),
            ('ETH', 'BTC'), ('USDT', 'BTC'), ('USDT', 'ETH')
        ]
    
    def _build_currency_graph(
        self,
        trading_pairs: List[Tuple[str, str]]
    ) -> Dict[str, List[str]]:
        """Build currency graph for triangular arbitrage."""
        graph = {}
        
        for base, quote in trading_pairs:
            if base not in graph:
                graph[base] = []
            if quote not in graph:
                graph[quote] = []
            
            graph[base].append(quote)
            graph[quote].append(base)
        
        return graph
    
    def _find_triangular_paths(
        self,
        graph: Dict[str, List[str]],
        start_currency: str
    ) -> List[List[str]]:
        """Find triangular arbitrage paths."""
        paths = []
        
        if start_currency not in graph:
            return paths
        
        # Find paths of length 3 that return to start
        for second in graph[start_currency]:
            for third in graph.get(second, []):
                if start_currency in graph.get(third, []):
                    paths.append([start_currency, second, third, start_currency])
        
        return paths
    
    async def _calculate_triangular_opportunity(
        self,
        exchange: str,
        path: List[str]
    ) -> Optional[TriangularArbitrage]:
        """Calculate triangular arbitrage opportunity."""
        # This would fetch actual prices and calculate the arbitrage
        # For now, return a placeholder
        return None
    
    async def _simulate_trade_execution(
        self,
        exchange: str,
        token: str,
        side: str,
        amount: float,
        price: float
    ) -> Dict[str, Any]:
        """Simulate trade execution."""
        return {
            'exchange': exchange,
            'token': token,
            'side': side,
            'amount': amount,
            'price': price,
            'cost': amount * price * 0.001,  # 0.1% fee
            'success': True,
            'timestamp': datetime.now()
        }
    
    async def _simulate_transfer(
        self,
        token: str,
        from_exchange: str,
        to_exchange: str,
        amount: float
    ) -> Dict[str, Any]:
        """Simulate token transfer between exchanges."""
        return {
            'token': token,
            'from_exchange': from_exchange,
            'to_exchange': to_exchange,
            'amount': amount,
            'cost': 10.0,  # Fixed transfer cost
            'time': 300,  # 5 minutes
            'success': True,
            'timestamp': datetime.now()
        }
    
    def _load_exchange_configs(self) -> Dict[str, Dict[str, Any]]:
        """Load exchange configurations."""
        return {
            'binance': {
                'type': 'binance',
                'fees': 0.001,
                'api_url': 'https://api.binance.com'
            },
            'coinbase': {
                'type': 'coinbase',
                'fees': 0.005,
                'api_url': 'https://api.exchange.coinbase.com'
            },
            'kraken': {
                'type': 'kraken',
                'fees': 0.0026,
                'api_url': 'https://api.kraken.com'
            }
        }
