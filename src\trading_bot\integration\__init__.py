"""Integration Layer Module.

This module provides comprehensive integration capabilities including:
- API Gateway for unified service access
- Event Bus for event-driven architecture
- Message Queue for asynchronous processing
- Service Mesh for service-to-service communication
- WebSocket management for real-time data
"""

from .api_gateway import APIGateway
from .event_bus import EventBus
from .message_queue import MessageQueue
from .service_mesh import ServiceMesh

__all__ = [
    'APIGateway',
    'EventBus',
    'MessageQueue',
    'ServiceMesh'
]
