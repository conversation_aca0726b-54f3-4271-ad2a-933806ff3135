"""
Pre-deployment validation and preflight checks for AI Trading Bot.

This module provides comprehensive validation of all system components
before production deployment to ensure system readiness and reliability.
"""

import asyncio
import logging
import ssl
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
import psutil
import aiohttp
import asyncpg
import redis.asyncio as redis
from motor.motor_asyncio import AsyncIOMotorClient

from ...core.config import Config
from ...data.database import DatabaseManager
from ...ml.model_manager import ModelManager
from ...risk.risk_manager import RiskManager
from ...utils.logger import get_logger

logger = get_logger(__name__)


class CheckStatus(Enum):
    """Status of a preflight check."""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    SKIPPED = "skipped"


@dataclass
class CheckResult:
    """Result of a single preflight check."""
    name: str
    status: CheckStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    duration: float = 0.0
    timestamp: datetime = field(default_factory=datetime.utcnow)
    critical: bool = False


@dataclass
class ValidationReport:
    """Comprehensive validation report."""
    overall_status: CheckStatus
    total_checks: int
    passed_checks: int
    failed_checks: int
    warning_checks: int
    skipped_checks: int
    critical_failures: int
    total_duration: float
    timestamp: datetime
    checks: List[CheckResult] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate percentage."""
        if self.total_checks == 0:
            return 0.0
        return (self.passed_checks / self.total_checks) * 100
    
    @property
    def is_ready_for_deployment(self) -> bool:
        """Check if system is ready for deployment."""
        return (
            self.critical_failures == 0 and
            self.overall_status in [CheckStatus.PASSED, CheckStatus.WARNING] and
            self.success_rate >= 95.0
        )


class PreflightChecker:
    """
    Comprehensive pre-deployment validation system.
    
    Validates all system components before production deployment:
    - API connectivity and authentication
    - Database health and connectivity
    - ML models loading and performance
    - Risk management configuration
    - System resources and capacity
    - SSL certificates and security
    - Network connectivity and latency
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.checks: List[Callable] = []
        self.results: List[CheckResult] = []
        
        # Register all checks
        self._register_checks()
    
    def _register_checks(self):
        """Register all preflight checks."""
        self.checks = [
            self.check_api_connectivity,
            self.check_database_health,
            self.check_ml_models_loaded,
            self.check_risk_limits_configured,
            self.check_strategies_initialized,
            self.check_monitoring_active,
            self.check_backup_systems,
            self.check_ssl_certificates,
            self.check_api_rate_limits,
            self.check_disk_space,
            self.check_memory_available,
            self.check_network_latency,
            self.check_environment_variables,
            self.check_log_rotation,
            self.check_security_settings,
        ]
    
    async def run_all_checks(self) -> ValidationReport:
        """Run all preflight checks and generate validation report."""
        logger.info("Starting comprehensive preflight checks...")
        start_time = time.time()
        
        self.results = []
        
        # Run all checks concurrently where possible
        tasks = []
        for check in self.checks:
            tasks.append(self._run_single_check(check))
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        total_duration = time.time() - start_time
        
        # Generate report
        report = self._generate_report(total_duration)
        
        logger.info(f"Preflight checks completed in {total_duration:.2f}s")
        logger.info(f"Overall status: {report.overall_status.value}")
        logger.info(f"Success rate: {report.success_rate:.1f}%")
        
        if report.critical_failures > 0:
            logger.error(f"Critical failures detected: {report.critical_failures}")
        
        return report
    
    async def _run_single_check(self, check_func: Callable) -> CheckResult:
        """Run a single preflight check with error handling."""
        check_name = check_func.__name__.replace('check_', '').replace('_', ' ').title()
        start_time = time.time()
        
        try:
            result = await check_func()
            result.duration = time.time() - start_time
            self.results.append(result)
            
            if result.status == CheckStatus.FAILED and result.critical:
                logger.error(f"Critical check failed: {check_name} - {result.message}")
            elif result.status == CheckStatus.FAILED:
                logger.warning(f"Check failed: {check_name} - {result.message}")
            else:
                logger.info(f"Check passed: {check_name}")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            result = CheckResult(
                name=check_name,
                status=CheckStatus.FAILED,
                message=f"Check execution failed: {str(e)}",
                duration=duration,
                critical=True
            )
            self.results.append(result)
            logger.error(f"Check execution failed: {check_name} - {str(e)}")
            return result
    
    def _generate_report(self, total_duration: float) -> ValidationReport:
        """Generate comprehensive validation report."""
        passed = sum(1 for r in self.results if r.status == CheckStatus.PASSED)
        failed = sum(1 for r in self.results if r.status == CheckStatus.FAILED)
        warning = sum(1 for r in self.results if r.status == CheckStatus.WARNING)
        skipped = sum(1 for r in self.results if r.status == CheckStatus.SKIPPED)
        critical_failures = sum(1 for r in self.results if r.status == CheckStatus.FAILED and r.critical)
        
        # Determine overall status
        if critical_failures > 0:
            overall_status = CheckStatus.FAILED
        elif failed > 0:
            overall_status = CheckStatus.FAILED
        elif warning > 0:
            overall_status = CheckStatus.WARNING
        else:
            overall_status = CheckStatus.PASSED
        
        return ValidationReport(
            overall_status=overall_status,
            total_checks=len(self.results),
            passed_checks=passed,
            failed_checks=failed,
            warning_checks=warning,
            skipped_checks=skipped,
            critical_failures=critical_failures,
            total_duration=total_duration,
            timestamp=datetime.utcnow(),
            checks=self.results.copy()
        )

    async def check_api_connectivity(self) -> CheckResult:
        """Check external API connectivity and authentication."""
        try:
            # Check Webull API connectivity
            webull_status = await self._check_webull_api()

            # Check other APIs if configured
            other_apis_status = await self._check_other_apis()

            if webull_status and other_apis_status:
                return CheckResult(
                    name="API Connectivity",
                    status=CheckStatus.PASSED,
                    message="All APIs are accessible and authenticated",
                    details={
                        "webull_api": "connected",
                        "other_apis": "connected"
                    },
                    critical=True
                )
            else:
                return CheckResult(
                    name="API Connectivity",
                    status=CheckStatus.FAILED,
                    message="One or more APIs are not accessible",
                    details={
                        "webull_api": "connected" if webull_status else "failed",
                        "other_apis": "connected" if other_apis_status else "failed"
                    },
                    critical=True
                )

        except Exception as e:
            return CheckResult(
                name="API Connectivity",
                status=CheckStatus.FAILED,
                message=f"API connectivity check failed: {str(e)}",
                critical=True
            )

    async def check_database_health(self) -> CheckResult:
        """Check database connectivity and health."""
        try:
            # Check PostgreSQL
            postgres_healthy = await self._check_postgres_health()

            # Check Redis
            redis_healthy = await self._check_redis_health()

            # Check MongoDB
            mongo_healthy = await self._check_mongo_health()

            all_healthy = postgres_healthy and redis_healthy and mongo_healthy

            if all_healthy:
                return CheckResult(
                    name="Database Health",
                    status=CheckStatus.PASSED,
                    message="All databases are healthy and accessible",
                    details={
                        "postgresql": "healthy",
                        "redis": "healthy",
                        "mongodb": "healthy"
                    },
                    critical=True
                )
            else:
                return CheckResult(
                    name="Database Health",
                    status=CheckStatus.FAILED,
                    message="One or more databases are unhealthy",
                    details={
                        "postgresql": "healthy" if postgres_healthy else "unhealthy",
                        "redis": "healthy" if redis_healthy else "unhealthy",
                        "mongodb": "healthy" if mongo_healthy else "unhealthy"
                    },
                    critical=True
                )

        except Exception as e:
            return CheckResult(
                name="Database Health",
                status=CheckStatus.FAILED,
                message=f"Database health check failed: {str(e)}",
                critical=True
            )

    async def check_ml_models_loaded(self) -> CheckResult:
        """Check ML models are loaded and performing."""
        try:
            # This would integrate with the ModelManager
            # For now, simulate the check
            models_loaded = True  # Replace with actual model check
            model_accuracy = 0.75  # Replace with actual accuracy check

            if models_loaded and model_accuracy > 0.6:
                return CheckResult(
                    name="ML Models",
                    status=CheckStatus.PASSED,
                    message="ML models are loaded and performing within acceptable range",
                    details={
                        "models_loaded": models_loaded,
                        "accuracy": model_accuracy,
                        "threshold": 0.6
                    },
                    critical=True
                )
            else:
                return CheckResult(
                    name="ML Models",
                    status=CheckStatus.FAILED,
                    message="ML models are not loaded or performing poorly",
                    details={
                        "models_loaded": models_loaded,
                        "accuracy": model_accuracy,
                        "threshold": 0.6
                    },
                    critical=True
                )

        except Exception as e:
            return CheckResult(
                name="ML Models",
                status=CheckStatus.FAILED,
                message=f"ML model check failed: {str(e)}",
                critical=True
            )

    async def check_risk_limits_configured(self) -> CheckResult:
        """Check risk management limits are properly configured."""
        try:
            # Check risk configuration
            risk_config = self.config.risk

            required_limits = [
                'max_daily_loss',
                'max_position_size',
                'max_drawdown_threshold',
                'emergency_stop_loss'
            ]

            missing_limits = []
            for limit in required_limits:
                if not hasattr(risk_config, limit) or getattr(risk_config, limit) is None:
                    missing_limits.append(limit)

            if not missing_limits:
                return CheckResult(
                    name="Risk Limits",
                    status=CheckStatus.PASSED,
                    message="All risk limits are properly configured",
                    details={
                        "configured_limits": required_limits,
                        "max_daily_loss": risk_config.max_daily_loss,
                        "max_position_size": risk_config.max_position_size
                    },
                    critical=True
                )
            else:
                return CheckResult(
                    name="Risk Limits",
                    status=CheckStatus.FAILED,
                    message=f"Missing risk limits: {', '.join(missing_limits)}",
                    details={
                        "missing_limits": missing_limits,
                        "configured_limits": [l for l in required_limits if l not in missing_limits]
                    },
                    critical=True
                )

        except Exception as e:
            return CheckResult(
                name="Risk Limits",
                status=CheckStatus.FAILED,
                message=f"Risk limits check failed: {str(e)}",
                critical=True
            )

    async def check_strategies_initialized(self) -> CheckResult:
        """Check trading strategies are properly initialized."""
        try:
            # This would integrate with the StrategyManager
            strategies_loaded = True  # Replace with actual strategy check
            strategy_count = 3  # Replace with actual count

            if strategies_loaded and strategy_count > 0:
                return CheckResult(
                    name="Trading Strategies",
                    status=CheckStatus.PASSED,
                    message=f"Trading strategies are initialized ({strategy_count} strategies)",
                    details={
                        "strategies_loaded": strategies_loaded,
                        "strategy_count": strategy_count
                    },
                    critical=True
                )
            else:
                return CheckResult(
                    name="Trading Strategies",
                    status=CheckStatus.FAILED,
                    message="Trading strategies are not properly initialized",
                    details={
                        "strategies_loaded": strategies_loaded,
                        "strategy_count": strategy_count
                    },
                    critical=True
                )

        except Exception as e:
            return CheckResult(
                name="Trading Strategies",
                status=CheckStatus.FAILED,
                message=f"Strategy initialization check failed: {str(e)}",
                critical=True
            )

    async def check_monitoring_active(self) -> CheckResult:
        """Check monitoring systems are active."""
        try:
            # Check if monitoring endpoints are accessible
            monitoring_active = await self._check_monitoring_endpoints()

            if monitoring_active:
                return CheckResult(
                    name="Monitoring Systems",
                    status=CheckStatus.PASSED,
                    message="Monitoring systems are active and accessible",
                    details={"monitoring_active": True},
                    critical=False
                )
            else:
                return CheckResult(
                    name="Monitoring Systems",
                    status=CheckStatus.WARNING,
                    message="Monitoring systems are not fully active",
                    details={"monitoring_active": False},
                    critical=False
                )

        except Exception as e:
            return CheckResult(
                name="Monitoring Systems",
                status=CheckStatus.WARNING,
                message=f"Monitoring check failed: {str(e)}",
                critical=False
            )

    async def check_backup_systems(self) -> CheckResult:
        """Check backup systems are configured and tested."""
        try:
            # Check backup configuration
            backup_configured = True  # Replace with actual backup check
            last_backup = datetime.utcnow() - timedelta(hours=2)  # Replace with actual check

            backup_recent = (datetime.utcnow() - last_backup).total_seconds() < 86400  # 24 hours

            if backup_configured and backup_recent:
                return CheckResult(
                    name="Backup Systems",
                    status=CheckStatus.PASSED,
                    message="Backup systems are configured and recent backup exists",
                    details={
                        "backup_configured": backup_configured,
                        "last_backup": last_backup.isoformat(),
                        "backup_recent": backup_recent
                    },
                    critical=False
                )
            else:
                return CheckResult(
                    name="Backup Systems",
                    status=CheckStatus.WARNING,
                    message="Backup systems may not be properly configured",
                    details={
                        "backup_configured": backup_configured,
                        "last_backup": last_backup.isoformat() if last_backup else None,
                        "backup_recent": backup_recent
                    },
                    critical=False
                )

        except Exception as e:
            return CheckResult(
                name="Backup Systems",
                status=CheckStatus.WARNING,
                message=f"Backup systems check failed: {str(e)}",
                critical=False
            )

    async def check_ssl_certificates(self) -> CheckResult:
        """Check SSL certificates are valid and not expiring soon."""
        try:
            # Check SSL certificates for external APIs
            ssl_valid = await self._check_ssl_certificates()

            if ssl_valid:
                return CheckResult(
                    name="SSL Certificates",
                    status=CheckStatus.PASSED,
                    message="SSL certificates are valid and not expiring soon",
                    details={"ssl_valid": True},
                    critical=False
                )
            else:
                return CheckResult(
                    name="SSL Certificates",
                    status=CheckStatus.WARNING,
                    message="SSL certificates may be expiring soon or invalid",
                    details={"ssl_valid": False},
                    critical=False
                )

        except Exception as e:
            return CheckResult(
                name="SSL Certificates",
                status=CheckStatus.WARNING,
                message=f"SSL certificate check failed: {str(e)}",
                critical=False
            )

    async def check_api_rate_limits(self) -> CheckResult:
        """Check API rate limits are properly configured."""
        try:
            # Check rate limit configuration
            rate_limits_configured = True  # Replace with actual check
            current_usage = 0.3  # Replace with actual usage check (30%)

            if rate_limits_configured and current_usage < 0.8:
                return CheckResult(
                    name="API Rate Limits",
                    status=CheckStatus.PASSED,
                    message="API rate limits are configured and usage is within limits",
                    details={
                        "rate_limits_configured": rate_limits_configured,
                        "current_usage": current_usage,
                        "usage_threshold": 0.8
                    },
                    critical=False
                )
            else:
                return CheckResult(
                    name="API Rate Limits",
                    status=CheckStatus.WARNING,
                    message="API rate limits may be approaching threshold",
                    details={
                        "rate_limits_configured": rate_limits_configured,
                        "current_usage": current_usage,
                        "usage_threshold": 0.8
                    },
                    critical=False
                )

        except Exception as e:
            return CheckResult(
                name="API Rate Limits",
                status=CheckStatus.WARNING,
                message=f"API rate limits check failed: {str(e)}",
                critical=False
            )

    async def check_disk_space(self) -> CheckResult:
        """Check available disk space."""
        try:
            disk_usage = psutil.disk_usage('/')
            free_space_gb = disk_usage.free / (1024**3)
            usage_percent = (disk_usage.used / disk_usage.total) * 100

            if free_space_gb > 10 and usage_percent < 85:
                return CheckResult(
                    name="Disk Space",
                    status=CheckStatus.PASSED,
                    message=f"Sufficient disk space available ({free_space_gb:.1f}GB free)",
                    details={
                        "free_space_gb": free_space_gb,
                        "usage_percent": usage_percent,
                        "total_gb": disk_usage.total / (1024**3)
                    },
                    critical=False
                )
            elif free_space_gb > 5:
                return CheckResult(
                    name="Disk Space",
                    status=CheckStatus.WARNING,
                    message=f"Low disk space ({free_space_gb:.1f}GB free)",
                    details={
                        "free_space_gb": free_space_gb,
                        "usage_percent": usage_percent,
                        "total_gb": disk_usage.total / (1024**3)
                    },
                    critical=False
                )
            else:
                return CheckResult(
                    name="Disk Space",
                    status=CheckStatus.FAILED,
                    message=f"Critical disk space shortage ({free_space_gb:.1f}GB free)",
                    details={
                        "free_space_gb": free_space_gb,
                        "usage_percent": usage_percent,
                        "total_gb": disk_usage.total / (1024**3)
                    },
                    critical=True
                )

        except Exception as e:
            return CheckResult(
                name="Disk Space",
                status=CheckStatus.FAILED,
                message=f"Disk space check failed: {str(e)}",
                critical=True
            )

    async def check_memory_available(self) -> CheckResult:
        """Check available memory."""
        try:
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            usage_percent = memory.percent

            if available_gb > 2 and usage_percent < 85:
                return CheckResult(
                    name="Memory Available",
                    status=CheckStatus.PASSED,
                    message=f"Sufficient memory available ({available_gb:.1f}GB free)",
                    details={
                        "available_gb": available_gb,
                        "usage_percent": usage_percent,
                        "total_gb": memory.total / (1024**3)
                    },
                    critical=False
                )
            elif available_gb > 1:
                return CheckResult(
                    name="Memory Available",
                    status=CheckStatus.WARNING,
                    message=f"Low memory available ({available_gb:.1f}GB free)",
                    details={
                        "available_gb": available_gb,
                        "usage_percent": usage_percent,
                        "total_gb": memory.total / (1024**3)
                    },
                    critical=False
                )
            else:
                return CheckResult(
                    name="Memory Available",
                    status=CheckStatus.FAILED,
                    message=f"Critical memory shortage ({available_gb:.1f}GB free)",
                    details={
                        "available_gb": available_gb,
                        "usage_percent": usage_percent,
                        "total_gb": memory.total / (1024**3)
                    },
                    critical=True
                )

        except Exception as e:
            return CheckResult(
                name="Memory Available",
                status=CheckStatus.FAILED,
                message=f"Memory check failed: {str(e)}",
                critical=True
            )

    async def check_network_latency(self) -> CheckResult:
        """Check network latency to critical services."""
        try:
            # Test latency to key endpoints
            latencies = await self._test_network_latency()
            avg_latency = sum(latencies.values()) / len(latencies) if latencies else 1000

            if avg_latency < 100:
                return CheckResult(
                    name="Network Latency",
                    status=CheckStatus.PASSED,
                    message=f"Network latency is acceptable ({avg_latency:.1f}ms avg)",
                    details={
                        "average_latency_ms": avg_latency,
                        "individual_latencies": latencies
                    },
                    critical=False
                )
            elif avg_latency < 500:
                return CheckResult(
                    name="Network Latency",
                    status=CheckStatus.WARNING,
                    message=f"Network latency is elevated ({avg_latency:.1f}ms avg)",
                    details={
                        "average_latency_ms": avg_latency,
                        "individual_latencies": latencies
                    },
                    critical=False
                )
            else:
                return CheckResult(
                    name="Network Latency",
                    status=CheckStatus.FAILED,
                    message=f"Network latency is too high ({avg_latency:.1f}ms avg)",
                    details={
                        "average_latency_ms": avg_latency,
                        "individual_latencies": latencies
                    },
                    critical=False
                )

        except Exception as e:
            return CheckResult(
                name="Network Latency",
                status=CheckStatus.WARNING,
                message=f"Network latency check failed: {str(e)}",
                critical=False
            )

    async def check_environment_variables(self) -> CheckResult:
        """Check required environment variables are set."""
        try:
            import os
            required_vars = [
                'WEBULL_USERNAME',
                'WEBULL_PASSWORD',
                'POSTGRES_PASSWORD',
                'REDIS_PASSWORD',
                'ENVIRONMENT'
            ]

            missing_vars = []
            for var in required_vars:
                if not os.getenv(var):
                    missing_vars.append(var)

            if not missing_vars:
                return CheckResult(
                    name="Environment Variables",
                    status=CheckStatus.PASSED,
                    message="All required environment variables are set",
                    details={"required_vars": required_vars},
                    critical=True
                )
            else:
                return CheckResult(
                    name="Environment Variables",
                    status=CheckStatus.FAILED,
                    message=f"Missing environment variables: {', '.join(missing_vars)}",
                    details={
                        "missing_vars": missing_vars,
                        "required_vars": required_vars
                    },
                    critical=True
                )

        except Exception as e:
            return CheckResult(
                name="Environment Variables",
                status=CheckStatus.FAILED,
                message=f"Environment variables check failed: {str(e)}",
                critical=True
            )

    async def check_log_rotation(self) -> CheckResult:
        """Check log rotation is configured."""
        try:
            # Check log rotation configuration
            log_rotation_configured = True  # Replace with actual check

            if log_rotation_configured:
                return CheckResult(
                    name="Log Rotation",
                    status=CheckStatus.PASSED,
                    message="Log rotation is properly configured",
                    details={"log_rotation_configured": True},
                    critical=False
                )
            else:
                return CheckResult(
                    name="Log Rotation",
                    status=CheckStatus.WARNING,
                    message="Log rotation may not be configured",
                    details={"log_rotation_configured": False},
                    critical=False
                )

        except Exception as e:
            return CheckResult(
                name="Log Rotation",
                status=CheckStatus.WARNING,
                message=f"Log rotation check failed: {str(e)}",
                critical=False
            )

    async def check_security_settings(self) -> CheckResult:
        """Check security settings and configurations."""
        try:
            # Check security configurations
            security_configured = True  # Replace with actual security checks

            if security_configured:
                return CheckResult(
                    name="Security Settings",
                    status=CheckStatus.PASSED,
                    message="Security settings are properly configured",
                    details={"security_configured": True},
                    critical=False
                )
            else:
                return CheckResult(
                    name="Security Settings",
                    status=CheckStatus.WARNING,
                    message="Security settings may need review",
                    details={"security_configured": False},
                    critical=False
                )

        except Exception as e:
            return CheckResult(
                name="Security Settings",
                status=CheckStatus.WARNING,
                message=f"Security settings check failed: {str(e)}",
                critical=False
            )

    # Helper methods for specific checks
    async def _check_webull_api(self) -> bool:
        """Check Webull API connectivity."""
        try:
            # This would integrate with the actual Webull API client
            # For now, simulate the check
            await asyncio.sleep(0.1)  # Simulate API call
            return True
        except Exception:
            return False

    async def _check_other_apis(self) -> bool:
        """Check other external APIs."""
        try:
            # Check other APIs if configured
            await asyncio.sleep(0.1)  # Simulate API calls
            return True
        except Exception:
            return False

    async def _check_postgres_health(self) -> bool:
        """Check PostgreSQL database health."""
        try:
            # This would use the actual database connection
            # For now, simulate the check
            await asyncio.sleep(0.1)  # Simulate database query
            return True
        except Exception:
            return False

    async def _check_redis_health(self) -> bool:
        """Check Redis health."""
        try:
            # This would use the actual Redis connection
            # For now, simulate the check
            await asyncio.sleep(0.1)  # Simulate Redis ping
            return True
        except Exception:
            return False

    async def _check_mongo_health(self) -> bool:
        """Check MongoDB health."""
        try:
            # This would use the actual MongoDB connection
            # For now, simulate the check
            await asyncio.sleep(0.1)  # Simulate MongoDB ping
            return True
        except Exception:
            return False

    async def _check_monitoring_endpoints(self) -> bool:
        """Check monitoring endpoints accessibility."""
        try:
            # Check Prometheus, Grafana, etc.
            await asyncio.sleep(0.1)  # Simulate endpoint checks
            return True
        except Exception:
            return False

    async def _check_ssl_certificates(self) -> bool:
        """Check SSL certificate validity."""
        try:
            # Check SSL certificates for external services
            await asyncio.sleep(0.1)  # Simulate SSL checks
            return True
        except Exception:
            return False

    async def _test_network_latency(self) -> Dict[str, float]:
        """Test network latency to key endpoints."""
        try:
            # Test latency to various endpoints
            latencies = {
                "webull_api": 50.0,  # Replace with actual latency test
                "database": 5.0,     # Replace with actual latency test
                "cache": 2.0         # Replace with actual latency test
            }
            return latencies
        except Exception:
            return {}
