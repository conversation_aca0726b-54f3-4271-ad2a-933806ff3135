"""
Quick ML Training for Yahoo Finance Data - Standalone Version
Simplified machine learning pipeline for volatility hunting strategy
Works independently while being compatible with the main trading bot
"""

import yfinance as yf
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, mean_squared_error, accuracy_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import pickle
from datetime import datetime, timedelta
import logging
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class YahooMLTrainer:
    """
    Machine Learning trainer specifically for Yahoo Finance volatility hunting
    Focuses on predicting high-probability trading opportunities
    """
    
    def __init__(self):
        # Volatility stocks for training
        self.training_symbols = [
            # Leveraged ETFs
            'TQQQ', 'SQQQ', 'UVXY', 'SOXL', 'SOXS',
            'LABU', 'LABD', 'TNA', 'TZA', 'UPRO', 'SPXU',
            # High volatility stocks
            'GME', 'AMC', 'TSLA', 'NVDA', 'AMD',
            'RIOT', 'MARA', 'COIN', 'MSTR',
            # Biotech volatility
            'MRNA', 'BNTX', 'NVAX'
        ]
        
        # Models
        self.gap_fill_model = None
        self.oversold_bounce_model = None
        self.breakout_model = None
        self.scaler = StandardScaler()
        
    def collect_training_data(self, period="2y"):
        """Collect and prepare training data from Yahoo Finance"""
        logger.info(f"Collecting training data for {len(self.training_symbols)} symbols...")
        
        all_data = []
        
        for symbol in self.training_symbols:
            try:
                logger.info(f"Processing {symbol}...")
                
                # Get historical data
                ticker = yf.Ticker(symbol)
                df = ticker.history(period=period, interval="1d")
                
                if df.empty or len(df) < 50:
                    continue
                
                # Calculate features
                df = self._calculate_features(df, symbol)
                
                # Calculate targets
                df = self._calculate_targets(df)
                
                # Clean data
                df = df.dropna()
                
                if len(df) > 30:
                    all_data.append(df)
                    
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
                continue
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            logger.info(f"✅ Collected {len(combined_df)} training samples")
            return combined_df
        else:
            logger.error("No training data collected!")
            return pd.DataFrame()
    
    def _calculate_features(self, df, symbol):
        """Calculate technical features for ML training"""
        # Price features
        df['daily_return'] = df['Close'].pct_change()
        df['daily_range'] = (df['High'] - df['Low']) / df['Open']
        df['gap'] = df['Open'] / df['Close'].shift(1) - 1
        df['gap_size'] = abs(df['gap'])
        
        # Volume features
        df['volume_sma_10'] = df['Volume'].rolling(10).mean()
        df['volume_sma_20'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma_20']
        df['volume_surge'] = (df['Volume'] > df['volume_sma_10'] * 2).astype(int)
        
        # Technical indicators
        df['rsi'] = self._calculate_rsi(df['Close'], 14)
        df['rsi_oversold'] = (df['rsi'] < 30).astype(int)
        df['rsi_overbought'] = (df['rsi'] > 70).astype(int)
        
        # Bollinger Bands
        df['bb_middle'] = df['Close'].rolling(20).mean()
        df['bb_std'] = df['Close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['Close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_squeeze'] = df['bb_std'] / df['bb_middle']
        
        # Moving averages
        df['sma_5'] = df['Close'].rolling(5).mean()
        df['sma_20'] = df['Close'].rolling(20).mean()
        df['sma_50'] = df['Close'].rolling(50).mean()
        df['above_sma_20'] = (df['Close'] > df['sma_20']).astype(int)
        df['above_sma_50'] = (df['Close'] > df['sma_50']).astype(int)
        
        # ATR and volatility
        df['atr'] = self._calculate_atr(df, 14)
        df['atr_ratio'] = df['atr'] / df['Close']
        df['volatility_rank'] = df['atr_ratio'].rolling(252).rank(pct=True)
        
        # Support/Resistance
        df['resistance_20'] = df['High'].rolling(20).max()
        df['support_20'] = df['Low'].rolling(20).min()
        df['near_resistance'] = (df['Close'] > df['resistance_20'] * 0.98).astype(int)
        df['near_support'] = (df['Close'] < df['support_20'] * 1.02).astype(int)
        
        # Momentum features
        df['momentum_5'] = df['Close'] / df['Close'].shift(5) - 1
        df['momentum_10'] = df['Close'] / df['Close'].shift(10) - 1
        df['momentum_20'] = df['Close'] / df['Close'].shift(20) - 1
        
        # Price patterns
        df['higher_high'] = (df['High'] > df['High'].shift(1)).astype(int)
        df['lower_low'] = (df['Low'] < df['Low'].shift(1)).astype(int)
        df['inside_day'] = ((df['High'] < df['High'].shift(1)) & 
                           (df['Low'] > df['Low'].shift(1))).astype(int)
        
        # Add symbol-specific features
        df['symbol'] = symbol
        df['is_leveraged_etf'] = (symbol in ['TQQQ', 'SQQQ', 'UVXY', 'SOXL', 'SOXS', 
                                            'LABU', 'LABD', 'TNA', 'TZA', 'UPRO', 'SPXU']).astype(int)
        df['is_meme_stock'] = (symbol in ['GME', 'AMC', 'BB', 'NOK']).astype(int)
        df['is_crypto_related'] = (symbol in ['RIOT', 'MARA', 'COIN', 'MSTR']).astype(int)
        
        return df
    
    def _calculate_targets(self, df):
        """Calculate target variables for different strategies"""
        # Gap fill targets
        df['has_gap'] = (abs(df['gap']) > 0.02).astype(int)
        df['gap_direction'] = np.sign(df['gap'])
        df['gap_filled_1d'] = 0
        df['gap_filled_3d'] = 0
        
        # Oversold bounce targets
        df['oversold_bounce_1d'] = 0
        df['oversold_bounce_3d'] = 0
        
        # Breakout pullback targets
        df['breakout_success_1d'] = 0
        df['breakout_success_3d'] = 0
        
        # Calculate forward-looking targets
        for i in range(len(df) - 5):
            current = df.iloc[i]
            next_1d = df.iloc[i + 1] if i + 1 < len(df) else None
            next_3d = df.iloc[i + 3] if i + 3 < len(df) else None
            
            if next_1d is not None and next_3d is not None:
                # Gap fill targets
                if abs(current['gap']) > 0.02:
                    prev_close = df.iloc[i - 1]['Close'] if i > 0 else current['Close']
                    
                    # Check if gap filled within 1 day
                    if current['gap'] > 0:  # Gap up
                        if next_1d['Low'] <= prev_close:
                            df.iloc[i, df.columns.get_loc('gap_filled_1d')] = 1
                    else:  # Gap down
                        if next_1d['High'] >= prev_close:
                            df.iloc[i, df.columns.get_loc('gap_filled_1d')] = 1
                    
                    # Check if gap filled within 3 days
                    for j in range(1, 4):
                        if i + j < len(df):
                            day_j = df.iloc[i + j]
                            if current['gap'] > 0 and day_j['Low'] <= prev_close:
                                df.iloc[i, df.columns.get_loc('gap_filled_3d')] = 1
                                break
                            elif current['gap'] < 0 and day_j['High'] >= prev_close:
                                df.iloc[i, df.columns.get_loc('gap_filled_3d')] = 1
                                break
                
                # Oversold bounce targets
                if current['rsi'] < 35:
                    entry_price = current['Close']
                    target_price = current['bb_middle']
                    
                    # 1-day bounce
                    if next_1d['High'] >= target_price * 0.98:
                        df.iloc[i, df.columns.get_loc('oversold_bounce_1d')] = 1
                    
                    # 3-day bounce
                    for j in range(1, 4):
                        if i + j < len(df):
                            day_j = df.iloc[i + j]
                            if day_j['High'] >= target_price * 0.98:
                                df.iloc[i, df.columns.get_loc('oversold_bounce_3d')] = 1
                                break
                
                # Breakout success targets
                if current['near_resistance']:
                    resistance_level = current['resistance_20']
                    
                    # 1-day breakout success
                    if next_1d['Close'] > resistance_level * 1.02:
                        df.iloc[i, df.columns.get_loc('breakout_success_1d')] = 1
                    
                    # 3-day breakout success
                    for j in range(1, 4):
                        if i + j < len(df):
                            day_j = df.iloc[i + j]
                            if day_j['Close'] > resistance_level * 1.02:
                                df.iloc[i, df.columns.get_loc('breakout_success_3d')] = 1
                                break
        
        return df
    
    def _calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df, period=14):
        """Calculate Average True Range"""
        high_low = df['High'] - df['Low']
        high_close = abs(df['High'] - df['Close'].shift())
        low_close = abs(df['Low'] - df['Close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(period).mean()
    
    def train_models(self, df):
        """Train ML models for each strategy"""
        logger.info("Training ML models...")
        
        # Feature columns
        feature_columns = [
            'daily_return', 'daily_range', 'gap_size', 'volume_ratio', 'volume_surge',
            'rsi', 'rsi_oversold', 'rsi_overbought', 'bb_position', 'bb_squeeze',
            'above_sma_20', 'above_sma_50', 'atr_ratio', 'volatility_rank',
            'near_resistance', 'near_support', 'momentum_5', 'momentum_10', 'momentum_20',
            'higher_high', 'lower_low', 'inside_day', 'is_leveraged_etf', 
            'is_meme_stock', 'is_crypto_related'
        ]
        
        # Prepare features
        X = df[feature_columns].fillna(0)
        X_scaled = self.scaler.fit_transform(X)
        
        # Train Gap Fill Model
        logger.info("Training Gap Fill model...")
        y_gap = df['gap_filled_3d']
        gap_mask = df['has_gap'] == 1
        if gap_mask.sum() > 100:
            X_gap = X_scaled[gap_mask]
            y_gap_filtered = y_gap[gap_mask]
            
            X_train, X_test, y_train, y_test = train_test_split(
                X_gap, y_gap_filtered, test_size=0.2, random_state=42
            )
            
            self.gap_fill_model = RandomForestClassifier(
                n_estimators=100, max_depth=10, random_state=42
            )
            self.gap_fill_model.fit(X_train, y_train)
            
            # Evaluate
            y_pred = self.gap_fill_model.predict(X_test)
            gap_accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"Gap Fill Model Accuracy: {gap_accuracy:.3f}")
        
        # Train Oversold Bounce Model
        logger.info("Training Oversold Bounce model...")
        y_oversold = df['oversold_bounce_3d']
        oversold_mask = df['rsi_oversold'] == 1
        if oversold_mask.sum() > 100:
            X_oversold = X_scaled[oversold_mask]
            y_oversold_filtered = y_oversold[oversold_mask]
            
            X_train, X_test, y_train, y_test = train_test_split(
                X_oversold, y_oversold_filtered, test_size=0.2, random_state=42
            )
            
            self.oversold_bounce_model = XGBClassifier(
                n_estimators=100, max_depth=6, random_state=42, eval_metric='logloss'
            )
            self.oversold_bounce_model.fit(X_train, y_train)
            
            # Evaluate
            y_pred = self.oversold_bounce_model.predict(X_test)
            oversold_accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"Oversold Bounce Model Accuracy: {oversold_accuracy:.3f}")
        
        # Train Breakout Model
        logger.info("Training Breakout model...")
        y_breakout = df['breakout_success_3d']
        breakout_mask = df['near_resistance'] == 1
        if breakout_mask.sum() > 100:
            X_breakout = X_scaled[breakout_mask]
            y_breakout_filtered = y_breakout[breakout_mask]
            
            X_train, X_test, y_train, y_test = train_test_split(
                X_breakout, y_breakout_filtered, test_size=0.2, random_state=42
            )
            
            self.breakout_model = RandomForestClassifier(
                n_estimators=100, max_depth=8, random_state=42
            )
            self.breakout_model.fit(X_train, y_train)
            
            # Evaluate
            y_pred = self.breakout_model.predict(X_test)
            breakout_accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"Breakout Model Accuracy: {breakout_accuracy:.3f}")
        
        logger.info("✅ Model training completed!")
    
    def save_models(self, model_dir="ml_models"):
        """Save trained models"""
        import os
        os.makedirs(model_dir, exist_ok=True)
        
        if self.gap_fill_model:
            with open(f"{model_dir}/gap_fill_model.pkl", "wb") as f:
                pickle.dump(self.gap_fill_model, f)
        
        if self.oversold_bounce_model:
            with open(f"{model_dir}/oversold_bounce_model.pkl", "wb") as f:
                pickle.dump(self.oversold_bounce_model, f)
        
        if self.breakout_model:
            with open(f"{model_dir}/breakout_model.pkl", "wb") as f:
                pickle.dump(self.breakout_model, f)
        
        # Save scaler
        with open(f"{model_dir}/scaler.pkl", "wb") as f:
            pickle.dump(self.scaler, f)
        
        logger.info(f"✅ Models saved to {model_dir}/")
    
    def load_models(self, model_dir="ml_models"):
        """Load trained models"""
        try:
            with open(f"{model_dir}/gap_fill_model.pkl", "rb") as f:
                self.gap_fill_model = pickle.load(f)
            
            with open(f"{model_dir}/oversold_bounce_model.pkl", "rb") as f:
                self.oversold_bounce_model = pickle.load(f)
            
            with open(f"{model_dir}/breakout_model.pkl", "rb") as f:
                self.breakout_model = pickle.load(f)
            
            with open(f"{model_dir}/scaler.pkl", "rb") as f:
                self.scaler = pickle.load(f)
            
            logger.info("✅ Models loaded successfully!")
            return True
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False
    
    def predict_opportunities(self, symbol):
        """Predict trading opportunities for a symbol"""
        try:
            # Get recent data
            ticker = yf.Ticker(symbol)
            df = ticker.history(period="3mo", interval="1d")
            
            if df.empty or len(df) < 50:
                return {}
            
            # Calculate features
            df = self._calculate_features(df, symbol)
            df = df.dropna()
            
            if len(df) == 0:
                return {}
            
            # Get latest data
            latest = df.iloc[-1]
            
            # Prepare features
            feature_columns = [
                'daily_return', 'daily_range', 'gap_size', 'volume_ratio', 'volume_surge',
                'rsi', 'rsi_oversold', 'rsi_overbought', 'bb_position', 'bb_squeeze',
                'above_sma_20', 'above_sma_50', 'atr_ratio', 'volatility_rank',
                'near_resistance', 'near_support', 'momentum_5', 'momentum_10', 'momentum_20',
                'higher_high', 'lower_low', 'inside_day', 'is_leveraged_etf', 
                'is_meme_stock', 'is_crypto_related'
            ]
            
            features = latest[feature_columns].fillna(0).values.reshape(1, -1)
            features_scaled = self.scaler.transform(features)
            
            predictions = {}
            
            # Gap fill prediction
            if self.gap_fill_model and latest['has_gap']:
                gap_prob = self.gap_fill_model.predict_proba(features_scaled)[0][1]
                predictions['gap_fill_probability'] = gap_prob
            
            # Oversold bounce prediction
            if self.oversold_bounce_model and latest['rsi_oversold']:
                oversold_prob = self.oversold_bounce_model.predict_proba(features_scaled)[0][1]
                predictions['oversold_bounce_probability'] = oversold_prob
            
            # Breakout prediction
            if self.breakout_model and latest['near_resistance']:
                breakout_prob = self.breakout_model.predict_proba(features_scaled)[0][1]
                predictions['breakout_probability'] = breakout_prob
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error predicting opportunities for {symbol}: {e}")
            return {}


def main():
    """Main training function"""
    trainer = YahooMLTrainer()
    
    print("🤖 Yahoo Finance ML Training Pipeline")
    print("=" * 50)
    
    # Collect training data
    print("\n📊 Collecting training data...")
    df = trainer.collect_training_data(period="2y")
    
    if df.empty:
        print("❌ No training data collected. Exiting.")
        return
    
    print(f"✅ Collected {len(df)} training samples")
    
    # Train models
    print("\n🎯 Training ML models...")
    trainer.train_models(df)
    
    # Save models
    print("\n💾 Saving models...")
    trainer.save_models()
    
    # Test predictions on a few symbols
    print("\n🔮 Testing predictions...")
    test_symbols = ['TQQQ', 'GME', 'TSLA']
    
    for symbol in test_symbols:
        print(f"\n{symbol} Predictions:")
        predictions = trainer.predict_opportunities(symbol)
        for strategy, prob in predictions.items():
            print(f"  {strategy}: {prob:.3f}")
    
    print("\n🎉 Training completed successfully!")
    print("Models saved to ml_models/ directory")
    print("Ready for paper trading!")


if __name__ == "__main__":
    main()