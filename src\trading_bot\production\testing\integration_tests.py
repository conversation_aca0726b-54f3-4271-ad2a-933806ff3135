"""
Comprehensive integration tests for AI Trading Bot.

This module provides end-to-end integration testing to validate
the complete trading system functionality before production deployment.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
import pytest

from ...core.config import Config
from ...data.market_data import MarketDataManager
from ...ml.model_manager import ModelManager
from ...strategies.strategy_manager import StrategyManager
from ...execution.order_manager import OrderManager
from ...risk.risk_manager import RiskManager
from ...orchestration.master_controller import MasterController
from ...utils.logger import get_logger

logger = get_logger(__name__)


class TestStatus(Enum):
    """Integration test status."""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class IntegrationTestResult:
    """Result of a single integration test."""
    test_name: str
    status: TestStatus
    message: str
    duration: float
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None


@dataclass
class IntegrationTestReport:
    """Comprehensive integration test report."""
    total_tests: int
    passed_tests: int
    failed_tests: int
    error_tests: int
    skipped_tests: int
    total_duration: float
    timestamp: datetime
    results: List[IntegrationTestResult] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate test success rate."""
        if self.total_tests == 0:
            return 0.0
        return (self.passed_tests / self.total_tests) * 100
    
    @property
    def is_ready_for_production(self) -> bool:
        """Check if system is ready for production based on test results."""
        return (
            self.failed_tests == 0 and
            self.error_tests == 0 and
            self.success_rate >= 95.0
        )


class IntegrationTestSuite:
    """
    Comprehensive integration test suite for the trading bot.
    
    Tests the complete end-to-end trading flow including:
    - Market data ingestion and processing
    - ML model inference and predictions
    - Strategy signal generation
    - Risk management validation
    - Order execution flow
    - Error recovery scenarios
    - Data consistency checks
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.results: List[IntegrationTestResult] = []
        
        # Initialize components for testing
        self.market_data_manager = None
        self.model_manager = None
        self.strategy_manager = None
        self.order_manager = None
        self.risk_manager = None
        self.master_controller = None
    
    async def run_all_tests(self) -> IntegrationTestReport:
        """Run all integration tests and generate report."""
        logger.info("Starting comprehensive integration tests...")
        start_time = time.time()
        
        self.results = []
        
        # Initialize test environment
        await self._setup_test_environment()
        
        # Run all integration tests
        test_methods = [
            self.test_market_data_flow,
            self.test_ml_prediction_pipeline,
            self.test_strategy_signal_generation,
            self.test_risk_management_enforcement,
            self.test_order_execution_flow,
            self.test_multi_strategy_coordination,
            self.test_error_recovery_scenarios,
            self.test_data_consistency,
            self.test_performance_under_load,
            self.test_system_shutdown_recovery,
        ]
        
        for test_method in test_methods:
            try:
                result = await self._run_single_test(test_method)
                self.results.append(result)
            except Exception as e:
                logger.error(f"Test execution failed: {test_method.__name__} - {e}")
                self.results.append(IntegrationTestResult(
                    test_name=test_method.__name__,
                    status=TestStatus.ERROR,
                    message=f"Test execution failed: {str(e)}",
                    duration=0.0,
                    timestamp=datetime.utcnow(),
                    error=str(e)
                ))
        
        # Cleanup test environment
        await self._cleanup_test_environment()
        
        total_duration = time.time() - start_time
        
        # Generate report
        report = self._generate_report(total_duration)
        
        logger.info(f"Integration tests completed in {total_duration:.2f}s")
        logger.info(f"Success rate: {report.success_rate:.1f}%")
        
        return report
    
    async def _setup_test_environment(self):
        """Set up test environment with all components."""
        try:
            # Initialize components in test mode
            self.market_data_manager = MarketDataManager(self.config)
            self.model_manager = ModelManager(self.config)
            self.strategy_manager = StrategyManager(self.config)
            self.order_manager = OrderManager(self.config)
            self.risk_manager = RiskManager(self.config)
            self.master_controller = MasterController(self.config)
            
            # Start components
            await self.market_data_manager.start()
            await self.model_manager.start()
            await self.strategy_manager.start()
            await self.order_manager.start()
            await self.risk_manager.start()
            
            logger.info("Test environment setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup test environment: {e}")
            raise
    
    async def _cleanup_test_environment(self):
        """Clean up test environment."""
        try:
            # Stop all components
            if self.master_controller:
                await self.master_controller.shutdown()
            if self.risk_manager:
                await self.risk_manager.stop()
            if self.order_manager:
                await self.order_manager.stop()
            if self.strategy_manager:
                await self.strategy_manager.stop()
            if self.model_manager:
                await self.model_manager.stop()
            if self.market_data_manager:
                await self.market_data_manager.stop()
            
            logger.info("Test environment cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup test environment: {e}")
    
    async def _run_single_test(self, test_method) -> IntegrationTestResult:
        """Run a single integration test."""
        test_name = test_method.__name__
        start_time = time.time()
        
        try:
            logger.info(f"Running test: {test_name}")
            
            # Run the test
            success, message, details = await test_method()
            
            duration = time.time() - start_time
            status = TestStatus.PASSED if success else TestStatus.FAILED
            
            result = IntegrationTestResult(
                test_name=test_name,
                status=status,
                message=message,
                duration=duration,
                timestamp=datetime.utcnow(),
                details=details
            )
            
            if success:
                logger.info(f"Test passed: {test_name} ({duration:.2f}s)")
            else:
                logger.warning(f"Test failed: {test_name} - {message}")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Test error: {test_name} - {e}")
            
            return IntegrationTestResult(
                test_name=test_name,
                status=TestStatus.ERROR,
                message=f"Test execution error: {str(e)}",
                duration=duration,
                timestamp=datetime.utcnow(),
                error=str(e)
            )
    
    async def test_market_data_flow(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test end-to-end market data flow."""
        try:
            # Test market data ingestion
            symbols = ['AAPL', 'GOOGL', 'MSFT']
            
            # Request market data
            market_data = {}
            for symbol in symbols:
                data = await self.market_data_manager.get_real_time_data(symbol)
                market_data[symbol] = data
            
            # Validate data quality
            data_quality_issues = []
            for symbol, data in market_data.items():
                if not data or 'price' not in data:
                    data_quality_issues.append(f"Missing price data for {symbol}")
                if data.get('price', 0) <= 0:
                    data_quality_issues.append(f"Invalid price for {symbol}")
            
            if data_quality_issues:
                return False, f"Data quality issues: {', '.join(data_quality_issues)}", {
                    "symbols_tested": symbols,
                    "data_quality_issues": data_quality_issues
                }
            
            return True, "Market data flow test passed", {
                "symbols_tested": symbols,
                "data_received": len(market_data),
                "sample_data": {k: v for k, v in list(market_data.items())[:2]}
            }
            
        except Exception as e:
            return False, f"Market data flow test failed: {str(e)}", {"error": str(e)}
    
    async def test_ml_prediction_pipeline(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test ML prediction pipeline."""
        try:
            # Test model loading and inference
            symbols = ['AAPL', 'GOOGL']
            predictions = {}
            
            for symbol in symbols:
                # Get market data
                market_data = await self.market_data_manager.get_real_time_data(symbol)
                
                # Generate prediction
                prediction = await self.model_manager.predict(symbol, market_data)
                predictions[symbol] = prediction
            
            # Validate predictions
            prediction_issues = []
            for symbol, prediction in predictions.items():
                if prediction is None:
                    prediction_issues.append(f"No prediction for {symbol}")
                elif not isinstance(prediction, dict):
                    prediction_issues.append(f"Invalid prediction format for {symbol}")
                elif 'confidence' not in prediction:
                    prediction_issues.append(f"Missing confidence for {symbol}")
            
            if prediction_issues:
                return False, f"Prediction issues: {', '.join(prediction_issues)}", {
                    "symbols_tested": symbols,
                    "prediction_issues": prediction_issues
                }
            
            return True, "ML prediction pipeline test passed", {
                "symbols_tested": symbols,
                "predictions_generated": len(predictions),
                "sample_predictions": predictions
            }
            
        except Exception as e:
            return False, f"ML prediction pipeline test failed: {str(e)}", {"error": str(e)}

    async def test_strategy_signal_generation(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test strategy signal generation."""
        try:
            # Test strategy signal generation
            symbols = ['AAPL', 'GOOGL']
            signals = {}

            for symbol in symbols:
                # Get market data and predictions
                market_data = await self.market_data_manager.get_real_time_data(symbol)
                prediction = await self.model_manager.predict(symbol, market_data)

                # Generate strategy signals
                strategy_signals = await self.strategy_manager.generate_signals(
                    symbol, market_data, prediction
                )
                signals[symbol] = strategy_signals

            # Validate signals
            signal_issues = []
            for symbol, signal_list in signals.items():
                if not signal_list:
                    signal_issues.append(f"No signals generated for {symbol}")
                for signal in signal_list:
                    if 'action' not in signal or 'confidence' not in signal:
                        signal_issues.append(f"Invalid signal format for {symbol}")

            if signal_issues:
                return False, f"Signal issues: {', '.join(signal_issues)}", {
                    "symbols_tested": symbols,
                    "signal_issues": signal_issues
                }

            return True, "Strategy signal generation test passed", {
                "symbols_tested": symbols,
                "signals_generated": sum(len(s) for s in signals.values()),
                "sample_signals": signals
            }

        except Exception as e:
            return False, f"Strategy signal generation test failed: {str(e)}", {"error": str(e)}

    async def test_risk_management_enforcement(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test risk management enforcement."""
        try:
            # Test risk limit enforcement
            test_orders = [
                {
                    'symbol': 'AAPL',
                    'action': 'BUY',
                    'quantity': 100,
                    'price': 150.0
                },
                {
                    'symbol': 'GOOGL',
                    'action': 'BUY',
                    'quantity': 1000,  # Large order to test limits
                    'price': 2500.0
                }
            ]

            risk_results = []
            for order in test_orders:
                # Check risk limits
                risk_check = await self.risk_manager.validate_order(order)
                risk_results.append({
                    'order': order,
                    'risk_check': risk_check
                })

            # Validate risk enforcement
            risk_issues = []
            for result in risk_results:
                order = result['order']
                risk_check = result['risk_check']

                # Large order should be rejected
                if order['quantity'] >= 1000 and risk_check.get('approved', True):
                    risk_issues.append(f"Large order not rejected: {order['symbol']}")

                # Normal order should be approved
                if order['quantity'] < 1000 and not risk_check.get('approved', False):
                    risk_issues.append(f"Normal order rejected: {order['symbol']}")

            if risk_issues:
                return False, f"Risk enforcement issues: {', '.join(risk_issues)}", {
                    "test_orders": test_orders,
                    "risk_results": risk_results,
                    "risk_issues": risk_issues
                }

            return True, "Risk management enforcement test passed", {
                "test_orders": len(test_orders),
                "risk_checks_performed": len(risk_results),
                "risk_results": risk_results
            }

        except Exception as e:
            return False, f"Risk management test failed: {str(e)}", {"error": str(e)}

    async def test_order_execution_flow(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test order execution flow."""
        try:
            # Test order execution in paper trading mode
            test_order = {
                'symbol': 'AAPL',
                'action': 'BUY',
                'quantity': 10,
                'order_type': 'MARKET'
            }

            # Submit order
            order_id = await self.order_manager.submit_order(test_order)

            # Wait for order processing
            await asyncio.sleep(2)

            # Check order status
            order_status = await self.order_manager.get_order_status(order_id)

            # Validate order execution
            execution_issues = []
            if not order_id:
                execution_issues.append("Order ID not generated")
            if not order_status:
                execution_issues.append("Order status not available")
            elif order_status.get('status') not in ['FILLED', 'PENDING', 'SUBMITTED']:
                execution_issues.append(f"Invalid order status: {order_status.get('status')}")

            if execution_issues:
                return False, f"Order execution issues: {', '.join(execution_issues)}", {
                    "test_order": test_order,
                    "order_id": order_id,
                    "order_status": order_status,
                    "execution_issues": execution_issues
                }

            return True, "Order execution flow test passed", {
                "test_order": test_order,
                "order_id": order_id,
                "order_status": order_status
            }

        except Exception as e:
            return False, f"Order execution test failed: {str(e)}", {"error": str(e)}

    async def test_multi_strategy_coordination(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test multi-strategy coordination."""
        try:
            # Test multiple strategies running simultaneously
            symbols = ['AAPL', 'GOOGL', 'MSFT']
            strategy_results = {}

            # Run strategies for each symbol
            for symbol in symbols:
                market_data = await self.market_data_manager.get_real_time_data(symbol)
                prediction = await self.model_manager.predict(symbol, market_data)

                # Get signals from multiple strategies
                signals = await self.strategy_manager.generate_signals(
                    symbol, market_data, prediction
                )
                strategy_results[symbol] = signals

            # Check for strategy conflicts
            coordination_issues = []
            for symbol, signals in strategy_results.items():
                buy_signals = sum(1 for s in signals if s.get('action') == 'BUY')
                sell_signals = sum(1 for s in signals if s.get('action') == 'SELL')

                # Check for conflicting signals
                if buy_signals > 0 and sell_signals > 0:
                    coordination_issues.append(f"Conflicting signals for {symbol}")

            if coordination_issues:
                return False, f"Strategy coordination issues: {', '.join(coordination_issues)}", {
                    "symbols_tested": symbols,
                    "strategy_results": strategy_results,
                    "coordination_issues": coordination_issues
                }

            return True, "Multi-strategy coordination test passed", {
                "symbols_tested": symbols,
                "total_signals": sum(len(s) for s in strategy_results.values()),
                "strategy_results": strategy_results
            }

        except Exception as e:
            return False, f"Multi-strategy coordination test failed: {str(e)}", {"error": str(e)}

    async def test_error_recovery_scenarios(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test error recovery scenarios."""
        try:
            # Test various error scenarios and recovery
            recovery_tests = []

            # Test 1: Network timeout simulation
            try:
                # Simulate network timeout
                await asyncio.wait_for(asyncio.sleep(0.1), timeout=0.05)
                recovery_tests.append({"test": "network_timeout", "recovered": False})
            except asyncio.TimeoutError:
                recovery_tests.append({"test": "network_timeout", "recovered": True})

            # Test 2: Invalid data handling
            try:
                invalid_data = {"invalid": "data"}
                prediction = await self.model_manager.predict("INVALID", invalid_data)
                recovery_tests.append({
                    "test": "invalid_data",
                    "recovered": prediction is None or "error" in str(prediction)
                })
            except Exception:
                recovery_tests.append({"test": "invalid_data", "recovered": True})

            # Test 3: Order rejection handling
            try:
                invalid_order = {
                    'symbol': 'INVALID_SYMBOL',
                    'action': 'BUY',
                    'quantity': -100  # Invalid quantity
                }
                order_id = await self.order_manager.submit_order(invalid_order)
                recovery_tests.append({
                    "test": "invalid_order",
                    "recovered": order_id is None
                })
            except Exception:
                recovery_tests.append({"test": "invalid_order", "recovered": True})

            # Check recovery success
            failed_recoveries = [t for t in recovery_tests if not t["recovered"]]

            if failed_recoveries:
                return False, f"Failed error recoveries: {failed_recoveries}", {
                    "recovery_tests": recovery_tests,
                    "failed_recoveries": failed_recoveries
                }

            return True, "Error recovery scenarios test passed", {
                "recovery_tests": recovery_tests,
                "total_scenarios": len(recovery_tests)
            }

        except Exception as e:
            return False, f"Error recovery test failed: {str(e)}", {"error": str(e)}

    async def test_data_consistency(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test data consistency across components."""
        try:
            # Test data consistency between components
            symbol = 'AAPL'

            # Get data from different sources
            market_data_1 = await self.market_data_manager.get_real_time_data(symbol)
            await asyncio.sleep(0.1)
            market_data_2 = await self.market_data_manager.get_real_time_data(symbol)

            # Check data consistency
            consistency_issues = []

            if not market_data_1 or not market_data_2:
                consistency_issues.append("Missing market data")
            else:
                # Check timestamp consistency
                ts1 = market_data_1.get('timestamp')
                ts2 = market_data_2.get('timestamp')

                if ts1 and ts2 and ts2 < ts1:
                    consistency_issues.append("Timestamp inconsistency")

                # Check price reasonableness
                price1 = market_data_1.get('price', 0)
                price2 = market_data_2.get('price', 0)

                if price1 > 0 and price2 > 0:
                    price_change = abs(price2 - price1) / price1
                    if price_change > 0.1:  # 10% change in 0.1 seconds is unrealistic
                        consistency_issues.append("Unrealistic price change")

            if consistency_issues:
                return False, f"Data consistency issues: {', '.join(consistency_issues)}", {
                    "symbol": symbol,
                    "market_data_1": market_data_1,
                    "market_data_2": market_data_2,
                    "consistency_issues": consistency_issues
                }

            return True, "Data consistency test passed", {
                "symbol": symbol,
                "data_points_checked": 2,
                "consistency_verified": True
            }

        except Exception as e:
            return False, f"Data consistency test failed: {str(e)}", {"error": str(e)}

    async def test_performance_under_load(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test system performance under load."""
        try:
            # Test performance with multiple concurrent operations
            symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA']

            start_time = time.time()

            # Run concurrent operations
            tasks = []
            for symbol in symbols:
                tasks.append(self._performance_test_operation(symbol))

            results = await asyncio.gather(*tasks, return_exceptions=True)

            end_time = time.time()
            total_duration = end_time - start_time

            # Analyze performance
            performance_issues = []
            successful_operations = 0

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    performance_issues.append(f"Operation failed for {symbols[i]}: {result}")
                else:
                    successful_operations += 1

            # Check performance thresholds
            if total_duration > 10.0:  # Should complete within 10 seconds
                performance_issues.append(f"Operations too slow: {total_duration:.2f}s")

            if successful_operations < len(symbols) * 0.8:  # 80% success rate
                performance_issues.append(f"Low success rate: {successful_operations}/{len(symbols)}")

            if performance_issues:
                return False, f"Performance issues: {', '.join(performance_issues)}", {
                    "symbols_tested": symbols,
                    "total_duration": total_duration,
                    "successful_operations": successful_operations,
                    "performance_issues": performance_issues
                }

            return True, "Performance under load test passed", {
                "symbols_tested": len(symbols),
                "total_duration": total_duration,
                "successful_operations": successful_operations,
                "operations_per_second": len(symbols) / total_duration
            }

        except Exception as e:
            return False, f"Performance test failed: {str(e)}", {"error": str(e)}

    async def test_system_shutdown_recovery(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Test system shutdown and recovery."""
        try:
            # Test graceful shutdown and restart

            # Simulate shutdown
            if self.master_controller:
                await self.master_controller.shutdown()

            # Wait a moment
            await asyncio.sleep(1)

            # Simulate restart
            await self._setup_test_environment()

            # Test basic functionality after restart
            market_data = await self.market_data_manager.get_real_time_data('AAPL')

            recovery_issues = []
            if not market_data:
                recovery_issues.append("Market data not available after restart")

            if recovery_issues:
                return False, f"Recovery issues: {', '.join(recovery_issues)}", {
                    "recovery_issues": recovery_issues
                }

            return True, "System shutdown recovery test passed", {
                "shutdown_successful": True,
                "restart_successful": True,
                "functionality_restored": True
            }

        except Exception as e:
            return False, f"Shutdown recovery test failed: {str(e)}", {"error": str(e)}

    async def _performance_test_operation(self, symbol: str) -> Dict[str, Any]:
        """Perform a complete operation for performance testing."""
        start_time = time.time()

        # Get market data
        market_data = await self.market_data_manager.get_real_time_data(symbol)

        # Generate prediction
        prediction = await self.model_manager.predict(symbol, market_data)

        # Generate signals
        signals = await self.strategy_manager.generate_signals(symbol, market_data, prediction)

        duration = time.time() - start_time

        return {
            "symbol": symbol,
            "duration": duration,
            "market_data_received": market_data is not None,
            "prediction_generated": prediction is not None,
            "signals_generated": len(signals) if signals else 0
        }

    def _generate_report(self, total_duration: float) -> IntegrationTestReport:
        """Generate comprehensive integration test report."""
        passed = sum(1 for r in self.results if r.status == TestStatus.PASSED)
        failed = sum(1 for r in self.results if r.status == TestStatus.FAILED)
        error = sum(1 for r in self.results if r.status == TestStatus.ERROR)
        skipped = sum(1 for r in self.results if r.status == TestStatus.SKIPPED)

        return IntegrationTestReport(
            total_tests=len(self.results),
            passed_tests=passed,
            failed_tests=failed,
            error_tests=error,
            skipped_tests=skipped,
            total_duration=total_duration,
            timestamp=datetime.utcnow(),
            results=self.results.copy()
        )
