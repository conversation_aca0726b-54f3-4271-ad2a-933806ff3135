#!/usr/bin/env python3
"""
Production Setup and Configuration Script

This script helps you configure and deploy the trading bot for production use.
It guides you through all necessary configuration steps and validates your setup.
"""

import os
import sys
import json
import asyncio
import getpass
from pathlib import Path
from typing import Dict, List, Any

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.trading_bot.config.production_config import (
    ProductionConfigManager, Environment, BrokerType,
    SecurityConfig, BrokerConfig, MonitoringConfig, RiskConfig,
    PerformanceConfig, DatabaseConfig, BackupConfig
)
from src.trading_bot.security.secrets_manager import EnhancedSecretsManager, SecurityLevel, SecretType


class ProductionSetup:
    """Interactive production setup wizard."""
    
    def __init__(self):
        self.config_manager = None
        self.secrets_manager = None
        
    async def run_setup(self):
        """Run the complete production setup process."""
        print("🚀 Trading Bot Production Setup Wizard")
        print("=" * 50)
        
        try:
            # Step 1: Environment Selection
            environment = self._select_environment()
            self.config_manager = ProductionConfigManager(environment)
            
            # Step 2: Security Configuration
            await self._configure_security()
            
            # Step 3: Broker Configuration
            await self._configure_broker()
            
            # Step 4: Risk Management Configuration
            await self._configure_risk_management()
            
            # Step 5: Performance Configuration
            await self._configure_performance()
            
            # Step 6: Database Configuration
            await self._configure_database()
            
            # Step 7: Monitoring Configuration
            await self._configure_monitoring()
            
            # Step 8: Backup Configuration
            await self._configure_backup()
            
            # Step 9: Validate Configuration
            await self._validate_configuration()
            
            # Step 10: Generate Deployment Files
            await self._generate_deployment_files()
            
            # Step 11: Setup Secrets
            await self._setup_secrets()
            
            # Step 12: Final Instructions
            self._show_final_instructions()
            
            print("\n✅ Production setup completed successfully!")
            
        except KeyboardInterrupt:
            print("\n\n❌ Setup cancelled by user")
            sys.exit(1)
        except Exception as e:
            print(f"\n\n❌ Setup failed: {e}")
            sys.exit(1)
    
    def _select_environment(self) -> Environment:
        """Select deployment environment."""
        print("\n📋 Step 1: Environment Selection")
        print("-" * 30)
        
        environments = list(Environment)
        for i, env in enumerate(environments, 1):
            print(f"{i}. {env.value.title()}")
        
        while True:
            try:
                choice = int(input("\nSelect environment (1-4): "))
                if 1 <= choice <= len(environments):
                    env = environments[choice - 1]
                    print(f"Selected: {env.value.title()}")
                    return env
                else:
                    print("Invalid choice. Please try again.")
            except ValueError:
                print("Please enter a number.")
    
    async def _configure_security(self):
        """Configure security settings."""
        print("\n🔒 Step 2: Security Configuration")
        print("-" * 30)
        
        # HSM Provider
        print("\nHSM (Hardware Security Module) Provider:")
        print("1. AWS CloudHSM (Production)")
        print("2. Azure Key Vault (Production)")
        print("3. Mock HSM (Development/Testing)")
        
        hsm_choice = input("Select HSM provider (1-3): ")
        hsm_providers = {"1": "aws_cloudhsm", "2": "azure_keyvault", "3": "mock"}
        self.config_manager.security.hsm_provider = hsm_providers.get(hsm_choice, "mock")
        
        if self.config_manager.security.hsm_provider != "mock":
            cluster_id = input("Enter HSM cluster ID: ")
            self.config_manager.security.hsm_cluster_id = cluster_id
        
        # MFA Provider
        print("\nMulti-Factor Authentication:")
        print("1. TOTP (Google Authenticator, Authy)")
        print("2. SMS")
        print("3. Email")
        
        mfa_choice = input("Select MFA provider (1-3): ")
        mfa_providers = {"1": "totp", "2": "sms", "3": "email"}
        self.config_manager.security.mfa_provider = mfa_providers.get(mfa_choice, "totp")
        
        # Secret rotation
        rotation_days = input("Secret rotation interval (days) [30]: ") or "30"
        self.config_manager.security.secret_rotation_days = int(rotation_days)
        
        print("✅ Security configuration completed")
    
    async def _configure_broker(self):
        """Configure broker settings."""
        print("\n📈 Step 3: Broker Configuration")
        print("-" * 30)
        
        # Broker selection
        print("\nSupported Brokers:")
        brokers = list(BrokerType)
        for i, broker in enumerate(brokers, 1):
            print(f"{i}. {broker.value.replace('_', ' ').title()}")
        
        while True:
            try:
                choice = int(input("Select broker (1-5): "))
                if 1 <= choice <= len(brokers):
                    self.config_manager.broker.broker_type = brokers[choice - 1]
                    break
                else:
                    print("Invalid choice. Please try again.")
            except ValueError:
                print("Please enter a number.")
        
        # API endpoint
        if self.config_manager.broker.broker_type == BrokerType.WEBULL:
            self.config_manager.broker.api_endpoint = "https://api.webull.com"
        elif self.config_manager.broker.broker_type == BrokerType.ALPACA:
            paper = input("Use paper trading? (y/n) [y]: ").lower() != 'n'
            if paper:
                self.config_manager.broker.api_endpoint = "https://paper-api.alpaca.markets"
            else:
                self.config_manager.broker.api_endpoint = "https://api.alpaca.markets"
        
        # Paper trading
        paper_trading = input("Enable paper trading? (y/n) [y]: ").lower() != 'n'
        self.config_manager.broker.paper_trading = paper_trading
        
        # Rate limiting
        rate_limit = input("API rate limit (requests/second) [10]: ") or "10"
        self.config_manager.broker.rate_limit_requests_per_second = int(rate_limit)
        
        print("✅ Broker configuration completed")
    
    async def _configure_risk_management(self):
        """Configure risk management settings."""
        print("\n⚠️  Step 4: Risk Management Configuration")
        print("-" * 30)
        
        # Daily loss limit
        daily_loss = input("Maximum daily loss percentage [2.0]: ") or "2.0"
        self.config_manager.risk.max_daily_loss_percent = float(daily_loss)
        
        # Position limit
        position_limit = input("Maximum single position percentage [5.0]: ") or "5.0"
        self.config_manager.risk.max_position_percent = float(position_limit)
        
        # Sector limit
        sector_limit = input("Maximum sector concentration percentage [25.0]: ") or "25.0"
        self.config_manager.risk.max_sector_percent = float(sector_limit)
        
        # Circuit breakers
        print("\nCircuit Breaker Levels:")
        level1 = input("Level 1 (warning) daily loss [2%]: ") or "0.02"
        level2 = input("Level 2 (reduce positions) daily loss [5%]: ") or "0.05"
        level3 = input("Level 3 (stop trading) daily loss [10%]: ") or "0.10"
        
        self.config_manager.risk.circuit_breaker_levels = {
            "level_1": float(level1),
            "level_2": float(level2),
            "level_3": float(level3)
        }
        
        # Compliance features
        pdt_enabled = input("Enable PDT (Pattern Day Trading) monitoring? (y/n) [y]: ").lower() != 'n'
        self.config_manager.risk.pdt_enabled = pdt_enabled
        
        wash_sale_enabled = input("Enable wash sale detection? (y/n) [y]: ").lower() != 'n'
        self.config_manager.risk.wash_sale_enabled = wash_sale_enabled
        
        print("✅ Risk management configuration completed")
    
    async def _configure_performance(self):
        """Configure performance settings."""
        print("\n⚡ Step 5: Performance Configuration")
        print("-" * 30)
        
        # Optimization level
        print("\nPerformance Optimization Levels:")
        print("1. Standard (Basic optimizations)")
        print("2. High (Memory pools + CPU affinity)")
        print("3. Ultra Low Latency (+ Huge pages + lock-free)")
        print("4. Real Time (+ Real-time scheduling + kernel bypass)")
        
        perf_choice = input("Select optimization level (1-4) [2]: ") or "2"
        perf_levels = {"1": "standard", "2": "high", "3": "ultra_low_latency", "4": "real_time"}
        self.config_manager.performance.optimization_level = perf_levels.get(perf_choice, "high")
        
        if self.config_manager.performance.optimization_level in ["high", "ultra_low_latency", "real_time"]:
            # CPU affinity
            import psutil
            cpu_count = psutil.cpu_count()
            print(f"\nDetected {cpu_count} CPU cores")
            
            trading_cores = input(f"Trading thread CPU cores (comma-separated) [0,1]: ") or "0,1"
            self.config_manager.performance.cpu_cores_trading = [int(x.strip()) for x in trading_cores.split(",")]
            
            data_cores = input(f"Data thread CPU cores (comma-separated) [2,3]: ") or "2,3"
            self.config_manager.performance.cpu_cores_data = [int(x.strip()) for x in data_cores.split(",")]
            
            # Memory pool size
            memory_pool = input("Memory pool size (MB) [64]: ") or "64"
            self.config_manager.performance.memory_pool_size_mb = int(memory_pool)
        
        if self.config_manager.performance.optimization_level in ["ultra_low_latency", "real_time"]:
            # Huge pages
            huge_pages = input("Enable huge pages? (y/n) [y]: ").lower() != 'n'
            self.config_manager.performance.huge_pages_enabled = huge_pages
        
        if self.config_manager.performance.optimization_level == "real_time":
            # Real-time priority
            rt_priority = input("Real-time priority (1-99) [50]: ") or "50"
            self.config_manager.performance.real_time_priority = int(rt_priority)
            
            print("⚠️  Real-time optimization requires root privileges")
        
        print("✅ Performance configuration completed")
    
    async def _configure_database(self):
        """Configure database settings."""
        print("\n🗄️  Step 6: Database Configuration")
        print("-" * 30)
        
        # PostgreSQL
        print("PostgreSQL Configuration:")
        pg_host = input("PostgreSQL host [localhost]: ") or "localhost"
        self.config_manager.database.postgres_host = pg_host
        
        pg_port = input("PostgreSQL port [5432]: ") or "5432"
        self.config_manager.database.postgres_port = int(pg_port)
        
        pg_db = input("PostgreSQL database name [trading_bot]: ") or "trading_bot"
        self.config_manager.database.postgres_database = pg_db
        
        pg_user = input("PostgreSQL username [trading_user]: ") or "trading_user"
        self.config_manager.database.postgres_user = pg_user
        
        # Redis
        print("\nRedis Configuration:")
        redis_host = input("Redis host [localhost]: ") or "localhost"
        self.config_manager.database.redis_host = redis_host
        
        redis_port = input("Redis port [6379]: ") or "6379"
        self.config_manager.database.redis_port = int(redis_port)
        
        # Connection pooling
        pool_size = input("Connection pool size [20]: ") or "20"
        self.config_manager.database.connection_pool_size = int(pool_size)
        
        print("✅ Database configuration completed")
    
    async def _configure_monitoring(self):
        """Configure monitoring settings."""
        print("\n📊 Step 7: Monitoring Configuration")
        print("-" * 30)
        
        # Prometheus
        prom_port = input("Prometheus port [9090]: ") or "9090"
        self.config_manager.monitoring.prometheus_port = int(prom_port)
        
        # Grafana
        grafana_port = input("Grafana port [3000]: ") or "3000"
        self.config_manager.monitoring.grafana_port = int(grafana_port)
        
        # Alerting
        print("\nAlert Configuration:")
        slack_webhook = input("Slack webhook URL (optional): ")
        if slack_webhook:
            self.config_manager.monitoring.slack_webhook_url = slack_webhook
        
        email_server = input("Email SMTP server [smtp.gmail.com]: ") or "smtp.gmail.com"
        self.config_manager.monitoring.email_smtp_server = email_server
        
        email_port = input("Email SMTP port [587]: ") or "587"
        self.config_manager.monitoring.email_smtp_port = int(email_port)
        
        # SMS/Phone
        sms_provider = input("SMS provider (twilio/aws_sns) [twilio]: ") or "twilio"
        self.config_manager.monitoring.sms_provider = sms_provider
        
        print("✅ Monitoring configuration completed")
    
    async def _configure_backup(self):
        """Configure backup settings."""
        print("\n💾 Step 8: Backup Configuration")
        print("-" * 30)
        
        # S3 bucket
        s3_bucket = input("S3 bucket for backups: ")
        self.config_manager.backup.s3_bucket = s3_bucket
        
        # Geographic regions
        regions = input("Geographic regions (comma-separated) [us-east-1,us-west-2]: ") or "us-east-1,us-west-2"
        self.config_manager.backup.geographic_regions = [r.strip() for r in regions.split(",")]
        
        # Retention
        retention = input("Backup retention (days) [30]: ") or "30"
        self.config_manager.backup.backup_retention_days = int(retention)
        
        # Schedule
        schedule = input("Backup schedule (cron) [0 2 * * *]: ") or "0 2 * * *"
        self.config_manager.backup.backup_schedule_cron = schedule
        
        print("✅ Backup configuration completed")
    
    async def _validate_configuration(self):
        """Validate the complete configuration."""
        print("\n✅ Step 9: Configuration Validation")
        print("-" * 30)
        
        issues = self.config_manager.validate_config()
        
        if issues["errors"]:
            print("❌ Configuration Errors:")
            for error in issues["errors"]:
                print(f"  • {error}")
            
            fix_errors = input("\nFix errors before continuing? (y/n): ").lower() == 'y'
            if fix_errors:
                print("Please fix the errors and run setup again.")
                sys.exit(1)
        
        if issues["warnings"]:
            print("⚠️  Configuration Warnings:")
            for warning in issues["warnings"]:
                print(f"  • {warning}")
        
        if issues["recommendations"]:
            print("💡 Recommendations:")
            for rec in issues["recommendations"]:
                print(f"  • {rec}")
        
        if not issues["errors"]:
            print("✅ Configuration validation passed")
    
    async def _generate_deployment_files(self):
        """Generate deployment configuration files."""
        print("\n📁 Step 10: Generating Deployment Files")
        print("-" * 30)
        
        # Save configuration template
        template_file = self.config_manager.save_config_template()
        print(f"✅ Configuration template: {template_file}")
        
        # Generate Docker Compose
        compose_content = self.config_manager.generate_docker_compose()
        with open("docker-compose.yml", "w") as f:
            f.write(compose_content)
        print("✅ Docker Compose file: docker-compose.yml")
        
        # Generate environment file
        env_vars = self.config_manager.get_environment_variables()
        with open(".env.template", "w") as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")
        print("✅ Environment template: .env.template")
        
        # Generate secrets list
        secrets = self.config_manager.get_secrets_list()
        with open("secrets_checklist.txt", "w") as f:
            f.write("Required Secrets Configuration:\n")
            f.write("=" * 35 + "\n\n")
            for secret in secrets:
                f.write(f"• {secret}\n")
        print("✅ Secrets checklist: secrets_checklist.txt")
    
    async def _setup_secrets(self):
        """Setup secrets management."""
        print("\n🔐 Step 11: Secrets Setup")
        print("-" * 30)
        
        setup_secrets = input("Setup secrets now? (y/n) [y]: ").lower() != 'n'
        
        if setup_secrets:
            self.secrets_manager = EnhancedSecretsManager()
            
            # Broker credentials
            print(f"\n{self.config_manager.broker.broker_type.value.title()} Credentials:")
            username = input("Username/Email: ")
            password = getpass.getpass("Password: ")
            api_key = input("API Key (if applicable): ")
            
            await self.secrets_manager.store_secret(
                name="broker_username",
                value=username,
                secret_type=SecretType.API_KEY,
                security_level=SecurityLevel.HIGH
            )
            
            await self.secrets_manager.store_secret(
                name="broker_password", 
                value=password,
                secret_type=SecretType.PASSWORD,
                security_level=SecurityLevel.CRITICAL
            )
            
            if api_key:
                await self.secrets_manager.store_secret(
                    name="broker_api_key",
                    value=api_key,
                    secret_type=SecretType.API_KEY,
                    security_level=SecurityLevel.CRITICAL
                )
            
            # Database passwords
            print("\nDatabase Passwords:")
            pg_password = getpass.getpass("PostgreSQL password: ")
            redis_password = getpass.getpass("Redis password: ")
            
            await self.secrets_manager.store_secret(
                name="postgres_password",
                value=pg_password,
                secret_type=SecretType.PASSWORD,
                security_level=SecurityLevel.HIGH
            )
            
            await self.secrets_manager.store_secret(
                name="redis_password",
                value=redis_password,
                secret_type=SecretType.PASSWORD,
                security_level=SecurityLevel.HIGH
            )
            
            print("✅ Secrets configured successfully")
        else:
            print("⚠️  Remember to configure secrets before deployment")
    
    def _show_final_instructions(self):
        """Show final deployment instructions."""
        print("\n🎯 Step 12: Final Instructions")
        print("-" * 30)
        
        print("\n📋 Next Steps:")
        print("1. Review generated configuration files")
        print("2. Copy .env.template to .env and fill in missing values")
        print("3. Configure all required secrets (see secrets_checklist.txt)")
        print("4. Set up monitoring dashboards")
        print("5. Test in staging environment first")
        print("6. Run extended testing suite")
        print("7. Deploy to production")
        
        print("\n🚀 Deployment Commands:")
        print("# Start services")
        print("docker-compose up -d")
        print("")
        print("# View logs")
        print("docker-compose logs -f trading-bot")
        print("")
        print("# Stop services")
        print("docker-compose down")
        
        print("\n📊 Monitoring URLs:")
        print(f"• Grafana: http://localhost:{self.config_manager.monitoring.grafana_port}")
        print(f"• Prometheus: http://localhost:{self.config_manager.monitoring.prometheus_port}")
        
        print("\n⚠️  IMPORTANT REMINDERS:")
        print("• Start with paper trading")
        print("• Test all emergency procedures")
        print("• Monitor system closely for first 24 hours")
        print("• Have emergency contacts ready")
        print("• Keep backups of all configuration")


async def main():
    """Main setup function."""
    setup = ProductionSetup()
    await setup.run_setup()


if __name__ == "__main__":
    asyncio.run(main())
