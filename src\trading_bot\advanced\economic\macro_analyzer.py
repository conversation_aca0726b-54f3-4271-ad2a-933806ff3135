"""Macroeconomic analysis and regime detection."""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

from ...core.config import settings
from ...core.logger import get_logger
from .indicators_fetcher import IndicatorsFetcher, EconomicSnapshot

logger = get_logger(__name__)


class EconomicRegime(Enum):
    """Economic regime types."""
    EXPANSION = "expansion"
    RECESSION = "recession"
    RECOVERY = "recovery"
    STAGFLATION = "stagflation"
    DEFLATION = "deflation"
    UNKNOWN = "unknown"


@dataclass
class RegimeAnalysis:
    """Economic regime analysis results."""
    current_regime: EconomicRegime
    regime_probability: float
    regime_duration: int  # months in current regime
    transition_probability: Dict[EconomicRegime, float]
    key_indicators: Dict[str, float]
    regime_characteristics: Dict[str, Any]


@dataclass
class MacroTrend:
    """Macroeconomic trend analysis."""
    indicator: str
    current_value: float
    trend_direction: str  # 'up', 'down', 'stable'
    trend_strength: float  # 0-1
    momentum: float
    support_level: Optional[float]
    resistance_level: Optional[float]
    forecast_3m: Optional[float]
    forecast_6m: Optional[float]


class MacroAnalyzer:
    """Macroeconomic analysis and regime detection."""
    
    def __init__(self):
        self.indicators_fetcher = IndicatorsFetcher()
        self.regime_history = []
        self.trend_cache = {}
        
    async def analyze_economic_regime(self) -> RegimeAnalysis:
        """
        Analyze current economic regime.
        
        Returns:
            Economic regime analysis
        """
        try:
            # Get current economic snapshot
            snapshot = await self.indicators_fetcher.fetch_economic_snapshot()
            
            # Get historical data for regime analysis
            historical_data = await self._fetch_regime_indicators()
            
            # Detect current regime
            current_regime = self._detect_regime(snapshot, historical_data)
            
            # Calculate regime probability
            regime_probability = self._calculate_regime_probability(snapshot, current_regime)
            
            # Estimate regime duration
            regime_duration = self._estimate_regime_duration(current_regime)
            
            # Calculate transition probabilities
            transition_probs = self._calculate_transition_probabilities(
                current_regime, snapshot
            )
            
            # Extract key indicators
            key_indicators = self._extract_key_indicators(snapshot)
            
            # Get regime characteristics
            regime_characteristics = self._get_regime_characteristics(current_regime)
            
            return RegimeAnalysis(
                current_regime=current_regime,
                regime_probability=regime_probability,
                regime_duration=regime_duration,
                transition_probability=transition_probs,
                key_indicators=key_indicators,
                regime_characteristics=regime_characteristics
            )
            
        except Exception as e:
            logger.error(f"Error analyzing economic regime: {e}")
            return self._empty_regime_analysis()
    
    async def analyze_macro_trends(
        self,
        indicators: List[str] = None
    ) -> Dict[str, MacroTrend]:
        """
        Analyze macroeconomic trends.
        
        Args:
            indicators: List of indicators to analyze
            
        Returns:
            Dictionary of macro trend analyses
        """
        if indicators is None:
            indicators = [
                'fed_funds_rate', 'unemployment_rate', 'cpi_all_items',
                'real_gdp', 'consumer_sentiment', 'housing_starts'
            ]
        
        try:
            trend_analyses = {}
            
            for indicator in indicators:
                series_id = self.indicators_fetcher.get_series_id(indicator)
                if not series_id:
                    continue
                
                # Get historical data
                historical_data = await self.indicators_fetcher.fetch_indicator_history(
                    series_id, limit=60  # 5 years of monthly data
                )
                
                if len(historical_data) < 12:  # Need at least 1 year
                    continue
                
                # Analyze trend
                trend = self._analyze_indicator_trend(indicator, historical_data)
                trend_analyses[indicator] = trend
            
            return trend_analyses
            
        except Exception as e:
            logger.error(f"Error analyzing macro trends: {e}")
            return {}
    
    async def get_recession_probability(self) -> Dict[str, float]:
        """
        Calculate recession probability using various models.
        
        Returns:
            Dictionary with recession probabilities from different models
        """
        try:
            # Get key indicators
            indicators = await self._fetch_recession_indicators()
            
            if not indicators:
                return {'overall': 0.5}
            
            # Yield curve model
            yield_curve_prob = self._yield_curve_recession_model(indicators)
            
            # Sahm rule model
            sahm_rule_prob = self._sahm_rule_model(indicators)
            
            # Leading indicators model
            leading_indicators_prob = self._leading_indicators_model(indicators)
            
            # Ensemble probability
            overall_prob = np.mean([
                yield_curve_prob, sahm_rule_prob, leading_indicators_prob
            ])
            
            return {
                'overall': overall_prob,
                'yield_curve': yield_curve_prob,
                'sahm_rule': sahm_rule_prob,
                'leading_indicators': leading_indicators_prob
            }
            
        except Exception as e:
            logger.error(f"Error calculating recession probability: {e}")
            return {'overall': 0.5}
    
    async def _fetch_regime_indicators(self) -> pd.DataFrame:
        """Fetch indicators for regime analysis."""
        try:
            # Key indicators for regime detection
            indicators = [
                'GDPC1',      # Real GDP
                'UNRATE',     # Unemployment Rate
                'CPIAUCSL',   # CPI
                'FEDFUNDS',   # Fed Funds Rate
                'GS10',       # 10-Year Treasury
                'UMCSENT',    # Consumer Sentiment
                'INDPRO'      # Industrial Production
            ]
            
            # Fetch data for all indicators
            indicator_data = await self.indicators_fetcher.fetch_multiple_indicators(
                indicators, limit=120  # 10 years of monthly data
            )
            
            # Convert to DataFrame
            df_data = {}
            for series_id, data_list in indicator_data.items():
                if data_list:
                    dates = [d.date for d in data_list]
                    values = [d.value for d in data_list]
                    df_data[series_id] = pd.Series(values, index=dates)
            
            if not df_data:
                return pd.DataFrame()
            
            df = pd.DataFrame(df_data)
            df = df.sort_index()
            
            # Calculate derived indicators
            if 'GDPC1' in df.columns:
                df['GDP_YoY'] = df['GDPC1'].pct_change(periods=4) * 100  # YoY growth
            
            if 'CPIAUCSL' in df.columns:
                df['Inflation_YoY'] = df['CPIAUCSL'].pct_change(periods=12) * 100
            
            if 'GS10' in df.columns and 'FEDFUNDS' in df.columns:
                df['Yield_Spread'] = df['GS10'] - df['FEDFUNDS']
            
            return df.dropna()
            
        except Exception as e:
            logger.error(f"Error fetching regime indicators: {e}")
            return pd.DataFrame()
    
    def _detect_regime(
        self,
        snapshot: EconomicSnapshot,
        historical_data: pd.DataFrame
    ) -> EconomicRegime:
        """Detect current economic regime."""
        try:
            if historical_data.empty:
                return EconomicRegime.UNKNOWN
            
            # Simple rule-based regime detection
            # In practice, this would use more sophisticated ML models
            
            gdp_growth = snapshot.gdp_growth or 0
            unemployment = snapshot.unemployment_rate or 5
            inflation = snapshot.inflation_rate or 2
            
            # Recession indicators
            if gdp_growth < -1 or unemployment > 7:
                return EconomicRegime.RECESSION
            
            # Stagflation indicators
            if inflation > 5 and gdp_growth < 1:
                return EconomicRegime.STAGFLATION
            
            # Deflation indicators
            if inflation < 0:
                return EconomicRegime.DEFLATION
            
            # Recovery indicators
            if gdp_growth > 0 and gdp_growth < 2 and unemployment > 5:
                return EconomicRegime.RECOVERY
            
            # Expansion (default)
            if gdp_growth > 2 and unemployment < 5:
                return EconomicRegime.EXPANSION
            
            return EconomicRegime.UNKNOWN
            
        except Exception as e:
            logger.error(f"Error detecting regime: {e}")
            return EconomicRegime.UNKNOWN
    
    def _calculate_regime_probability(
        self,
        snapshot: EconomicSnapshot,
        regime: EconomicRegime
    ) -> float:
        """Calculate probability of being in the detected regime."""
        # Simplified probability calculation
        # In practice, this would use ML models trained on historical data
        
        base_probability = 0.7
        
        # Adjust based on indicator strength
        if regime == EconomicRegime.RECESSION:
            if snapshot.unemployment_rate and snapshot.unemployment_rate > 8:
                base_probability += 0.2
            if snapshot.gdp_growth and snapshot.gdp_growth < -2:
                base_probability += 0.1
        
        elif regime == EconomicRegime.EXPANSION:
            if snapshot.gdp_growth and snapshot.gdp_growth > 3:
                base_probability += 0.2
            if snapshot.unemployment_rate and snapshot.unemployment_rate < 4:
                base_probability += 0.1
        
        return min(1.0, base_probability)
    
    def _estimate_regime_duration(self, regime: EconomicRegime) -> int:
        """Estimate how long we've been in the current regime."""
        # This would typically analyze historical regime transitions
        # For now, return typical durations
        
        typical_durations = {
            EconomicRegime.EXPANSION: 60,    # 5 years
            EconomicRegime.RECESSION: 12,    # 1 year
            EconomicRegime.RECOVERY: 24,     # 2 years
            EconomicRegime.STAGFLATION: 36,  # 3 years
            EconomicRegime.DEFLATION: 18,    # 1.5 years
            EconomicRegime.UNKNOWN: 6
        }
        
        return typical_durations.get(regime, 12)
    
    def _calculate_transition_probabilities(
        self,
        current_regime: EconomicRegime,
        snapshot: EconomicSnapshot
    ) -> Dict[EconomicRegime, float]:
        """Calculate probabilities of transitioning to other regimes."""
        # Simplified transition matrix
        # In practice, this would be estimated from historical data
        
        transition_matrices = {
            EconomicRegime.EXPANSION: {
                EconomicRegime.EXPANSION: 0.85,
                EconomicRegime.RECESSION: 0.10,
                EconomicRegime.STAGFLATION: 0.05
            },
            EconomicRegime.RECESSION: {
                EconomicRegime.RECESSION: 0.60,
                EconomicRegime.RECOVERY: 0.35,
                EconomicRegime.DEFLATION: 0.05
            },
            EconomicRegime.RECOVERY: {
                EconomicRegime.RECOVERY: 0.70,
                EconomicRegime.EXPANSION: 0.25,
                EconomicRegime.RECESSION: 0.05
            }
        }
        
        return transition_matrices.get(current_regime, {})
    
    def _analyze_indicator_trend(
        self,
        indicator: str,
        historical_data: List
    ) -> MacroTrend:
        """Analyze trend for a specific indicator."""
        try:
            if len(historical_data) < 3:
                return self._empty_trend(indicator)
            
            # Extract values and dates
            values = [d.value for d in historical_data]
            dates = [d.date for d in historical_data]
            
            # Sort by date
            sorted_data = sorted(zip(dates, values))
            values = [v for d, v in sorted_data]
            
            current_value = values[-1]
            
            # Calculate trend direction and strength
            if len(values) >= 12:
                # Use linear regression for trend
                x = np.arange(len(values))
                slope = np.polyfit(x, values, 1)[0]
                
                # Normalize slope
                value_range = max(values) - min(values)
                if value_range > 0:
                    trend_strength = abs(slope) / (value_range / len(values))
                else:
                    trend_strength = 0
                
                trend_strength = min(1.0, trend_strength)
                
                if slope > 0:
                    trend_direction = 'up'
                elif slope < 0:
                    trend_direction = 'down'
                else:
                    trend_direction = 'stable'
            else:
                # Simple comparison for short series
                if values[-1] > values[0]:
                    trend_direction = 'up'
                    trend_strength = 0.5
                elif values[-1] < values[0]:
                    trend_direction = 'down'
                    trend_strength = 0.5
                else:
                    trend_direction = 'stable'
                    trend_strength = 0.1
            
            # Calculate momentum (recent change)
            if len(values) >= 3:
                recent_change = (values[-1] - values[-3]) / values[-3] if values[-3] != 0 else 0
                momentum = np.tanh(recent_change * 10)  # Normalize to [-1, 1]
            else:
                momentum = 0.0
            
            # Calculate support/resistance (simplified)
            support_level = min(values[-12:]) if len(values) >= 12 else min(values)
            resistance_level = max(values[-12:]) if len(values) >= 12 else max(values)
            
            return MacroTrend(
                indicator=indicator,
                current_value=current_value,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                momentum=momentum,
                support_level=support_level,
                resistance_level=resistance_level,
                forecast_3m=None,  # Would need forecasting model
                forecast_6m=None
            )
            
        except Exception as e:
            logger.error(f"Error analyzing trend for {indicator}: {e}")
            return self._empty_trend(indicator)
    
    async def _fetch_recession_indicators(self) -> Dict[str, List]:
        """Fetch indicators for recession probability models."""
        try:
            indicators = ['GS10', 'GS2', 'UNRATE', 'PAYEMS', 'INDPRO']
            return await self.indicators_fetcher.fetch_multiple_indicators(
                indicators, limit=60
            )
        except Exception as e:
            logger.error(f"Error fetching recession indicators: {e}")
            return {}
    
    def _yield_curve_recession_model(self, indicators: Dict[str, List]) -> float:
        """Recession probability based on yield curve inversion."""
        try:
            if 'GS10' not in indicators or 'GS2' not in indicators:
                return 0.5
            
            gs10_data = indicators['GS10']
            gs2_data = indicators['GS2']
            
            if not gs10_data or not gs2_data:
                return 0.5
            
            # Get latest yield spread
            latest_10y = gs10_data[0].value
            latest_2y = gs2_data[0].value
            
            yield_spread = latest_10y - latest_2y
            
            # Empirical relationship: inverted curve increases recession probability
            if yield_spread < -0.5:
                return 0.8
            elif yield_spread < 0:
                return 0.6
            elif yield_spread < 0.5:
                return 0.4
            else:
                return 0.2
                
        except Exception as e:
            logger.error(f"Error in yield curve model: {e}")
            return 0.5
    
    def _sahm_rule_model(self, indicators: Dict[str, List]) -> float:
        """Recession probability based on Sahm rule."""
        try:
            if 'UNRATE' not in indicators:
                return 0.5
            
            unrate_data = indicators['UNRATE']
            if len(unrate_data) < 12:
                return 0.5
            
            # Sahm rule: recession when 3-month average unemployment rate
            # rises by 0.5pp or more above its low in the prior 12 months
            
            recent_values = [d.value for d in unrate_data[:3]]
            past_12_values = [d.value for d in unrate_data[:12]]
            
            current_3m_avg = np.mean(recent_values)
            min_12m = min(past_12_values)
            
            sahm_indicator = current_3m_avg - min_12m
            
            if sahm_indicator >= 0.5:
                return 0.9
            elif sahm_indicator >= 0.3:
                return 0.6
            else:
                return 0.2
                
        except Exception as e:
            logger.error(f"Error in Sahm rule model: {e}")
            return 0.5
    
    def _leading_indicators_model(self, indicators: Dict[str, List]) -> float:
        """Recession probability based on leading indicators."""
        # Simplified model using industrial production and employment
        try:
            recession_signals = 0
            total_signals = 0
            
            # Industrial production declining
            if 'INDPRO' in indicators and len(indicators['INDPRO']) >= 6:
                indpro_data = [d.value for d in indicators['INDPRO'][:6]]
                if indpro_data[0] < indpro_data[5]:  # 6-month decline
                    recession_signals += 1
                total_signals += 1
            
            # Employment declining
            if 'PAYEMS' in indicators and len(indicators['PAYEMS']) >= 3:
                payems_data = [d.value for d in indicators['PAYEMS'][:3]]
                if payems_data[0] < payems_data[2]:  # 3-month decline
                    recession_signals += 1
                total_signals += 1
            
            if total_signals == 0:
                return 0.5
            
            signal_ratio = recession_signals / total_signals
            return 0.2 + (signal_ratio * 0.6)  # Scale to 0.2-0.8 range
            
        except Exception as e:
            logger.error(f"Error in leading indicators model: {e}")
            return 0.5
    
    def _extract_key_indicators(self, snapshot: EconomicSnapshot) -> Dict[str, float]:
        """Extract key indicators from economic snapshot."""
        return {
            'gdp_growth': snapshot.gdp_growth or 0,
            'inflation_rate': snapshot.inflation_rate or 0,
            'unemployment_rate': snapshot.unemployment_rate or 0,
            'fed_funds_rate': snapshot.fed_funds_rate or 0,
            'yield_curve_10y2y': snapshot.yield_curve_10y2y or 0,
            'consumer_confidence': snapshot.consumer_confidence or 0
        }
    
    def _get_regime_characteristics(self, regime: EconomicRegime) -> Dict[str, Any]:
        """Get characteristics of the economic regime."""
        characteristics = {
            EconomicRegime.EXPANSION: {
                'description': 'Economic growth, low unemployment, stable inflation',
                'typical_duration_months': 60,
                'asset_preferences': ['stocks', 'commodities'],
                'risk_level': 'low'
            },
            EconomicRegime.RECESSION: {
                'description': 'Economic contraction, rising unemployment',
                'typical_duration_months': 12,
                'asset_preferences': ['bonds', 'cash'],
                'risk_level': 'high'
            },
            EconomicRegime.RECOVERY: {
                'description': 'Economic recovery from recession',
                'typical_duration_months': 24,
                'asset_preferences': ['stocks', 'real_estate'],
                'risk_level': 'medium'
            },
            EconomicRegime.STAGFLATION: {
                'description': 'High inflation with slow growth',
                'typical_duration_months': 36,
                'asset_preferences': ['commodities', 'real_estate'],
                'risk_level': 'high'
            },
            EconomicRegime.DEFLATION: {
                'description': 'Falling prices and economic weakness',
                'typical_duration_months': 18,
                'asset_preferences': ['bonds', 'cash'],
                'risk_level': 'very_high'
            }
        }
        
        return characteristics.get(regime, {})
    
    def _empty_regime_analysis(self) -> RegimeAnalysis:
        """Return empty regime analysis."""
        return RegimeAnalysis(
            current_regime=EconomicRegime.UNKNOWN,
            regime_probability=0.5,
            regime_duration=0,
            transition_probability={},
            key_indicators={},
            regime_characteristics={}
        )
    
    def _empty_trend(self, indicator: str) -> MacroTrend:
        """Return empty trend analysis."""
        return MacroTrend(
            indicator=indicator,
            current_value=0.0,
            trend_direction='stable',
            trend_strength=0.0,
            momentum=0.0,
            support_level=None,
            resistance_level=None,
            forecast_3m=None,
            forecast_6m=None
        )
