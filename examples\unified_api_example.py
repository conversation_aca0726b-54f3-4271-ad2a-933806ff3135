"""
Comprehensive example of the unified Webull API framework.

This example demonstrates:
- Connecting to Webull
- Subscribing to real-time data
- Placing orders with proper error handling
- Graceful shutdown
"""

import asyncio
import signal
from decimal import Decimal
from typing import Dict, List

from trading_bot.api import WebullAPI
from trading_bot.models.enums import OrderSide, OrderType, TimeInForce
from trading_bot.models.market import Quote
from trading_bot.models.orders import OrderRequest
from trading_bot.utils.logger import get_structured_logger

logger = get_structured_logger(__name__)


class TradingBotExample:
    """Example trading bot using the unified API."""
    
    def __init__(self):
        self.api = WebullAPI(paper_trading=True)  # Use paper trading for safety
        self.running = True
        self.watchlist = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"]
        self.quote_cache: Dict[str, Quote] = {}
        
    async def start(self):
        """Start the trading bot."""
        try:
            logger.info("Starting trading bot example")
            
            # Initialize API
            async with self.api:
                # Login
                await self.login()
                
                # Get account information
                await self.get_account_info()
                
                # Connect WebSocket for real-time data
                await self.connect_realtime_data()
                
                # Subscribe to quotes
                await self.subscribe_to_quotes()
                
                # Get market data
                await self.get_market_data()
                
                # Example trading operations
                await self.example_trading_operations()
                
                # Run main loop
                await self.run_main_loop()
                
        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
        except Exception as e:
            logger.error(f"Trading bot error: {e}")
        finally:
            await self.shutdown()
    
    async def login(self):
        """Login to Webull."""
        try:
            logger.info("Logging in to Webull...")
            
            # Login with credentials from config
            success = await self.api.login()
            
            if success:
                logger.info("Login successful")
            else:
                logger.error("Login failed")
                raise Exception("Authentication failed")
                
        except Exception as e:
            logger.error(f"Login error: {e}")
            raise
    
    async def get_account_info(self):
        """Get and display account information."""
        try:
            logger.info("Getting account information...")
            
            # Get account summary
            account_summary = await self.api.get_account_summary()
            
            logger.info(
                "Account Summary",
                extra={
                    "account_id": account_summary.account.id,
                    "total_value": float(account_summary.balance.total_value),
                    "cash": float(account_summary.balance.cash),
                    "buying_power": float(account_summary.balance.buying_power),
                    "day_pnl": float(account_summary.balance.day_pnl),
                    "is_paper": account_summary.account.is_paper,
                }
            )
            
            # Get positions
            positions = await self.api.get_positions()
            logger.info(f"Current positions: {len(positions)}")
            
            for position in positions:
                logger.info(
                    f"Position: {position.symbol}",
                    extra={
                        "symbol": position.symbol,
                        "side": position.side.value,
                        "quantity": position.quantity,
                        "avg_price": float(position.avg_price),
                        "current_price": float(position.current_price) if position.current_price else None,
                        "unrealized_pnl": float(position.unrealized_pnl) if position.unrealized_pnl else None,
                    }
                )
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
    
    async def connect_realtime_data(self):
        """Connect to real-time data stream."""
        try:
            logger.info("Connecting to real-time data...")
            
            success = await self.api.connect_websocket()
            
            if success:
                logger.info("WebSocket connected successfully")
            else:
                logger.warning("WebSocket connection failed, continuing without real-time data")
                
        except Exception as e:
            logger.warning(f"WebSocket connection error: {e}")
    
    async def subscribe_to_quotes(self):
        """Subscribe to real-time quotes for watchlist."""
        if not self.api.is_websocket_connected:
            logger.warning("WebSocket not connected, skipping quote subscription")
            return
        
        try:
            logger.info(f"Subscribing to quotes for {len(self.watchlist)} symbols")
            
            # Subscribe with callback
            success = await self.api.subscribe_quotes(
                self.watchlist,
                callback=self.on_quote_update
            )
            
            if success:
                logger.info("Quote subscription successful")
            else:
                logger.warning("Quote subscription failed")
                
        except Exception as e:
            logger.error(f"Quote subscription error: {e}")
    
    async def on_quote_update(self, quote: Quote):
        """Handle real-time quote updates."""
        self.quote_cache[quote.symbol] = quote
        
        logger.debug(
            f"Quote update: {quote.symbol}",
            extra={
                "symbol": quote.symbol,
                "price": float(quote.price),
                "bid": float(quote.bid) if quote.bid else None,
                "ask": float(quote.ask) if quote.ask else None,
                "volume": quote.volume,
            }
        )
        
        # Example: Simple price alert
        if quote.symbol == "AAPL" and quote.price > Decimal("200"):
            logger.info(f"AAPL price alert: ${quote.price}")
    
    async def get_market_data(self):
        """Get various market data."""
        try:
            logger.info("Getting market data...")
            
            # Get quotes for watchlist
            quotes = await self.api.get_quotes(self.watchlist)
            logger.info(f"Retrieved quotes for {len(quotes)} symbols")
            
            # Get historical bars for AAPL
            bars = await self.api.get_bars("AAPL", count=50)
            logger.info(f"Retrieved {len(bars)} bars for AAPL")
            
            # Get market hours
            market_hours = await self.api.get_market_hours()
            logger.info(f"Market is {'open' if market_hours.is_open else 'closed'}")
            
            # Get market movers
            movers = await self.api.get_movers("up", limit=5)
            logger.info(f"Top 5 gainers: {[m.get('symbol') for m in movers]}")
            
            # Search for stocks
            search_results = await self.api.search_stocks("tech", limit=5)
            logger.info(f"Search results: {[s.symbol for s in search_results]}")
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
    
    async def example_trading_operations(self):
        """Example trading operations."""
        try:
            logger.info("Demonstrating trading operations...")
            
            # Example 1: Place a limit buy order for AAPL
            order_request = OrderRequest(
                symbol="AAPL",
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                quantity=1,
                price=Decimal("150.00"),
                time_in_force=TimeInForce.DAY,
            )
            
            logger.info("Placing example limit buy order for AAPL")
            order = await self.api.place_order(order_request)
            logger.info(f"Order placed: {order.id}")
            
            # Wait a moment
            await asyncio.sleep(2)
            
            # Cancel the order
            logger.info("Cancelling the order")
            cancelled = await self.api.cancel_order(order.id)
            logger.info(f"Order cancelled: {cancelled}")
            
            # Get recent orders
            orders = await self.api.get_orders()
            logger.info(f"Total orders: {len(orders)}")
            
            # Example 2: Paper trading specific features
            if self.api.paper_trading:
                logger.info("Demonstrating paper trading features...")
                
                # Get paper performance
                performance = await self.api.get_paper_performance()
                logger.info(
                    "Paper trading performance",
                    extra={
                        "total_pnl": performance["total_pnl"],
                        "total_return_pct": performance["total_return_pct"],
                        "win_rate": performance["win_rate"],
                        "profit_factor": performance["profit_factor"],
                    }
                )
                
                # Simulate order scenarios
                scenarios = [
                    {"name": "Bull Market", "price": 155.0, "volume": 1000000, "volatility": 0.01},
                    {"name": "Bear Market", "price": 145.0, "volume": 2000000, "volatility": 0.03},
                    {"name": "Sideways", "price": 150.0, "volume": 500000, "volatility": 0.005},
                ]
                
                simulation_results = await self.api.simulate_order_scenarios(order_request, scenarios)
                
                for result in simulation_results:
                    logger.info(
                        f"Simulation: {result['scenario']}",
                        extra={
                            "filled": result.get("filled", False),
                            "fill_price": result.get("fill_price"),
                            "unrealized_pnl": result.get("unrealized_pnl"),
                        }
                    )
            
        except Exception as e:
            logger.error(f"Error in trading operations: {e}")
    
    async def run_main_loop(self):
        """Run the main trading loop."""
        logger.info("Starting main trading loop (press Ctrl+C to stop)")
        
        try:
            while self.running:
                # Perform periodic tasks
                await self.periodic_tasks()
                
                # Sleep for a bit
                await asyncio.sleep(30)
                
        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
            self.running = False
    
    async def periodic_tasks(self):
        """Perform periodic tasks."""
        try:
            # Health check
            health = await self.api.health_check()
            
            if health["status"] != "healthy":
                logger.warning("API health check failed", extra=health)
            
            # Get cache stats
            cache_stats = await self.api.get_cache_stats()
            logger.debug("Cache stats", extra=cache_stats)
            
            # Check portfolio
            portfolio = await self.api.get_portfolio()
            logger.info(
                "Portfolio update",
                extra={
                    "total_value": float(portfolio.balance.total_value),
                    "day_pnl": float(portfolio.balance.day_pnl),
                    "position_count": portfolio.position_count,
                }
            )
            
        except Exception as e:
            logger.error(f"Error in periodic tasks: {e}")
    
    async def shutdown(self):
        """Graceful shutdown."""
        logger.info("Shutting down trading bot...")
        
        self.running = False
        
        try:
            # Unsubscribe from quotes
            if self.api.is_websocket_connected:
                await self.api.unsubscribe_quotes(self.watchlist)
                await self.api.disconnect_websocket()
            
            # Clear cache
            cleared = await self.api.clear_cache()
            logger.info(f"Cleared {cleared} cache entries")
            
            logger.info("Shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


async def main():
    """Main entry point."""
    # Set up signal handlers for graceful shutdown
    bot = TradingBotExample()
    
    def signal_handler():
        bot.running = False
    
    # Register signal handlers
    if hasattr(signal, 'SIGINT'):
        signal.signal(signal.SIGINT, lambda s, f: signal_handler())
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, lambda s, f: signal_handler())
    
    # Start the bot
    await bot.start()


if __name__ == "__main__":
    asyncio.run(main())
