"""Dynamic stop-loss management system."""

import math
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Dict, List, Optional, Tuple

import numpy as np
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import Bar, Position, Symbol, TechnicalIndicator

logger = get_logger(__name__)


class StopType(Enum):
    """Stop loss types."""
    FIXED_PERCENTAGE = "fixed_percentage"
    ATR_BASED = "atr_based"
    TRAILING = "trailing"
    TIME_BASED = "time_based"
    SUPPORT_RESISTANCE = "support_resistance"
    VOLATILITY_ADJUSTED = "volatility_adjusted"


class StopLossManager:
    """Advanced stop-loss management system."""
    
    def __init__(self):
        self.config = settings.risk
        self.active_stops = {}  # symbol -> stop info
        
    async def initialize(self):
        """Initialize stop-loss manager."""
        await self._load_active_stops()
        logger.info("Stop-loss manager initialized")
    
    async def _load_active_stops(self):
        """Load active stop-loss orders from database."""
        try:
            async with get_postgres_session() as session:
                stmt = (
                    select(Position, Symbol.symbol)
                    .join(Symbol)
                    .where(
                        and_(
                            Position.status == "OPEN",
                            Position.stop_loss.isnot(None)
                        )
                    )
                )
                result = await session.execute(stmt)
                
                self.active_stops = {}
                for position, symbol in result:
                    self.active_stops[symbol] = {
                        "position_id": position.id,
                        "stop_price": float(position.stop_loss),
                        "entry_price": float(position.entry_price),
                        "current_price": float(position.current_price or position.entry_price),
                        "side": position.side,
                        "quantity": position.quantity,
                        "stop_type": "fixed_percentage",  # Default
                        "created_at": position.opened_at,
                    }
                    
        except Exception as e:
            logger.error(f"Error loading active stops: {e}")
            self.active_stops = {}
    
    async def calculate_stop_loss(
        self,
        symbol: str,
        entry_price: float,
        side: str,
        stop_type: StopType = StopType.ATR_BASED,
        multiplier: float = 2.0,
        lookback_days: int = 20
    ) -> float:
        """
        Calculate stop-loss price based on specified method.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price
            side: Position side (LONG/SHORT)
            stop_type: Type of stop-loss calculation
            multiplier: Multiplier for ATR or volatility
            lookback_days: Lookback period for calculations
            
        Returns:
            Stop-loss price
        """
        try:
            if stop_type == StopType.FIXED_PERCENTAGE:
                return self._calculate_fixed_percentage_stop(entry_price, side)
            
            elif stop_type == StopType.ATR_BASED:
                return await self._calculate_atr_stop(symbol, entry_price, side, multiplier, lookback_days)
            
            elif stop_type == StopType.VOLATILITY_ADJUSTED:
                return await self._calculate_volatility_stop(symbol, entry_price, side, multiplier, lookback_days)
            
            elif stop_type == StopType.SUPPORT_RESISTANCE:
                return await self._calculate_support_resistance_stop(symbol, entry_price, side, lookback_days)
            
            else:
                # Default to fixed percentage
                return self._calculate_fixed_percentage_stop(entry_price, side)
                
        except Exception as e:
            logger.error(f"Error calculating stop loss for {symbol}: {e}")
            return self._calculate_fixed_percentage_stop(entry_price, side)
    
    def _calculate_fixed_percentage_stop(self, entry_price: float, side: str) -> float:
        """Calculate fixed percentage stop-loss."""
        stop_distance = entry_price * self.config.default_stop_loss
        
        if side.upper() == "LONG":
            return entry_price - stop_distance
        else:  # SHORT
            return entry_price + stop_distance
    
    async def _calculate_atr_stop(
        self,
        symbol: str,
        entry_price: float,
        side: str,
        multiplier: float,
        lookback_days: int
    ) -> float:
        """Calculate ATR-based stop-loss."""
        try:
            atr = await self._get_atr(symbol, lookback_days)
            if not atr:
                logger.warning(f"No ATR data for {symbol}, using fixed percentage stop")
                return self._calculate_fixed_percentage_stop(entry_price, side)
            
            stop_distance = atr * multiplier
            
            if side.upper() == "LONG":
                stop_price = entry_price - stop_distance
            else:  # SHORT
                stop_price = entry_price + stop_distance
            
            # Ensure reasonable bounds
            min_stop_distance = entry_price * 0.005  # 0.5% minimum
            max_stop_distance = entry_price * 0.15   # 15% maximum
            
            actual_distance = abs(entry_price - stop_price)
            if actual_distance < min_stop_distance:
                if side.upper() == "LONG":
                    stop_price = entry_price - min_stop_distance
                else:
                    stop_price = entry_price + min_stop_distance
            elif actual_distance > max_stop_distance:
                if side.upper() == "LONG":
                    stop_price = entry_price - max_stop_distance
                else:
                    stop_price = entry_price + max_stop_distance
            
            return round(stop_price, 2)
            
        except Exception as e:
            logger.error(f"Error calculating ATR stop for {symbol}: {e}")
            return self._calculate_fixed_percentage_stop(entry_price, side)
    
    async def _calculate_volatility_stop(
        self,
        symbol: str,
        entry_price: float,
        side: str,
        multiplier: float,
        lookback_days: int
    ) -> float:
        """Calculate volatility-adjusted stop-loss."""
        try:
            volatility = await self._get_volatility(symbol, lookback_days)
            if not volatility:
                return self._calculate_fixed_percentage_stop(entry_price, side)
            
            # Convert annual volatility to daily and apply multiplier
            daily_volatility = volatility / math.sqrt(252)
            stop_distance = entry_price * daily_volatility * multiplier
            
            if side.upper() == "LONG":
                stop_price = entry_price - stop_distance
            else:
                stop_price = entry_price + stop_distance
            
            # Apply bounds
            min_stop_distance = entry_price * 0.005
            max_stop_distance = entry_price * 0.15
            
            actual_distance = abs(entry_price - stop_price)
            if actual_distance < min_stop_distance:
                if side.upper() == "LONG":
                    stop_price = entry_price - min_stop_distance
                else:
                    stop_price = entry_price + min_stop_distance
            elif actual_distance > max_stop_distance:
                if side.upper() == "LONG":
                    stop_price = entry_price - max_stop_distance
                else:
                    stop_price = entry_price + max_stop_distance
            
            return round(stop_price, 2)
            
        except Exception as e:
            logger.error(f"Error calculating volatility stop for {symbol}: {e}")
            return self._calculate_fixed_percentage_stop(entry_price, side)
    
    async def _calculate_support_resistance_stop(
        self,
        symbol: str,
        entry_price: float,
        side: str,
        lookback_days: int
    ) -> float:
        """Calculate support/resistance-based stop-loss."""
        try:
            levels = await self._get_support_resistance_levels(symbol, lookback_days)
            if not levels:
                return self._calculate_fixed_percentage_stop(entry_price, side)
            
            if side.upper() == "LONG":
                # Find nearest support level below entry price
                support_levels = [level for level in levels["support"] if level < entry_price]
                if support_levels:
                    stop_price = max(support_levels) * 0.99  # Slightly below support
                else:
                    stop_price = self._calculate_fixed_percentage_stop(entry_price, side)
            else:  # SHORT
                # Find nearest resistance level above entry price
                resistance_levels = [level for level in levels["resistance"] if level > entry_price]
                if resistance_levels:
                    stop_price = min(resistance_levels) * 1.01  # Slightly above resistance
                else:
                    stop_price = self._calculate_fixed_percentage_stop(entry_price, side)
            
            return round(stop_price, 2)
            
        except Exception as e:
            logger.error(f"Error calculating support/resistance stop for {symbol}: {e}")
            return self._calculate_fixed_percentage_stop(entry_price, side)
    
    async def update_trailing_stop(
        self,
        symbol: str,
        current_price: float,
        trailing_distance: Optional[float] = None
    ) -> Optional[float]:
        """
        Update trailing stop-loss based on current price.
        
        Args:
            symbol: Stock symbol
            current_price: Current market price
            trailing_distance: Trailing distance (default from config)
            
        Returns:
            New stop price if updated, None if no change
        """
        try:
            if symbol not in self.active_stops:
                return None
            
            stop_info = self.active_stops[symbol]
            side = stop_info["side"]
            current_stop = stop_info["stop_price"]
            
            if trailing_distance is None:
                trailing_distance = self.config.trailing_stop_distance
            
            if side.upper() == "LONG":
                # For long positions, only move stop up
                new_stop = current_price * (1 - trailing_distance)
                if new_stop > current_stop:
                    stop_info["stop_price"] = new_stop
                    await self._update_stop_in_database(symbol, new_stop)
                    logger.info(f"Updated trailing stop for {symbol}: ${new_stop:.2f}")
                    return new_stop
            else:  # SHORT
                # For short positions, only move stop down
                new_stop = current_price * (1 + trailing_distance)
                if new_stop < current_stop:
                    stop_info["stop_price"] = new_stop
                    await self._update_stop_in_database(symbol, new_stop)
                    logger.info(f"Updated trailing stop for {symbol}: ${new_stop:.2f}")
                    return new_stop
            
            return None
            
        except Exception as e:
            logger.error(f"Error updating trailing stop for {symbol}: {e}")
            return None
    
    async def check_time_based_stops(self) -> List[str]:
        """
        Check for time-based stop conditions.
        
        Returns:
            List of symbols that should be closed due to time limits
        """
        try:
            symbols_to_close = []
            current_time = datetime.utcnow()
            
            for symbol, stop_info in self.active_stops.items():
                # Check if position has been open too long (default 5 days)
                max_hold_time = timedelta(days=5)
                if current_time - stop_info["created_at"] > max_hold_time:
                    symbols_to_close.append(symbol)
                    logger.info(f"Time-based stop triggered for {symbol}")
            
            return symbols_to_close
            
        except Exception as e:
            logger.error(f"Error checking time-based stops: {e}")
            return []
    
    async def check_partial_stop_conditions(
        self,
        symbol: str,
        current_price: float,
        profit_target_1: float = 0.05,  # 5% profit
        profit_target_2: float = 0.10   # 10% profit
    ) -> Optional[Dict]:
        """
        Check for partial position stop conditions.
        
        Args:
            symbol: Stock symbol
            current_price: Current market price
            profit_target_1: First profit target percentage
            profit_target_2: Second profit target percentage
            
        Returns:
            Dictionary with partial stop information if triggered
        """
        try:
            if symbol not in self.active_stops:
                return None
            
            stop_info = self.active_stops[symbol]
            entry_price = stop_info["entry_price"]
            side = stop_info["side"]
            quantity = stop_info["quantity"]
            
            if side.upper() == "LONG":
                profit_pct = (current_price - entry_price) / entry_price
            else:  # SHORT
                profit_pct = (entry_price - current_price) / entry_price
            
            # Check profit targets
            if profit_pct >= profit_target_2:
                # Close 50% at second target
                return {
                    "action": "partial_close",
                    "quantity": quantity // 2,
                    "reason": f"Profit target 2 reached: {profit_pct:.2%}",
                    "new_stop": entry_price  # Move stop to breakeven
                }
            elif profit_pct >= profit_target_1:
                # Close 25% at first target
                return {
                    "action": "partial_close",
                    "quantity": quantity // 4,
                    "reason": f"Profit target 1 reached: {profit_pct:.2%}",
                    "new_stop": entry_price * 1.02 if side.upper() == "LONG" else entry_price * 0.98
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking partial stop conditions for {symbol}: {e}")
            return None
    
    async def _get_atr(self, symbol: str, lookback_days: int = 20) -> Optional[float]:
        """Get Average True Range for the symbol."""
        try:
            async with get_postgres_session() as session:
                # Get symbol ID
                stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                result = await session.execute(stmt)
                symbol_id = result.scalar_one_or_none()
                
                if not symbol_id:
                    return None
                
                # Get latest ATR value
                stmt = (
                    select(TechnicalIndicator.value)
                    .where(
                        and_(
                            TechnicalIndicator.symbol_id == symbol_id,
                            TechnicalIndicator.indicator_name == "atr"
                        )
                    )
                    .order_by(TechnicalIndicator.timestamp.desc())
                    .limit(1)
                )
                result = await session.execute(stmt)
                atr_value = result.scalar_one_or_none()
                
                return float(atr_value) if atr_value else None
                
        except Exception as e:
            logger.error(f"Error getting ATR for {symbol}: {e}")
            return None
    
    async def _get_volatility(self, symbol: str, lookback_days: int = 30) -> Optional[float]:
        """Get historical volatility for the symbol."""
        try:
            async with get_postgres_session() as session:
                # Get symbol ID
                stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                result = await session.execute(stmt)
                symbol_id = result.scalar_one_or_none()
                
                if not symbol_id:
                    return None
                
                # Get recent close prices
                cutoff_date = datetime.utcnow() - timedelta(days=lookback_days)
                
                stmt = (
                    select(Bar.close)
                    .where(
                        and_(
                            Bar.symbol_id == symbol_id,
                            Bar.timeframe == "1d",
                            Bar.timestamp >= cutoff_date
                        )
                    )
                    .order_by(Bar.timestamp.asc())
                )
                result = await session.execute(stmt)
                prices = [float(row[0]) for row in result.fetchall()]
                
                if len(prices) < 10:
                    return None
                
                # Calculate volatility
                returns = np.diff(np.log(prices))
                volatility = np.std(returns) * np.sqrt(252)
                
                return float(volatility)
                
        except Exception as e:
            logger.error(f"Error calculating volatility for {symbol}: {e}")
            return None
    
    async def _get_support_resistance_levels(
        self,
        symbol: str,
        lookback_days: int = 50
    ) -> Optional[Dict[str, List[float]]]:
        """Get support and resistance levels for the symbol."""
        try:
            async with get_postgres_session() as session:
                # Get symbol ID
                stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                result = await session.execute(stmt)
                symbol_id = result.scalar_one_or_none()
                
                if not symbol_id:
                    return None
                
                # Get recent price data
                cutoff_date = datetime.utcnow() - timedelta(days=lookback_days)
                
                stmt = (
                    select(Bar.high, Bar.low, Bar.close)
                    .where(
                        and_(
                            Bar.symbol_id == symbol_id,
                            Bar.timeframe == "1d",
                            Bar.timestamp >= cutoff_date
                        )
                    )
                    .order_by(Bar.timestamp.asc())
                )
                result = await session.execute(stmt)
                price_data = [(float(row[0]), float(row[1]), float(row[2])) for row in result.fetchall()]
                
                if len(price_data) < 20:
                    return None
                
                # Simple support/resistance calculation
                highs = [data[0] for data in price_data]
                lows = [data[1] for data in price_data]
                
                # Find local maxima and minima
                resistance_levels = []
                support_levels = []
                
                for i in range(2, len(highs) - 2):
                    # Resistance (local maxima)
                    if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and
                        highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                        resistance_levels.append(highs[i])
                    
                    # Support (local minima)
                    if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and
                        lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                        support_levels.append(lows[i])
                
                return {
                    "support": sorted(support_levels, reverse=True)[:5],  # Top 5 support levels
                    "resistance": sorted(resistance_levels)[:5]  # Top 5 resistance levels
                }
                
        except Exception as e:
            logger.error(f"Error getting support/resistance levels for {symbol}: {e}")
            return None
    
    async def _update_stop_in_database(self, symbol: str, new_stop_price: float):
        """Update stop-loss price in database."""
        try:
            async with get_postgres_session() as session:
                # Get position
                stmt = (
                    select(Position)
                    .join(Symbol)
                    .where(
                        and_(
                            Symbol.symbol == symbol,
                            Position.status == "OPEN"
                        )
                    )
                )
                result = await session.execute(stmt)
                position = result.scalar_one_or_none()
                
                if position:
                    position.stop_loss = new_stop_price
                    position.updated_at = datetime.utcnow()
                    await session.commit()
                    
        except Exception as e:
            logger.error(f"Error updating stop in database for {symbol}: {e}")
