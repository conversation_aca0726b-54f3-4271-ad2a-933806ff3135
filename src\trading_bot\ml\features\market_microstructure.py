"""Market Microstructure Features for high-frequency trading insights."""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
import logging
from collections import defaultdict
from scipy import stats

logger = logging.getLogger(__name__)


class MarketMicrostructure:
    """Market microstructure analysis for order flow and liquidity features."""
    
    def __init__(self):
        self.tick_data = None
        self.order_book_data = None
        
    def calculate_microstructure_features(self, 
                                        price_data: pd.DataFrame,
                                        tick_data: Optional[pd.DataFrame] = None,
                                        order_book_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """Calculate all microstructure features."""
        df = price_data.copy()
        
        # Basic microstructure features from OHLCV
        df = self._add_price_impact_features(df)
        df = self._add_volume_profile_features(df)
        df = self._add_volatility_microstructure(df)
        df = self._add_liquidity_features(df)
        
        # Advanced features if tick data available
        if tick_data is not None:
            df = self._add_tick_features(df, tick_data)
        
        # Order book features if available
        if order_book_data is not None:
            df = self._add_order_book_features(df, order_book_data)
        
        return df
    
    def _add_price_impact_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add price impact and efficiency features."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Price impact measures
        df['amihud_illiquidity'] = abs(close.pct_change()) / (volume + 1e-8)
        
        # Volume-weighted price impact
        typical_price = (high + low + close) / 3
        df['vwap'] = (typical_price * volume).rolling(20).sum() / volume.rolling(20).sum()
        df['price_efficiency'] = abs(close - df['vwap']) / close
        
        # Kyle's Lambda (simplified)
        price_change = close.diff()
        df['kyles_lambda'] = price_change.rolling(20).std() / volume.rolling(20).mean()
        
        # Roll measure (bid-ask spread proxy)
        df['roll_measure'] = 2 * np.sqrt(abs(close.diff().rolling(20).cov(close.diff().shift(1))))
        
        # Variance ratio test
        df['variance_ratio'] = self._calculate_variance_ratio(close)
        
        return df
    
    def _add_volume_profile_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume profile and VPIN features."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Volume-synchronized probability of informed trading (VPIN)
        df['vpin'] = self._calculate_vpin(df)
        
        # Volume at price levels
        df = self._calculate_volume_at_price(df)
        
        # Volume imbalance
        df['volume_imbalance'] = self._calculate_volume_imbalance(df)
        
        # Relative volume
        df['relative_volume'] = volume / volume.rolling(20).mean()
        
        # Volume acceleration
        df['volume_acceleration'] = volume.diff() / volume.shift(1)
        
        # Time-weighted volume
        df['time_weighted_volume'] = self._calculate_time_weighted_volume(df)
        
        # Volume clustering
        df['volume_clustering'] = self._calculate_volume_clustering(df)
        
        return df
    
    def _add_volatility_microstructure(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add microstructure-based volatility measures."""
        high, low, close = df['high'], df['low'], df['close']
        
        # Realized volatility
        df['realized_volatility'] = close.pct_change().rolling(20).std() * np.sqrt(252)
        
        # Parkinson volatility estimator
        df['parkinson_volatility'] = np.sqrt(
            (np.log(high / low) ** 2).rolling(20).mean() / (4 * np.log(2))
        ) * np.sqrt(252)
        
        # Garman-Klass volatility
        df['garman_klass_volatility'] = np.sqrt(
            0.5 * (np.log(high / low) ** 2).rolling(20).mean() -
            (2 * np.log(2) - 1) * (np.log(close / close.shift(1)) ** 2).rolling(20).mean()
        ) * np.sqrt(252)
        
        # Rogers-Satchell volatility
        df['rogers_satchell_volatility'] = np.sqrt(
            (np.log(high / close) * np.log(high / df['open']) +
             np.log(low / close) * np.log(low / df['open'])).rolling(20).mean()
        ) * np.sqrt(252)
        
        # Intraday range
        df['intraday_range'] = (high - low) / close
        
        # Close-to-close volatility
        df['close_to_close_vol'] = close.pct_change().rolling(20).std()
        
        # Volatility of volatility
        df['vol_of_vol'] = df['realized_volatility'].rolling(10).std()
        
        return df
    
    def _add_liquidity_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add liquidity measures."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Market depth proxy
        df['market_depth'] = volume / (high - low + 1e-8)
        
        # Liquidity ratio
        df['liquidity_ratio'] = volume / abs(close.pct_change())
        
        # Price dispersion
        df['price_dispersion'] = (high - low) / ((high + low) / 2)
        
        # Effective spread proxy
        df['effective_spread'] = (high - low) / close
        
        # Turnover rate
        market_cap_proxy = close * volume  # Simplified
        df['turnover_rate'] = volume / market_cap_proxy.rolling(20).mean()
        
        # Liquidity cost proxy
        df['liquidity_cost'] = abs(close.pct_change()) / (volume / volume.rolling(20).mean())
        
        return df
    
    def _add_tick_features(self, df: pd.DataFrame, tick_data: pd.DataFrame) -> pd.DataFrame:
        """Add features from tick-by-tick data."""
        # This would require tick data with timestamps, prices, and volumes
        
        # Trade direction classification (Lee-Ready algorithm proxy)
        df['trade_direction'] = self._classify_trade_direction(df)
        
        # Order flow imbalance
        df['order_flow_imbalance'] = self._calculate_order_flow_imbalance(df)
        
        # Quote revision frequency
        df['quote_revision_freq'] = self._calculate_quote_revision_frequency(df)
        
        # Trade size analysis
        df = self._analyze_trade_sizes(df)
        
        return df
    
    def _add_order_book_features(self, df: pd.DataFrame, order_book_data: pd.DataFrame) -> pd.DataFrame:
        """Add order book-based features."""
        # This would require order book data with bid/ask prices and sizes
        
        # Bid-ask spread
        df['bid_ask_spread'] = self._calculate_bid_ask_spread(order_book_data)
        
        # Order book imbalance
        df['order_book_imbalance'] = self._calculate_order_book_imbalance(order_book_data)
        
        # Market depth
        df['bid_depth'] = self._calculate_market_depth(order_book_data, 'bid')
        df['ask_depth'] = self._calculate_market_depth(order_book_data, 'ask')
        
        # Price improvement
        df['price_improvement'] = self._calculate_price_improvement(order_book_data)
        
        return df
    
    def _calculate_variance_ratio(self, prices: pd.Series) -> pd.Series:
        """Calculate variance ratio for market efficiency test."""
        returns = prices.pct_change().dropna()
        
        def variance_ratio_window(returns, window=20, lag=2):
            vr_values = []
            for i in range(window, len(returns)):
                window_returns = returns.iloc[i-window:i]
                
                # Variance of single-period returns
                var1 = window_returns.var()
                
                # Variance of lag-period returns
                lag_returns = window_returns.rolling(lag).sum()[lag-1::lag]
                var_lag = lag_returns.var() / lag
                
                # Variance ratio
                vr = var_lag / var1 if var1 > 0 else 1
                vr_values.append(vr)
            
            return pd.Series([1] * window + vr_values, index=returns.index)
        
        return variance_ratio_window(returns)
    
    def _calculate_vpin(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Volume-synchronized Probability of Informed Trading."""
        close, volume = df['close'], df['volume']
        
        # Simplified VPIN calculation
        price_change = close.diff()
        
        # Volume buckets
        volume_buckets = volume.rolling(50).sum() / 50  # Average volume bucket
        
        # Buy/sell volume estimation
        buy_volume = np.where(price_change > 0, volume, 0)
        sell_volume = np.where(price_change < 0, volume, 0)
        
        # VPIN
        volume_imbalance = abs(buy_volume - sell_volume)
        vpin = volume_imbalance.rolling(20).sum() / volume.rolling(20).sum()
        
        return vpin
    
    def _calculate_volume_at_price(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate volume at different price levels."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Volume at different percentiles of the range
        price_range = high - low
        
        # Volume near high
        df['volume_near_high'] = np.where(close > (low + 0.8 * price_range), volume, 0)
        
        # Volume near low  
        df['volume_near_low'] = np.where(close < (low + 0.2 * price_range), volume, 0)
        
        # Volume in middle
        df['volume_middle'] = np.where(
            (close >= (low + 0.2 * price_range)) & (close <= (low + 0.8 * price_range)),
            volume, 0
        )
        
        # Rolling sums
        for col in ['volume_near_high', 'volume_near_low', 'volume_middle']:
            df[f'{col}_ratio'] = df[col].rolling(20).sum() / volume.rolling(20).sum()
        
        return df
    
    def _calculate_volume_imbalance(self, df: pd.DataFrame) -> pd.Series:
        """Calculate volume imbalance."""
        close, volume = df['close'], df['volume']
        
        # Price direction
        price_direction = np.sign(close.diff())
        
        # Volume imbalance
        signed_volume = price_direction * volume
        volume_imbalance = signed_volume.rolling(20).sum() / volume.rolling(20).sum()
        
        return volume_imbalance
    
    def _calculate_time_weighted_volume(self, df: pd.DataFrame) -> pd.Series:
        """Calculate time-weighted volume."""
        volume = df['volume']
        
        # Time weights (more recent = higher weight)
        time_weights = np.arange(1, len(volume) + 1)
        
        # Rolling time-weighted volume
        def time_weighted_avg(series, window=20):
            result = []
            for i in range(len(series)):
                start = max(0, i - window + 1)
                window_data = series.iloc[start:i+1]
                weights = np.arange(1, len(window_data) + 1)
                
                if len(window_data) > 0:
                    weighted_avg = np.average(window_data, weights=weights)
                    result.append(weighted_avg)
                else:
                    result.append(0)
            
            return pd.Series(result, index=series.index)
        
        return time_weighted_avg(volume)
    
    def _calculate_volume_clustering(self, df: pd.DataFrame) -> pd.Series:
        """Calculate volume clustering coefficient."""
        volume = df['volume']
        
        # Volume clustering using autocorrelation
        def volume_clustering_coefficient(series, window=20):
            clustering = []
            for i in range(window, len(series)):
                window_data = series.iloc[i-window:i]
                
                # Autocorrelation at lag 1
                autocorr = window_data.autocorr(lag=1)
                clustering.append(autocorr if not np.isnan(autocorr) else 0)
            
            return pd.Series([0] * window + clustering, index=series.index)
        
        return volume_clustering_coefficient(volume)
    
    def _classify_trade_direction(self, df: pd.DataFrame) -> pd.Series:
        """Classify trade direction using tick rule."""
        close = df['close']
        
        # Simplified tick rule
        price_change = close.diff()
        
        # Trade direction: 1 for buy, -1 for sell, 0 for no change
        trade_direction = np.sign(price_change)
        
        return trade_direction
    
    def _calculate_order_flow_imbalance(self, df: pd.DataFrame) -> pd.Series:
        """Calculate order flow imbalance."""
        trade_direction = self._classify_trade_direction(df)
        volume = df['volume']
        
        # Buy and sell volumes
        buy_volume = np.where(trade_direction > 0, volume, 0)
        sell_volume = np.where(trade_direction < 0, volume, 0)
        
        # Order flow imbalance
        ofi = (buy_volume - sell_volume).rolling(20).sum()
        
        return ofi
    
    def _calculate_quote_revision_frequency(self, df: pd.DataFrame) -> pd.Series:
        """Calculate quote revision frequency."""
        high, low = df['high'], df['low']
        
        # Proxy for quote revisions using price range changes
        range_change = (high - low).diff()
        revision_indicator = (abs(range_change) > 0).astype(int)
        
        # Frequency over rolling window
        revision_frequency = revision_indicator.rolling(20).mean()
        
        return revision_frequency
    
    def _analyze_trade_sizes(self, df: pd.DataFrame) -> pd.DataFrame:
        """Analyze trade size distribution."""
        volume = df['volume']
        
        # Trade size moments
        df['avg_trade_size'] = volume.rolling(20).mean()
        df['trade_size_std'] = volume.rolling(20).std()
        df['trade_size_skew'] = volume.rolling(20).skew()
        df['trade_size_kurt'] = volume.rolling(20).kurt()
        
        # Large trade indicator
        large_trade_threshold = volume.rolling(50).quantile(0.9)
        df['large_trade_indicator'] = (volume > large_trade_threshold).astype(int)
        
        # Small trade indicator
        small_trade_threshold = volume.rolling(50).quantile(0.1)
        df['small_trade_indicator'] = (volume < small_trade_threshold).astype(int)
        
        return df
    
    def _calculate_bid_ask_spread(self, order_book_data: pd.DataFrame) -> pd.Series:
        """Calculate bid-ask spread from order book data."""
        # Placeholder - would need actual bid/ask prices
        spread = order_book_data.get('ask_price', 0) - order_book_data.get('bid_price', 0)
        return spread
    
    def _calculate_order_book_imbalance(self, order_book_data: pd.DataFrame) -> pd.Series:
        """Calculate order book imbalance."""
        # Placeholder - would need actual bid/ask sizes
        bid_size = order_book_data.get('bid_size', 0)
        ask_size = order_book_data.get('ask_size', 0)
        
        imbalance = (bid_size - ask_size) / (bid_size + ask_size + 1e-8)
        return imbalance
    
    def _calculate_market_depth(self, order_book_data: pd.DataFrame, side: str) -> pd.Series:
        """Calculate market depth for bid or ask side."""
        # Placeholder - would sum sizes at multiple levels
        depth = order_book_data.get(f'{side}_size', 0)
        return depth
    
    def _calculate_price_improvement(self, order_book_data: pd.DataFrame) -> pd.Series:
        """Calculate price improvement."""
        # Placeholder - would compare execution price to quotes
        improvement = pd.Series(0, index=order_book_data.index)
        return improvement
    
    def get_microstructure_feature_list(self) -> List[str]:
        """Get list of all microstructure features."""
        return [
            # Price impact features
            'amihud_illiquidity', 'price_efficiency', 'kyles_lambda', 
            'roll_measure', 'variance_ratio',
            
            # Volume profile features
            'vpin', 'volume_imbalance', 'relative_volume', 'volume_acceleration',
            'time_weighted_volume', 'volume_clustering',
            'volume_near_high_ratio', 'volume_near_low_ratio', 'volume_middle_ratio',
            
            # Volatility microstructure
            'realized_volatility', 'parkinson_volatility', 'garman_klass_volatility',
            'rogers_satchell_volatility', 'intraday_range', 'close_to_close_vol', 'vol_of_vol',
            
            # Liquidity features
            'market_depth', 'liquidity_ratio', 'price_dispersion', 'effective_spread',
            'turnover_rate', 'liquidity_cost',
            
            # Tick features
            'trade_direction', 'order_flow_imbalance', 'quote_revision_freq',
            'avg_trade_size', 'trade_size_std', 'trade_size_skew', 'trade_size_kurt',
            'large_trade_indicator', 'small_trade_indicator',
            
            # Order book features (if available)
            'bid_ask_spread', 'order_book_imbalance', 'bid_depth', 'ask_depth', 'price_improvement'
        ]