"""Retry logic with exponential backoff and circuit breaker."""

import asyncio
import random
import time
from typing import Any, Callable, Dict, List, Optional, Type, Union

from ..core.logger import get_logger
from .exceptions import APIConnectionError, CircuitBreakerError

logger = get_logger(__name__)


class CircuitBreaker:
    """Circuit breaker pattern implementation."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: Type[Exception] = Exception,
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def __call__(self, func: Callable) -> Callable:
        """Decorator to apply circuit breaker."""
        async def wrapper(*args, **kwargs):
            return await self.call(func, *args, **kwargs)
        return wrapper
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Call function with circuit breaker protection."""
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
                logger.info(f"Circuit breaker entering HALF_OPEN state for {func.__name__}")
            else:
                raise CircuitBreakerError(
                    f"Circuit breaker is OPEN for {func.__name__}",
                    service_name=func.__name__,
                    failure_count=self.failure_count,
                )
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit breaker."""
        return (
            self.last_failure_time is not None
            and time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self) -> None:
        """Handle successful call."""
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            logger.info("Circuit breaker reset to CLOSED state")
        
        self.failure_count = 0
        self.last_failure_time = None
    
    def _on_failure(self) -> None:
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(
                f"Circuit breaker opened after {self.failure_count} failures",
                extra={"failure_count": self.failure_count}
            )


class RetryManager:
    """Advanced retry manager with exponential backoff."""
    
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
        retryable_exceptions: Optional[List[Type[Exception]]] = None,
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
        self.retryable_exceptions = retryable_exceptions or [
            APIConnectionError,
            asyncio.TimeoutError,
            ConnectionError,
        ]
        
        # Circuit breakers for different services
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for service."""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker()
        return self.circuit_breakers[service_name]
    
    async def retry(
        self,
        func: Callable,
        *args,
        service_name: Optional[str] = None,
        max_retries: Optional[int] = None,
        **kwargs,
    ) -> Any:
        """
        Retry function with exponential backoff.
        
        Args:
            func: Function to retry
            *args: Function arguments
            service_name: Service name for circuit breaker
            max_retries: Override default max retries
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: Last exception if all retries failed
        """
        max_attempts = (max_retries or self.max_retries) + 1
        last_exception = None
        
        # Apply circuit breaker if service name provided
        if service_name:
            circuit_breaker = self.get_circuit_breaker(service_name)
            func = circuit_breaker(func)
        
        for attempt in range(max_attempts):
            try:
                result = await func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(
                        f"Function succeeded after {attempt} retries",
                        extra={
                            "function": func.__name__,
                            "attempt": attempt,
                            "service_name": service_name,
                        }
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if exception is retryable
                if not self._is_retryable(e):
                    logger.error(
                        f"Non-retryable exception: {e}",
                        extra={"function": func.__name__, "exception_type": type(e).__name__}
                    )
                    raise e
                
                # Don't retry on last attempt
                if attempt == max_attempts - 1:
                    break
                
                # Calculate delay
                delay = self._calculate_delay(attempt)
                
                logger.warning(
                    f"Attempt {attempt + 1} failed, retrying in {delay:.2f}s",
                    extra={
                        "function": func.__name__,
                        "attempt": attempt + 1,
                        "max_attempts": max_attempts,
                        "delay": delay,
                        "exception": str(e),
                        "service_name": service_name,
                    }
                )
                
                await asyncio.sleep(delay)
        
        # All retries failed
        logger.error(
            f"All {max_attempts} attempts failed",
            extra={
                "function": func.__name__,
                "service_name": service_name,
                "last_exception": str(last_exception),
            }
        )
        
        raise last_exception
    
    def _is_retryable(self, exception: Exception) -> bool:
        """Check if exception is retryable."""
        return any(isinstance(exception, exc_type) for exc_type in self.retryable_exceptions)
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt."""
        # Exponential backoff
        delay = self.base_delay * (self.exponential_base ** attempt)
        
        # Apply jitter to avoid thundering herd
        if self.jitter:
            delay *= (0.5 + random.random() * 0.5)
        
        # Cap at max delay
        delay = min(delay, self.max_delay)
        
        return delay
    
    def add_retryable_exception(self, exception_type: Type[Exception]) -> None:
        """Add exception type to retryable list."""
        if exception_type not in self.retryable_exceptions:
            self.retryable_exceptions.append(exception_type)
    
    def remove_retryable_exception(self, exception_type: Type[Exception]) -> None:
        """Remove exception type from retryable list."""
        if exception_type in self.retryable_exceptions:
            self.retryable_exceptions.remove(exception_type)
    
    def reset_circuit_breaker(self, service_name: str) -> None:
        """Reset circuit breaker for service."""
        if service_name in self.circuit_breakers:
            circuit_breaker = self.circuit_breakers[service_name]
            circuit_breaker.failure_count = 0
            circuit_breaker.last_failure_time = None
            circuit_breaker.state = "CLOSED"
            logger.info(f"Reset circuit breaker for {service_name}")
    
    def get_circuit_breaker_status(self, service_name: str) -> Dict[str, Any]:
        """Get circuit breaker status."""
        if service_name not in self.circuit_breakers:
            return {"state": "NOT_INITIALIZED"}
        
        circuit_breaker = self.circuit_breakers[service_name]
        return {
            "state": circuit_breaker.state,
            "failure_count": circuit_breaker.failure_count,
            "failure_threshold": circuit_breaker.failure_threshold,
            "last_failure_time": circuit_breaker.last_failure_time,
            "recovery_timeout": circuit_breaker.recovery_timeout,
        }


# Global retry manager instance
retry_manager = RetryManager()
