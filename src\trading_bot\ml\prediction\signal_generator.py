"""Signal generator for converting predictions to trading signals."""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import warnings

from .predictor import PredictionResult

logger = logging.getLogger(__name__)


class SignalType(Enum):
    """Types of trading signals."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    STRONG_BUY = "STRONG_BUY"
    STRONG_SELL = "STRONG_SELL"


class SignalStrength(Enum):
    """Signal strength levels."""
    WEAK = 1
    MODERATE = 2
    STRONG = 3
    VERY_STRONG = 4


@dataclass
class SignalConfig:
    """Configuration for signal generation."""
    # Confidence thresholds
    min_confidence: float = 0.6
    strong_confidence: float = 0.8
    very_strong_confidence: float = 0.9
    
    # Signal filtering
    min_agreement: float = 0.6
    max_risk_score: float = 0.7
    
    # Position sizing
    base_position_size: float = 0.1  # 10% of portfolio
    max_position_size: float = 0.3   # 30% of portfolio
    use_kelly_sizing: bool = False
    
    # Signal timing
    signal_cooldown_minutes: int = 15
    max_signals_per_hour: int = 4
    
    # Risk management
    stop_loss_multiplier: float = 2.0
    take_profit_multiplier: float = 3.0
    max_drawdown_threshold: float = 0.15
    
    # Market conditions
    consider_market_conditions: bool = True
    avoid_low_volume: bool = True
    avoid_high_volatility: bool = True
    
    # Signal combination
    combine_timeframes: bool = False
    timeframe_weights: Dict[str, float] = field(default_factory=lambda: {
        '1min': 0.2, '5min': 0.3, '15min': 0.3, '1hour': 0.2
    })


@dataclass
class TradingSignal:
    """Complete trading signal with all necessary information."""
    timestamp: datetime
    symbol: str
    signal_type: SignalType
    signal_strength: SignalStrength
    confidence: float
    
    # Position sizing
    position_size: float
    position_direction: int  # 1 for long, -1 for short, 0 for close
    
    # Risk management
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    risk_reward_ratio: Optional[float] = None
    
    # Signal context
    entry_price: Optional[float] = None
    volatility: Optional[float] = None
    market_conditions: Optional[Dict[str, Any]] = None
    
    # Model information
    model_predictions: Optional[Dict[str, Any]] = None
    ensemble_agreement: Optional[float] = None
    
    # Signal validation
    is_valid: bool = True
    validation_reasons: List[str] = field(default_factory=list)
    
    # Execution details
    execution_urgency: str = "NORMAL"  # LOW, NORMAL, HIGH
    time_in_force: str = "DAY"  # DAY, GTC, IOC, FOK
    order_type: str = "MARKET"  # MARKET, LIMIT, STOP
    
    # Additional metadata
    signal_id: Optional[str] = None
    parent_signal_id: Optional[str] = None
    signal_group: Optional[str] = None


class SignalValidator:
    """Validates trading signals against various criteria."""
    
    def __init__(self, config: SignalConfig):
        self.config = config
        self.recent_signals = []
        self.signal_history = []
        
    def validate_signal(self, 
                       signal: TradingSignal,
                       prediction: PredictionResult,
                       market_data: pd.DataFrame) -> TradingSignal:
        """Validate trading signal and update accordingly."""
        
        validation_results = []
        
        # Confidence validation
        if prediction.confidence < self.config.min_confidence:
            validation_results.append(f"Low confidence: {prediction.confidence:.2f} < {self.config.min_confidence}")
            signal.is_valid = False
        
        # Agreement validation
        if prediction.ensemble_agreement and prediction.ensemble_agreement < self.config.min_agreement:
            validation_results.append(f"Low agreement: {prediction.ensemble_agreement:.2f} < {self.config.min_agreement}")
            signal.is_valid = False
        
        # Risk validation
        if prediction.risk_score and prediction.risk_score > self.config.max_risk_score:
            validation_results.append(f"High risk: {prediction.risk_score:.2f} > {self.config.max_risk_score}")
            signal.is_valid = False
        
        # Market conditions validation
        if self.config.consider_market_conditions:
            market_validation = self._validate_market_conditions(signal, prediction, market_data)
            validation_results.extend(market_validation)
        
        # Timing validation
        timing_validation = self._validate_timing(signal)
        validation_results.extend(timing_validation)
        
        # Volume validation
        if self.config.avoid_low_volume:
            volume_validation = self._validate_volume(signal, market_data)
            validation_results.extend(volume_validation)
        
        # Volatility validation
        if self.config.avoid_high_volatility:
            volatility_validation = self._validate_volatility(signal, market_data)
            validation_results.extend(volatility_validation)
        
        signal.validation_reasons = validation_results
        
        # Track signal for future validation
        if signal.is_valid:
            self._track_signal(signal)
        
        return signal
    
    def _validate_market_conditions(self, 
                                   signal: TradingSignal,
                                   prediction: PredictionResult,
                                   market_data: pd.DataFrame) -> List[str]:
        """Validate market conditions."""
        issues = []
        
        if prediction.market_conditions:
            conditions = prediction.market_conditions
            
            # Check trend alignment
            if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                if conditions.get('trend') == 'DOWN':
                    issues.append("Buy signal against downward trend")
                    signal.is_valid = False
            elif signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                if conditions.get('trend') == 'UP':
                    issues.append("Sell signal against upward trend")
                    signal.is_valid = False
            
            # Check volatility regime
            if conditions.get('volatility_regime') == 'HIGH':
                issues.append("High volatility market conditions")
                if signal.signal_strength == SignalStrength.VERY_STRONG:
                    signal.signal_strength = SignalStrength.STRONG
        
        return issues
    
    def _validate_timing(self, signal: TradingSignal) -> List[str]:
        """Validate signal timing."""
        issues = []
        
        current_time = signal.timestamp
        
        # Check signal cooldown
        recent_signals = [s for s in self.recent_signals 
                         if (current_time - s.timestamp).total_seconds() < self.config.signal_cooldown_minutes * 60]
        
        if recent_signals:
            issues.append(f"Signal within cooldown period ({len(recent_signals)} recent signals)")
            signal.is_valid = False
        
        # Check signals per hour
        hour_ago = current_time - timedelta(hours=1)
        hourly_signals = [s for s in self.signal_history if s.timestamp > hour_ago]
        
        if len(hourly_signals) >= self.config.max_signals_per_hour:
            issues.append(f"Too many signals in past hour ({len(hourly_signals)})")
            signal.is_valid = False
        
        return issues
    
    def _validate_volume(self, signal: TradingSignal, market_data: pd.DataFrame) -> List[str]:
        """Validate volume conditions."""
        issues = []
        
        if 'volume' not in market_data.columns or len(market_data) < 20:
            return issues
        
        current_volume = market_data['volume'].iloc[-1]
        avg_volume = market_data['volume'].tail(20).mean()
        
        # Check for low volume
        if current_volume < avg_volume * 0.5:
            issues.append("Low volume conditions")
            signal.is_valid = False
        
        return issues
    
    def _validate_volatility(self, signal: TradingSignal, market_data: pd.DataFrame) -> List[str]:
        """Validate volatility conditions."""
        issues = []
        
        if 'close' not in market_data.columns or len(market_data) < 20:
            return issues
        
        returns = market_data['close'].pct_change().tail(20)
        current_volatility = returns.std()
        historical_volatility = market_data['close'].pct_change().rolling(100).std().mean()
        
        # Check for high volatility
        if current_volatility > historical_volatility * 2:
            issues.append("High volatility conditions")
            # Reduce position size instead of invalidating
            signal.position_size *= 0.5
        
        return issues
    
    def _track_signal(self, signal: TradingSignal):
        """Track signal for future validation."""
        self.recent_signals.append(signal)
        self.signal_history.append(signal)
        
        # Clean up old signals
        cutoff_time = signal.timestamp - timedelta(hours=1)
        self.recent_signals = [s for s in self.recent_signals if s.timestamp > cutoff_time]
        
        # Keep last 1000 signals in history
        if len(self.signal_history) > 1000:
            self.signal_history = self.signal_history[-1000:]


class PositionSizer:
    """Calculates position sizes based on various methods."""
    
    def __init__(self, config: SignalConfig):
        self.config = config
        
    def calculate_position_size(self, 
                               signal: TradingSignal,
                               prediction: PredictionResult,
                               portfolio_value: float,
                               current_positions: Dict[str, float],
                               price_data: pd.DataFrame) -> float:
        """Calculate optimal position size."""
        
        if self.config.use_kelly_sizing:
            return self._kelly_position_sizing(signal, prediction, portfolio_value, price_data)
        else:
            return self._confidence_based_sizing(signal, prediction, portfolio_value)
    
    def _confidence_based_sizing(self, 
                                signal: TradingSignal,
                                prediction: PredictionResult,
                                portfolio_value: float) -> float:
        """Position sizing based on prediction confidence."""
        
        # Base size from configuration
        base_size = self.config.base_position_size
        
        # Scale by confidence
        confidence_multiplier = prediction.confidence
        
        # Scale by signal strength
        strength_multipliers = {
            SignalStrength.WEAK: 0.5,
            SignalStrength.MODERATE: 0.75,
            SignalStrength.STRONG: 1.0,
            SignalStrength.VERY_STRONG: 1.25
        }
        strength_multiplier = strength_multipliers.get(signal.signal_strength, 1.0)
        
        # Calculate position size
        position_size = base_size * confidence_multiplier * strength_multiplier
        
        # Apply maximum limit
        position_size = min(position_size, self.config.max_position_size)
        
        return position_size
    
    def _kelly_position_sizing(self, 
                              signal: TradingSignal,
                              prediction: PredictionResult,
                              portfolio_value: float,
                              price_data: pd.DataFrame) -> float:
        """Kelly criterion position sizing."""
        
        # Estimate win probability and average win/loss
        # This is simplified - in practice you'd use historical performance
        win_prob = prediction.confidence
        
        # Estimate from recent price movements
        if len(price_data) >= 50:
            returns = price_data['close'].pct_change().dropna()
            positive_returns = returns[returns > 0]
            negative_returns = returns[returns < 0]
            
            avg_win = positive_returns.mean() if len(positive_returns) > 0 else 0.02
            avg_loss = abs(negative_returns.mean()) if len(negative_returns) > 0 else 0.015
        else:
            avg_win = 0.02
            avg_loss = 0.015
        
        # Kelly formula: f = (bp - q) / b
        # where b = odds, p = win prob, q = loss prob
        if avg_loss > 0:
            kelly_fraction = (win_prob * avg_win - (1 - win_prob) * avg_loss) / avg_win
            kelly_fraction = max(0, min(kelly_fraction, self.config.max_position_size))
        else:
            kelly_fraction = self.config.base_position_size
        
        return kelly_fraction


class RiskManager:
    """Manages risk parameters for trading signals."""
    
    def __init__(self, config: SignalConfig):
        self.config = config
        
    def calculate_risk_parameters(self, 
                                 signal: TradingSignal,
                                 current_price: float,
                                 volatility: Optional[float] = None) -> TradingSignal:
        """Calculate stop loss and take profit levels."""
        
        if volatility is None:
            # Use default volatility estimate
            volatility = 0.02  # 2%
        
        # Calculate stop loss
        if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
            # Long position
            stop_distance = volatility * self.config.stop_loss_multiplier
            signal.stop_loss = current_price * (1 - stop_distance)
            
            profit_distance = volatility * self.config.take_profit_multiplier
            signal.take_profit = current_price * (1 + profit_distance)
            
        elif signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
            # Short position
            stop_distance = volatility * self.config.stop_loss_multiplier
            signal.stop_loss = current_price * (1 + stop_distance)
            
            profit_distance = volatility * self.config.take_profit_multiplier
            signal.take_profit = current_price * (1 - profit_distance)
        
        # Calculate risk-reward ratio
        if signal.stop_loss and signal.take_profit:
            risk = abs(current_price - signal.stop_loss)
            reward = abs(signal.take_profit - current_price)
            signal.risk_reward_ratio = reward / risk if risk > 0 else 0
        
        return signal


class SignalGenerator:
    """Main signal generator that converts predictions to trading signals."""
    
    def __init__(self, config: Optional[SignalConfig] = None):
        self.config = config or SignalConfig()
        self.validator = SignalValidator(self.config)
        self.position_sizer = PositionSizer(self.config)
        self.risk_manager = RiskManager(self.config)
        
        # Signal tracking
        self.signal_counter = 0
        self.active_signals = {}
        
        logger.info("Initialized SignalGenerator")
    
    def generate_signal(self, 
                       prediction: PredictionResult,
                       market_data: pd.DataFrame,
                       portfolio_value: float = 100000,
                       current_positions: Optional[Dict[str, float]] = None) -> Optional[TradingSignal]:
        """Generate trading signal from prediction."""
        
        current_positions = current_positions or {}
        
        try:
            # Convert prediction to basic signal
            signal = self._prediction_to_signal(prediction, market_data)
            
            if signal is None:
                return None
            
            # Calculate position size
            position_size = self.position_sizer.calculate_position_size(
                signal, prediction, portfolio_value, current_positions, market_data
            )
            signal.position_size = position_size
            
            # Calculate risk parameters
            current_price = market_data['close'].iloc[-1] if 'close' in market_data.columns else None
            if current_price:
                signal.entry_price = current_price
                volatility = self._estimate_volatility(market_data)
                signal.volatility = volatility
                signal = self.risk_manager.calculate_risk_parameters(signal, current_price, volatility)
            
            # Validate signal
            signal = self.validator.validate_signal(signal, prediction, market_data)
            
            # Set execution parameters
            signal = self._set_execution_parameters(signal, prediction)
            
            # Generate unique signal ID
            self.signal_counter += 1
            signal.signal_id = f"{signal.symbol}_{signal.timestamp.strftime('%Y%m%d_%H%M%S')}_{self.signal_counter}"
            
            # Store active signal
            if signal.is_valid:
                self.active_signals[signal.signal_id] = signal
            
            return signal
            
        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return None
    
    def _prediction_to_signal(self, 
                             prediction: PredictionResult,
                             market_data: pd.DataFrame) -> Optional[TradingSignal]:
        """Convert prediction result to trading signal."""
        
        # Determine signal type
        signal_map = {
            'BUY': SignalType.BUY,
            'SELL': SignalType.SELL,
            'HOLD': SignalType.HOLD
        }
        
        signal_type = signal_map.get(prediction.signal, SignalType.HOLD)
        
        # Don't generate signals for HOLD
        if signal_type == SignalType.HOLD:
            return None
        
        # Determine signal strength based on confidence
        if prediction.confidence >= self.config.very_strong_confidence:
            if signal_type == SignalType.BUY:
                signal_type = SignalType.STRONG_BUY
            elif signal_type == SignalType.SELL:
                signal_type = SignalType.STRONG_SELL
            signal_strength = SignalStrength.VERY_STRONG
        elif prediction.confidence >= self.config.strong_confidence:
            signal_strength = SignalStrength.STRONG
        elif prediction.confidence >= self.config.min_confidence:
            signal_strength = SignalStrength.MODERATE
        else:
            signal_strength = SignalStrength.WEAK
        
        # Determine position direction
        if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
            position_direction = 1
        elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
            position_direction = -1
        else:
            position_direction = 0
        
        # Create signal
        signal = TradingSignal(
            timestamp=prediction.timestamp,
            symbol=prediction.symbol,
            signal_type=signal_type,
            signal_strength=signal_strength,
            confidence=prediction.confidence,
            position_size=0.0,  # Will be calculated later
            position_direction=position_direction,
            market_conditions=prediction.market_conditions,
            model_predictions=prediction.model_predictions,
            ensemble_agreement=prediction.ensemble_agreement
        )
        
        return signal
    
    def _estimate_volatility(self, market_data: pd.DataFrame) -> float:
        """Estimate current volatility."""
        
        if 'close' not in market_data.columns or len(market_data) < 20:
            return 0.02  # Default 2%
        
        returns = market_data['close'].pct_change().tail(20)
        volatility = returns.std()
        
        return volatility
    
    def _set_execution_parameters(self, 
                                 signal: TradingSignal,
                                 prediction: PredictionResult) -> TradingSignal:
        """Set execution parameters based on signal characteristics."""
        
        # Set urgency based on confidence and market conditions
        if signal.confidence >= 0.9:
            signal.execution_urgency = "HIGH"
            signal.order_type = "MARKET"
        elif signal.confidence >= 0.7:
            signal.execution_urgency = "NORMAL"
            signal.order_type = "LIMIT"
        else:
            signal.execution_urgency = "LOW"
            signal.order_type = "LIMIT"
        
        # Set time in force
        if signal.signal_strength == SignalStrength.VERY_STRONG:
            signal.time_in_force = "IOC"  # Immediate or Cancel
        else:
            signal.time_in_force = "DAY"
        
        # Check for alerts that might affect execution
        if prediction.alerts:
            for alert in prediction.alerts:
                if "HIGH_RISK" in alert:
                    signal.execution_urgency = "LOW"
                    signal.position_size *= 0.5
                elif "STRONG_SIGNAL" in alert:
                    signal.execution_urgency = "HIGH"
        
        return signal
    
    def update_signal_status(self, signal_id: str, status: str, **kwargs):
        """Update status of an active signal."""
        
        if signal_id in self.active_signals:
            signal = self.active_signals[signal_id]
            
            # Update signal with new information
            for key, value in kwargs.items():
                if hasattr(signal, key):
                    setattr(signal, key, value)
            
            # Remove signal if filled or canceled
            if status in ['FILLED', 'CANCELED', 'EXPIRED']:
                del self.active_signals[signal_id]
                
            logger.info(f"Updated signal {signal_id} status to {status}")
    
    def get_active_signals(self) -> Dict[str, TradingSignal]:
        """Get all active signals."""
        return self.active_signals.copy()
    
    def cancel_signal(self, signal_id: str):
        """Cancel an active signal."""
        
        if signal_id in self.active_signals:
            del self.active_signals[signal_id]
            logger.info(f"Canceled signal {signal_id}")
    
    def get_signal_statistics(self) -> Dict[str, Any]:
        """Get signal generation statistics."""
        
        stats = {
            'total_signals_generated': self.signal_counter,
            'active_signals_count': len(self.active_signals),
            'active_signals': list(self.active_signals.keys())
        }
        
        # Signal type distribution
        if self.active_signals:
            signal_types = [s.signal_type.value for s in self.active_signals.values()]
            stats['signal_type_distribution'] = {
                signal_type: signal_types.count(signal_type) 
                for signal_type in set(signal_types)
            }
        
        return stats
    
    def cleanup_expired_signals(self, max_age_hours: int = 24):
        """Clean up expired signals."""
        
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        expired_signals = [
            signal_id for signal_id, signal in self.active_signals.items()
            if signal.timestamp < cutoff_time
        ]
        
        for signal_id in expired_signals:
            del self.active_signals[signal_id]
            
        if expired_signals:
            logger.info(f"Cleaned up {len(expired_signals)} expired signals")
    
    def generate_exit_signal(self, 
                            entry_signal_id: str,
                            exit_reason: str = "TAKE_PROFIT") -> Optional[TradingSignal]:
        """Generate exit signal for an existing position."""
        
        if entry_signal_id not in self.active_signals:
            logger.warning(f"Entry signal {entry_signal_id} not found")
            return None
        
        entry_signal = self.active_signals[entry_signal_id]
        
        # Create exit signal
        exit_signal = TradingSignal(
            timestamp=datetime.now(),
            symbol=entry_signal.symbol,
            signal_type=SignalType.HOLD,  # Exit signal
            signal_strength=SignalStrength.STRONG,
            confidence=0.9,  # High confidence for exits
            position_size=entry_signal.position_size,
            position_direction=-entry_signal.position_direction,  # Opposite direction
            execution_urgency="HIGH",
            order_type="MARKET",
            time_in_force="IOC",
            parent_signal_id=entry_signal_id
        )
        
        # Generate unique ID
        self.signal_counter += 1
        exit_signal.signal_id = f"EXIT_{entry_signal.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.signal_counter}"
        
        # Add exit reason to validation
        exit_signal.validation_reasons.append(f"Exit reason: {exit_reason}")
        
        return exit_signal