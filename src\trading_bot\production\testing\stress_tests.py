"""
Stress testing suite for AI Trading Bot.

This module provides comprehensive stress testing to validate
system performance under high load conditions and ensure
production readiness for 24/7 operations.
"""

import asyncio
import logging
import time
import psutil
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
import concurrent.futures
import threading

from ...core.config import Config
from ...data.market_data import MarketDataManager
from ...ml.model_manager import ModelManager
from ...strategies.strategy_manager import StrategyManager
from ...execution.order_manager import OrderManager
from ...risk.risk_manager import RiskManager
from ...utils.logger import get_logger

logger = get_logger(__name__)


class StressTestType(Enum):
    """Types of stress tests."""
    CONCURRENT_ORDERS = "concurrent_orders"
    HIGH_FREQUENCY_DATA = "high_frequency_data"
    ML_INFERENCE_LOAD = "ml_inference_load"
    DATABASE_STRESS = "database_stress"
    MEMORY_STRESS = "memory_stress"
    NETWORK_STRESS = "network_stress"
    CASCADING_FAILURE = "cascading_failure"


@dataclass
class StressTestMetrics:
    """Metrics collected during stress testing."""
    test_type: StressTestType
    duration: float
    operations_completed: int
    operations_failed: int
    peak_cpu_usage: float
    peak_memory_usage: float
    average_response_time: float
    max_response_time: float
    min_response_time: float
    throughput_ops_per_second: float
    error_rate: float
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class StressTestResult:
    """Result of a stress test."""
    test_name: str
    test_type: StressTestType
    status: str  # "passed", "failed", "warning"
    message: str
    metrics: StressTestMetrics
    details: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)


@dataclass
class StressTestReport:
    """Comprehensive stress test report."""
    total_tests: int
    passed_tests: int
    failed_tests: int
    warning_tests: int
    total_duration: float
    timestamp: datetime
    results: List[StressTestResult] = field(default_factory=list)
    system_limits: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        """Calculate test success rate."""
        if self.total_tests == 0:
            return 0.0
        return (self.passed_tests / self.total_tests) * 100
    
    @property
    def is_production_ready(self) -> bool:
        """Check if system is ready for production load."""
        return (
            self.failed_tests == 0 and
            self.success_rate >= 90.0
        )


class StressTestSuite:
    """
    Comprehensive stress testing suite for the trading bot.
    
    Tests system performance under various high-load conditions:
    - 1000+ concurrent orders
    - 10,000+ market data updates per second
    - ML inference under heavy load
    - Database connection pooling stress
    - Memory leak detection
    - Network failure scenarios
    - Cascading failure recovery
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.results: List[StressTestResult] = []
        self.system_monitor = SystemMonitor()
        
        # Initialize components
        self.market_data_manager = None
        self.model_manager = None
        self.strategy_manager = None
        self.order_manager = None
        self.risk_manager = None
    
    async def run_all_stress_tests(self) -> StressTestReport:
        """Run all stress tests and generate comprehensive report."""
        logger.info("Starting comprehensive stress testing...")
        start_time = time.time()
        
        self.results = []
        
        # Initialize test environment
        await self._setup_stress_test_environment()
        
        # Start system monitoring
        self.system_monitor.start_monitoring()
        
        try:
            # Run stress tests
            stress_tests = [
                self.test_concurrent_orders,
                self.test_high_frequency_data,
                self.test_ml_inference_load,
                self.test_database_stress,
                self.test_memory_stress,
                self.test_network_stress,
                self.test_cascading_failure,
            ]
            
            for test_method in stress_tests:
                try:
                    result = await self._run_stress_test(test_method)
                    self.results.append(result)
                    
                    # Cool down between tests
                    await asyncio.sleep(5)
                    
                except Exception as e:
                    logger.error(f"Stress test failed: {test_method.__name__} - {e}")
                    self.results.append(StressTestResult(
                        test_name=test_method.__name__,
                        test_type=StressTestType.CONCURRENT_ORDERS,  # Default
                        status="failed",
                        message=f"Test execution failed: {str(e)}",
                        metrics=StressTestMetrics(
                            test_type=StressTestType.CONCURRENT_ORDERS,
                            duration=0.0,
                            operations_completed=0,
                            operations_failed=1,
                            peak_cpu_usage=0.0,
                            peak_memory_usage=0.0,
                            average_response_time=0.0,
                            max_response_time=0.0,
                            min_response_time=0.0,
                            throughput_ops_per_second=0.0,
                            error_rate=1.0
                        )
                    ))
        
        finally:
            # Stop system monitoring
            system_limits = self.system_monitor.stop_monitoring()
            
            # Cleanup test environment
            await self._cleanup_stress_test_environment()
        
        total_duration = time.time() - start_time
        
        # Generate report
        report = self._generate_stress_report(total_duration, system_limits)
        
        logger.info(f"Stress testing completed in {total_duration:.2f}s")
        logger.info(f"Success rate: {report.success_rate:.1f}%")
        
        return report
    
    async def _setup_stress_test_environment(self):
        """Set up stress test environment."""
        try:
            # Initialize components with stress test configurations
            self.market_data_manager = MarketDataManager(self.config)
            self.model_manager = ModelManager(self.config)
            self.strategy_manager = StrategyManager(self.config)
            self.order_manager = OrderManager(self.config)
            self.risk_manager = RiskManager(self.config)
            
            # Start components
            await self.market_data_manager.start()
            await self.model_manager.start()
            await self.strategy_manager.start()
            await self.order_manager.start()
            await self.risk_manager.start()
            
            logger.info("Stress test environment setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup stress test environment: {e}")
            raise
    
    async def _cleanup_stress_test_environment(self):
        """Clean up stress test environment."""
        try:
            # Stop all components
            if self.risk_manager:
                await self.risk_manager.stop()
            if self.order_manager:
                await self.order_manager.stop()
            if self.strategy_manager:
                await self.strategy_manager.stop()
            if self.model_manager:
                await self.model_manager.stop()
            if self.market_data_manager:
                await self.market_data_manager.stop()
            
            logger.info("Stress test environment cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup stress test environment: {e}")
    
    async def _run_stress_test(self, test_method) -> StressTestResult:
        """Run a single stress test with monitoring."""
        test_name = test_method.__name__
        
        try:
            logger.info(f"Running stress test: {test_name}")
            
            # Reset monitoring
            self.system_monitor.reset_metrics()
            
            # Run the stress test
            result = await test_method()
            
            if result.status == "passed":
                logger.info(f"Stress test passed: {test_name}")
            else:
                logger.warning(f"Stress test failed: {test_name} - {result.message}")
            
            return result
            
        except Exception as e:
            logger.error(f"Stress test error: {test_name} - {e}")
            
            return StressTestResult(
                test_name=test_name,
                test_type=StressTestType.CONCURRENT_ORDERS,  # Default
                status="failed",
                message=f"Test execution error: {str(e)}",
                metrics=StressTestMetrics(
                    test_type=StressTestType.CONCURRENT_ORDERS,
                    duration=0.0,
                    operations_completed=0,
                    operations_failed=1,
                    peak_cpu_usage=0.0,
                    peak_memory_usage=0.0,
                    average_response_time=0.0,
                    max_response_time=0.0,
                    min_response_time=0.0,
                    throughput_ops_per_second=0.0,
                    error_rate=1.0
                )
            )
    
    async def test_concurrent_orders(self) -> StressTestResult:
        """Test system with 1000+ concurrent orders."""
        test_type = StressTestType.CONCURRENT_ORDERS
        start_time = time.time()
        
        try:
            # Generate 1000 concurrent orders
            num_orders = 1000
            symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'] * 200
            
            # Create order tasks
            order_tasks = []
            for i in range(num_orders):
                order = {
                    'symbol': symbols[i],
                    'action': 'BUY' if i % 2 == 0 else 'SELL',
                    'quantity': 10 + (i % 90),  # 10-99 shares
                    'order_type': 'MARKET'
                }
                order_tasks.append(self._submit_test_order(order))
            
            # Execute all orders concurrently
            results = await asyncio.gather(*order_tasks, return_exceptions=True)
            
            duration = time.time() - start_time
            
            # Analyze results
            successful_orders = sum(1 for r in results if not isinstance(r, Exception))
            failed_orders = num_orders - successful_orders
            
            # Calculate metrics
            response_times = [r.get('response_time', 0) for r in results if isinstance(r, dict)]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            max_response_time = max(response_times) if response_times else 0
            min_response_time = min(response_times) if response_times else 0
            
            metrics = StressTestMetrics(
                test_type=test_type,
                duration=duration,
                operations_completed=successful_orders,
                operations_failed=failed_orders,
                peak_cpu_usage=self.system_monitor.get_peak_cpu(),
                peak_memory_usage=self.system_monitor.get_peak_memory(),
                average_response_time=avg_response_time,
                max_response_time=max_response_time,
                min_response_time=min_response_time,
                throughput_ops_per_second=successful_orders / duration,
                error_rate=failed_orders / num_orders
            )
            
            # Determine test status
            if failed_orders > num_orders * 0.1:  # More than 10% failure rate
                status = "failed"
                message = f"High failure rate: {failed_orders}/{num_orders} orders failed"
            elif avg_response_time > 1.0:  # Average response time > 1 second
                status = "warning"
                message = f"Slow response time: {avg_response_time:.2f}s average"
            else:
                status = "passed"
                message = f"Successfully processed {successful_orders}/{num_orders} concurrent orders"
            
            return StressTestResult(
                test_name="Concurrent Orders Stress Test",
                test_type=test_type,
                status=status,
                message=message,
                metrics=metrics,
                details={
                    "total_orders": num_orders,
                    "successful_orders": successful_orders,
                    "failed_orders": failed_orders,
                    "symbols_used": len(set(symbols[:num_orders]))
                }
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return StressTestResult(
                test_name="Concurrent Orders Stress Test",
                test_type=test_type,
                status="failed",
                message=f"Test failed: {str(e)}",
                metrics=StressTestMetrics(
                    test_type=test_type,
                    duration=duration,
                    operations_completed=0,
                    operations_failed=1,
                    peak_cpu_usage=0.0,
                    peak_memory_usage=0.0,
                    average_response_time=0.0,
                    max_response_time=0.0,
                    min_response_time=0.0,
                    throughput_ops_per_second=0.0,
                    error_rate=1.0
                )
            )

    async def _submit_test_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """Submit a test order and measure response time."""
        start_time = time.time()

        try:
            # Submit order through order manager
            order_id = await self.order_manager.submit_order(order)
            response_time = time.time() - start_time

            return {
                "order_id": order_id,
                "response_time": response_time,
                "success": order_id is not None
            }

        except Exception as e:
            response_time = time.time() - start_time
            return {
                "order_id": None,
                "response_time": response_time,
                "success": False,
                "error": str(e)
            }

    def _generate_stress_report(self, total_duration: float, system_limits: Dict[str, Any]) -> StressTestReport:
        """Generate comprehensive stress test report."""
        passed = sum(1 for r in self.results if r.status == "passed")
        failed = sum(1 for r in self.results if r.status == "failed")
        warning = sum(1 for r in self.results if r.status == "warning")

        return StressTestReport(
            total_tests=len(self.results),
            passed_tests=passed,
            failed_tests=failed,
            warning_tests=warning,
            total_duration=total_duration,
            timestamp=datetime.utcnow(),
            results=self.results.copy(),
            system_limits=system_limits
        )


class SystemMonitor:
    """System resource monitoring during stress tests."""

    def __init__(self):
        self.monitoring = False
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_io': [],
            'network_io': []
        }
        self.monitor_thread = None

    def start_monitoring(self):
        """Start system monitoring."""
        self.monitoring = True
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_io': [],
            'network_io': []
        }

        self.monitor_thread = threading.Thread(target=self._monitor_system)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def stop_monitoring(self) -> Dict[str, Any]:
        """Stop monitoring and return collected metrics."""
        self.monitoring = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        return {
            'peak_cpu': max(self.metrics['cpu_usage']) if self.metrics['cpu_usage'] else 0,
            'peak_memory': max(self.metrics['memory_usage']) if self.metrics['memory_usage'] else 0,
            'avg_cpu': sum(self.metrics['cpu_usage']) / len(self.metrics['cpu_usage']) if self.metrics['cpu_usage'] else 0,
            'avg_memory': sum(self.metrics['memory_usage']) / len(self.metrics['memory_usage']) if self.metrics['memory_usage'] else 0,
            'total_samples': len(self.metrics['cpu_usage'])
        }

    def reset_metrics(self):
        """Reset collected metrics."""
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_io': [],
            'network_io': []
        }

    def get_peak_cpu(self) -> float:
        """Get peak CPU usage."""
        return max(self.metrics['cpu_usage']) if self.metrics['cpu_usage'] else 0.0

    def get_peak_memory(self) -> float:
        """Get peak memory usage."""
        return max(self.metrics['memory_usage']) if self.metrics['memory_usage'] else 0.0

    def _monitor_system(self):
        """Monitor system resources in background thread."""
        while self.monitoring:
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=0.1)
                self.metrics['cpu_usage'].append(cpu_percent)

                # Memory usage
                memory = psutil.virtual_memory()
                self.metrics['memory_usage'].append(memory.percent)

                # Disk I/O
                disk_io = psutil.disk_io_counters()
                if disk_io:
                    self.metrics['disk_io'].append({
                        'read_bytes': disk_io.read_bytes,
                        'write_bytes': disk_io.write_bytes
                    })

                # Network I/O
                network_io = psutil.net_io_counters()
                if network_io:
                    self.metrics['network_io'].append({
                        'bytes_sent': network_io.bytes_sent,
                        'bytes_recv': network_io.bytes_recv
                    })

                time.sleep(1)  # Sample every second

            except Exception as e:
                logger.error(f"Error monitoring system: {e}")
                time.sleep(1)
