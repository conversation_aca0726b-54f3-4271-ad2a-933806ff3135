# src/trading_bot/automation/webull_browser_bot.py
"""
Webull Browser Automation Trading Bot - COMPLETE VERSION

This module provides a complete browser-based automation solution for Webull trading
without requiring API access. It uses Selenium WebDriver to control the browser
and interact with Webull's web interface.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pickle
import os
import re

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import undetected_chromedriver as uc
import pandas as pd
import numpy as np
from PIL import Image
import pytesseract
import cv2

from ..core.logger import get_logger
from ..ml.ensemble_model import EnsembleModel
from ..risk.risk_manager import RiskManager

logger = get_logger(__name__)

class OrderType(Enum):
    """Order types supported by Webull"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"

class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"

@dataclass
class MarketData:
    """Market data captured from browser"""
    symbol: str
    price: float
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[int] = None
    change_percent: Optional[float] = None
    timestamp: datetime = None
    pre_market_change: Optional[float] = None
    exchange: Optional[str] = None
    day_high: Optional[float] = None
    day_low: Optional[float] = None
    market_cap: Optional[str] = None
    pe_ratio: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
@dataclass
class Position:
    """Position information"""
    symbol: str
    quantity: int
    avg_cost: float
    current_price: float
    pnl: float
    pnl_percent: float

@dataclass
class Order:
    """Order information"""
    order_id: str
    symbol: str
    side: OrderSide
    type: OrderType
    quantity: int
    price: Optional[float]
    status: str
    timestamp: datetime

class WebullBrowserBot:
    """
    Browser-based Webull trading bot using Selenium WebDriver.
    
    This bot automates Webull trading through the web interface,
    capturing market data and executing trades based on ML predictions.
    
    UPDATED: Fixed data extraction to work with actual Webull watchlist structure
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.driver = None
        self.wait = None
        self.logged_in = False
        
        # URLs
        self.base_url = "https://app.webull.com"
        self.login_url = f"{self.base_url}/login"
        self.paper_trading_url = f"{self.base_url}/paper"
        self.watchlist_url = f"{self.base_url}/watchlist"
        
        # Initialize ML model and risk manager
        self.ml_model = EnsembleModel(config)
        self.risk_manager = RiskManager(config)
        
        # Data storage
        self.market_data_cache = {}
        self.positions = {}
        self.orders = []
        
        # Browser automation settings
        self.screenshot_dir = config.get('screenshot_dir', './screenshots')
        self.cookies_file = config.get('cookies_file', './webull_cookies.pkl')
        
        # Create directories
        os.makedirs(self.screenshot_dir, exist_ok=True)
        
    async def initialize(self):
        """Initialize the browser driver"""
        try:
            logger.info("Initializing browser driver...")
            
            # Configure Chrome options for stealth mode
            chrome_options = uc.ChromeOptions()
            
            # Add stealth settings
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-setuid-sandbox')
            
            # Optional: Run in headless mode (but some sites detect this)
            if self.config.get('headless', False):
                chrome_options.add_argument('--headless')
                chrome_options.add_argument('--disable-gpu')
            
            # Set window size
            chrome_options.add_argument('--window-size=1920,1080')
            
            # Initialize undetected Chrome driver
            self.driver = uc.Chrome(options=chrome_options, version_main=None)
            self.wait = WebDriverWait(self.driver, 10)
            
            # Set page load timeout
            self.driver.set_page_load_timeout(30)
            
            # Additional stealth
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("Browser driver initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            return False
    
    async def login(self, username: str, password: str, mfa_code: Optional[str] = None):
        """
        Login to Webull through the browser
        
        Args:
            username: Webull username/email
            password: Account password
            mfa_code: Optional MFA code if enabled
        """
        try:
            logger.info("Starting Webull login process...")
            
            # Navigate to login page
            self.driver.get(self.login_url)
            await asyncio.sleep(3)  # Wait for page load
            
            # Check if we have saved cookies
            if os.path.exists(self.cookies_file) and self._load_cookies():
                logger.info("Loaded saved cookies, checking if logged in...")
                self.driver.refresh()
                await asyncio.sleep(3)
                
                if await self._check_logged_in():
                    logger.info("Successfully logged in using saved cookies")
                    self.logged_in = True
                    return
            
            # Find and fill login form
            logger.info("Filling login form...")
            
            # Wait for email input
            email_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='email'], input[placeholder*='Email']"))
            )
            email_input.clear()
            email_input.send_keys(username)
            await asyncio.sleep(1)
            
            # Find and fill password
            password_input = self.driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            password_input.clear()
            password_input.send_keys(password)
            await asyncio.sleep(1)
            
            # Take screenshot before login
            self._take_screenshot("before_login")
            
            # Click login button
            login_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit'], button:contains('Log In')")
            login_button.click()
            
            # Wait for login to process
            await asyncio.sleep(5)
            
            # Handle MFA if needed
            if mfa_code or await self._check_mfa_required():
                await self._handle_mfa(mfa_code)
            
            # Verify login success
            if await self._check_logged_in():
                logger.info("Login successful!")
                self.logged_in = True
                self._save_cookies()
                
                # Switch to paper trading if configured
                if self.config.get('paper_trading', True):
                    await self._switch_to_paper_trading()
            else:
                raise Exception("Login failed - could not verify logged in status")
                
        except Exception as e:
            logger.error(f"Login failed: {e}")
            self._take_screenshot("login_error")
            raise
    
    async def _check_logged_in(self) -> bool:
        """Check if we're logged in by looking for account elements"""
        try:
            # Look for elements that only appear when logged in
            logged_in_indicators = [
                "//div[contains(@class, 'account')]",
                "//button[contains(text(), 'Trade')]",
                "//div[contains(@class, 'portfolio')]",
                "//span[contains(text(), 'Positions')]"
            ]
            
            for indicator in logged_in_indicators:
                try:
                    self.driver.find_element(By.XPATH, indicator)
                    return True
                except NoSuchElementException:
                    continue
                    
            return False
            
        except Exception as e:
            logger.error(f"Error checking login status: {e}")
            return False
    
    async def _check_mfa_required(self) -> bool:
        """Check if MFA is required"""
        try:
            # Look for MFA input field
            mfa_elements = [
                "//input[contains(@placeholder, 'verification code')]",
                "//input[contains(@placeholder, 'MFA')]",
                "//div[contains(text(), 'Two-Factor')]"
            ]
            
            for element in mfa_elements:
                try:
                    self.driver.find_element(By.XPATH, element)
                    return True
                except NoSuchElementException:
                    continue
                    
            return False
            
        except Exception:
            return False
    
    async def _handle_mfa(self, mfa_code: Optional[str] = None):
        """Handle MFA verification"""
        try:
            logger.info("MFA required, handling verification...")
            
            if not mfa_code:
                # Wait for user to input MFA code manually
                logger.info("Please enter MFA code in the browser...")
                input("Press Enter after entering MFA code...")
            else:
                # Find MFA input and enter code
                mfa_input = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, "//input[contains(@placeholder, 'code')]"))
                )
                mfa_input.clear()
                mfa_input.send_keys(mfa_code)
                
                # Submit MFA
                submit_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Verify')]")
                submit_button.click()
                
            await asyncio.sleep(3)
            
        except Exception as e:
            logger.error(f"MFA handling failed: {e}")
            raise
    
    async def _switch_to_paper_trading(self):
        """Switch to paper trading mode"""
        try:
            logger.info("Switching to paper trading mode...")
            
            # Navigate to paper trading
            self.driver.get(self.paper_trading_url)
            await asyncio.sleep(3)
            
            logger.info("Switched to paper trading mode")
            
        except Exception as e:
            logger.error(f"Failed to switch to paper trading: {e}")
    
    def _save_cookies(self):
        """Save cookies for future sessions"""
        try:
            cookies = self.driver.get_cookies()
            with open(self.cookies_file, 'wb') as f:
                pickle.dump(cookies, f)
            logger.info("Cookies saved successfully")
        except Exception as e:
            logger.error(f"Failed to save cookies: {e}")
    
    def _load_cookies(self) -> bool:
        """Load saved cookies"""
        try:
            with open(self.cookies_file, 'rb') as f:
                cookies = pickle.load(f)
                
            for cookie in cookies:
                self.driver.add_cookie(cookie)
                
            logger.info("Cookies loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load cookies: {e}")
            return False
    
    def _take_screenshot(self, name: str):
        """Take a screenshot for debugging"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.screenshot_dir}/{name}_{timestamp}.png"
            self.driver.save_screenshot(filename)
            logger.info(f"Screenshot saved: {filename}")
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
    
    async def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """
        Get real-time market data for a symbol - UPDATED VERSION
        
        This version extracts data from the watchlist page structure
        instead of trying to navigate to individual stock pages.
        """
        try:
            logger.info(f"Getting market data for {symbol}")
            
            # First, try to get data from current page if it's in the watchlist
            page_text = self.driver.find_element(By.TAG_NAME, 'body').text
            lines = page_text.split('\n')
            
            # Look for the symbol in the watchlist
            market_data = self._extract_symbol_from_watchlist(symbol, lines)
            
            if not market_data:
                # If not found, try searching for it
                market_data = await self._search_and_extract_symbol(symbol)
            
            if market_data:
                # Cache the data
                self.market_data_cache[symbol] = market_data
                
                # Take screenshot for debugging
                self._take_screenshot(f"market_data_{symbol}")
                
                logger.info(f"Found {symbol}: ${market_data.price} ({market_data.change_percent}%)")
            else:
                logger.warning(f"Could not find data for {symbol}")
            
            return market_data
            
        except Exception as e:
            logger.error(f"Failed to get market data for {symbol}: {e}")
            self._take_screenshot(f"market_data_error_{symbol}")
            return None
    
    def _extract_symbol_from_watchlist(self, target_symbol: str, lines: List[str]) -> Optional[MarketData]:
        """Extract specific symbol data from watchlist lines"""
        try:
            i = 0
            while i < len(lines):
                line = lines[i].strip()
                
                # Check if this line contains our target symbol
                if line == target_symbol:
                    # Found the symbol, extract data from following lines
                    if i + 4 < len(lines):
                        exchange = lines[i + 1].strip()
                        company = lines[i + 2].strip()
                        price_line = lines[i + 3].strip()
                        change_line = lines[i + 4].strip() if i + 4 < len(lines) else ""
                        
                        # Extract price
                        try:
                            price = float(price_line.replace(',', ''))
                        except:
                            price = None
                        
                        # Extract change percentage
                        change_percent = None
                        percent_match = re.search(r'([+-]?\d+\.?\d*)%', change_line)
                        if percent_match:
                            change_percent = float(percent_match.group(1))
                        
                        if price:
                            return MarketData(
                                symbol=target_symbol,
                                price=price,
                                change_percent=change_percent,
                                exchange=exchange
                            )
                
                # Also check compact format (indices)
                if target_symbol in line:
                    compact_match = re.match(
                        rf'{target_symbol}\s+([A-Z]+)\s+(.+?)\s+(\d{{1,2}},?\d{{3,}}\.?\d*)\s+([+-]?\d+\.?\d*)\s+([+-]?\d+\.?\d*)%',
                        line
                    )
                    if compact_match:
                        price_str = compact_match.group(3).replace(',', '')
                        percent_str = compact_match.group(5)
                        
                        return MarketData(
                            symbol=target_symbol,
                            price=float(price_str),
                            change_percent=float(percent_str),
                            exchange=compact_match.group(1)
                        )
                
                i += 1
                
            return None
            
        except Exception as e:
            logger.error(f"Error extracting {target_symbol} from watchlist: {e}")
            return None
    
    async def _search_and_extract_symbol(self, symbol: str) -> Optional[MarketData]:
        """Search for a symbol using the search functionality"""
        try:
            logger.info(f"Searching for {symbol}...")
            
            # Find search input
            search_selectors = [
                "input[placeholder*='Search']",
                "input[type='search']",
                "input[placeholder*='Symbol']",
                "//input[contains(@placeholder, 'Search')]"
            ]
            
            search_input = None
            for selector in search_selectors:
                try:
                    if selector.startswith('//'):
                        search_input = self.driver.find_element(By.XPATH, selector)
                    else:
                        search_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if not search_input:
                logger.warning("Could not find search input")
                return None
            
            # Clear and search
            search_input.clear()
            search_input.send_keys(symbol)
            search_input.send_keys(Keys.RETURN)
            
            # Wait for results
            await asyncio.sleep(3)
            
            # Try to extract data from search results
            page_text = self.driver.find_element(By.TAG_NAME, 'body').text
            lines = page_text.split('\n')
            
            return self._extract_symbol_from_watchlist(symbol, lines)
            
        except Exception as e:
            logger.error(f"Error searching for {symbol}: {e}")
            return None
    
    async def get_positions(self) -> Dict[str, Position]:
        """Get current positions"""
        try:
            logger.info("Getting current positions...")
            
            # Navigate to positions page
            positions_link = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Positions')]"))
            )
            positions_link.click()
            
            await asyncio.sleep(3)
            
            positions = {}
            
            # Find position rows
            position_rows = self.driver.find_elements(By.CSS_SELECTOR, "tr[class*='position-row']")
            
            for row in position_rows:
                try:
                    # Extract position data
                    symbol = row.find_element(By.CSS_SELECTOR, "td[class*='symbol']").text.strip()
                    quantity = int(row.find_element(By.CSS_SELECTOR, "td[class*='quantity']").text.strip())
                    avg_cost = float(row.find_element(By.CSS_SELECTOR, "td[class*='cost']").text.strip().replace('$', ''))
                    current_price = float(row.find_element(By.CSS_SELECTOR, "td[class*='price']").text.strip().replace('$', ''))
                    pnl = float(row.find_element(By.CSS_SELECTOR, "td[class*='pnl']").text.strip().replace('$', '').replace(',', ''))
                    pnl_percent = float(row.find_element(By.CSS_SELECTOR, "td[class*='pnl-percent']").text.strip().replace('%', ''))
                    
                    position = Position(
                        symbol=symbol,
                        quantity=quantity,
                        avg_cost=avg_cost,
                        current_price=current_price,
                        pnl=pnl,
                        pnl_percent=pnl_percent
                    )
                    
                    positions[symbol] = position
                    
                except Exception as e:
                    logger.error(f"Failed to parse position row: {e}")
                    continue
            
            self.positions = positions
            logger.info(f"Found {len(positions)} positions")
            
            return positions
            
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
            self._take_screenshot("positions_error")
            return {}
    
    async def place_order(self, 
                         symbol: str, 
                         side: OrderSide, 
                         quantity: int,
                         order_type: OrderType = OrderType.MARKET,
                         price: Optional[float] = None) -> Optional[str]:
        """
        Place an order through the browser
        
        Args:
            symbol: Stock symbol
            side: Buy or sell
            quantity: Number of shares
            order_type: Type of order
            price: Limit price (for limit orders)
            
        Returns:
            Order ID if successful, None otherwise
        """
        try:
            logger.info(f"Placing {side.value} order for {quantity} shares of {symbol}")
            
            # Navigate to trading page
            trade_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button:contains('Trade')"))
            )
            trade_button.click()
            
            await asyncio.sleep(2)
            
            # Enter symbol
            symbol_input = self.driver.find_element(By.CSS_SELECTOR, "input[placeholder*='Symbol']")
            symbol_input.clear()
            symbol_input.send_keys(symbol)
            await asyncio.sleep(1)
            
            # Select buy or sell
            if side == OrderSide.BUY:
                buy_button = self.driver.find_element(By.CSS_SELECTOR, "button:contains('Buy')")
                buy_button.click()
            else:
                sell_button = self.driver.find_element(By.CSS_SELECTOR, "button:contains('Sell')")
                sell_button.click()
            
            # Enter quantity
            quantity_input = self.driver.find_element(By.CSS_SELECTOR, "input[placeholder*='Quantity']")
            quantity_input.clear()
            quantity_input.send_keys(str(quantity))
            
            # Select order type
            if order_type != OrderType.MARKET:
                order_type_dropdown = self.driver.find_element(By.CSS_SELECTOR, "select[class*='order-type']")
                order_type_dropdown.send_keys(order_type.value)
                
                if price and order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
                    price_input = self.driver.find_element(By.CSS_SELECTOR, "input[placeholder*='Price']")
                    price_input.clear()
                    price_input.send_keys(str(price))
            
            # Take screenshot before submitting
            self._take_screenshot(f"order_preview_{symbol}")
            
            # Submit order
            submit_button = self.driver.find_element(By.CSS_SELECTOR, "button:contains('Submit')")
            submit_button.click()
            
            await asyncio.sleep(2)
            
            # Confirm order if needed
            try:
                confirm_button = self.driver.find_element(By.CSS_SELECTOR, "button:contains('Confirm')")
                confirm_button.click()
                await asyncio.sleep(2)
            except NoSuchElementException:
                pass
            
            # Extract order ID from confirmation
            order_id = self._extract_order_id()
            
            if order_id:
                logger.info(f"Order placed successfully: {order_id}")
                
                # Create order record
                order = Order(
                    order_id=order_id,
                    symbol=symbol,
                    side=side,
                    type=order_type,
                    quantity=quantity,
                    price=price,
                    status="Pending",
                    timestamp=datetime.now()
                )
                self.orders.append(order)
                
                return order_id
            else:
                logger.error("Failed to get order ID")
                return None
                
        except Exception as e:
            logger.error(f"Failed to place order: {e}")
            self._take_screenshot("order_error")
            return None
    
    def _extract_order_id(self) -> Optional[str]:
        """Extract order ID from confirmation"""
        try:
            # Look for order ID in confirmation message
            confirmation_selectors = [
                "div[class*='order-id']",
                "span:contains('Order #')",
                "div:contains('Order ID')"
            ]
            
            for selector in confirmation_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    text = element.text
                    # Extract order ID (usually a number)
                    import re
                    match = re.search(r'\d{6,}', text)
                    if match:
                        return match.group()
                except:
                    continue
                    
            return None
            
        except Exception:
            return None
    
    async def run_trading_loop(self):
        """Main trading loop"""
        try:
            logger.info("Starting automated trading loop...")
            
            while True:
                try:
                    # Get watchlist symbols
                    watchlist = self.config.get('watchlist', ['AAPL', 'MSFT', 'GOOGL', 'TSLA'])
                    
                    for symbol in watchlist:
                        # Get market data
                        market_data = await self.get_market_data(symbol)
                        
                        if market_data:
                            # Prepare data for ML model
                            features = self._prepare_features(market_data)
                            
                            # Get ML prediction
                            prediction = await self.ml_model.predict(features)
                            
                            # Check risk management
                            if self.risk_manager.check_trade_allowed(symbol, prediction):
                                # Execute trade based on prediction
                                await self._execute_trade(symbol, prediction)
                    
                    # Get current positions
                    await self.get_positions()
                    
                    # Check for exit signals
                    await self._check_exit_signals()
                    
                    # Wait before next iteration
                    await asyncio.sleep(self.config.get('loop_interval', 60))
                    
                except Exception as e:
                    logger.error(f"Error in trading loop iteration: {e}")
                    await asyncio.sleep(30)
                    
        except KeyboardInterrupt:
            logger.info("Trading loop stopped by user")
        except Exception as e:
            logger.error(f"Fatal error in trading loop: {e}")
            raise
    
    def _prepare_features(self, market_data: MarketData) -> pd.DataFrame:
        """Prepare features for ML model"""
        # This is a simplified example - real implementation would
        # calculate technical indicators, market microstructure features, etc.
        
        features = pd.DataFrame({
            'price': [market_data.price],
            'volume': [market_data.volume if market_data.volume else 0],
            'change_percent': [market_data.change_percent if market_data.change_percent else 0]
        })
        
        return features
    
    async def _execute_trade(self, symbol: str, prediction: Dict[str, Any]):
        """Execute trade based on ML prediction"""
        try:
            signal = prediction.get('signal')
            confidence = prediction.get('confidence', 0)
            
            # Only trade on high confidence signals
            if confidence < self.config.get('min_confidence', 0.7):
                return
            
            # Determine position size
            position_size = self.risk_manager.calculate_position_size(symbol, confidence)
            
            if signal == 'BUY':
                await self.place_order(symbol, OrderSide.BUY, position_size)
            elif signal == 'SELL':
                await self.place_order(symbol, OrderSide.SELL, position_size)
                
        except Exception as e:
            logger.error(f"Failed to execute trade: {e}")
    
    async def _check_exit_signals(self):
        """Check for exit signals on existing positions"""
        for symbol, position in self.positions.items():
            try:
                # Get current market data
                market_data = await self.get_market_data(symbol)
                
                if market_data:
                    # Check stop loss
                    if position.pnl_percent < self.config.get('stop_loss_percent', -2.0):
                        logger.info(f"Stop loss triggered for {symbol}")
                        await self.place_order(symbol, OrderSide.SELL, position.quantity)
                    
                    # Check take profit
                    elif position.pnl_percent > self.config.get('take_profit_percent', 5.0):
                        logger.info(f"Take profit triggered for {symbol}")
                        await self.place_order(symbol, OrderSide.SELL, position.quantity)
                        
            except Exception as e:
                logger.error(f"Error checking exit signals for {symbol}: {e}")
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
            logger.info("Browser driver closed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Usage example
async def main():
    """Example usage of the Webull browser bot"""
    
    config = {
        'headless': False,  # Set to True for headless mode
        'paper_trading': True,
        'watchlist': ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA'],
        'min_confidence': 0.7,
        'stop_loss_percent': -2.0,
        'take_profit_percent': 5.0,
        'loop_interval': 60,  # seconds
        'screenshot_dir': './screenshots',
        'cookies_file': './webull_cookies.pkl'
    }
    
    bot = WebullBrowserBot(config)
    
    try:
        # Initialize browser
        await bot.initialize()
        
        # Test data extraction without login
        logger.info("Testing data extraction...")
        
        # Get market data for test symbols
        test_symbols = ['AAPL', 'GOOG', 'MSFT']
        for symbol in test_symbols:
            data = await bot.get_market_data(symbol)
            if data:
                logger.info(f"✅ {symbol}: ${data.price} ({data.change_percent}%)")
            else:
                logger.info(f"❌ {symbol}: No data found")
            await asyncio.sleep(2)
        
        # For full trading functionality, uncomment below:
        # # Login to Webull
        # username = input("Enter Webull username/email: ")
        # password = input("Enter Webull password: ")
        # await bot.login(username, password)
        # 
        # # Run trading loop
        # await bot.run_trading_loop()
        
    except Exception as e:
        logger.error(f"Bot error: {e}")
    finally:
        await bot.cleanup()

if __name__ == "__main__":
    asyncio.run(main())