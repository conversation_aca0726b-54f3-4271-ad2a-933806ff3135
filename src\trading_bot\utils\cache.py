"""Redis-based caching for API responses."""

import json
import pickle
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import redis.asyncio as redis

from ..core.config import settings
from ..core.logger import get_logger

logger = get_logger(__name__)


class CacheManager:
    """Redis-based cache manager for API responses."""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.default_ttl = 300  # 5 minutes default TTL
        
        # Cache TTL settings for different data types
        self.ttl_settings = {
            "quote": 5,           # Real-time quotes: 5 seconds
            "bars": 60,           # Historical bars: 1 minute
            "account": 30,        # Account info: 30 seconds
            "positions": 10,      # Positions: 10 seconds
            "orders": 5,          # Orders: 5 seconds
            "symbols": 3600,      # Symbol info: 1 hour
            "watchlist": 300,     # Watchlist: 5 minutes
            "market_hours": 1800, # Market hours: 30 minutes
        }
    
    async def initialize(self):
        """Initialize Redis connection."""
        try:
            self.redis_client = redis.from_url(
                settings.database.redis_url,
                encoding="utf-8",
                decode_responses=False,  # We'll handle encoding ourselves
                socket_keepalive=True,
                health_check_interval=30,
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Cache manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cache manager: {e}")
            self.redis_client = None
    
    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
    
    def _get_key(self, prefix: str, identifier: str) -> str:
        """Generate cache key."""
        return f"trading_bot:{prefix}:{identifier}"
    
    def _serialize_data(self, data: Any) -> bytes:
        """Serialize data for storage."""
        try:
            # Try JSON first (faster and more readable)
            return json.dumps(data, default=str).encode('utf-8')
        except (TypeError, ValueError):
            # Fall back to pickle for complex objects
            return pickle.dumps(data)
    
    def _deserialize_data(self, data: bytes) -> Any:
        """Deserialize data from storage."""
        try:
            # Try JSON first
            return json.loads(data.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # Fall back to pickle
            return pickle.loads(data)
    
    async def get(self, cache_type: str, key: str) -> Optional[Any]:
        """
        Get data from cache.
        
        Args:
            cache_type: Type of cached data (quote, bars, etc.)
            key: Cache key identifier
            
        Returns:
            Cached data or None if not found
        """
        if not self.redis_client:
            return None
        
        try:
            cache_key = self._get_key(cache_type, key)
            data = await self.redis_client.get(cache_key)
            
            if data is None:
                return None
            
            result = self._deserialize_data(data)
            
            logger.debug(
                f"Cache hit",
                extra={
                    "cache_type": cache_type,
                    "key": key,
                    "cache_key": cache_key,
                }
            )
            
            return result
            
        except Exception as e:
            logger.warning(f"Cache get error: {e}", extra={"cache_type": cache_type, "key": key})
            return None
    
    async def set(
        self,
        cache_type: str,
        key: str,
        data: Any,
        ttl: Optional[int] = None,
    ) -> bool:
        """
        Set data in cache.
        
        Args:
            cache_type: Type of cached data
            key: Cache key identifier
            data: Data to cache
            ttl: Time to live in seconds (uses default if None)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.redis_client:
            return False
        
        try:
            cache_key = self._get_key(cache_type, key)
            serialized_data = self._serialize_data(data)
            
            # Use type-specific TTL or provided TTL or default
            cache_ttl = ttl or self.ttl_settings.get(cache_type, self.default_ttl)
            
            await self.redis_client.setex(cache_key, cache_ttl, serialized_data)
            
            logger.debug(
                f"Cache set",
                extra={
                    "cache_type": cache_type,
                    "key": key,
                    "cache_key": cache_key,
                    "ttl": cache_ttl,
                }
            )
            
            return True
            
        except Exception as e:
            logger.warning(f"Cache set error: {e}", extra={"cache_type": cache_type, "key": key})
            return False
    
    async def delete(self, cache_type: str, key: str) -> bool:
        """
        Delete data from cache.
        
        Args:
            cache_type: Type of cached data
            key: Cache key identifier
            
        Returns:
            True if successful, False otherwise
        """
        if not self.redis_client:
            return False
        
        try:
            cache_key = self._get_key(cache_type, key)
            result = await self.redis_client.delete(cache_key)
            
            logger.debug(
                f"Cache delete",
                extra={
                    "cache_type": cache_type,
                    "key": key,
                    "cache_key": cache_key,
                    "deleted": bool(result),
                }
            )
            
            return bool(result)
            
        except Exception as e:
            logger.warning(f"Cache delete error: {e}", extra={"cache_type": cache_type, "key": key})
            return False
    
    async def clear_type(self, cache_type: str) -> int:
        """
        Clear all cache entries of a specific type.
        
        Args:
            cache_type: Type of cached data to clear
            
        Returns:
            Number of keys deleted
        """
        if not self.redis_client:
            return 0
        
        try:
            pattern = self._get_key(cache_type, "*")
            keys = await self.redis_client.keys(pattern)
            
            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info(f"Cleared {deleted} cache entries of type {cache_type}")
                return deleted
            
            return 0
            
        except Exception as e:
            logger.warning(f"Cache clear error: {e}", extra={"cache_type": cache_type})
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self.redis_client:
            return {"status": "disconnected"}
        
        try:
            info = await self.redis_client.info()
            
            # Count keys by type
            type_counts = {}
            for cache_type in self.ttl_settings.keys():
                pattern = self._get_key(cache_type, "*")
                keys = await self.redis_client.keys(pattern)
                type_counts[cache_type] = len(keys)
            
            return {
                "status": "connected",
                "total_keys": info.get("db0", {}).get("keys", 0),
                "memory_used": info.get("used_memory_human", "0B"),
                "type_counts": type_counts,
                "ttl_settings": self.ttl_settings,
            }
            
        except Exception as e:
            logger.warning(f"Cache stats error: {e}")
            return {"status": "error", "error": str(e)}
    
    async def health_check(self) -> bool:
        """Check if cache is healthy."""
        if not self.redis_client:
            return False
        
        try:
            await self.redis_client.ping()
            return True
        except Exception:
            return False


# Global cache manager instance
cache_manager = CacheManager()
