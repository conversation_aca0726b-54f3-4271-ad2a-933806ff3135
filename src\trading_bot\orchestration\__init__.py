"""System Orchestration Module.

This module provides comprehensive system orchestration and integration capabilities:
- Master controller for coordinating all trading bot components
- Service management for microservices architecture
- Health monitoring and performance tracking
- Failover management and disaster recovery
- Event-driven architecture and service mesh
"""

from .master_controller import MasterController
from .service_manager import ServiceManager
from .health_monitor import HealthMonitor
from .performance_tracker import PerformanceTracker
from .failover_manager import FailoverManager

__all__ = [
    'MasterController',
    'ServiceManager', 
    'HealthMonitor',
    'PerformanceTracker',
    'FailoverManager'
]
