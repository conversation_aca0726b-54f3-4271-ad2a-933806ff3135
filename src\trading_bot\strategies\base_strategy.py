"""Base strategy framework for all trading strategies."""

import asyncio
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class SignalType(Enum):
    """Types of trading signals."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    CLOSE_LONG = "CLOSE_LONG"
    CLOSE_SHORT = "CLOSE_SHORT"
    SCALE_IN = "SCALE_IN"
    SCALE_OUT = "SCALE_OUT"


class OrderType(Enum):
    """Types of orders."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"
    TRAILING_STOP = "TRAILING_STOP"


@dataclass
class MarketData:
    """Market data container."""
    timestamp: datetime
    symbol: str
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    # Optional additional data
    bid: Optional[float] = None
    ask: Optional[float] = None
    bid_size: Optional[float] = None
    ask_size: Optional[float] = None
    
    # Technical indicators (if pre-calculated)
    indicators: Dict[str, float] = field(default_factory=dict)
    
    # Market microstructure data
    order_flow: Optional[Dict[str, Any]] = None


@dataclass 
class Signal:
    """Trading signal generated by strategy."""
    timestamp: datetime
    symbol: str
    signal_type: SignalType
    confidence: float  # 0.0 to 1.0
    
    # Position sizing
    position_size: float  # As percentage of portfolio
    direction: int  # 1 for long, -1 for short, 0 for close
    
    # Entry conditions
    entry_price: Optional[float] = None
    order_type: OrderType = OrderType.MARKET
    
    # Risk management
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    max_holding_time: Optional[timedelta] = None
    
    # Strategy context
    strategy_name: str = ""
    reason: str = ""
    timeframe: str = "1m"
    
    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Position:
    """Open position representation."""
    symbol: str
    quantity: float
    entry_price: float
    entry_time: datetime
    direction: int  # 1 for long, -1 for short
    
    # Current status
    current_price: float
    unrealized_pnl: float
    
    # Risk management
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # Strategy information
    strategy_name: str = ""
    signal_id: Optional[str] = None
    
    # Position management
    max_holding_time: Optional[timedelta] = None
    trailing_stop_distance: Optional[float] = None
    
    def update_price(self, new_price: float):
        """Update position with new price."""
        self.current_price = new_price
        self.unrealized_pnl = (new_price - self.entry_price) * self.quantity * self.direction
    
    def is_expired(self, current_time: datetime) -> bool:
        """Check if position has exceeded max holding time."""
        if self.max_holding_time is None:
            return False
        return current_time - self.entry_time > self.max_holding_time


@dataclass
class ExitConditions:
    """Exit conditions for a position."""
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    trailing_stop: Optional[float] = None
    time_stop: Optional[datetime] = None
    
    # Dynamic conditions
    exit_signal_conditions: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Order:
    """Order representation."""
    symbol: str
    quantity: float
    order_type: OrderType
    direction: int  # 1 for buy, -1 for sell
    
    # Pricing
    price: Optional[float] = None  # For limit orders
    stop_price: Optional[float] = None  # For stop orders
    
    # Order management
    order_id: Optional[str] = None
    strategy_name: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Execution parameters
    time_in_force: str = "DAY"  # DAY, GTC, IOC, FOK
    execution_algo: Optional[str] = None  # TWAP, VWAP, etc.


class BaseStrategy(ABC):
    """Abstract base class for all trading strategies."""
    
    def __init__(self, 
                 name: str,
                 config: Dict[str, Any],
                 risk_manager=None,
                 ml_system=None):
        self.name = name
        self.config = config
        self.risk_manager = risk_manager
        self.ml_system = ml_system
        
        # Strategy state
        self.is_active = True
        self.positions: Dict[str, Position] = {}
        self.signals_history: List[Signal] = []
        self.performance_metrics: Dict[str, float] = {}
        
        # Risk limits
        self.max_position_size = config.get('max_position_size', 0.1)
        self.max_positions = config.get('max_positions', 5)
        self.max_drawdown = config.get('max_drawdown', 0.05)
        
        # Performance tracking
        self.total_pnl = 0.0
        self.trades_count = 0
        self.winning_trades = 0
        self.max_drawdown_reached = 0.0
        
        logger.info(f"Initialized strategy: {name}")
    
    @abstractmethod
    async def generate_signals(self, market_data: MarketData) -> List[Signal]:
        """Generate trading signals based on market data.
        
        Args:
            market_data: Current market data
            
        Returns:
            List of trading signals
        """
        pass
    
    @abstractmethod 
    async def calculate_position_size(self, signal: Signal, portfolio_value: float) -> float:
        """Calculate position size for a signal.
        
        Args:
            signal: Trading signal
            portfolio_value: Current portfolio value
            
        Returns:
            Position size as absolute value
        """
        pass
    
    @abstractmethod
    async def set_exit_conditions(self, position: Position, market_data: MarketData) -> ExitConditions:
        """Set exit conditions for a position.
        
        Args:
            position: Current position
            market_data: Current market data
            
        Returns:
            Exit conditions for the position
        """
        pass
    
    async def should_enter_trade(self, signal: Signal, market_data: MarketData) -> bool:
        """Determine if trade should be entered based on risk checks.
        
        Args:
            signal: Trading signal to evaluate
            market_data: Current market data
            
        Returns:
            True if trade should be entered
        """
        try:
            # Check if strategy is active
            if not self.is_active:
                logger.warning(f"Strategy {self.name} is inactive")
                return False
            
            # Check signal confidence
            min_confidence = self.config.get('min_confidence', 0.6)
            if signal.confidence < min_confidence:
                logger.debug(f"Signal confidence {signal.confidence} below threshold {min_confidence}")
                return False
            
            # Check position limits
            if len(self.positions) >= self.max_positions:
                logger.debug(f"Maximum positions ({self.max_positions}) reached")
                return False
            
            # Check if already have position in this symbol
            if signal.symbol in self.positions:
                existing_position = self.positions[signal.symbol]
                # Only allow if it's scaling in/out or closing
                if signal.signal_type not in [SignalType.SCALE_IN, SignalType.SCALE_OUT, 
                                            SignalType.CLOSE_LONG, SignalType.CLOSE_SHORT]:
                    logger.debug(f"Already have position in {signal.symbol}")
                    return False
            
            # Risk manager checks
            if self.risk_manager:
                risk_check = await self.risk_manager.validate_trade(signal, market_data)
                if not risk_check.approved:
                    logger.debug(f"Risk manager rejected trade: {risk_check.reason}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error in should_enter_trade: {e}")
            return False
    
    async def execute_trade(self, signal: Signal, portfolio_value: float) -> Optional[Order]:
        """Execute a trading signal.
        
        Args:
            signal: Signal to execute
            portfolio_value: Current portfolio value
            
        Returns:
            Order object if trade was executed
        """
        try:
            # Calculate position size
            position_size = await self.calculate_position_size(signal, portfolio_value)
            
            if position_size <= 0:
                logger.warning(f"Invalid position size: {position_size}")
                return None
            
            # Create order
            order = Order(
                symbol=signal.symbol,
                quantity=position_size,
                order_type=signal.order_type,
                direction=signal.direction,
                price=signal.entry_price,
                strategy_name=self.name,
                timestamp=signal.timestamp
            )
            
            # Add to signals history
            self.signals_history.append(signal)
            
            logger.info(f"Executing trade: {signal.signal_type.value} {signal.symbol} "
                       f"size={position_size} confidence={signal.confidence}")
            
            return order
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return None
    
    async def monitor_positions(self, market_data: MarketData) -> List[Order]:
        """Monitor existing positions and generate exit orders if needed.
        
        Args:
            market_data: Current market data
            
        Returns:
            List of exit orders
        """
        exit_orders = []
        
        try:
            for symbol, position in list(self.positions.items()):
                # Update position with current price
                if symbol == market_data.symbol:
                    position.update_price(market_data.close)
                
                # Check exit conditions
                exit_conditions = await self.set_exit_conditions(position, market_data)
                
                # Check for exit signals
                exit_order = await self._check_exit_conditions(position, market_data, exit_conditions)
                
                if exit_order:
                    exit_orders.append(exit_order)
                    # Remove position (will be updated by execution system)
                    logger.info(f"Closing position {symbol}: {exit_order}")
                
            return exit_orders
            
        except Exception as e:
            logger.error(f"Error monitoring positions: {e}")
            return []
    
    async def _check_exit_conditions(self, 
                                   position: Position, 
                                   market_data: MarketData,
                                   exit_conditions: ExitConditions) -> Optional[Order]:
        """Check if any exit conditions are met."""
        
        current_price = market_data.close
        
        # Stop loss check
        if exit_conditions.stop_loss:
            if position.direction == 1:  # Long position
                if current_price <= exit_conditions.stop_loss:
                    return self._create_exit_order(position, "Stop Loss")
            else:  # Short position
                if current_price >= exit_conditions.stop_loss:
                    return self._create_exit_order(position, "Stop Loss")
        
        # Take profit check
        if exit_conditions.take_profit:
            if position.direction == 1:  # Long position
                if current_price >= exit_conditions.take_profit:
                    return self._create_exit_order(position, "Take Profit")
            else:  # Short position
                if current_price <= exit_conditions.take_profit:
                    return self._create_exit_order(position, "Take Profit")
        
        # Time-based exit
        if exit_conditions.time_stop:
            if market_data.timestamp >= exit_conditions.time_stop:
                return self._create_exit_order(position, "Time Stop")
        
        # Position expiry
        if position.is_expired(market_data.timestamp):
            return self._create_exit_order(position, "Position Expired")
        
        # Trailing stop check
        if exit_conditions.trailing_stop:
            # Implementation depends on how trailing stop is tracked
            pass
        
        return None
    
    def _create_exit_order(self, position: Position, reason: str) -> Order:
        """Create an exit order for a position."""
        return Order(
            symbol=position.symbol,
            quantity=abs(position.quantity),
            order_type=OrderType.MARKET,
            direction=-position.direction,  # Opposite direction to close
            strategy_name=self.name,
            timestamp=datetime.now()
        )
    
    def update_position(self, symbol: str, executed_order: Order):
        """Update position after order execution."""
        try:
            if executed_order.direction > 0:  # Buy order
                if symbol in self.positions:
                    # Add to existing position
                    pos = self.positions[symbol]
                    total_quantity = pos.quantity + executed_order.quantity
                    weighted_price = ((pos.quantity * pos.entry_price) + 
                                    (executed_order.quantity * executed_order.price)) / total_quantity
                    pos.quantity = total_quantity
                    pos.entry_price = weighted_price
                else:
                    # Create new long position
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=executed_order.quantity,
                        entry_price=executed_order.price,
                        entry_time=executed_order.timestamp,
                        direction=1,
                        current_price=executed_order.price,
                        unrealized_pnl=0.0,
                        strategy_name=self.name
                    )
            else:  # Sell order
                if symbol in self.positions:
                    pos = self.positions[symbol]
                    if executed_order.quantity >= pos.quantity:
                        # Close position completely
                        realized_pnl = (executed_order.price - pos.entry_price) * pos.quantity * pos.direction
                        self.total_pnl += realized_pnl
                        self.trades_count += 1
                        if realized_pnl > 0:
                            self.winning_trades += 1
                        del self.positions[symbol]
                    else:
                        # Partially close position
                        pos.quantity -= executed_order.quantity
                        realized_pnl = (executed_order.price - pos.entry_price) * executed_order.quantity * pos.direction
                        self.total_pnl += realized_pnl
                else:
                    # Create new short position
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=executed_order.quantity,
                        entry_price=executed_order.price,
                        entry_time=executed_order.timestamp,
                        direction=-1,
                        current_price=executed_order.price,
                        unrealized_pnl=0.0,
                        strategy_name=self.name
                    )
                    
        except Exception as e:
            logger.error(f"Error updating position: {e}")
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get strategy performance metrics."""
        if self.trades_count == 0:
            return {
                'total_pnl': self.total_pnl,
                'trades_count': 0,
                'win_rate': 0.0,
                'avg_trade_pnl': 0.0,
                'max_drawdown': self.max_drawdown_reached
            }
        
        win_rate = self.winning_trades / self.trades_count
        avg_trade_pnl = self.total_pnl / self.trades_count
        
        return {
            'total_pnl': self.total_pnl,
            'trades_count': self.trades_count,
            'win_rate': win_rate,
            'avg_trade_pnl': avg_trade_pnl,
            'winning_trades': self.winning_trades,
            'losing_trades': self.trades_count - self.winning_trades,
            'max_drawdown': self.max_drawdown_reached,
            'active_positions': len(self.positions)
        }
    
    def reset_strategy(self):
        """Reset strategy state (for backtesting)."""
        self.positions.clear()
        self.signals_history.clear()
        self.total_pnl = 0.0
        self.trades_count = 0
        self.winning_trades = 0
        self.max_drawdown_reached = 0.0
        self.is_active = True
    
    def set_active(self, active: bool):
        """Activate or deactivate strategy."""
        self.is_active = active
        logger.info(f"Strategy {self.name} {'activated' if active else 'deactivated'}")
    
    async def on_market_data(self, market_data: MarketData) -> Tuple[List[Signal], List[Order]]:
        """Main strategy processing method called on each market data update.
        
        Args:
            market_data: Current market data
            
        Returns:
            Tuple of (new_signals, exit_orders)
        """
        try:
            # Generate new signals
            new_signals = await self.generate_signals(market_data)
            
            # Monitor existing positions
            exit_orders = await self.monitor_positions(market_data)
            
            return new_signals, exit_orders
            
        except Exception as e:
            logger.error(f"Error in {self.name} strategy processing: {e}")
            return [], []
    
    def __str__(self) -> str:
        """String representation of strategy."""
        return f"Strategy({self.name}, active={self.is_active}, positions={len(self.positions)})"