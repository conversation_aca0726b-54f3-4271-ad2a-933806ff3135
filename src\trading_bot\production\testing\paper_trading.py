"""
Paper trading validation for AI Trading Bot.

This module provides comprehensive 30-day paper trading validation
to ensure strategy performance and risk management before live deployment.
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np

from ...core.config import Config
from ...data.market_data import MarketDataManager
from ...ml.model_manager import ModelManager
from ...strategies.strategy_manager import StrategyManager
from ...execution.order_manager import OrderManager
from ...risk.risk_manager import RiskManager
from ...utils.logger import get_logger

logger = get_logger(__name__)


class ValidationStatus(Enum):
    """Paper trading validation status."""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    IN_PROGRESS = "in_progress"


@dataclass
class TradingMetrics:
    """Trading performance metrics."""
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_pnl: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    average_win: float = 0.0
    average_loss: float = 0.0
    profit_factor: float = 0.0
    max_consecutive_losses: int = 0
    volatility: float = 0.0
    
    @property
    def risk_adjusted_return(self) -> float:
        """Calculate risk-adjusted return."""
        if self.volatility == 0:
            return 0.0
        return self.total_pnl / self.volatility


@dataclass
class RiskMetrics:
    """Risk management metrics."""
    max_position_size: float = 0.0
    max_daily_loss: float = 0.0
    max_sector_exposure: float = 0.0
    var_95: float = 0.0  # Value at Risk 95%
    cvar_95: float = 0.0  # Conditional Value at Risk 95%
    risk_limit_breaches: int = 0
    emergency_stops: int = 0
    position_size_violations: int = 0
    
    @property
    def risk_score(self) -> float:
        """Calculate overall risk score (0-100, lower is better)."""
        score = 0
        score += min(self.risk_limit_breaches * 10, 50)
        score += min(self.emergency_stops * 20, 30)
        score += min(self.position_size_violations * 5, 20)
        return min(score, 100)


@dataclass
class PaperTradingReport:
    """Comprehensive paper trading validation report."""
    validation_period: int  # days
    start_date: datetime
    end_date: datetime
    status: ValidationStatus
    trading_metrics: TradingMetrics
    risk_metrics: RiskMetrics
    strategy_performance: Dict[str, Any] = field(default_factory=dict)
    daily_pnl: List[float] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    issues: List[str] = field(default_factory=list)
    
    @property
    def is_ready_for_live_trading(self) -> bool:
        """Check if ready for live trading based on validation criteria."""
        return (
            self.status == ValidationStatus.PASSED and
            self.trading_metrics.win_rate >= 0.55 and  # 55% win rate
            self.trading_metrics.sharpe_ratio >= 1.0 and  # Sharpe ratio >= 1.0
            self.trading_metrics.max_drawdown <= 0.15 and  # Max 15% drawdown
            self.risk_metrics.risk_score <= 20 and  # Low risk score
            len(self.issues) == 0
        )


class PaperTradingValidator:
    """
    Comprehensive paper trading validation system.
    
    Validates trading bot performance over 30-day period including:
    - Strategy performance analysis
    - Risk limit validation
    - Slippage simulation
    - Commission impact analysis
    - Market condition stress testing
    - Performance metrics tracking
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.validation_period = 30  # days
        self.start_date = None
        self.end_date = None
        
        # Trading data
        self.trades = []
        self.daily_pnl = []
        self.positions = {}
        self.portfolio_value = []
        
        # Initialize components
        self.market_data_manager = None
        self.model_manager = None
        self.strategy_manager = None
        self.order_manager = None
        self.risk_manager = None
    
    async def run_validation(self, validation_days: int = 30) -> PaperTradingReport:
        """Run comprehensive paper trading validation."""
        logger.info(f"Starting {validation_days}-day paper trading validation...")
        
        self.validation_period = validation_days
        self.start_date = datetime.utcnow() - timedelta(days=validation_days)
        self.end_date = datetime.utcnow()
        
        # Initialize validation environment
        await self._setup_validation_environment()
        
        try:
            # Run paper trading simulation
            await self._run_paper_trading_simulation()
            
            # Analyze results
            trading_metrics = self._calculate_trading_metrics()
            risk_metrics = self._calculate_risk_metrics()
            
            # Generate report
            report = self._generate_validation_report(trading_metrics, risk_metrics)
            
            logger.info(f"Paper trading validation completed")
            logger.info(f"Total trades: {trading_metrics.total_trades}")
            logger.info(f"Win rate: {trading_metrics.win_rate:.1%}")
            logger.info(f"Total P&L: ${trading_metrics.total_pnl:.2f}")
            logger.info(f"Sharpe ratio: {trading_metrics.sharpe_ratio:.2f}")
            
            return report
            
        finally:
            # Cleanup validation environment
            await self._cleanup_validation_environment()
    
    async def _setup_validation_environment(self):
        """Set up paper trading validation environment."""
        try:
            # Initialize components in paper trading mode
            paper_config = self.config.copy()
            paper_config.trading.mode = "paper"
            
            self.market_data_manager = MarketDataManager(paper_config)
            self.model_manager = ModelManager(paper_config)
            self.strategy_manager = StrategyManager(paper_config)
            self.order_manager = OrderManager(paper_config)
            self.risk_manager = RiskManager(paper_config)
            
            # Start components
            await self.market_data_manager.start()
            await self.model_manager.start()
            await self.strategy_manager.start()
            await self.order_manager.start()
            await self.risk_manager.start()
            
            # Initialize portfolio
            self.portfolio_value = [100000.0]  # Start with $100k
            self.positions = {}
            self.trades = []
            self.daily_pnl = []
            
            logger.info("Paper trading validation environment setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup validation environment: {e}")
            raise
    
    async def _cleanup_validation_environment(self):
        """Clean up validation environment."""
        try:
            # Stop all components
            if self.risk_manager:
                await self.risk_manager.stop()
            if self.order_manager:
                await self.order_manager.stop()
            if self.strategy_manager:
                await self.strategy_manager.stop()
            if self.model_manager:
                await self.model_manager.stop()
            if self.market_data_manager:
                await self.market_data_manager.stop()
            
            logger.info("Paper trading validation environment cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup validation environment: {e}")
    
    async def _run_paper_trading_simulation(self):
        """Run paper trading simulation for validation period."""
        try:
            # Simulate trading for each day
            current_date = self.start_date
            
            while current_date <= self.end_date:
                logger.info(f"Simulating trading for {current_date.date()}")
                
                # Simulate daily trading
                daily_trades = await self._simulate_daily_trading(current_date)
                self.trades.extend(daily_trades)
                
                # Calculate daily P&L
                daily_pnl = self._calculate_daily_pnl(daily_trades)
                self.daily_pnl.append(daily_pnl)
                
                # Update portfolio value
                current_portfolio_value = self.portfolio_value[-1] + daily_pnl
                self.portfolio_value.append(current_portfolio_value)
                
                # Move to next day
                current_date += timedelta(days=1)
                
                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.1)
            
            logger.info(f"Paper trading simulation completed for {self.validation_period} days")
            
        except Exception as e:
            logger.error(f"Paper trading simulation failed: {e}")
            raise
    
    async def _simulate_daily_trading(self, trading_date: datetime) -> List[Dict[str, Any]]:
        """Simulate trading for a single day."""
        daily_trades = []
        
        try:
            # Get market data for major symbols
            symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX']
            
            for symbol in symbols:
                try:
                    # Get historical market data for the date
                    market_data = await self._get_historical_data(symbol, trading_date)
                    
                    if not market_data:
                        continue
                    
                    # Generate ML prediction
                    prediction = await self.model_manager.predict(symbol, market_data)
                    
                    if not prediction:
                        continue
                    
                    # Generate strategy signals
                    signals = await self.strategy_manager.generate_signals(
                        symbol, market_data, prediction
                    )
                    
                    # Process signals and generate trades
                    for signal in signals:
                        if signal.get('confidence', 0) > 0.7:  # High confidence signals only
                            trade = await self._execute_paper_trade(symbol, signal, market_data)
                            if trade:
                                daily_trades.append(trade)
                
                except Exception as e:
                    logger.warning(f"Failed to process {symbol} for {trading_date}: {e}")
                    continue
            
            return daily_trades
            
        except Exception as e:
            logger.error(f"Daily trading simulation failed for {trading_date}: {e}")
            return []
    
    async def _get_historical_data(self, symbol: str, date: datetime) -> Optional[Dict[str, Any]]:
        """Get historical market data for a specific date."""
        try:
            # This would integrate with historical data provider
            # For now, simulate historical data
            base_price = 150.0  # Base price for simulation
            
            # Add some realistic price movement
            price_change = np.random.normal(0, 0.02)  # 2% daily volatility
            price = base_price * (1 + price_change)
            
            return {
                'symbol': symbol,
                'price': price,
                'volume': np.random.randint(1000000, 10000000),
                'timestamp': date,
                'open': price * 0.99,
                'high': price * 1.02,
                'low': price * 0.98,
                'close': price
            }
            
        except Exception as e:
            logger.error(f"Failed to get historical data for {symbol}: {e}")
            return None
    
    async def _execute_paper_trade(self, symbol: str, signal: Dict[str, Any], 
                                 market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Execute a paper trade based on signal."""
        try:
            action = signal.get('action')
            confidence = signal.get('confidence', 0)
            
            if action not in ['BUY', 'SELL']:
                return None
            
            # Calculate position size based on confidence and risk management
            portfolio_value = self.portfolio_value[-1]
            max_position_value = portfolio_value * 0.05  # 5% max position size
            
            price = market_data.get('price', 0)
            if price <= 0:
                return None
            
            # Calculate quantity
            position_value = max_position_value * confidence
            quantity = int(position_value / price)
            
            if quantity <= 0:
                return None
            
            # Check risk limits
            risk_check = await self.risk_manager.validate_order({
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': price
            })
            
            if not risk_check.get('approved', False):
                return None
            
            # Simulate slippage and commission
            slippage = 0.001  # 0.1% slippage
            commission = 1.0  # $1 commission
            
            execution_price = price * (1 + slippage if action == 'BUY' else 1 - slippage)
            total_cost = quantity * execution_price + commission
            
            # Create trade record
            trade = {
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': execution_price,
                'total_cost': total_cost,
                'commission': commission,
                'slippage': slippage,
                'timestamp': market_data.get('timestamp'),
                'confidence': confidence,
                'signal': signal
            }
            
            # Update positions
            if symbol not in self.positions:
                self.positions[symbol] = {'quantity': 0, 'avg_price': 0}
            
            if action == 'BUY':
                old_quantity = self.positions[symbol]['quantity']
                old_avg_price = self.positions[symbol]['avg_price']
                
                new_quantity = old_quantity + quantity
                new_avg_price = ((old_quantity * old_avg_price) + (quantity * execution_price)) / new_quantity
                
                self.positions[symbol] = {
                    'quantity': new_quantity,
                    'avg_price': new_avg_price
                }
            else:  # SELL
                self.positions[symbol]['quantity'] -= quantity
                if self.positions[symbol]['quantity'] <= 0:
                    del self.positions[symbol]
            
            return trade
            
        except Exception as e:
            logger.error(f"Failed to execute paper trade for {symbol}: {e}")
            return None

    def _calculate_daily_pnl(self, daily_trades: List[Dict[str, Any]]) -> float:
        """Calculate daily P&L from trades."""
        daily_pnl = 0.0

        for trade in daily_trades:
            if trade['action'] == 'SELL':
                # For sells, calculate realized P&L
                symbol = trade['symbol']
                if symbol in self.positions:
                    avg_price = self.positions[symbol]['avg_price']
                    sell_price = trade['price']
                    quantity = trade['quantity']

                    pnl = (sell_price - avg_price) * quantity - trade['commission']
                    daily_pnl += pnl

        return daily_pnl

    def _calculate_trading_metrics(self) -> TradingMetrics:
        """Calculate comprehensive trading performance metrics."""
        if not self.trades:
            return TradingMetrics()

        # Separate winning and losing trades
        winning_trades = []
        losing_trades = []
        total_pnl = 0.0

        for trade in self.trades:
            if trade['action'] == 'SELL':
                # Calculate P&L for this trade
                symbol = trade['symbol']
                # Find corresponding buy trade (simplified)
                buy_trades = [t for t in self.trades if t['symbol'] == symbol and t['action'] == 'BUY']
                if buy_trades:
                    buy_price = buy_trades[-1]['price']  # Use most recent buy
                    sell_price = trade['price']
                    quantity = trade['quantity']

                    pnl = (sell_price - buy_price) * quantity - trade['commission']
                    total_pnl += pnl

                    if pnl > 0:
                        winning_trades.append(pnl)
                    else:
                        losing_trades.append(abs(pnl))

        # Calculate metrics
        total_trades = len(winning_trades) + len(losing_trades)
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0

        average_win = sum(winning_trades) / len(winning_trades) if winning_trades else 0
        average_loss = sum(losing_trades) / len(losing_trades) if losing_trades else 0

        profit_factor = sum(winning_trades) / sum(losing_trades) if losing_trades else float('inf')

        # Calculate drawdown
        portfolio_values = np.array(self.portfolio_value)
        running_max = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - running_max) / running_max
        max_drawdown = abs(min(drawdown)) if len(drawdown) > 0 else 0

        # Calculate Sharpe ratio
        if len(self.daily_pnl) > 1:
            returns = np.array(self.daily_pnl) / self.portfolio_value[0]  # Daily returns
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
            volatility = np.std(returns) * np.sqrt(252)
        else:
            sharpe_ratio = 0
            volatility = 0

        # Calculate consecutive losses
        consecutive_losses = 0
        max_consecutive_losses = 0
        for pnl in self.daily_pnl:
            if pnl < 0:
                consecutive_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
            else:
                consecutive_losses = 0

        return TradingMetrics(
            total_trades=total_trades,
            winning_trades=len(winning_trades),
            losing_trades=len(losing_trades),
            total_pnl=total_pnl,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            average_win=average_win,
            average_loss=average_loss,
            profit_factor=profit_factor,
            max_consecutive_losses=max_consecutive_losses,
            volatility=volatility
        )

    def _calculate_risk_metrics(self) -> RiskMetrics:
        """Calculate risk management metrics."""
        # Calculate position sizes
        max_position_size = 0.0
        for trade in self.trades:
            position_value = trade['quantity'] * trade['price']
            portfolio_value = self.portfolio_value[0]  # Initial portfolio value
            position_size = position_value / portfolio_value
            max_position_size = max(max_position_size, position_size)

        # Calculate daily losses
        daily_losses = [pnl for pnl in self.daily_pnl if pnl < 0]
        max_daily_loss = abs(min(daily_losses)) if daily_losses else 0

        # Calculate VaR and CVaR (simplified)
        if len(self.daily_pnl) > 10:
            returns = np.array(self.daily_pnl) / self.portfolio_value[0]
            var_95 = np.percentile(returns, 5)  # 5th percentile
            cvar_95 = np.mean(returns[returns <= var_95])  # Expected shortfall
        else:
            var_95 = 0
            cvar_95 = 0

        # Count risk violations (simplified)
        risk_limit_breaches = sum(1 for pnl in self.daily_pnl if pnl < -self.portfolio_value[0] * 0.02)
        emergency_stops = sum(1 for pnl in self.daily_pnl if pnl < -self.portfolio_value[0] * 0.05)
        position_size_violations = sum(1 for trade in self.trades
                                     if (trade['quantity'] * trade['price']) / self.portfolio_value[0] > 0.1)

        return RiskMetrics(
            max_position_size=max_position_size,
            max_daily_loss=max_daily_loss,
            max_sector_exposure=0.0,  # Would need sector classification
            var_95=var_95,
            cvar_95=cvar_95,
            risk_limit_breaches=risk_limit_breaches,
            emergency_stops=emergency_stops,
            position_size_violations=position_size_violations
        )

    def _generate_validation_report(self, trading_metrics: TradingMetrics,
                                  risk_metrics: RiskMetrics) -> PaperTradingReport:
        """Generate comprehensive validation report."""
        # Determine validation status
        issues = []
        recommendations = []

        # Check trading performance
        if trading_metrics.win_rate < 0.55:
            issues.append(f"Low win rate: {trading_metrics.win_rate:.1%} (target: 55%+)")

        if trading_metrics.sharpe_ratio < 1.0:
            issues.append(f"Low Sharpe ratio: {trading_metrics.sharpe_ratio:.2f} (target: 1.0+)")

        if trading_metrics.max_drawdown > 0.15:
            issues.append(f"High drawdown: {trading_metrics.max_drawdown:.1%} (target: <15%)")

        # Check risk management
        if risk_metrics.risk_score > 20:
            issues.append(f"High risk score: {risk_metrics.risk_score} (target: <20)")

        if risk_metrics.emergency_stops > 0:
            issues.append(f"Emergency stops triggered: {risk_metrics.emergency_stops}")

        # Generate recommendations
        if trading_metrics.win_rate < 0.6:
            recommendations.append("Consider improving signal quality or entry criteria")

        if trading_metrics.max_drawdown > 0.1:
            recommendations.append("Implement tighter risk controls or position sizing")

        if risk_metrics.position_size_violations > 0:
            recommendations.append("Review position sizing algorithm")

        # Determine overall status
        if len(issues) == 0:
            status = ValidationStatus.PASSED
        elif any("emergency" in issue.lower() for issue in issues):
            status = ValidationStatus.FAILED
        else:
            status = ValidationStatus.WARNING

        return PaperTradingReport(
            validation_period=self.validation_period,
            start_date=self.start_date,
            end_date=self.end_date,
            status=status,
            trading_metrics=trading_metrics,
            risk_metrics=risk_metrics,
            daily_pnl=self.daily_pnl.copy(),
            recommendations=recommendations,
            issues=issues
        )
