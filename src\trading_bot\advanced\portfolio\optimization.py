"""Advanced portfolio optimization algorithms."""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from scipy.optimize import minimize
from scipy import linalg
import cvxpy as cp

from ...core.config import settings
from ...core.logger import get_logger

logger = get_logger(__name__)


class OptimizationMethod(Enum):
    """Portfolio optimization methods."""
    MEAN_VARIANCE = "mean_variance"
    BLACK_LITTERMAN = "black_litterman"
    RISK_PARITY = "risk_parity"
    MIN_VARIANCE = "min_variance"
    MAX_SHARPE = "max_sharpe"
    CVAR = "cvar"
    ROBUST = "robust"


@dataclass
class OptimizationConstraints:
    """Portfolio optimization constraints."""
    min_weight: float = 0.0
    max_weight: float = 1.0
    max_turnover: Optional[float] = None
    sector_limits: Optional[Dict[str, float]] = None
    transaction_costs: Optional[Dict[str, float]] = None
    leverage_limit: float = 1.0


@dataclass
class OptimizationResult:
    """Portfolio optimization result."""
    weights: Dict[str, float]
    expected_return: float
    expected_volatility: float
    sharpe_ratio: float
    max_drawdown: float
    var_95: float
    cvar_95: float
    turnover: float
    transaction_costs: float
    optimization_method: OptimizationMethod


@dataclass
class MarketView:
    """Market view for Black-Litterman model."""
    asset: str
    expected_return: float
    confidence: float  # 0-1 scale


class AdvancedPortfolioOptimizer:
    """Advanced portfolio optimization with multiple algorithms."""
    
    def __init__(self):
        self.risk_free_rate = 0.02  # 2% risk-free rate
        self.confidence_level = 0.95
        
    async def optimize_portfolio(
        self,
        returns_data: pd.DataFrame,
        method: OptimizationMethod = OptimizationMethod.MEAN_VARIANCE,
        constraints: OptimizationConstraints = None,
        market_views: List[MarketView] = None,
        current_weights: Dict[str, float] = None
    ) -> OptimizationResult:
        """
        Optimize portfolio using specified method.
        
        Args:
            returns_data: Historical returns data
            method: Optimization method
            constraints: Portfolio constraints
            market_views: Market views for Black-Litterman
            current_weights: Current portfolio weights
            
        Returns:
            Optimization result
        """
        try:
            if constraints is None:
                constraints = OptimizationConstraints()
            
            # Calculate expected returns and covariance matrix
            expected_returns = self._calculate_expected_returns(returns_data)
            cov_matrix = self._calculate_covariance_matrix(returns_data)
            
            # Apply optimization method
            if method == OptimizationMethod.MEAN_VARIANCE:
                weights = self._mean_variance_optimization(
                    expected_returns, cov_matrix, constraints
                )
            elif method == OptimizationMethod.BLACK_LITTERMAN:
                weights = self._black_litterman_optimization(
                    returns_data, expected_returns, cov_matrix, market_views, constraints
                )
            elif method == OptimizationMethod.RISK_PARITY:
                weights = self._risk_parity_optimization(cov_matrix, constraints)
            elif method == OptimizationMethod.MIN_VARIANCE:
                weights = self._min_variance_optimization(cov_matrix, constraints)
            elif method == OptimizationMethod.MAX_SHARPE:
                weights = self._max_sharpe_optimization(
                    expected_returns, cov_matrix, constraints
                )
            elif method == OptimizationMethod.CVAR:
                weights = self._cvar_optimization(
                    returns_data, expected_returns, constraints
                )
            else:
                raise ValueError(f"Unsupported optimization method: {method}")
            
            # Calculate portfolio metrics
            portfolio_return = np.dot(weights, expected_returns)
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_vol
            
            # Calculate risk metrics
            portfolio_returns = returns_data.dot(weights)
            var_95 = np.percentile(portfolio_returns, 5)
            cvar_95 = portfolio_returns[portfolio_returns <= var_95].mean()
            max_drawdown = self._calculate_max_drawdown(portfolio_returns)
            
            # Calculate turnover and transaction costs
            turnover = 0.0
            transaction_costs = 0.0
            
            if current_weights:
                current_weights_array = np.array([current_weights.get(asset, 0) 
                                                for asset in returns_data.columns])
                turnover = np.sum(np.abs(weights - current_weights_array))
                
                if constraints.transaction_costs:
                    transaction_costs = sum(
                        abs(weights[i] - current_weights.get(asset, 0)) * 
                        constraints.transaction_costs.get(asset, 0.001)
                        for i, asset in enumerate(returns_data.columns)
                    )
            
            # Convert weights to dictionary
            weights_dict = {asset: weight for asset, weight in 
                          zip(returns_data.columns, weights)}
            
            return OptimizationResult(
                weights=weights_dict,
                expected_return=portfolio_return,
                expected_volatility=portfolio_vol,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                var_95=var_95,
                cvar_95=cvar_95,
                turnover=turnover,
                transaction_costs=transaction_costs,
                optimization_method=method
            )
            
        except Exception as e:
            logger.error(f"Error optimizing portfolio: {e}")
            return self._empty_result(method)
    
    def _calculate_expected_returns(self, returns_data: pd.DataFrame) -> np.ndarray:
        """Calculate expected returns using historical mean."""
        return returns_data.mean().values * 252  # Annualized
    
    def _calculate_covariance_matrix(self, returns_data: pd.DataFrame) -> np.ndarray:
        """Calculate covariance matrix."""
        return returns_data.cov().values * 252  # Annualized
    
    def _mean_variance_optimization(
        self,
        expected_returns: np.ndarray,
        cov_matrix: np.ndarray,
        constraints: OptimizationConstraints
    ) -> np.ndarray:
        """Mean-variance optimization (Markowitz)."""
        try:
            n_assets = len(expected_returns)
            
            # Define optimization variables
            weights = cp.Variable(n_assets)
            
            # Objective: maximize utility (return - risk penalty)
            risk_aversion = 1.0  # Risk aversion parameter
            portfolio_return = expected_returns.T @ weights
            portfolio_risk = cp.quad_form(weights, cov_matrix)
            utility = portfolio_return - 0.5 * risk_aversion * portfolio_risk
            
            # Constraints
            constraints_list = [
                cp.sum(weights) == 1,  # Weights sum to 1
                weights >= constraints.min_weight,
                weights <= constraints.max_weight
            ]
            
            # Solve optimization
            problem = cp.Problem(cp.Maximize(utility), constraints_list)
            problem.solve()
            
            if weights.value is not None:
                return weights.value
            else:
                # Fallback to equal weights
                return np.ones(n_assets) / n_assets
                
        except Exception as e:
            logger.error(f"Error in mean-variance optimization: {e}")
            return np.ones(len(expected_returns)) / len(expected_returns)
    
    def _black_litterman_optimization(
        self,
        returns_data: pd.DataFrame,
        expected_returns: np.ndarray,
        cov_matrix: np.ndarray,
        market_views: List[MarketView],
        constraints: OptimizationConstraints
    ) -> np.ndarray:
        """Black-Litterman optimization with market views."""
        try:
            if not market_views:
                # No views, fall back to market cap weights
                return np.ones(len(expected_returns)) / len(expected_returns)
            
            # Market capitalization weights (simplified - equal weights)
            market_weights = np.ones(len(expected_returns)) / len(expected_returns)
            
            # Risk aversion parameter
            risk_aversion = 3.0
            
            # Implied equilibrium returns
            pi = risk_aversion * np.dot(cov_matrix, market_weights)
            
            # Views matrix and uncertainty
            n_assets = len(expected_returns)
            n_views = len(market_views)
            
            P = np.zeros((n_views, n_assets))  # Picking matrix
            Q = np.zeros(n_views)  # Views vector
            
            for i, view in enumerate(market_views):
                asset_idx = returns_data.columns.get_loc(view.asset)
                P[i, asset_idx] = 1
                Q[i] = view.expected_return
            
            # Uncertainty matrix (diagonal)
            omega = np.diag([1 / view.confidence for view in market_views])
            
            # Black-Litterman formula
            tau = 0.025  # Scaling factor
            
            M1 = linalg.inv(tau * cov_matrix)
            M2 = np.dot(P.T, np.dot(linalg.inv(omega), P))
            M3 = np.dot(linalg.inv(tau * cov_matrix), pi)
            M4 = np.dot(P.T, np.dot(linalg.inv(omega), Q))
            
            mu_bl = np.dot(linalg.inv(M1 + M2), M3 + M4)
            cov_bl = linalg.inv(M1 + M2)
            
            # Optimize with Black-Litterman inputs
            return self._mean_variance_optimization(mu_bl, cov_bl, constraints)
            
        except Exception as e:
            logger.error(f"Error in Black-Litterman optimization: {e}")
            return np.ones(len(expected_returns)) / len(expected_returns)
    
    def _risk_parity_optimization(
        self,
        cov_matrix: np.ndarray,
        constraints: OptimizationConstraints
    ) -> np.ndarray:
        """Risk parity optimization (equal risk contribution)."""
        try:
            n_assets = len(cov_matrix)
            
            def risk_budget_objective(weights):
                """Objective function for risk parity."""
                weights = np.array(weights)
                portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
                
                # Risk contributions
                marginal_contrib = np.dot(cov_matrix, weights) / portfolio_vol
                contrib = weights * marginal_contrib
                
                # Target equal risk contributions
                target_contrib = np.ones(n_assets) / n_assets
                
                # Sum of squared deviations from target
                return np.sum((contrib - target_contrib) ** 2)
            
            # Constraints
            cons = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # Weights sum to 1
            ]
            
            # Bounds
            bounds = [(constraints.min_weight, constraints.max_weight) 
                     for _ in range(n_assets)]
            
            # Initial guess (equal weights)
            x0 = np.ones(n_assets) / n_assets
            
            # Optimize
            result = minimize(
                risk_budget_objective,
                x0,
                method='SLSQP',
                bounds=bounds,
                constraints=cons
            )
            
            if result.success:
                return result.x
            else:
                return np.ones(n_assets) / n_assets
                
        except Exception as e:
            logger.error(f"Error in risk parity optimization: {e}")
            return np.ones(len(cov_matrix)) / len(cov_matrix)
    
    def _min_variance_optimization(
        self,
        cov_matrix: np.ndarray,
        constraints: OptimizationConstraints
    ) -> np.ndarray:
        """Minimum variance optimization."""
        try:
            n_assets = len(cov_matrix)
            
            # Define optimization variables
            weights = cp.Variable(n_assets)
            
            # Objective: minimize portfolio variance
            portfolio_variance = cp.quad_form(weights, cov_matrix)
            
            # Constraints
            constraints_list = [
                cp.sum(weights) == 1,
                weights >= constraints.min_weight,
                weights <= constraints.max_weight
            ]
            
            # Solve optimization
            problem = cp.Problem(cp.Minimize(portfolio_variance), constraints_list)
            problem.solve()
            
            if weights.value is not None:
                return weights.value
            else:
                return np.ones(n_assets) / n_assets
                
        except Exception as e:
            logger.error(f"Error in minimum variance optimization: {e}")
            return np.ones(len(cov_matrix)) / len(cov_matrix)
    
    def _max_sharpe_optimization(
        self,
        expected_returns: np.ndarray,
        cov_matrix: np.ndarray,
        constraints: OptimizationConstraints
    ) -> np.ndarray:
        """Maximum Sharpe ratio optimization."""
        try:
            n_assets = len(expected_returns)
            
            # Define optimization variables
            weights = cp.Variable(n_assets)
            
            # Objective: maximize Sharpe ratio
            # We maximize (return - risk_free_rate) / volatility
            excess_returns = expected_returns - self.risk_free_rate
            portfolio_return = excess_returns.T @ weights
            portfolio_risk = cp.quad_form(weights, cov_matrix)
            
            # Constraints
            constraints_list = [
                cp.sum(weights) == 1,
                weights >= constraints.min_weight,
                weights <= constraints.max_weight,
                portfolio_risk <= 1  # Normalize risk
            ]
            
            # Solve optimization (maximize return for unit risk)
            problem = cp.Problem(cp.Maximize(portfolio_return), constraints_list)
            problem.solve()
            
            if weights.value is not None:
                return weights.value
            else:
                return np.ones(n_assets) / n_assets
                
        except Exception as e:
            logger.error(f"Error in max Sharpe optimization: {e}")
            return np.ones(len(expected_returns)) / len(expected_returns)
    
    def _cvar_optimization(
        self,
        returns_data: pd.DataFrame,
        expected_returns: np.ndarray,
        constraints: OptimizationConstraints
    ) -> np.ndarray:
        """Conditional Value at Risk (CVaR) optimization."""
        try:
            n_assets = len(expected_returns)
            n_scenarios = len(returns_data)
            
            # Define optimization variables
            weights = cp.Variable(n_assets)
            alpha = cp.Variable()  # VaR
            u = cp.Variable(n_scenarios)  # Auxiliary variables
            
            # Portfolio returns for each scenario
            portfolio_returns = returns_data.values @ weights
            
            # CVaR calculation
            cvar = alpha - (1 / (1 - self.confidence_level)) * cp.sum(u) / n_scenarios
            
            # Objective: maximize expected return - lambda * CVaR
            lambda_cvar = 1.0  # Risk aversion parameter
            objective = expected_returns.T @ weights + lambda_cvar * cvar
            
            # Constraints
            constraints_list = [
                cp.sum(weights) == 1,
                weights >= constraints.min_weight,
                weights <= constraints.max_weight,
                u >= 0,
                u >= alpha + portfolio_returns
            ]
            
            # Solve optimization
            problem = cp.Problem(cp.Maximize(objective), constraints_list)
            problem.solve()
            
            if weights.value is not None:
                return weights.value
            else:
                return np.ones(n_assets) / n_assets
                
        except Exception as e:
            logger.error(f"Error in CVaR optimization: {e}")
            return np.ones(len(expected_returns)) / len(expected_returns)
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """Calculate maximum drawdown."""
        try:
            cumulative = (1 + returns).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            return drawdown.min()
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def _empty_result(self, method: OptimizationMethod) -> OptimizationResult:
        """Return empty optimization result."""
        return OptimizationResult(
            weights={},
            expected_return=0.0,
            expected_volatility=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            var_95=0.0,
            cvar_95=0.0,
            turnover=0.0,
            transaction_costs=0.0,
            optimization_method=method
        )
