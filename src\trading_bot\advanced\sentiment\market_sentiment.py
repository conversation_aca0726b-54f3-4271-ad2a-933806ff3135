"""Market sentiment aggregation and analysis."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import pandas as pd
import numpy as np

from ...core.config import settings
from ...core.logger import get_logger
from .news_analyzer import NewsAnalyzer
from .social_media import SocialMediaAnalyzer
from .earnings_analyzer import EarningsAnalyzer

logger = get_logger(__name__)


@dataclass
class MarketSentimentScore:
    """Market sentiment score data structure."""
    overall_sentiment: float
    confidence: float
    news_sentiment: float
    social_sentiment: float
    earnings_sentiment: float
    fear_greed_index: float
    volatility_sentiment: float
    sector_sentiment: Dict[str, float]
    timestamp: datetime


@dataclass
class SentimentSignal:
    """Trading signal based on sentiment analysis."""
    symbol: str
    signal_strength: float  # -1 to 1
    signal_type: str  # 'bullish', 'bearish', 'neutral'
    confidence: float
    components: Dict[str, float]
    reasoning: List[str]
    timestamp: datetime


class MarketSentimentAggregator:
    """Aggregate sentiment from multiple sources for trading signals."""
    
    def __init__(self):
        self.news_analyzer = NewsAnalyzer()
        self.social_analyzer = SocialMediaAnalyzer()
        self.earnings_analyzer = EarningsAnalyzer()
        
        # Sentiment component weights
        self.weights = {
            'news': 0.35,
            'social': 0.25,
            'earnings': 0.30,
            'technical': 0.10
        }
        
        # Sector mappings
        self.sector_symbols = self._load_sector_mappings()
        
    async def get_market_sentiment(
        self,
        symbols: List[str] = None,
        include_overall: bool = True
    ) -> Dict[str, Any]:
        """
        Get comprehensive market sentiment analysis.
        
        Args:
            symbols: Specific symbols to analyze (None for market-wide)
            include_overall: Include overall market sentiment
            
        Returns:
            Dictionary with sentiment analysis results
        """
        try:
            results = {
                'timestamp': datetime.utcnow(),
                'symbols': {},
                'overall': None,
                'sectors': {}
            }
            
            # Default symbols for market analysis
            if symbols is None:
                symbols = ['SPY', 'QQQ', 'IWM', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
            
            # Gather sentiment from all sources
            sentiment_tasks = [
                self._get_news_sentiment(symbols),
                self._get_social_sentiment(symbols),
                self._get_earnings_sentiment(symbols)
            ]
            
            news_sentiment, social_sentiment, earnings_sentiment = await asyncio.gather(
                *sentiment_tasks, return_exceptions=True
            )
            
            # Handle exceptions
            if isinstance(news_sentiment, Exception):
                logger.error(f"News sentiment error: {news_sentiment}")
                news_sentiment = {}
            
            if isinstance(social_sentiment, Exception):
                logger.error(f"Social sentiment error: {social_sentiment}")
                social_sentiment = {}
            
            if isinstance(earnings_sentiment, Exception):
                logger.error(f"Earnings sentiment error: {earnings_sentiment}")
                earnings_sentiment = {}
            
            # Aggregate sentiment for each symbol
            for symbol in symbols:
                symbol_sentiment = self._aggregate_symbol_sentiment(
                    symbol,
                    news_sentiment.get(symbol, {}),
                    social_sentiment.get(symbol, {}),
                    earnings_sentiment.get(symbol, {})
                )
                results['symbols'][symbol] = symbol_sentiment
            
            # Calculate overall market sentiment
            if include_overall:
                results['overall'] = self._calculate_overall_sentiment(results['symbols'])
            
            # Calculate sector sentiment
            results['sectors'] = self._calculate_sector_sentiment(results['symbols'])
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting market sentiment: {e}")
            return self._empty_sentiment_result()
    
    async def generate_sentiment_signals(
        self,
        symbols: List[str],
        min_confidence: float = 0.6
    ) -> List[SentimentSignal]:
        """
        Generate trading signals based on sentiment analysis.
        
        Args:
            symbols: Symbols to analyze
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of sentiment-based trading signals
        """
        try:
            # Get sentiment data
            sentiment_data = await self.get_market_sentiment(symbols, include_overall=False)
            
            signals = []
            
            for symbol, data in sentiment_data['symbols'].items():
                signal = self._generate_symbol_signal(symbol, data)
                
                if signal and signal.confidence >= min_confidence:
                    signals.append(signal)
            
            # Sort by signal strength
            signals.sort(key=lambda x: abs(x.signal_strength), reverse=True)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating sentiment signals: {e}")
            return []
    
    async def _get_news_sentiment(self, symbols: List[str]) -> Dict[str, Any]:
        """Get news sentiment for symbols."""
        try:
            return await self.news_analyzer.analyze_news_sentiment(symbols, hours_back=24)
        except Exception as e:
            logger.error(f"Error getting news sentiment: {e}")
            return {}
    
    async def _get_social_sentiment(self, symbols: List[str]) -> Dict[str, Any]:
        """Get social media sentiment for symbols."""
        try:
            return await self.social_analyzer.analyze_social_sentiment(symbols, hours_back=24)
        except Exception as e:
            logger.error(f"Error getting social sentiment: {e}")
            return {}
    
    async def _get_earnings_sentiment(self, symbols: List[str]) -> Dict[str, Any]:
        """Get earnings sentiment for symbols."""
        try:
            return await self.earnings_analyzer.analyze_earnings_sentiment(symbols, quarters_back=2)
        except Exception as e:
            logger.error(f"Error getting earnings sentiment: {e}")
            return {}
    
    def _aggregate_symbol_sentiment(
        self,
        symbol: str,
        news_data: Dict[str, Any],
        social_data: Dict[str, Any],
        earnings_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Aggregate sentiment for a single symbol."""
        # Extract sentiment scores
        news_score = news_data.get('sentiment_score', 0.0) if news_data else 0.0
        news_confidence = news_data.get('confidence', 0.0) if news_data else 0.0
        
        social_score = social_data.sentiment_score if hasattr(social_data, 'sentiment_score') else 0.0
        social_confidence = social_data.confidence if hasattr(social_data, 'confidence') else 0.0
        
        earnings_score = earnings_data.overall_sentiment if hasattr(earnings_data, 'overall_sentiment') else 0.0
        earnings_confidence = 0.8 if earnings_score != 0.0 else 0.0  # High confidence for earnings
        
        # Calculate weighted sentiment
        total_weight = 0
        weighted_sentiment = 0
        
        components = {
            'news': news_score,
            'social': social_score,
            'earnings': earnings_score
        }
        
        confidences = {
            'news': news_confidence,
            'social': social_confidence,
            'earnings': earnings_confidence
        }
        
        for component, score in components.items():
            weight = self.weights[component] * confidences[component]
            weighted_sentiment += score * weight
            total_weight += weight
        
        # Final sentiment and confidence
        final_sentiment = weighted_sentiment / total_weight if total_weight > 0 else 0.0
        final_confidence = total_weight / sum(self.weights[c] for c in components.keys())
        
        return {
            'sentiment_score': final_sentiment,
            'confidence': final_confidence,
            'components': components,
            'component_confidences': confidences,
            'news_volume': news_data.get('article_count', 0) if news_data else 0,
            'social_volume': social_data.volume if hasattr(social_data, 'volume') else 0,
            'social_buzz': social_data.buzz_score if hasattr(social_data, 'buzz_score') else 0.0
        }
    
    def _calculate_overall_sentiment(self, symbol_sentiments: Dict[str, Any]) -> MarketSentimentScore:
        """Calculate overall market sentiment."""
        if not symbol_sentiments:
            return self._empty_market_sentiment()
        
        # Weight by market cap (simplified - using equal weights for now)
        total_sentiment = 0
        total_confidence = 0
        count = 0
        
        news_sentiments = []
        social_sentiments = []
        earnings_sentiments = []
        
        for symbol, data in symbol_sentiments.items():
            if data['confidence'] > 0.3:  # Minimum confidence threshold
                total_sentiment += data['sentiment_score'] * data['confidence']
                total_confidence += data['confidence']
                count += 1
                
                news_sentiments.append(data['components']['news'])
                social_sentiments.append(data['components']['social'])
                earnings_sentiments.append(data['components']['earnings'])
        
        if count == 0:
            return self._empty_market_sentiment()
        
        overall_sentiment = total_sentiment / total_confidence
        overall_confidence = total_confidence / count
        
        # Calculate component averages
        avg_news = np.mean([s for s in news_sentiments if s != 0]) if news_sentiments else 0
        avg_social = np.mean([s for s in social_sentiments if s != 0]) if social_sentiments else 0
        avg_earnings = np.mean([s for s in earnings_sentiments if s != 0]) if earnings_sentiments else 0
        
        # Calculate fear & greed index (simplified)
        fear_greed = self._calculate_fear_greed_index(overall_sentiment, overall_confidence)
        
        return MarketSentimentScore(
            overall_sentiment=overall_sentiment,
            confidence=overall_confidence,
            news_sentiment=avg_news,
            social_sentiment=avg_social,
            earnings_sentiment=avg_earnings,
            fear_greed_index=fear_greed,
            volatility_sentiment=0.0,  # Would need VIX data
            sector_sentiment={},
            timestamp=datetime.utcnow()
        )
    
    def _calculate_sector_sentiment(self, symbol_sentiments: Dict[str, Any]) -> Dict[str, float]:
        """Calculate sentiment by sector."""
        sector_sentiment = {}
        
        for symbol, data in symbol_sentiments.items():
            sector = self._get_symbol_sector(symbol)
            if sector:
                if sector not in sector_sentiment:
                    sector_sentiment[sector] = []
                
                if data['confidence'] > 0.3:
                    sector_sentiment[sector].append(data['sentiment_score'])
        
        # Average sentiment by sector
        for sector, sentiments in sector_sentiment.items():
            sector_sentiment[sector] = np.mean(sentiments) if sentiments else 0.0
        
        return sector_sentiment
    
    def _generate_symbol_signal(self, symbol: str, sentiment_data: Dict[str, Any]) -> Optional[SentimentSignal]:
        """Generate trading signal for a symbol based on sentiment."""
        sentiment_score = sentiment_data['sentiment_score']
        confidence = sentiment_data['confidence']
        
        # Minimum confidence threshold
        if confidence < 0.4:
            return None
        
        # Determine signal type and strength
        if sentiment_score > 0.3:
            signal_type = 'bullish'
            signal_strength = min(sentiment_score, 1.0)
        elif sentiment_score < -0.3:
            signal_type = 'bearish'
            signal_strength = max(sentiment_score, -1.0)
        else:
            signal_type = 'neutral'
            signal_strength = 0.0
        
        # Generate reasoning
        reasoning = []
        
        if sentiment_data['components']['news'] > 0.2:
            reasoning.append("Positive news sentiment detected")
        elif sentiment_data['components']['news'] < -0.2:
            reasoning.append("Negative news sentiment detected")
        
        if sentiment_data['social_buzz'] > 0.5:
            reasoning.append("High social media buzz")
        
        if sentiment_data['components']['earnings'] > 0.2:
            reasoning.append("Positive earnings sentiment")
        elif sentiment_data['components']['earnings'] < -0.2:
            reasoning.append("Negative earnings sentiment")
        
        return SentimentSignal(
            symbol=symbol,
            signal_strength=signal_strength,
            signal_type=signal_type,
            confidence=confidence,
            components=sentiment_data['components'],
            reasoning=reasoning,
            timestamp=datetime.utcnow()
        )
    
    def _calculate_fear_greed_index(self, sentiment: float, confidence: float) -> float:
        """Calculate fear & greed index (0-100 scale)."""
        # Convert sentiment (-1 to 1) to fear/greed scale (0 to 100)
        # 0 = Extreme Fear, 50 = Neutral, 100 = Extreme Greed
        
        base_index = (sentiment + 1) * 50  # Convert to 0-100 scale
        
        # Adjust based on confidence
        if confidence < 0.5:
            # Low confidence pushes toward neutral
            base_index = base_index * 0.7 + 50 * 0.3
        
        return max(0, min(100, base_index))
    
    def _get_symbol_sector(self, symbol: str) -> Optional[str]:
        """Get sector for a symbol."""
        for sector, symbols in self.sector_symbols.items():
            if symbol in symbols:
                return sector
        return None
    
    def _load_sector_mappings(self) -> Dict[str, List[str]]:
        """Load sector to symbol mappings."""
        return {
            'Technology': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META'],
            'Healthcare': ['JNJ', 'PFE', 'UNH', 'ABBV', 'TMO'],
            'Financial': ['JPM', 'BAC', 'WFC', 'GS', 'MS'],
            'Energy': ['XOM', 'CVX', 'COP', 'EOG'],
            'Consumer': ['WMT', 'PG', 'KO', 'PEP', 'MCD'],
            'Industrial': ['BA', 'CAT', 'GE', 'MMM', 'HON']
        }
    
    def _empty_sentiment_result(self) -> Dict[str, Any]:
        """Return empty sentiment result."""
        return {
            'timestamp': datetime.utcnow(),
            'symbols': {},
            'overall': self._empty_market_sentiment(),
            'sectors': {}
        }
    
    def _empty_market_sentiment(self) -> MarketSentimentScore:
        """Return empty market sentiment."""
        return MarketSentimentScore(
            overall_sentiment=0.0,
            confidence=0.0,
            news_sentiment=0.0,
            social_sentiment=0.0,
            earnings_sentiment=0.0,
            fear_greed_index=50.0,
            volatility_sentiment=0.0,
            sector_sentiment={},
            timestamp=datetime.utcnow()
        )
