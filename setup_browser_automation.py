#!/usr/bin/env python3
"""
Setup script for Webull browser automation with production-grade integration

This script sets up the complete browser automation environment with all
enterprise-grade components including security, monitoring, compliance, etc.
"""

import os
import subprocess
import sys
import platform
import asyncio
from pathlib import Path

def install_requirements():
    """Install required packages for browser automation"""
    print("🔧 Installing browser automation packages...")
    
    packages = [
        # Browser automation
        "selenium>=4.15.0",
        "undetected-chromedriver>=3.5.0",
        
        # Image processing and OCR
        "pillow>=10.0.0",
        "opencv-python>=4.8.0",
        "pytesseract>=0.3.10",
        
        # Data processing
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        
        # Additional ML and monitoring
        "scikit-learn>=1.3.0",
        "psutil>=5.9.0",
        "aiofiles>=23.0.0",
        "aiohttp>=3.8.0",
        
        # Security and encryption
        "cryptography>=41.0.0",
        "keyring>=24.0.0",
        
        # Configuration management
        "pyyaml>=6.0.0",
        "python-dotenv>=1.0.0",
        
        # Monitoring and metrics
        "prometheus-client>=0.17.0",
        "grafana-api>=1.0.3"
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
    
    print("✅ Python packages installed successfully")

def install_chrome_driver():
    """Install and configure ChromeDriver"""
    print("\n🌐 Setting up ChromeDriver...")
    
    # undetected-chromedriver handles this automatically
    print("✅ ChromeDriver will be installed automatically by undetected-chromedriver")
    
    # Check if Chrome is installed
    system = platform.system()
    chrome_installed = False
    
    if system == "Windows":
        chrome_paths = [
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
        ]
        chrome_installed = any(os.path.exists(path) for path in chrome_paths)
    elif system == "Darwin":  # macOS
        chrome_installed = os.path.exists("/Applications/Google Chrome.app")
    elif system == "Linux":
        try:
            subprocess.run(["which", "google-chrome"], check=True, capture_output=True)
            chrome_installed = True
        except subprocess.CalledProcessError:
            chrome_installed = False
    
    if not chrome_installed:
        print("⚠️  Google Chrome not detected. Please install Chrome:")
        print("   https://www.google.com/chrome/")
    else:
        print("✅ Google Chrome detected")

def install_tesseract():
    """Install Tesseract OCR for text extraction"""
    print("\n🔍 Installing Tesseract OCR...")
    
    system = platform.system()
    
    if system == "Windows":
        print("Please install Tesseract OCR from:")
        print("https://github.com/UB-Mannheim/tesseract/wiki")
        print("Add Tesseract to your PATH environment variable")
    elif system == "Darwin":  # macOS
        try:
            subprocess.run(["brew", "install", "tesseract"], check=True)
            print("✅ Tesseract installed via Homebrew")
        except subprocess.CalledProcessError:
            print("⚠️  Please install Homebrew first: https://brew.sh/")
    elif system == "Linux":
        try:
            subprocess.run(["sudo", "apt-get", "update"], check=True)
            subprocess.run(["sudo", "apt-get", "install", "-y", "tesseract-ocr"], check=True)
            print("✅ Tesseract installed via apt")
        except subprocess.CalledProcessError:
            print("⚠️  Please install Tesseract manually")

def create_directories():
    """Create necessary directories for browser automation"""
    print("\n📁 Creating directories...")
    
    directories = [
        "screenshots",
        "logs/browser",
        "data/browser_cache",
        "data/cookies",
        "models/browser_ml",
        "config/browser",
        "monitoring/browser_metrics"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created: {directory}")

def setup_browser_config():
    """Create browser automation configuration"""
    print("\n⚙️  Setting up browser configuration...")
    
    config_content = """# Browser Automation Configuration
browser:
  # Browser settings
  headless: false  # Set to true for headless mode
  window_size: "1920,1080"
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  
  # Stealth settings
  disable_automation_flags: true
  disable_dev_shm_usage: true
  no_sandbox: false  # Set to true in Docker
  
  # Timeouts
  page_load_timeout: 30
  implicit_wait: 10
  explicit_wait: 10
  
  # Screenshots
  screenshot_on_error: true
  screenshot_dir: "./screenshots"
  
  # Cookies and session
  cookies_file: "./data/cookies/webull_cookies.pkl"
  session_timeout: 3600  # 1 hour

webull:
  # URLs
  base_url: "https://app.webull.com"
  login_url: "https://app.webull.com/login"
  paper_trading_url: "https://app.webull.com/paper"
  
  # Trading settings
  paper_trading: true  # ALWAYS START WITH TRUE!
  default_order_type: "market"
  
  # Watchlist
  watchlist:
    - "AAPL"
    - "MSFT" 
    - "GOOGL"
    - "TSLA"
    - "NVDA"
  
  # Risk management
  max_position_size: 100  # shares
  stop_loss_percent: -2.0
  take_profit_percent: 5.0
  
  # Trading loop
  loop_interval: 60  # seconds
  min_confidence: 0.7

# Integration with production components
integration:
  # Enhanced security
  use_enhanced_secrets: true
  mfa_required: true
  
  # Monitoring
  enable_metrics: true
  prometheus_port: 9090
  
  # Compliance
  enable_compliance_checks: true
  pdt_monitoring: true
  wash_sale_detection: true
  
  # Dead man's switch
  enable_dead_mans_switch: true
  heartbeat_interval: 300  # 5 minutes
  
  # Market microstructure
  enable_microstructure_analysis: true
  order_flow_analysis: true
  
  # Performance optimization
  enable_performance_optimization: true
  optimization_level: "high"
"""
    
    config_file = Path("config/browser/browser_config.yaml")
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print(f"✅ Browser configuration created: {config_file}")

def create_browser_launcher():
    """Create enhanced browser launcher script"""
    print("\n🚀 Creating browser launcher...")
    
    launcher_content = '''#!/usr/bin/env python3
"""
Enhanced Webull Browser Bot Launcher

This script launches the browser automation with all production-grade integrations.
"""

import asyncio
import sys
import yaml
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.trading_bot.automation.enhanced_webull_browser_bot import EnhancedWebullBrowserBot
from src.trading_bot.config.production_config import get_config_manager, Environment

async def main():
    """Main launcher function"""
    print("🤖 Enhanced Webull Browser Bot")
    print("=" * 40)
    
    # Load configuration
    config_manager = get_config_manager(Environment.DEVELOPMENT)
    
    # Load browser-specific config
    browser_config_file = Path("config/browser/browser_config.yaml")
    if browser_config_file.exists():
        with open(browser_config_file, 'r') as f:
            browser_config = yaml.safe_load(f)
    else:
        print("⚠️  Browser config not found, using defaults")
        browser_config = {}
    
    # Create enhanced bot
    bot = EnhancedWebullBrowserBot(config_manager, browser_config)
    
    try:
        # Initialize all systems
        await bot.initialize_all_systems()
        
        # Get credentials
        print("\\n🔐 Login to Webull")
        username = input("Username/Email: ")
        password = input("Password: ")
        
        # Login
        await bot.login(username, password)
        
        # Start trading
        print("\\n🚀 Starting enhanced trading loop...")
        await bot.run_enhanced_trading_loop()
        
    except KeyboardInterrupt:
        print("\\n⏹️  Stopping bot...")
    except Exception as e:
        print(f"\\n❌ Error: {e}")
    finally:
        await bot.cleanup_all_systems()

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    launcher_file = Path("launch_browser_bot.py")
    with open(launcher_file, 'w') as f:
        f.write(launcher_content)
    
    # Make executable on Unix systems
    if platform.system() != "Windows":
        os.chmod(launcher_file, 0o755)
    
    print(f"✅ Browser launcher created: {launcher_file}")

def setup_monitoring_dashboard():
    """Setup browser-specific monitoring dashboard"""
    print("\n📊 Setting up browser monitoring...")
    
    dashboard_config = """{
  "dashboard": {
    "title": "Browser Automation Dashboard",
    "panels": [
      {
        "title": "Browser Status",
        "type": "stat",
        "targets": [{"expr": "browser_automation_status"}]
      },
      {
        "title": "Login Success Rate",
        "type": "stat", 
        "targets": [{"expr": "rate(browser_login_success_total[5m])"}]
      },
      {
        "title": "Order Execution Rate",
        "type": "graph",
        "targets": [{"expr": "rate(browser_orders_total[5m])"}]
      },
      {
        "title": "Screenshot Count",
        "type": "stat",
        "targets": [{"expr": "browser_screenshots_total"}]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [{"expr": "rate(browser_errors_total[5m])"}]
      }
    ]
  }
}"""
    
    dashboard_file = Path("monitoring/grafana/dashboards/browser_automation_dashboard.json")
    dashboard_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(dashboard_file, 'w') as f:
        f.write(dashboard_config)
    
    print(f"✅ Browser monitoring dashboard: {dashboard_file}")

def create_test_script():
    """Create browser automation test script"""
    print("\n🧪 Creating test script...")
    
    test_content = '''#!/usr/bin/env python3
"""
Browser Automation Test Script

Tests all browser automation functionality without real trading.
"""

import asyncio
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.trading_bot.automation.enhanced_webull_browser_bot import EnhancedWebullBrowserBot

async def test_browser_automation():
    """Test browser automation functionality"""
    print("🧪 Testing Browser Automation")
    print("=" * 30)
    
    config = {
        'browser': {
            'headless': False,
            'screenshot_on_error': True
        },
        'webull': {
            'paper_trading': True,
            'watchlist': ['AAPL']
        },
        'integration': {
            'enable_metrics': False,  # Disable for testing
            'enable_compliance_checks': False
        }
    }
    
    bot = EnhancedWebullBrowserBot(None, config)
    
    try:
        print("1. Initializing browser...")
        await bot.initialize()
        
        print("2. Testing navigation...")
        await bot.test_navigation()
        
        print("3. Testing market data extraction...")
        await bot.test_market_data_extraction()
        
        print("4. Testing order form...")
        await bot.test_order_form()
        
        print("\\n✅ All tests passed!")
        
    except Exception as e:
        print(f"\\n❌ Test failed: {e}")
    finally:
        await bot.cleanup()

if __name__ == "__main__":
    asyncio.run(test_browser_automation())
'''
    
    test_file = Path("test_browser_automation.py")
    with open(test_file, 'w') as f:
        f.write(test_content)
    
    if platform.system() != "Windows":
        os.chmod(test_file, 0o755)
    
    print(f"✅ Test script created: {test_file}")

def main():
    """Main setup function"""
    print("🤖 Webull Browser Automation Setup with Production Integration")
    print("=" * 70)
    
    try:
        install_requirements()
        install_chrome_driver()
        install_tesseract()
        create_directories()
        setup_browser_config()
        create_browser_launcher()
        setup_monitoring_dashboard()
        create_test_script()
        
        print("\n✅ Browser automation setup complete!")
        print("\n📋 Next steps:")
        print("1. Ensure Google Chrome is installed")
        print("2. Configure your Webull credentials in the enhanced secrets manager")
        print("3. Test the setup: python test_browser_automation.py")
        print("4. Run the bot: python launch_browser_bot.py")
        print("\n⚠️  IMPORTANT:")
        print("- Always start with paper_trading: true")
        print("- Test thoroughly before using real money")
        print("- Monitor all systems during operation")
        
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
