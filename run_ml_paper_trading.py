#!/usr/bin/env python3
"""
Main script to run ML-enhanced paper trading
Combines volatility hunting with ML predictions and paper trading
"""
import asyncio
import sys
import os
import joblib
import pandas as pd
import numpy as np
from datetime import datetime

# Import our standalone modules
from test_volatility_hunter import VolatilityHunter, VolatilityOpportunity
from test_paper_trading_standalone import PaperTradingEngine
from test_ml_training import VolatilityModelTrainer

class MLEnhancedVolatilityHunter(VolatilityHunter):
    """
    Enhanced volatility hunter with ML predictions
    """
    def __init__(self):
        super().__init__()
        self.models = {}
        self.feature_names = []
        self.model_dir = "models/volatility"
        self._load_models()
    
    def _load_models(self):
        """Load trained ML models"""
        try:
            if os.path.exists(f"{self.model_dir}/random_forest.pkl"):
                self.models['random_forest'] = joblib.load(f"{self.model_dir}/random_forest.pkl")
                print("✅ Loaded Random Forest model")
            
            if os.path.exists(f"{self.model_dir}/xgboost.pkl"):
                self.models['xgboost'] = joblib.load(f"{self.model_dir}/xgboost.pkl")
                print("✅ Loaded XGBoost model")
            
            if os.path.exists(f"{self.model_dir}/feature_names.pkl"):
                self.feature_names = joblib.load(f"{self.model_dir}/feature_names.pkl")
                print(f"✅ Loaded {len(self.feature_names)} feature names")
                
        except Exception as e:
            print(f"⚠️ Error loading models: {e}")
    
    def _get_ml_prediction(self, symbol: str, df: pd.DataFrame) -> float:
        """Get ML prediction for a symbol"""
        if not self.models or df.empty:
            return 0.5  # Default neutral prediction
        
        try:
            # Create features (simplified version)
            latest = df.iloc[-1]
            features = {
                'returns_1d': df['Close'].pct_change().iloc[-1],
                'returns_5d': df['Close'].pct_change(5).iloc[-1],
                'gap': (latest['Open'] - df['Close'].iloc[-2]) / df['Close'].iloc[-2] if len(df) > 1 else 0,
                'gap_abs': abs((latest['Open'] - df['Close'].iloc[-2]) / df['Close'].iloc[-2]) if len(df) > 1 else 0,
                'intraday_range': (latest['High'] - latest['Low']) / latest['Open'],
                'volume_ratio': latest['Volume'] / df['Volume'].rolling(20).mean().iloc[-1] if len(df) > 20 else 1,
                'volume_spike': 1 if latest['Volume'] / df['Volume'].rolling(20).mean().iloc[-1] > 2 else 0,
                'rsi': self._calculate_rsi(df['Close']).iloc[-1],
                'rsi_oversold': 1 if self._calculate_rsi(df['Close']).iloc[-1] < 30 else 0,
                'bb_position': 0.5,  # Simplified
                'atr_ratio': 0.02,  # Simplified
                'volatility_20d': df['Close'].pct_change().rolling(20).std().iloc[-1] if len(df) > 20 else 0.02,
                'gap_fill_setup': 0,  # Simplified
                'oversold_setup': 0   # Simplified
            }
            
            # Create feature vector in correct order
            feature_vector = []
            for fname in self.feature_names:
                feature_vector.append(features.get(fname, 0))
            
            X = np.array(feature_vector).reshape(1, -1)
            
            # Get ensemble prediction
            predictions = []
            if 'random_forest' in self.models:
                rf_pred = self.models['random_forest'].predict_proba(X)[0, 1]
                predictions.append(rf_pred * 0.6)
            
            if 'xgboost' in self.models:
                xgb_pred = self.models['xgboost'].predict_proba(X)[0, 1]
                predictions.append(xgb_pred * 0.4)
            
            if predictions:
                return sum(predictions)
            else:
                return 0.5
                
        except Exception as e:
            print(f"Error getting ML prediction for {symbol}: {e}")
            return 0.5
    
    def scan_for_opportunities(self, 
                             additional_symbols=None,
                             portfolio_value: float = 10000) -> list:
        """
        Enhanced scan with ML filtering
        """
        # Get base opportunities
        opportunities = super().scan_for_opportunities(additional_symbols, portfolio_value)
        
        if not self.models:
            print("⚠️ No ML models loaded, using base volatility hunter")
            return opportunities
        
        # Enhance with ML predictions
        enhanced_opportunities = []
        
        for opp in opportunities:
            try:
                # Get historical data for ML prediction
                ticker_data = self._get_ticker_data(opp.symbol)
                if ticker_data is not None:
                    ml_confidence = self._get_ml_prediction(opp.symbol, ticker_data)
                    
                    # Combine rule-based confidence with ML
                    combined_confidence = (opp.confidence * 0.7) + (ml_confidence * 0.3)
                    
                    # Update opportunity with combined confidence
                    opp.confidence = combined_confidence
                    opp.signals['ml_confidence'] = ml_confidence
                    
                    # Filter out low confidence opportunities
                    if combined_confidence > 0.6:
                        enhanced_opportunities.append(opp)
                        
            except Exception as e:
                print(f"Error enhancing {opp.symbol}: {e}")
                enhanced_opportunities.append(opp)
        
        # Sort by combined confidence
        enhanced_opportunities.sort(key=lambda x: x.confidence, reverse=True)
        
        print(f"🤖 ML enhanced: {len(enhanced_opportunities)}/{len(opportunities)} opportunities")
        return enhanced_opportunities
    
    def _get_ticker_data(self, symbol: str):
        """Get ticker data for ML prediction"""
        try:
            import yfinance as yf
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="1mo", interval="1d")
            return hist if not hist.empty else None
        except:
            return None

class MLPaperTradingEngine(PaperTradingEngine):
    """
    Enhanced paper trading engine with ML integration
    """
    def __init__(self, starting_capital: float = 10000):
        super().__init__(starting_capital)
        self.volatility_hunter = MLEnhancedVolatilityHunter()
        print("🤖 ML-Enhanced Paper Trading Engine initialized")

async def main():
    print("🚀 ML-Enhanced Volatility Trading System")
    print("="*60)
    
    # Step 1: Train ML models (if needed)
    if not os.path.exists("models/volatility/random_forest.pkl"):
        print("\n📚 Training ML models...")
        trainer = VolatilityModelTrainer()
        symbols = [
            'TQQQ', 'SQQQ', 'UVXY', 'SOXL', 'SOXS',
            'GME', 'AMC', 'TSLA', 'NVDA', 'RIOT',
            'MARA', 'COIN'
        ]
        await trainer.train_models(symbols)
    else:
        print("\n✅ ML models already trained")
    
    # Step 2: Start ML-enhanced paper trading
    print("\n💰 Starting ML-enhanced paper trading...")
    engine = MLPaperTradingEngine(starting_capital=10000)
    await engine.run_paper_trading(max_iterations=5)
    
    # Step 3: Display final results
    print("\n🎯 ML-Enhanced Trading Session Complete!")
    
    # Check if we have trade log
    if os.path.exists("paper_trades.json"):
        import json
        with open("paper_trades.json", 'r') as f:
            data = json.load(f)
            closed_trades = data.get('closed_trades', [])
            if closed_trades:
                wins = sum(1 for t in closed_trades if t['pnl'] > 0)
                total = len(closed_trades)
                win_rate = (wins / total) * 100 if total > 0 else 0
                print(f"\n📊 Session Results:")
                print(f"   Win Rate: {win_rate:.1f}% ({wins}/{total})")
                print(f"   Total P&L: ${sum(t['pnl'] for t in closed_trades):.2f}")

if __name__ == "__main__":
    asyncio.run(main())