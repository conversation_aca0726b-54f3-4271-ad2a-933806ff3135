"""
Enhanced Regulatory Compliance Framework

This module provides comprehensive compliance management including:
- Pattern Day Trading (PDT) rule enforcement
- Wash sale tracking and prevention
- Tax reporting (Form 8949, Schedule D)
- Position limits monitoring
- Trade surveillance
- Regulatory reporting automation
- Audit trail maintenance
"""

import asyncio
import json
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta, date
from dataclasses import dataclass, asdict
from enum import Enum
from decimal import Decimal
import aiofiles

from ..core.logger import get_logger
from ..core.config import settings
from ..data.models import Order, Trade, Position

logger = get_logger(__name__)


class ComplianceViolationType(Enum):
    """Types of compliance violations."""
    PDT_VIOLATION = "pdt_violation"
    WASH_SALE = "wash_sale"
    POSITION_LIMIT = "position_limit"
    CONCENTRATION_LIMIT = "concentration_limit"
    TRADING_HALT = "trading_halt"
    INSIDER_TRADING = "insider_trading"
    MARKET_MANIPULATION = "market_manipulation"
    REPORTING_VIOLATION = "reporting_violation"


class TaxLotMethod(Enum):
    """Tax lot identification methods."""
    FIFO = "fifo"  # First In, First Out
    LIFO = "lifo"  # Last In, First Out
    SPECIFIC_ID = "specific_id"  # Specific Identification
    AVERAGE_COST = "average_cost"  # Average Cost


@dataclass
class ComplianceViolation:
    """Compliance violation record."""
    violation_id: str
    timestamp: datetime
    violation_type: ComplianceViolationType
    symbol: str
    description: str
    severity: str  # low, medium, high, critical
    trade_ids: List[str]
    financial_impact: Optional[Decimal] = None
    resolved: bool = False
    resolution_notes: Optional[str] = None


@dataclass
class TaxLot:
    """Tax lot for cost basis tracking."""
    lot_id: str
    symbol: str
    acquisition_date: date
    quantity: Decimal
    cost_basis_per_share: Decimal
    total_cost_basis: Decimal
    trade_id: str
    is_wash_sale: bool = False
    wash_sale_loss_disallowed: Optional[Decimal] = None


@dataclass
class WashSaleEvent:
    """Wash sale event tracking."""
    event_id: str
    symbol: str
    sale_date: date
    sale_trade_id: str
    purchase_date: date
    purchase_trade_id: str
    disallowed_loss: Decimal
    adjusted_basis: Decimal


@dataclass
class PDTStatus:
    """Pattern Day Trader status tracking."""
    account_id: str
    is_pdt: bool
    day_trades_count: int
    day_trades_period_start: date
    buying_power: Decimal
    minimum_equity: Decimal
    last_updated: datetime


class EnhancedComplianceManager:
    """Enhanced compliance management system."""
    
    def __init__(self):
        self.violations: List[ComplianceViolation] = []
        self.tax_lots: Dict[str, List[TaxLot]] = {}  # symbol -> tax lots
        self.wash_sale_events: List[WashSaleEvent] = []
        self.pdt_status: Optional[PDTStatus] = None
        
        # Configuration
        self.config = {
            'pdt_day_trade_threshold': 4,  # 4+ day trades in 5 business days
            'pdt_minimum_equity': Decimal('25000'),
            'wash_sale_period_days': 30,
            'position_limit_percent': Decimal('0.05'),  # 5% of portfolio
            'concentration_limit_percent': Decimal('0.25'),  # 25% in single sector
            'tax_lot_method': TaxLotMethod.FIFO
        }
        
        # Compliance rules
        self.compliance_rules = self._initialize_compliance_rules()
        
        # Monitoring
        self.is_monitoring = False
        self.monitoring_tasks: List[asyncio.Task] = []
    
    def _initialize_compliance_rules(self) -> Dict[str, Any]:
        """Initialize compliance rules and thresholds."""
        return {
            'pdt_rules': {
                'enabled': True,
                'day_trade_threshold': 4,
                'lookback_days': 5,
                'minimum_equity': 25000
            },
            'wash_sale_rules': {
                'enabled': True,
                'period_days': 30,
                'substantially_identical_threshold': 0.95
            },
            'position_limits': {
                'enabled': True,
                'single_position_limit': 0.05,  # 5% of portfolio
                'sector_concentration_limit': 0.25,  # 25% in single sector
                'daily_volume_limit': 0.10  # 10% of average daily volume
            },
            'reporting_requirements': {
                'large_trader_threshold': 2000000,  # $2M
                'form_13f_threshold': 100000000,  # $100M
                'beneficial_ownership_threshold': 0.05  # 5%
            }
        }
    
    async def start_monitoring(self):
        """Start compliance monitoring."""
        if self.is_monitoring:
            logger.warning("Compliance monitoring already started")
            return
        
        self.is_monitoring = True
        logger.info("Starting enhanced compliance monitoring...")
        
        # Start monitoring tasks
        self.monitoring_tasks = [
            asyncio.create_task(self._monitor_pdt_compliance()),
            asyncio.create_task(self._monitor_wash_sales()),
            asyncio.create_task(self._monitor_position_limits()),
            asyncio.create_task(self._monitor_trading_patterns()),
            asyncio.create_task(self._generate_daily_reports())
        ]
        
        logger.info("Enhanced compliance monitoring started")
        
        try:
            await asyncio.gather(*self.monitoring_tasks)
        except Exception as e:
            logger.error(f"Compliance monitoring error: {e}")
        finally:
            self.is_monitoring = False
    
    async def stop_monitoring(self):
        """Stop compliance monitoring."""
        self.is_monitoring = False
        
        for task in self.monitoring_tasks:
            if not task.done():
                task.cancel()
        
        if self.monitoring_tasks:
            await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
        
        self.monitoring_tasks.clear()
        logger.info("Compliance monitoring stopped")
    
    async def process_trade(self, trade: Trade) -> List[ComplianceViolation]:
        """Process a trade for compliance checks."""
        violations = []
        
        try:
            # Check PDT compliance
            pdt_violation = await self._check_pdt_compliance(trade)
            if pdt_violation:
                violations.append(pdt_violation)
            
            # Check wash sale rules
            wash_sale_violation = await self._check_wash_sale(trade)
            if wash_sale_violation:
                violations.append(wash_sale_violation)
            
            # Check position limits
            position_violation = await self._check_position_limits(trade)
            if position_violation:
                violations.append(position_violation)
            
            # Update tax lots
            await self._update_tax_lots(trade)
            
            # Store violations
            self.violations.extend(violations)
            
            return violations
            
        except Exception as e:
            logger.error(f"Error processing trade for compliance: {e}")
            return []
    
    async def _check_pdt_compliance(self, trade: Trade) -> Optional[ComplianceViolation]:
        """Check Pattern Day Trading compliance."""
        try:
            if not self.compliance_rules['pdt_rules']['enabled']:
                return None
            
            # Get recent day trades
            day_trades = await self._get_day_trades_in_period(
                days=self.compliance_rules['pdt_rules']['lookback_days']
            )
            
            # Check if this trade creates a day trade
            is_day_trade = await self._is_day_trade(trade)
            
            if is_day_trade:
                day_trades_count = len(day_trades) + 1
                
                # Check if this would exceed PDT threshold
                if day_trades_count >= self.compliance_rules['pdt_rules']['day_trade_threshold']:
                    # Check if account has sufficient equity
                    account_equity = await self._get_account_equity()
                    
                    if account_equity < self.compliance_rules['pdt_rules']['minimum_equity']:
                        return ComplianceViolation(
                            violation_id=f"pdt_{trade.trade_id}",
                            timestamp=datetime.utcnow(),
                            violation_type=ComplianceViolationType.PDT_VIOLATION,
                            symbol=trade.symbol,
                            description=f"PDT violation: {day_trades_count} day trades with insufficient equity (${account_equity:,.2f} < ${self.compliance_rules['pdt_rules']['minimum_equity']:,.2f})",
                            severity="high",
                            trade_ids=[trade.trade_id],
                            financial_impact=None
                        )
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking PDT compliance: {e}")
            return None
    
    async def _check_wash_sale(self, trade: Trade) -> Optional[ComplianceViolation]:
        """Check for wash sale violations."""
        try:
            if not self.compliance_rules['wash_sale_rules']['enabled']:
                return None
            
            # Only check for sales
            if trade.side != 'sell':
                return None
            
            # Check if sale resulted in a loss
            if trade.realized_pnl >= 0:
                return None
            
            # Look for purchases of substantially identical securities
            # within 30 days before or after the sale
            wash_sale_period = self.compliance_rules['wash_sale_rules']['period_days']
            start_date = trade.executed_at.date() - timedelta(days=wash_sale_period)
            end_date = trade.executed_at.date() + timedelta(days=wash_sale_period)
            
            purchases = await self._get_purchases_in_period(
                symbol=trade.symbol,
                start_date=start_date,
                end_date=end_date,
                exclude_trade_id=trade.trade_id
            )
            
            if purchases:
                # Calculate disallowed loss
                disallowed_loss = abs(trade.realized_pnl)
                
                # Create wash sale event
                wash_sale_event = WashSaleEvent(
                    event_id=f"ws_{trade.trade_id}",
                    symbol=trade.symbol,
                    sale_date=trade.executed_at.date(),
                    sale_trade_id=trade.trade_id,
                    purchase_date=purchases[0].executed_at.date(),
                    purchase_trade_id=purchases[0].trade_id,
                    disallowed_loss=disallowed_loss,
                    adjusted_basis=purchases[0].price + (disallowed_loss / purchases[0].quantity)
                )
                
                self.wash_sale_events.append(wash_sale_event)
                
                return ComplianceViolation(
                    violation_id=f"wash_sale_{trade.trade_id}",
                    timestamp=datetime.utcnow(),
                    violation_type=ComplianceViolationType.WASH_SALE,
                    symbol=trade.symbol,
                    description=f"Wash sale detected: ${disallowed_loss:,.2f} loss disallowed",
                    severity="medium",
                    trade_ids=[trade.trade_id, purchases[0].trade_id],
                    financial_impact=disallowed_loss
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking wash sale: {e}")
            return None
    
    async def _check_position_limits(self, trade: Trade) -> Optional[ComplianceViolation]:
        """Check position limit compliance."""
        try:
            if not self.compliance_rules['position_limits']['enabled']:
                return None
            
            # Get current portfolio value
            portfolio_value = await self._get_portfolio_value()
            
            # Get position value after trade
            position_value = await self._get_position_value_after_trade(trade)
            
            # Check single position limit
            position_percent = position_value / portfolio_value
            single_limit = self.compliance_rules['position_limits']['single_position_limit']
            
            if position_percent > single_limit:
                return ComplianceViolation(
                    violation_id=f"pos_limit_{trade.trade_id}",
                    timestamp=datetime.utcnow(),
                    violation_type=ComplianceViolationType.POSITION_LIMIT,
                    symbol=trade.symbol,
                    description=f"Position limit exceeded: {position_percent:.2%} > {single_limit:.2%}",
                    severity="high",
                    trade_ids=[trade.trade_id],
                    financial_impact=None
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking position limits: {e}")
            return None
    
    async def _update_tax_lots(self, trade: Trade):
        """Update tax lots for cost basis tracking."""
        try:
            symbol = trade.symbol
            
            if symbol not in self.tax_lots:
                self.tax_lots[symbol] = []
            
            if trade.side == 'buy':
                # Create new tax lot
                tax_lot = TaxLot(
                    lot_id=f"lot_{trade.trade_id}",
                    symbol=symbol,
                    acquisition_date=trade.executed_at.date(),
                    quantity=trade.quantity,
                    cost_basis_per_share=trade.price,
                    total_cost_basis=trade.price * trade.quantity,
                    trade_id=trade.trade_id
                )
                
                self.tax_lots[symbol].append(tax_lot)
                
            elif trade.side == 'sell':
                # Reduce tax lots based on method
                await self._reduce_tax_lots(trade)
            
        except Exception as e:
            logger.error(f"Error updating tax lots: {e}")
    
    async def _reduce_tax_lots(self, trade: Trade):
        """Reduce tax lots for a sale."""
        symbol = trade.symbol
        remaining_quantity = trade.quantity
        
        if symbol not in self.tax_lots:
            logger.warning(f"No tax lots found for {symbol}")
            return
        
        # Sort lots based on tax lot method
        if self.config['tax_lot_method'] == TaxLotMethod.FIFO:
            lots = sorted(self.tax_lots[symbol], key=lambda x: x.acquisition_date)
        elif self.config['tax_lot_method'] == TaxLotMethod.LIFO:
            lots = sorted(self.tax_lots[symbol], key=lambda x: x.acquisition_date, reverse=True)
        else:
            lots = self.tax_lots[symbol]
        
        # Reduce lots
        for lot in lots:
            if remaining_quantity <= 0:
                break
            
            if lot.quantity > 0:
                reduction = min(lot.quantity, remaining_quantity)
                lot.quantity -= reduction
                remaining_quantity -= reduction
                
                # Remove empty lots
                if lot.quantity == 0:
                    self.tax_lots[symbol].remove(lot)
