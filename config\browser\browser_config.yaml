# Browser Automation Configuration
browser:
  # Browser settings
  headless: false  # Set to true for headless mode
  window_size: "1920,1080"
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  
  # Stealth settings
  disable_automation_flags: true
  disable_dev_shm_usage: true
  no_sandbox: false  # Set to true in Docker
  
  # Timeouts
  page_load_timeout: 30
  implicit_wait: 10
  explicit_wait: 10
  
  # Screenshots
  screenshot_on_error: true
  screenshot_dir: "./screenshots"
  
  # Cookies and session
  cookies_file: "./data/cookies/webull_cookies.pkl"
  session_timeout: 3600  # 1 hour

webull:
  # URLs
  base_url: "https://app.webull.com"
  login_url: "https://app.webull.com/login"
  paper_trading_url: "https://app.webull.com/paper"
  
  # Trading settings
  paper_trading: true  # ALWAYS START WITH TRUE!
  default_order_type: "market"
  
  # Watchlist
  watchlist:
    - "AAPL"
    - "MSFT" 
    - "GOOGL"
    - "TSLA"
    - "NVDA"
  
  # Risk management
  max_position_size: 100  # shares
  stop_loss_percent: -2.0
  take_profit_percent: 5.0
  
  # Trading loop
  loop_interval: 60  # seconds
  min_confidence: 0.7

# Integration with production components
integration:
  # Enhanced security
  use_enhanced_secrets: true
  mfa_required: true
  
  # Monitoring
  enable_metrics: true
  prometheus_port: 9090
  
  # Compliance
  enable_compliance_checks: true
  pdt_monitoring: true
  wash_sale_detection: true
  
  # Dead man's switch
  enable_dead_mans_switch: true
  heartbeat_interval: 300  # 5 minutes
  
  # Market microstructure
  enable_microstructure_analysis: true
  order_flow_analysis: true
  
  # Performance optimization
  enable_performance_optimization: true
  optimization_level: "high"
