"""Enhanced structured logging for the API framework."""

import json
import sys
from datetime import datetime
from typing import Any, Dict, Optional

from loguru import logger

from ..core.config import settings


class StructuredLogger:
    """Structured logger with enhanced context for trading operations."""
    
    def __init__(self, name: str):
        self.name = name
        self.base_context = {"component": name}
    
    def _format_extra(self, extra: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Format extra context for logging."""
        context = self.base_context.copy()
        if extra:
            context.update(extra)
        
        # Add timestamp
        context["timestamp"] = datetime.utcnow().isoformat()
        
        return context
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """Log debug message with structured context."""
        logger.bind(**self._format_extra(extra)).debug(message)
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """Log info message with structured context."""
        logger.bind(**self._format_extra(extra)).info(message)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """Log warning message with structured context."""
        logger.bind(**self._format_extra(extra)).warning(message)
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """Log error message with structured context."""
        logger.bind(**self._format_extra(extra)).error(message)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """Log critical message with structured context."""
        logger.bind(**self._format_extra(extra)).critical(message)
    
    def api_request(
        self,
        method: str,
        endpoint: str,
        status_code: Optional[int] = None,
        response_time: Optional[float] = None,
        error: Optional[str] = None,
    ) -> None:
        """Log API request with structured data."""
        extra = {
            "event_type": "api_request",
            "method": method,
            "endpoint": endpoint,
            "status_code": status_code,
            "response_time_ms": response_time * 1000 if response_time else None,
            "error": error,
        }
        
        if error:
            self.error(f"API request failed: {method} {endpoint}", extra=extra)
        else:
            self.info(f"API request: {method} {endpoint}", extra=extra)
    
    def order_event(
        self,
        event_type: str,
        order_id: Optional[str] = None,
        symbol: Optional[str] = None,
        side: Optional[str] = None,
        quantity: Optional[int] = None,
        price: Optional[float] = None,
        status: Optional[str] = None,
        error: Optional[str] = None,
    ) -> None:
        """Log order event with structured data."""
        extra = {
            "event_type": "order_event",
            "order_event_type": event_type,
            "order_id": order_id,
            "symbol": symbol,
            "side": side,
            "quantity": quantity,
            "price": price,
            "status": status,
            "error": error,
        }
        
        if error:
            self.error(f"Order {event_type} failed: {error}", extra=extra)
        else:
            self.info(f"Order {event_type}: {symbol} {side} {quantity}", extra=extra)
    
    def position_event(
        self,
        event_type: str,
        symbol: str,
        side: Optional[str] = None,
        quantity: Optional[int] = None,
        avg_price: Optional[float] = None,
        current_price: Optional[float] = None,
        unrealized_pnl: Optional[float] = None,
    ) -> None:
        """Log position event with structured data."""
        extra = {
            "event_type": "position_event",
            "position_event_type": event_type,
            "symbol": symbol,
            "side": side,
            "quantity": quantity,
            "avg_price": avg_price,
            "current_price": current_price,
            "unrealized_pnl": unrealized_pnl,
        }
        
        self.info(f"Position {event_type}: {symbol}", extra=extra)
    
    def market_data_event(
        self,
        event_type: str,
        symbol: str,
        price: Optional[float] = None,
        volume: Optional[int] = None,
        bid: Optional[float] = None,
        ask: Optional[float] = None,
        data_source: Optional[str] = None,
    ) -> None:
        """Log market data event with structured data."""
        extra = {
            "event_type": "market_data",
            "market_event_type": event_type,
            "symbol": symbol,
            "price": price,
            "volume": volume,
            "bid": bid,
            "ask": ask,
            "data_source": data_source,
        }
        
        self.debug(f"Market data {event_type}: {symbol}", extra=extra)
    
    def performance_metric(
        self,
        metric_name: str,
        value: float,
        unit: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None:
        """Log performance metric with structured data."""
        extra = {
            "event_type": "performance_metric",
            "metric_name": metric_name,
            "value": value,
            "unit": unit,
            "tags": tags or {},
        }
        
        self.info(f"Metric {metric_name}: {value} {unit or ''}", extra=extra)
    
    def risk_event(
        self,
        event_type: str,
        risk_type: str,
        symbol: Optional[str] = None,
        current_value: Optional[float] = None,
        limit_value: Optional[float] = None,
        action_taken: Optional[str] = None,
    ) -> None:
        """Log risk management event with structured data."""
        extra = {
            "event_type": "risk_event",
            "risk_event_type": event_type,
            "risk_type": risk_type,
            "symbol": symbol,
            "current_value": current_value,
            "limit_value": limit_value,
            "action_taken": action_taken,
        }
        
        if event_type in ["violation", "breach"]:
            self.warning(f"Risk {event_type}: {risk_type}", extra=extra)
        else:
            self.info(f"Risk {event_type}: {risk_type}", extra=extra)
    
    def websocket_event(
        self,
        event_type: str,
        connection_id: Optional[str] = None,
        message_type: Optional[str] = None,
        symbol: Optional[str] = None,
        error: Optional[str] = None,
    ) -> None:
        """Log WebSocket event with structured data."""
        extra = {
            "event_type": "websocket_event",
            "ws_event_type": event_type,
            "connection_id": connection_id,
            "message_type": message_type,
            "symbol": symbol,
            "error": error,
        }
        
        if error:
            self.error(f"WebSocket {event_type}: {error}", extra=extra)
        else:
            self.debug(f"WebSocket {event_type}", extra=extra)


def get_structured_logger(name: str) -> StructuredLogger:
    """Get a structured logger instance."""
    return StructuredLogger(name)


def setup_json_logging():
    """Set up JSON logging format for production."""
    def json_formatter(record):
        """Format log record as JSON."""
        log_entry = {
            "timestamp": record["time"].isoformat(),
            "level": record["level"].name,
            "logger": record["name"],
            "message": record["message"],
            "module": record["module"],
            "function": record["function"],
            "line": record["line"],
        }
        
        # Add extra fields
        if record.get("extra"):
            log_entry.update(record["extra"])
        
        # Add exception info if present
        if record.get("exception"):
            log_entry["exception"] = {
                "type": record["exception"].type.__name__,
                "value": str(record["exception"].value),
                "traceback": record["exception"].traceback,
            }
        
        return json.dumps(log_entry, default=str)
    
    # Remove default logger
    logger.remove()
    
    # Add JSON formatter
    logger.add(
        sys.stdout,
        format=json_formatter,
        level=settings.log_level,
        serialize=False,
    )
    
    # Add file logger if configured
    if settings.log_file:
        logger.add(
            settings.log_file,
            format=json_formatter,
            level=settings.log_level,
            rotation=settings.log_rotation,
            retention=settings.log_retention,
            compression="zip",
            serialize=False,
        )


# Set up JSON logging in production
if settings.environment == "production":
    setup_json_logging()
