"""Comprehensive risk management system for the trading bot."""

import asyncio
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import Order, Portfolio, Position, Symbol, RiskMetrics, RiskLimitBreach
from .correlation import CorrelationTracker
from .drawdown import DrawdownMonitor
from .portfolio_risk import PortfolioRiskCalculator
from .position_sizing import PositionSizer
from .risk_limits import RiskLimitEnforcer
from .stop_loss import StopLossManager

logger = get_logger(__name__)


class RiskManager:
    """Comprehensive risk management system with all components integrated."""

    def __init__(self):
        self.config = settings.risk
        self.portfolio_value = 0.0
        self.current_positions = {}
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0

        # Initialize all risk management components
        self.position_sizer = PositionSizer()
        self.stop_loss_manager = StopLossManager()
        self.portfolio_risk_calculator = PortfolioRiskCalculator()
        self.risk_limit_enforcer = RiskLimitEnforcer()
        self.correlation_tracker = CorrelationTracker()
        self.drawdown_monitor = DrawdownMonitor()

        # Risk monitoring state
        self.last_risk_update = None
        self.risk_update_frequency = timedelta(minutes=5)  # Update every 5 minutes
        self.alert_history = []
        
    async def initialize(self):
        """Initialize risk manager with current portfolio state and all components."""
        try:
            # Load portfolio state
            await self._load_portfolio_state()
            await self._load_current_positions()

            # Initialize all components
            await self.position_sizer.initialize(self.portfolio_value)
            await self.stop_loss_manager.initialize()
            await self.risk_limit_enforcer.initialize()
            await self.correlation_tracker.initialize()
            await self.drawdown_monitor.initialize(self.portfolio_value)

            logger.info("Comprehensive risk manager initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize risk manager: {e}")
            raise RiskError(f"Risk manager initialization failed: {e}")
    
    async def _load_portfolio_state(self):
        """Load current portfolio state from database."""
        async with get_postgres_session() as session:
            # Get latest portfolio snapshot
            stmt = select(Portfolio).order_by(Portfolio.timestamp.desc()).limit(1)
            result = await session.execute(stmt)
            portfolio = result.scalar_one_or_none()
            
            if portfolio:
                self.portfolio_value = float(portfolio.total_value)
                self.daily_pnl = float(portfolio.daily_pnl or 0)
                self.max_drawdown = float(portfolio.max_drawdown or 0)
            else:
                # Default values if no portfolio data
                self.portfolio_value = 100000.0  # Default $100k
                self.daily_pnl = 0.0
                self.max_drawdown = 0.0
    
    async def _load_current_positions(self):
        """Load current open positions."""
        async with get_postgres_session() as session:
            stmt = (
                select(Position, Symbol.symbol)
                .join(Symbol)
                .where(Position.status == "OPEN")
            )
            result = await session.execute(stmt)
            
            self.current_positions = {}
            for position, symbol in result:
                self.current_positions[symbol] = {
                    "quantity": position.quantity,
                    "side": position.side,
                    "entry_price": float(position.entry_price),
                    "current_price": float(position.current_price or position.entry_price),
                    "unrealized_pnl": float(position.unrealized_pnl or 0),
                }
    
    def calculate_position_size(
        self,
        symbol: str,
        entry_price: float,
        stop_loss: float,
        win_rate: Optional[float] = None,
        avg_win: Optional[float] = None,
        avg_loss: Optional[float] = None,
    ) -> int:
        """
        Calculate optimal position size using Kelly Criterion and risk limits.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price
            stop_loss: Stop loss price
            win_rate: Historical win rate (optional)
            avg_win: Average win amount (optional)
            avg_loss: Average loss amount (optional)
            
        Returns:
            Position size in shares
        """
        try:
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss)
            
            # Maximum risk per trade (percentage of portfolio)
            max_risk_amount = self.portfolio_value * self.config.max_portfolio_risk
            
            # Basic position size based on risk
            basic_position_size = int(max_risk_amount / risk_per_share)
            
            # Apply Kelly Criterion if we have historical data
            if win_rate and avg_win and avg_loss:
                kelly_fraction = self._calculate_kelly_fraction(win_rate, avg_win, avg_loss)
                kelly_position_size = int(
                    (self.portfolio_value * kelly_fraction * self.config.kelly_fraction) / entry_price
                )
                position_size = min(basic_position_size, kelly_position_size)
            else:
                position_size = basic_position_size
            
            # Apply maximum position size limit
            max_position_value = self.portfolio_value * self.config.max_position_size
            max_position_size = int(max_position_value / entry_price)
            position_size = min(position_size, max_position_size)
            
            # Ensure minimum position size
            position_size = max(position_size, 1)
            
            logger.info(
                f"Calculated position size for {symbol}: {position_size} shares "
                f"(${position_size * entry_price:.2f})"
            )
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating position size for {symbol}: {e}")
            raise RiskError(f"Position size calculation failed: {e}")
    
    def _calculate_kelly_fraction(self, win_rate: float, avg_win: float, avg_loss: float) -> float:
        """Calculate Kelly Criterion fraction."""
        if avg_loss <= 0:
            return 0.0
        
        win_loss_ratio = avg_win / avg_loss
        kelly_fraction = win_rate - ((1 - win_rate) / win_loss_ratio)
        
        # Cap Kelly fraction to prevent over-leveraging
        return max(0.0, min(kelly_fraction, 0.25))
    
    async def validate_order(
        self,
        symbol: str,
        side: str,
        quantity: int,
        price: float,
        order_type: str = "MARKET",
    ) -> Tuple[bool, str]:
        """
        Validate an order against risk management rules.
        
        Returns:
            Tuple of (is_valid, reason)
        """
        try:
            # Check if trading is enabled
            if not settings.trading_enabled:
                return False, "Trading is disabled"
            
            # Check portfolio value
            if self.portfolio_value <= 0:
                return False, "Invalid portfolio value"
            
            # Check position size limits
            position_value = quantity * price
            max_position_value = self.portfolio_value * self.config.max_position_size
            
            if position_value > max_position_value:
                return False, f"Position size exceeds limit: ${position_value:.2f} > ${max_position_value:.2f}"
            
            # Check daily drawdown
            if self.daily_pnl < 0:
                daily_drawdown = abs(self.daily_pnl) / self.portfolio_value
                if daily_drawdown >= self.config.max_daily_drawdown:
                    return False, f"Daily drawdown limit exceeded: {daily_drawdown:.2%}"
            
            # Check correlation limits for new positions
            if side == "BUY":
                correlation_risk = await self._check_correlation_risk(symbol, position_value)
                if not correlation_risk[0]:
                    return False, correlation_risk[1]
            
            # Check sector concentration
            sector_risk = await self._check_sector_concentration(symbol, position_value)
            if not sector_risk[0]:
                return False, sector_risk[1]
            
            return True, "Order validated successfully"
            
        except Exception as e:
            logger.error(f"Error validating order for {symbol}: {e}")
            return False, f"Validation error: {e}"
    
    async def _check_correlation_risk(self, symbol: str, position_value: float) -> Tuple[bool, str]:
        """Check correlation risk with existing positions."""
        try:
            # For now, implement basic sector-based correlation check
            # In production, this would use actual correlation calculations
            
            if not self.current_positions:
                return True, "No existing positions"
            
            # Simple check: limit exposure to similar symbols
            similar_symbols = [s for s in self.current_positions.keys() if s.startswith(symbol[:2])]
            
            if len(similar_symbols) >= 3:
                return False, f"Too many similar positions: {similar_symbols}"
            
            return True, "Correlation check passed"
            
        except Exception as e:
            logger.error(f"Error checking correlation risk: {e}")
            return False, f"Correlation check error: {e}"
    
    async def _check_sector_concentration(self, symbol: str, position_value: float) -> Tuple[bool, str]:
        """Check sector concentration limits."""
        try:
            async with get_postgres_session() as session:
                # Get symbol sector
                stmt = select(Symbol.sector).where(Symbol.symbol == symbol)
                result = await session.execute(stmt)
                sector = result.scalar_one_or_none()
                
                if not sector:
                    return True, "Sector information not available"
                
                # Calculate current sector exposure
                sector_exposure = 0.0
                for pos_symbol, position in self.current_positions.items():
                    # Get sector for existing position
                    stmt = select(Symbol.sector).where(Symbol.symbol == pos_symbol)
                    result = await session.execute(stmt)
                    pos_sector = result.scalar_one_or_none()
                    
                    if pos_sector == sector:
                        pos_value = position["quantity"] * position["current_price"]
                        sector_exposure += pos_value
                
                # Add new position value
                total_sector_exposure = (sector_exposure + position_value) / self.portfolio_value
                
                if total_sector_exposure > self.config.max_sector_exposure:
                    return False, f"Sector exposure limit exceeded: {total_sector_exposure:.2%}"
                
                return True, "Sector concentration check passed"
                
        except Exception as e:
            logger.error(f"Error checking sector concentration: {e}")
            return False, f"Sector check error: {e}"
    
    def calculate_stop_loss(
        self,
        entry_price: float,
        side: str,
        atr: Optional[float] = None,
        volatility: Optional[float] = None,
    ) -> float:
        """
        Calculate dynamic stop loss based on volatility.
        
        Args:
            entry_price: Entry price
            side: Position side (LONG/SHORT)
            atr: Average True Range
            volatility: Historical volatility
            
        Returns:
            Stop loss price
        """
        try:
            # Use ATR-based stop if available
            if atr:
                stop_distance = atr * 2.0  # 2x ATR
            elif volatility:
                stop_distance = entry_price * volatility * 2.0  # 2x volatility
            else:
                # Default percentage stop
                stop_distance = entry_price * self.config.default_stop_loss
            
            if side.upper() == "LONG":
                stop_loss = entry_price - stop_distance
            else:  # SHORT
                stop_loss = entry_price + stop_distance
            
            # Ensure stop loss is reasonable
            min_stop_distance = entry_price * 0.005  # 0.5% minimum
            max_stop_distance = entry_price * 0.10   # 10% maximum
            
            actual_distance = abs(entry_price - stop_loss)
            if actual_distance < min_stop_distance:
                if side.upper() == "LONG":
                    stop_loss = entry_price - min_stop_distance
                else:
                    stop_loss = entry_price + min_stop_distance
            elif actual_distance > max_stop_distance:
                if side.upper() == "LONG":
                    stop_loss = entry_price - max_stop_distance
                else:
                    stop_loss = entry_price + max_stop_distance
            
            return round(stop_loss, 2)
            
        except Exception as e:
            logger.error(f"Error calculating stop loss: {e}")
            # Return default stop loss
            if side.upper() == "LONG":
                return entry_price * (1 - self.config.default_stop_loss)
            else:
                return entry_price * (1 + self.config.default_stop_loss)
    
    async def calculate_comprehensive_position_size(
        self,
        symbol: str,
        entry_price: float,
        stop_loss: float,
        signal_strength: float = 0.5,
        strategy_performance: Optional[Dict[str, float]] = None,
        sizing_method: str = "kelly"
    ) -> int:
        """
        Calculate position size using advanced algorithms.

        Args:
            symbol: Stock symbol
            entry_price: Entry price
            stop_loss: Stop loss price
            signal_strength: Signal strength (0-1)
            strategy_performance: Strategy performance metrics
            sizing_method: Sizing method (kelly, volatility, risk_parity, dynamic)

        Returns:
            Position size in shares
        """
        try:
            if sizing_method == "kelly" and strategy_performance:
                return self.position_sizer.calculate_kelly_position_size(
                    symbol, entry_price, stop_loss,
                    strategy_performance.get("win_rate", 0.5),
                    strategy_performance.get("avg_win", 0.05),
                    strategy_performance.get("avg_loss", 0.02)
                )

            elif sizing_method == "volatility":
                return await self.position_sizer.calculate_volatility_adjusted_size(
                    symbol, entry_price, stop_loss
                )

            elif sizing_method == "risk_parity":
                return await self.position_sizer.calculate_risk_parity_size(
                    symbol, entry_price, self.current_positions
                )

            elif sizing_method == "dynamic":
                return self.position_sizer.calculate_dynamic_size(
                    symbol, entry_price, stop_loss, signal_strength,
                    strategy_performance or {}
                )

            else:
                # Default to basic calculation
                return self.calculate_position_size(symbol, entry_price, stop_loss)

        except Exception as e:
            logger.error(f"Error calculating comprehensive position size: {e}")
            return self.calculate_position_size(symbol, entry_price, stop_loss)

    async def calculate_advanced_stop_loss(
        self,
        symbol: str,
        entry_price: float,
        side: str,
        stop_type: str = "atr",
        multiplier: float = 2.0
    ) -> float:
        """
        Calculate stop loss using advanced methods.

        Args:
            symbol: Stock symbol
            entry_price: Entry price
            side: Position side (LONG/SHORT)
            stop_type: Stop type (atr, volatility, support_resistance, fixed)
            multiplier: Multiplier for ATR or volatility

        Returns:
            Stop loss price
        """
        try:
            from .stop_loss import StopType

            if stop_type == "atr":
                return await self.stop_loss_manager.calculate_stop_loss(
                    symbol, entry_price, side, StopType.ATR_BASED, multiplier
                )
            elif stop_type == "volatility":
                return await self.stop_loss_manager.calculate_stop_loss(
                    symbol, entry_price, side, StopType.VOLATILITY_ADJUSTED, multiplier
                )
            elif stop_type == "support_resistance":
                return await self.stop_loss_manager.calculate_stop_loss(
                    symbol, entry_price, side, StopType.SUPPORT_RESISTANCE
                )
            else:
                return self.calculate_stop_loss(entry_price, side)

        except Exception as e:
            logger.error(f"Error calculating advanced stop loss: {e}")
            return self.calculate_stop_loss(entry_price, side)

    async def get_comprehensive_risk_metrics(self) -> Dict[str, any]:
        """Get comprehensive real-time risk metrics."""
        try:
            if not self.current_positions:
                return {"error": "No positions to analyze"}

            # Calculate all risk metrics
            var_metrics = await self.portfolio_risk_calculator.calculate_var(self.current_positions)
            cvar = await self.portfolio_risk_calculator.calculate_cvar(self.current_positions)
            sharpe_ratio = await self.portfolio_risk_calculator.calculate_sharpe_ratio(self.current_positions)
            sortino_ratio = await self.portfolio_risk_calculator.calculate_sortino_ratio(self.current_positions)
            beta = await self.portfolio_risk_calculator.calculate_beta(self.current_positions)
            drawdown_metrics = await self.portfolio_risk_calculator.calculate_maximum_drawdown(self.current_positions)
            correlation_matrix = await self.portfolio_risk_calculator.calculate_correlation_matrix(self.current_positions)

            # Get correlation analysis
            correlation_analysis = await self.correlation_tracker.get_portfolio_correlations(self.current_positions)

            # Get drawdown metrics
            drawdown_monitor_metrics = self.drawdown_monitor.get_drawdown_metrics()

            # Combine all metrics
            comprehensive_metrics = {
                "timestamp": datetime.utcnow(),
                "portfolio_value": self.portfolio_value,

                # VaR metrics
                "var_95": var_metrics.get("var_95", 0.0),
                "var_99": var_metrics.get("var_99", 0.0),
                "cvar_95": cvar,

                # Risk ratios
                "sharpe_ratio": sharpe_ratio,
                "sortino_ratio": sortino_ratio,
                "beta": beta,

                # Drawdown metrics
                "current_drawdown": drawdown_monitor_metrics["current_drawdown"],
                "max_drawdown": drawdown_monitor_metrics["max_drawdown"],
                "drawdown_duration": drawdown_monitor_metrics["drawdown_duration"],

                # Correlation metrics
                "avg_correlation": correlation_analysis["avg_correlation"],
                "max_correlation": correlation_analysis["max_correlation"],
                "correlation_risk_score": correlation_analysis["correlation_risk_score"],
                "high_correlations": correlation_analysis["high_correlations"],

                # Volatility metrics
                "daily_volatility": var_metrics.get("daily_volatility", 0.0),
                "portfolio_volatility": var_metrics.get("daily_volatility", 0.0) * np.sqrt(252),

                # Position metrics
                "position_count": len(self.current_positions),
                "correlation_matrix": correlation_matrix.to_dict() if not correlation_matrix.empty else {}
            }

            return comprehensive_metrics

        except Exception as e:
            logger.error(f"Error getting comprehensive risk metrics: {e}")
            return {"error": str(e)}

    async def check_all_risk_limits(self) -> Dict[str, any]:
        """Check all risk limits and return enforcement actions."""
        try:
            # Check risk limits
            limit_results = await self.risk_limit_enforcer.check_all_limits(
                self.portfolio_value, self.current_positions, self.daily_pnl
            )

            # Check drawdown alerts
            drawdown_alerts = self.drawdown_monitor.check_drawdown_alerts()

            # Check time-based stops
            time_stops = await self.stop_loss_manager.check_time_based_stops()

            # Combine all results
            combined_results = {
                "timestamp": datetime.utcnow(),
                "limit_breaches": limit_results["breaches"],
                "warnings": limit_results["warnings"],
                "actions": limit_results["actions"],
                "circuit_breaker_level": limit_results["circuit_breaker_level"],
                "drawdown_alerts": drawdown_alerts,
                "time_based_stops": time_stops,
                "total_breaches": len(limit_results["breaches"]) + len(drawdown_alerts),
                "requires_action": len(limit_results["actions"]) > 0 or len(time_stops) > 0
            }

            # Log breaches
            if combined_results["total_breaches"] > 0:
                await self._log_risk_breaches(combined_results)

            return combined_results

        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return {"error": str(e)}

    async def update_portfolio_metrics(self):
        """Update comprehensive portfolio risk metrics."""
        try:
            # Update basic portfolio state
            await self._update_basic_portfolio_state()

            # Update drawdown monitor
            drawdown_metrics = self.drawdown_monitor.update_drawdown(self.portfolio_value)

            # Update correlation matrix if needed
            if (not self.correlation_tracker.last_update or
                datetime.utcnow() - self.correlation_tracker.last_update > self.correlation_tracker.update_frequency):
                await self.correlation_tracker.update_correlation_matrix(list(self.current_positions.keys()))

            # Update trailing stops
            for symbol, position in self.current_positions.items():
                await self.stop_loss_manager.update_trailing_stop(symbol, position["current_price"])

            # Save risk metrics to database
            await self._save_risk_metrics()

            # Save drawdown snapshot
            await self.drawdown_monitor.save_drawdown_snapshot(self.portfolio_value)

            logger.debug(f"Comprehensive portfolio metrics updated: Value=${self.portfolio_value:.2f}")

        except Exception as e:
            logger.error(f"Error updating portfolio metrics: {e}")
            raise RiskError(f"Portfolio metrics update failed: {e}")

    async def _update_basic_portfolio_state(self):
        """Update basic portfolio state calculations."""
        total_value = 0.0
        total_pnl = 0.0

        for symbol, position in self.current_positions.items():
            position_value = position["quantity"] * position["current_price"]
            total_value += position_value
            total_pnl += position["unrealized_pnl"]

        self.portfolio_value = total_value
        # Daily P&L would be calculated from start-of-day value

    async def _save_risk_metrics(self):
        """Save current risk metrics to database."""
        try:
            metrics = await self.get_comprehensive_risk_metrics()
            if "error" in metrics:
                return

            async with get_postgres_session() as session:
                risk_record = RiskMetrics(
                    timestamp=datetime.utcnow(),
                    portfolio_value=self.portfolio_value,
                    var_95=metrics.get("var_95"),
                    var_99=metrics.get("var_99"),
                    cvar_95=metrics.get("cvar_95"),
                    sharpe_ratio=metrics.get("sharpe_ratio"),
                    sortino_ratio=metrics.get("sortino_ratio"),
                    beta=metrics.get("beta"),
                    current_drawdown=metrics.get("current_drawdown"),
                    max_drawdown=metrics.get("max_drawdown"),
                    drawdown_duration=metrics.get("drawdown_duration"),
                    avg_correlation=metrics.get("avg_correlation"),
                    max_correlation=metrics.get("max_correlation"),
                    correlation_risk_score=metrics.get("correlation_risk_score"),
                    portfolio_volatility=metrics.get("portfolio_volatility"),
                    daily_volatility=metrics.get("daily_volatility")
                )

                session.add(risk_record)
                await session.commit()

        except Exception as e:
            logger.error(f"Error saving risk metrics: {e}")

    async def _log_risk_breaches(self, breach_results: Dict):
        """Log risk breaches to database."""
        try:
            async with get_postgres_session() as session:
                # Log limit breaches
                for breach in breach_results["limit_breaches"]:
                    breach_record = RiskLimitBreach(
                        timestamp=datetime.utcnow(),
                        breach_type=breach["type"].value if hasattr(breach["type"], "value") else str(breach["type"]),
                        severity="HIGH",
                        current_value=breach.get("current"),
                        limit_value=breach.get("limit"),
                        portfolio_value=self.portfolio_value,
                        message=breach["message"],
                        actions_taken=str(breach.get("actions", []))
                    )
                    session.add(breach_record)

                # Log drawdown alerts
                for alert in breach_results["drawdown_alerts"]:
                    breach_record = RiskLimitBreach(
                        timestamp=datetime.utcnow(),
                        breach_type=alert["type"],
                        severity=alert["severity"],
                        current_value=alert.get("current_value"),
                        limit_value=alert.get("limit"),
                        portfolio_value=self.portfolio_value,
                        message=alert["message"],
                        actions_taken="[]"
                    )
                    session.add(breach_record)

                await session.commit()

        except Exception as e:
            logger.error(f"Error logging risk breaches: {e}")


# Global risk manager instance
risk_manager = RiskManager()
