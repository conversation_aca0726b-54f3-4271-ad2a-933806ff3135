# Enhanced Webull Browser Automation Setup

This guide covers setting up the enhanced browser automation system with all production-grade components integrated.

## 🚀 Quick Start

### 1. Run Browser Automation Setup

```bash
python setup_browser_automation.py
```

This will install all required packages and set up the environment.

### 2. Launch Enhanced Browser Bot

```bash
python launch_enhanced_browser_bot.py
```

## 📋 Manual Setup Steps

### Step 1: Install Dependencies

```bash
# Install browser automation packages
pip install selenium>=4.15.0
pip install undetected-chromedriver>=3.5.0
pip install pillow>=10.0.0
pip install opencv-python>=4.8.0
pip install pytesseract>=0.3.10

# Install production-grade components
pip install scikit-learn>=1.3.0
pip install psutil>=5.9.0
pip install aiofiles>=23.0.0
pip install cryptography>=41.0.0
pip install prometheus-client>=0.17.0
```

### Step 2: Install System Dependencies

**Windows:**
- Install Google Chrome
- Install Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki

**macOS:**
```bash
brew install tesseract
```

**Linux:**
```bash
sudo apt-get update
sudo apt-get install -y tesseract-ocr google-chrome-stable
```

### Step 3: Configure Browser Settings

Edit `config/browser/browser_config.yaml`:

```yaml
browser:
  headless: false  # Set to true for headless mode
  window_size: "1920,1080"
  screenshot_on_error: true
  page_load_timeout: 30

webull:
  paper_trading: true  # ALWAYS START WITH TRUE!
  watchlist:
    - "AAPL"
    - "MSFT"
    - "GOOGL"
  min_confidence: 0.7

integration:
  use_enhanced_secrets: true
  enable_metrics: true
  enable_dead_mans_switch: true
  enable_compliance_checks: true
```

## 🔧 Production-Grade Features

### Enhanced Security Integration

The browser automation includes:

- **Secure Credential Storage**: Webull credentials stored in encrypted secrets manager
- **MFA Support**: Multi-factor authentication integration
- **Session Management**: Secure cookie storage and session timeout
- **Audit Logging**: All browser actions logged for compliance

```python
# Credentials are automatically retrieved from secure storage
await bot.login()  # Uses stored credentials

# Or manual entry with MFA
await bot.login(username, password, mfa_code)
```

### Advanced Monitoring

Real-time monitoring of browser automation:

- **Execution Metrics**: Order placement latency and success rates
- **Browser Health**: Screenshot counts, error rates, session duration
- **Performance Tracking**: CPU/memory usage during automation
- **Anomaly Detection**: ML-powered detection of unusual patterns

**Grafana Dashboard**: `http://localhost:3000`
- Browser automation status
- Login success rates
- Order execution metrics
- Error tracking

### Dead Man's Switch Integration

Automatic safety shutdown if system becomes unresponsive:

- **Heartbeat Monitoring**: Regular system health checks
- **Emergency Contacts**: Automatic notifications on failure
- **Graceful Shutdown**: Safe position closure and system cleanup

```python
# Heartbeats sent automatically every 5 minutes
# Emergency shutdown if no heartbeat for 30 minutes
```

### Compliance Checking

Automated regulatory compliance:

- **PDT Rule Monitoring**: Pattern Day Trading rule enforcement
- **Wash Sale Detection**: Automatic detection and reporting
- **Position Limits**: Real-time position size monitoring
- **Trade Surveillance**: All trades checked for compliance violations

### Market Microstructure Analysis

Advanced market analysis for better execution:

- **Order Flow Analysis**: Real-time buy/sell pressure detection
- **Hidden Liquidity Detection**: Iceberg order identification
- **Market Regime Classification**: Trending vs mean-reverting markets
- **Execution Timing**: Optimal order placement recommendations

```python
# Get execution recommendation before placing order
recommendation = await bot.market_analyzer.get_execution_recommendation("AAPL", 100)

if recommendation['confidence'] > 0.7:
    strategy = recommendation['recommendation']
    # Execute with recommended strategy
```

## 🎯 Configuration Options

### Browser Settings

```yaml
browser:
  headless: false              # Headless mode
  window_size: "1920,1080"     # Browser window size
  user_agent: "Mozilla/5.0..." # Custom user agent
  page_load_timeout: 30        # Page load timeout
  implicit_wait: 10            # Implicit wait time
  explicit_wait: 10            # Explicit wait time
  screenshot_on_error: true    # Take screenshots on errors
  screenshot_dir: "./screenshots"
  cookies_file: "./data/cookies/webull_cookies.pkl"
  session_timeout: 3600        # Session timeout (1 hour)
```

### Stealth Settings

```yaml
browser:
  disable_automation_flags: true  # Hide automation flags
  disable_dev_shm_usage: true     # Disable /dev/shm usage
  no_sandbox: false               # Disable sandbox (Docker)
```

### Trading Settings

```yaml
webull:
  paper_trading: true          # Paper trading mode
  default_order_type: "market" # Default order type
  max_position_size: 100       # Maximum position size
  stop_loss_percent: -2.0      # Stop loss percentage
  take_profit_percent: 5.0     # Take profit percentage
  loop_interval: 60            # Trading loop interval
  min_confidence: 0.7          # Minimum ML confidence
```

### Integration Settings

```yaml
integration:
  use_enhanced_secrets: true           # Use secure credential storage
  mfa_required: true                   # Require MFA
  enable_metrics: true                 # Enable monitoring
  prometheus_port: 9090                # Metrics port
  enable_compliance_checks: true       # Enable compliance
  pdt_monitoring: true                 # PDT rule monitoring
  wash_sale_detection: true            # Wash sale detection
  enable_dead_mans_switch: true        # Dead man's switch
  heartbeat_interval: 300              # Heartbeat interval (5 min)
  enable_microstructure_analysis: true # Market analysis
  order_flow_analysis: true            # Order flow analysis
  enable_performance_optimization: true # Performance optimization
  optimization_level: "high"           # Optimization level
```

## 🧪 Testing

### Test Browser Functionality

```bash
python test_browser_automation.py
```

This tests:
- Browser initialization
- Navigation to Webull
- Market data extraction
- Order form interaction

### Test Production Integration

```bash
# Test with all systems enabled
python launch_enhanced_browser_bot.py
# Select 'development' environment
# Test login and basic functionality
```

## 🚀 Deployment

### Development Environment

```bash
# Start with development settings
python launch_enhanced_browser_bot.py
# Select: development
# Paper trading: enabled
# All safety systems: enabled
```

### Staging Environment

```bash
# Deploy to staging
python deploy.py staging

# Or manual launch
python launch_enhanced_browser_bot.py
# Select: staging
# Paper trading: enabled
# Production monitoring: enabled
```

### Production Environment

```bash
# Deploy to production (requires confirmation)
python deploy.py production

# Or manual launch with safety checks
python launch_enhanced_browser_bot.py
# Select: production
# Confirm: CONFIRM PRODUCTION
# Live trading: requires additional confirmation
```

## ⚠️ Safety Considerations

### Always Start with Paper Trading

```yaml
webull:
  paper_trading: true  # NEVER set to false initially
```

### Monitor System Health

- Check Grafana dashboard regularly
- Monitor error rates and execution quality
- Verify compliance violations are zero
- Ensure dead man's switch is active

### Emergency Procedures

```bash
# Emergency stop
curl -X POST http://localhost:8000/emergency/kill

# Graceful shutdown
# Press Ctrl+C in terminal

# Check system status
curl http://localhost:8000/health
```

## 📊 Monitoring URLs

- **Grafana Dashboard**: http://localhost:3000
- **Prometheus Metrics**: http://localhost:9090
- **Trading Bot API**: http://localhost:8000
- **Health Check**: http://localhost:8000/health

## 🔍 Troubleshooting

### Common Issues

1. **Chrome not found**
   ```bash
   # Install Google Chrome
   # Windows: Download from google.com/chrome
   # macOS: brew install --cask google-chrome
   # Linux: sudo apt-get install google-chrome-stable
   ```

2. **Tesseract not found**
   ```bash
   # Install Tesseract OCR
   # Windows: Download from GitHub
   # macOS: brew install tesseract
   # Linux: sudo apt-get install tesseract-ocr
   ```

3. **Login failures**
   - Check credentials in secure storage
   - Verify MFA code if required
   - Check for Webull security challenges
   - Review browser screenshots for errors

4. **Order placement failures**
   - Verify paper trading mode
   - Check position limits
   - Review compliance violations
   - Monitor execution quality metrics

### Debug Mode

```yaml
browser:
  headless: false          # See browser actions
  screenshot_on_error: true # Capture error screenshots

integration:
  enable_metrics: true     # Monitor all metrics
```

## 📝 Best Practices

1. **Always test thoroughly in paper trading mode**
2. **Monitor all systems during operation**
3. **Keep browser automation stealthy**
4. **Use secure credential storage**
5. **Enable all safety systems**
6. **Start with small position sizes**
7. **Monitor compliance violations**
8. **Have emergency procedures ready**

## 🎯 Next Steps

1. **Run setup**: `python setup_browser_automation.py`
2. **Test functionality**: `python test_browser_automation.py`
3. **Configure credentials**: Store in secure manager
4. **Start paper trading**: `python launch_enhanced_browser_bot.py`
5. **Monitor performance**: Check Grafana dashboards
6. **Scale gradually**: Increase position sizes slowly

Remember: **Browser automation is powerful but requires careful monitoring and testing!**
