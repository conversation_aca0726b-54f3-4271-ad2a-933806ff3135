#!/usr/bin/env python3
"""
Continuous ML Training System
90-day rolling window training with self-improvement
"""

import asyncio
import sys
import os
import schedule
import time
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
import pickle
from typing import Dict, List, Any, Optional
import yfinance as yf
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('continuous_ml_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ContinuousMLTrainer:
    """Continuous ML trainer with 90-day rolling windows."""
    
    def __init__(self, 
                 symbols: List[str] = None,
                 training_window_days: int = 90,
                 retrain_frequency_hours: int = 24,
                 models_dir: str = "ml_models",
                 data_dir: str = "ml_data",
                 performance_threshold: float = 0.55):
        
        self.symbols = symbols or [
            'AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN', 
            'META', 'NFLX', 'SPY', 'QQQ'
        ]
        
        self.training_window_days = training_window_days
        self.retrain_frequency_hours = retrain_frequency_hours
        self.models_dir = Path(models_dir)
        self.data_dir = Path(data_dir)
        self.performance_threshold = performance_threshold
        
        # Create directories
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Training state
        self.current_models = {}
        self.model_performance = {}
        self.training_history = []
        self.is_running = False
        
        # Model configurations
        self.model_configs = {
            'random_forest': {
                'n_estimators': 200,
                'max_depth': 15,
                'min_samples_split': 20,
                'min_samples_leaf': 10,
                'random_state': 42
            },
            'ensemble': {
                'n_models': 5,
                'diversity_threshold': 0.3
            }
        }
        
        logger.info(f"Initialized ContinuousMLTrainer with {len(self.symbols)} symbols")
        logger.info(f"Training window: {training_window_days} days")
        logger.info(f"Retrain frequency: {retrain_frequency_hours} hours")
    
    def calculate_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate advanced technical indicators."""
        
        # Basic price features
        df['returns'] = df['Close'].pct_change()
        df['log_returns'] = np.log(df['Close'] / df['Close'].shift(1))
        df['volatility'] = df['returns'].rolling(20).std()
        
        # Multiple timeframe returns
        for period in [1, 5, 10, 20, 50]:
            df[f'return_{period}d'] = df['Close'].pct_change(period)
            df[f'volatility_{period}d'] = df['returns'].rolling(period).std()
        
        # Moving averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'sma_{period}'] = df['Close'].rolling(period).mean()
            df[f'ema_{period}'] = df['Close'].ewm(span=period).mean()
        
        # Price ratios
        df['price_to_sma20'] = df['Close'] / df['sma_20']
        df['price_to_sma50'] = df['Close'] / df['sma_50']
        df['price_to_sma200'] = df['Close'] / df['sma_200']
        df['sma_ratio_20_50'] = df['sma_20'] / df['sma_50']
        df['sma_ratio_50_200'] = df['sma_50'] / df['sma_200']
        
        # RSI
        def calculate_rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
        
        df['rsi'] = calculate_rsi(df['Close'])
        df['rsi_14'] = calculate_rsi(df['Close'], 14)
        df['rsi_30'] = calculate_rsi(df['Close'], 30)
        
        # MACD
        def calculate_macd(prices, fast=12, slow=26, signal=9):
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            macd_signal = macd.ewm(span=signal).mean()
            macd_hist = macd - macd_signal
            return macd, macd_signal, macd_hist
        
        macd, macd_signal, macd_hist = calculate_macd(df['Close'])
        df['macd'] = macd
        df['macd_signal'] = macd_signal
        df['macd_hist'] = macd_hist
        
        # Bollinger Bands
        def calculate_bollinger_bands(prices, window=20, num_std=2):
            rolling_mean = prices.rolling(window=window).mean()
            rolling_std = prices.rolling(window=window).std()
            upper_band = rolling_mean + (rolling_std * num_std)
            lower_band = rolling_mean - (rolling_std * num_std)
            return upper_band, lower_band, rolling_mean
        
        bb_upper, bb_lower, bb_middle = calculate_bollinger_bands(df['Close'])
        df['bb_upper'] = bb_upper
        df['bb_lower'] = bb_lower
        df['bb_middle'] = bb_middle
        df['bb_width'] = (bb_upper - bb_lower) / bb_middle
        df['bb_position'] = (df['Close'] - bb_lower) / (bb_upper - bb_lower)
        
        # Volume features
        df['volume_sma20'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma20']
        df['volume_change'] = df['Volume'].pct_change()
        df['price_volume'] = df['Close'] * df['Volume']
        
        # High/Low features
        df['high_low_ratio'] = df['High'] / df['Low']
        df['close_high_ratio'] = df['Close'] / df['High']
        df['close_low_ratio'] = df['Close'] / df['Low']
        
        # Momentum indicators
        for period in [10, 20, 50]:
            df[f'momentum_{period}'] = df['Close'] / df['Close'].shift(period)
        
        # Stochastic oscillator
        def calculate_stochastic(high, low, close, k_window=14, d_window=3):
            lowest_low = low.rolling(window=k_window).min()
            highest_high = high.rolling(window=k_window).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_window).mean()
            return k_percent, d_percent
        
        stoch_k, stoch_d = calculate_stochastic(df['High'], df['Low'], df['Close'])
        df['stoch_k'] = stoch_k
        df['stoch_d'] = stoch_d
        
        # Market regime indicators
        df['trend_strength'] = abs(df['sma_20'] - df['sma_50']) / df['Close']
        df['volatility_regime'] = df['volatility_20d'] / df['volatility_50d']
        
        # Lag features
        for lag in [1, 2, 3, 5]:
            df[f'close_lag_{lag}'] = df['Close'].shift(lag)
            df[f'volume_lag_{lag}'] = df['Volume'].shift(lag)
            df[f'rsi_lag_{lag}'] = df['rsi'].shift(lag)
        
        # Create targets for different horizons
        for horizon in [1, 2, 3, 5]:
            df[f'target_{horizon}d'] = (df['Close'].shift(-horizon) > df['Close']).astype(int)
            df[f'return_target_{horizon}d'] = df['Close'].shift(-horizon) / df['Close'] - 1
        
        # Primary target (next day)
        df['target'] = df['target_1d']
        
        return df
    
    async def fetch_data(self, symbol: str, days: int = None) -> pd.DataFrame:
        """Fetch data for a symbol."""
        try:
            days = days or self.training_window_days + 50  # Extra buffer for indicators
            
            ticker = yf.Ticker(symbol)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            hist = ticker.history(start=start_date, end=end_date)
            
            if hist.empty:
                logger.warning(f"No data found for {symbol}")
                return pd.DataFrame()
            
            # Reset index to make Date a column
            df = hist.reset_index()
            df['symbol'] = symbol
            
            logger.info(f"Raw data for {symbol}: {len(df)} records")
            
            # Calculate features
            try:
                df = self.calculate_advanced_features(df)
                logger.info(f"After feature engineering: {len(df)} records")
            except Exception as e:
                logger.error(f"Error in feature engineering for {symbol}: {e}")
                return pd.DataFrame()
            
            # Remove rows with NaN values in critical columns
            critical_cols = ['Close', 'Volume', 'target']
            df_clean = df.dropna(subset=critical_cols)
            logger.info(f"After cleaning critical NaN: {len(df_clean)} records")
            
            # Fill remaining NaN values with forward fill then backward fill
            df_clean = df_clean.fillna(method='ffill').fillna(method='bfill')
            logger.info(f"After filling remaining NaN: {len(df_clean)} records")
            
            if len(df_clean) < 50:
                logger.warning(f"Insufficient clean data for {symbol}: {len(df_clean)} records")
                return pd.DataFrame()
            
            logger.info(f"✅ Fetched {len(df_clean)} clean records for {symbol}")
            return df_clean
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    async def prepare_training_data(self, symbols: List[str] = None) -> pd.DataFrame:
        """Prepare training data for all symbols."""
        symbols = symbols or self.symbols
        
        logger.info(f"Preparing training data for {len(symbols)} symbols...")
        
        all_data = []
        
        for symbol in symbols:
            df = await self.fetch_data(symbol)
            if not df.empty:
                all_data.append(df)
        
        if not all_data:
            raise Exception("No training data collected")
        
        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # Add symbol encoding
        symbol_encoder = {symbol: i for i, symbol in enumerate(symbols)}
        combined_df['symbol_encoded'] = combined_df['symbol'].map(symbol_encoder)
        
        # Sort by date
        combined_df = combined_df.sort_values('Date').reset_index(drop=True)
        
        # Keep only last N days
        cutoff_date = pd.Timestamp.now() - timedelta(days=self.training_window_days)
        # Make cutoff_date timezone-aware to match the data
        if combined_df['Date'].dt.tz is not None:
            cutoff_date = cutoff_date.tz_localize(combined_df['Date'].dt.tz)
        combined_df = combined_df[combined_df['Date'] >= cutoff_date]
        
        logger.info(f"Prepared {len(combined_df)} total training records")
        return combined_df
    
    def get_feature_columns(self, df: pd.DataFrame) -> List[str]:
        """Get feature columns for training."""
        exclude_cols = [
            'Date', 'symbol', 'Open', 'High', 'Low', 'Close', 'Volume',
            'Dividends', 'Stock Splits', 'target', 'target_1d', 'target_2d', 
            'target_3d', 'target_5d', 'return_target_1d', 'return_target_2d',
            'return_target_3d', 'return_target_5d'
        ]
        
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        return feature_cols
    
    def create_ensemble_model(self, n_models: int = 5) -> List[RandomForestClassifier]:
        """Create ensemble of models with different configurations."""
        models = []
        
        base_config = self.model_configs['random_forest']
        
        for i in range(n_models):
            # Vary parameters slightly for diversity
            config = base_config.copy()
            config['n_estimators'] = base_config['n_estimators'] + np.random.randint(-50, 50)
            config['max_depth'] = base_config['max_depth'] + np.random.randint(-3, 3)
            config['min_samples_split'] = base_config['min_samples_split'] + np.random.randint(-5, 5)
            config['random_state'] = base_config['random_state'] + i
            
            model = RandomForestClassifier(**config)
            models.append(model)
        
        return models
    
    async def train_model(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """Train ML model with current data."""
        
        logger.info("Starting model training...")
        
        # Prepare features and target
        feature_cols = self.get_feature_columns(training_data)
        X = training_data[feature_cols]
        y = training_data['target']
        
        # Handle infinite values
        X = X.replace([np.inf, -np.inf], np.nan)
        X = X.fillna(X.mean())
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Create and train ensemble
        models = self.create_ensemble_model()
        trained_models = []
        
        for i, model in enumerate(models):
            model.fit(X_train_scaled, y_train)
            trained_models.append(model)
            
            # Evaluate individual model
            train_score = model.score(X_train_scaled, y_train)
            test_score = model.score(X_test_scaled, y_test)
            
            logger.info(f"Model {i+1}: Train={train_score:.4f}, Test={test_score:.4f}")
        
        # Ensemble predictions
        train_predictions = np.array([model.predict(X_train_scaled) for model in trained_models])
        test_predictions = np.array([model.predict(X_test_scaled) for model in trained_models])
        
        # Majority voting
        ensemble_train_pred = np.round(train_predictions.mean(axis=0)).astype(int)
        ensemble_test_pred = np.round(test_predictions.mean(axis=0)).astype(int)
        
        # Calculate metrics
        train_accuracy = accuracy_score(y_train, ensemble_train_pred)
        test_accuracy = accuracy_score(y_test, ensemble_test_pred)
        
        # Cross-validation
        cv_scores = []
        for model in trained_models:
            scores = cross_val_score(model, X_train_scaled, y_train, cv=5)
            cv_scores.extend(scores)
        
        cv_mean = np.mean(cv_scores)
        cv_std = np.std(cv_scores)
        
        # Feature importance (average across models)
        feature_importance = np.mean([model.feature_importances_ for model in trained_models], axis=0)
        feature_importance_df = pd.DataFrame({
            'feature': feature_cols,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        # Training results
        results = {
            'timestamp': datetime.now().isoformat(),
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'features': len(feature_cols),
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'cv_mean': cv_mean,
            'cv_std': cv_std,
            'feature_importance': feature_importance_df.head(20).to_dict('records'),
            'models': trained_models,
            'scaler': scaler,
            'feature_cols': feature_cols,
            'ensemble_size': len(trained_models)
        }
        
        logger.info(f"Training completed: Test accuracy = {test_accuracy:.4f}")
        return results
    
    async def evaluate_model_performance(self, model_results: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate model performance on recent data."""
        
        logger.info("Evaluating model performance...")
        
        # Get fresh data for evaluation
        eval_data = await self.prepare_training_data()
        
        if eval_data.empty:
            return {'error': 'No evaluation data available'}
        
        # Use most recent data for evaluation
        eval_data = eval_data.tail(1000)  # Last 1000 records
        
        # Prepare features
        feature_cols = model_results['feature_cols']
        X_eval = eval_data[feature_cols]
        y_eval = eval_data['target']
        
        # Handle missing values
        X_eval = X_eval.replace([np.inf, -np.inf], np.nan)
        X_eval = X_eval.fillna(X_eval.mean())
        
        # Scale features
        scaler = model_results['scaler']
        X_eval_scaled = scaler.transform(X_eval)
        
        # Make predictions
        models = model_results['models']
        predictions = np.array([model.predict(X_eval_scaled) for model in models])
        ensemble_pred = np.round(predictions.mean(axis=0)).astype(int)
        
        # Calculate metrics
        accuracy = accuracy_score(y_eval, ensemble_pred)
        
        # Calculate performance by symbol
        symbol_performance = {}
        for symbol in eval_data['symbol'].unique():
            symbol_mask = eval_data['symbol'] == symbol
            if symbol_mask.sum() > 0:
                symbol_acc = accuracy_score(y_eval[symbol_mask], ensemble_pred[symbol_mask])
                symbol_performance[symbol] = symbol_acc
        
        evaluation_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_accuracy': accuracy,
            'evaluation_samples': len(X_eval),
            'symbol_performance': symbol_performance,
            'prediction_distribution': {
                'buy_signals': int(ensemble_pred.sum()),
                'sell_signals': int(len(ensemble_pred) - ensemble_pred.sum())
            }
        }
        
        logger.info(f"Evaluation completed: Accuracy = {accuracy:.4f}")
        return evaluation_results
    
    async def save_model(self, model_results: Dict[str, Any], performance: Dict[str, Any]):
        """Save trained model and performance metrics."""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save model
        model_path = self.models_dir / f"model_{timestamp}.pkl"
        with open(model_path, 'wb') as f:
            pickle.dump(model_results, f)
        
        # Save performance
        performance_path = self.models_dir / f"performance_{timestamp}.json"
        with open(performance_path, 'w') as f:
            json.dump(performance, f, indent=2)
        
        # Update current model reference
        current_model_path = self.models_dir / "current_model.pkl"
        with open(current_model_path, 'wb') as f:
            pickle.dump(model_results, f)
        
        logger.info(f"Model saved: {model_path}")
        
        # Track in history
        self.training_history.append({
            'timestamp': timestamp,
            'model_path': str(model_path),
            'performance': performance,
            'accuracy': performance.get('overall_accuracy', 0)
        })
        
        # Keep only last 30 training records
        self.training_history = self.training_history[-30:]
    
    async def load_current_model(self) -> Optional[Dict[str, Any]]:
        """Load the current model."""
        
        current_model_path = self.models_dir / "current_model.pkl"
        
        if current_model_path.exists():
            try:
                with open(current_model_path, 'rb') as f:
                    model_results = pickle.load(f)
                logger.info("Current model loaded successfully")
                return model_results
            except Exception as e:
                logger.error(f"Error loading current model: {e}")
        
        return None
    
    async def single_training_cycle(self):
        """Execute a single training cycle."""
        
        logger.info("=" * 60)
        logger.info("🎯 Starting Training Cycle")
        logger.info("=" * 60)
        
        try:
            # Prepare training data
            training_data = await self.prepare_training_data()
            
            if len(training_data) < 50:
                logger.warning(f"Insufficient training data: {len(training_data)} samples")
                return
            
            # Train model
            model_results = await self.train_model(training_data)
            
            # Evaluate performance
            performance = await self.evaluate_model_performance(model_results)
            
            # Check if model meets performance threshold
            accuracy = performance.get('overall_accuracy', 0)
            
            if accuracy >= self.performance_threshold:
                logger.info(f"✅ Model meets performance threshold: {accuracy:.4f}")
                await self.save_model(model_results, performance)
                
                # Update current model
                self.current_models['main'] = model_results
                self.model_performance['main'] = performance
                
            else:
                logger.warning(f"⚠️ Model below threshold: {accuracy:.4f} < {self.performance_threshold}")
            
            # Log training summary
            logger.info(f"Training Summary:")
            logger.info(f"  Data samples: {len(training_data):,}")
            logger.info(f"  Features: {model_results['features']}")
            logger.info(f"  Test accuracy: {model_results['test_accuracy']:.4f}")
            logger.info(f"  Evaluation accuracy: {accuracy:.4f}")
            logger.info(f"  Ensemble size: {model_results['ensemble_size']}")
            
        except Exception as e:
            logger.error(f"Error in training cycle: {e}")
            import traceback
            traceback.print_exc()
    
    def schedule_training(self):
        """Schedule periodic training."""
        
        # Schedule training every N hours
        schedule.every(self.retrain_frequency_hours).hours.do(
            lambda: asyncio.run(self.single_training_cycle())
        )
        
        logger.info(f"Training scheduled every {self.retrain_frequency_hours} hours")
    
    async def run_continuous_training(self):
        """Run continuous training loop."""
        
        logger.info("🚀 Starting Continuous ML Training System")
        logger.info(f"Symbols: {self.symbols}")
        logger.info(f"Training window: {self.training_window_days} days")
        logger.info(f"Retrain frequency: {self.retrain_frequency_hours} hours")
        
        self.is_running = True
        
        # Run initial training
        await self.single_training_cycle()
        
        # Schedule periodic training
        self.schedule_training()
        
        # Keep the training loop running
        while self.is_running:
            schedule.run_pending()
            await asyncio.sleep(60)  # Check every minute
    
    def stop_training(self):
        """Stop continuous training."""
        self.is_running = False
        logger.info("Continuous training stopped")
    
    def get_training_status(self) -> Dict[str, Any]:
        """Get current training status."""
        
        return {
            'is_running': self.is_running,
            'symbols': self.symbols,
            'training_window_days': self.training_window_days,
            'retrain_frequency_hours': self.retrain_frequency_hours,
            'current_models': len(self.current_models),
            'training_history': len(self.training_history),
            'last_training': self.training_history[-1] if self.training_history else None,
            'performance_threshold': self.performance_threshold
        }


async def main():
    """Main function to run continuous training."""
    
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║              Continuous ML Training System                   ║
    ║                                                              ║
    ║  🤖 90-day rolling window training                          ║
    ║  🔄 Self-improving ML models                                ║
    ║  📊 Ensemble learning with feature engineering             ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Initialize trainer
    trainer = ContinuousMLTrainer(
        symbols=['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN'],
        training_window_days=90,
        retrain_frequency_hours=24,
        performance_threshold=0.55
    )
    
    try:
        # Start continuous training
        await trainer.run_continuous_training()
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        trainer.stop_training()
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())