apiVersion: v1
kind: ConfigMap
metadata:
  name: trading-bot-config
  namespace: trading-bot
  labels:
    app: ai-trading-bot
    component: config
data:
  # Application Configuration
  app.yaml: |
    app:
      name: "AI Trading Bot"
      version: "1.0.0"
      environment: "production"
      debug: false
      
    logging:
      level: "INFO"
      format: "json"
      rotation: "1 day"
      retention: "30 days"
      
    api:
      host: "0.0.0.0"
      port: 8000
      workers: 4
      timeout: 30
      
    trading:
      mode: "live"
      max_positions: 20
      max_daily_trades: 100
      position_check_interval: 30
      
    risk:
      max_daily_loss: 0.02
      max_position_size: 0.05
      max_drawdown_threshold: 0.10
      emergency_stop_loss: 0.05
      
    ml:
      inference_batch_size: 32
      model_update_frequency: "daily"
      prediction_frequency: 60
      confidence_threshold: 0.7
      
    monitoring:
      metrics_port: 9090
      health_check_interval: 30
      performance_update_interval: 300
      heartbeat_interval: 30

  # Database Configuration
  database.yaml: |
    postgres:
      host: "postgres-service"
      port: 5432
      database: "trading_bot"
      username: "trading_bot"
      pool_size: 20
      max_overflow: 30
      pool_timeout: 30
      pool_recycle: 3600
      
    redis:
      host: "redis-service"
      port: 6379
      db: 0
      max_connections: 50
      socket_timeout: 5
      socket_connect_timeout: 5
      
    mongodb:
      host: "mongodb-service"
      port: 27017
      database: "trading_bot"
      username: "trading_bot"
      max_pool_size: 100
      min_pool_size: 10

  # Prometheus Configuration
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      
    rule_files:
      - "alert_rules.yml"
      
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
              
    scrape_configs:
      - job_name: 'trading-bot'
        static_configs:
          - targets: ['trading-bot-service:9090']
        scrape_interval: 15s
        metrics_path: /metrics
        
      - job_name: 'postgres'
        static_configs:
          - targets: ['postgres-exporter:9187']
          
      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']
          
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - trading-bot
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true

  # Alert Rules
  alert_rules.yml: |
    groups:
      - name: trading_bot_alerts
        rules:
          - alert: TradingBotDown
            expr: up{job="trading-bot"} == 0
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: "Trading Bot is down"
              description: "Trading Bot has been down for more than 1 minute"
              
          - alert: HighErrorRate
            expr: rate(trading_bot_errors_total[5m]) > 0.1
            for: 2m
            labels:
              severity: warning
            annotations:
              summary: "High error rate detected"
              description: "Error rate is {{ $value }} errors per second"
              
          - alert: HighLatency
            expr: trading_bot_request_duration_seconds{quantile="0.95"} > 1
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High latency detected"
              description: "95th percentile latency is {{ $value }} seconds"
              
          - alert: MaxDrawdownExceeded
            expr: trading_bot_current_drawdown > 0.05
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: "Maximum drawdown exceeded"
              description: "Current drawdown is {{ $value | humanizePercentage }}"
              
          - alert: DailyLossLimitExceeded
            expr: trading_bot_daily_pnl < -2000
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: "Daily loss limit exceeded"
              description: "Daily P&L is {{ $value }}"

  # Nginx Configuration
  nginx.conf: |
    upstream trading_bot_backend {
        least_conn;
        server trading-bot-service:8000 max_fails=3 fail_timeout=30s;
        server trading-bot-standby-service:8000 backup;
    }
    
    server {
        listen 80;
        server_name _;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # API endpoints
        location /api/ {
            proxy_pass http://trading_bot_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_timeout 30s;
            proxy_read_timeout 30s;
            proxy_connect_timeout 10s;
        }
        
        # WebSocket endpoints
        location /ws/ {
            proxy_pass http://trading_bot_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Metrics endpoint (internal only)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            proxy_pass http://trading_bot_backend;
        }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: trading-bot
  labels:
    app: postgres
    component: config
data:
  postgresql.conf: |
    # Connection settings
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    
    # Memory settings
    shared_buffers = 256MB
    effective_cache_size = 1GB
    work_mem = 4MB
    maintenance_work_mem = 64MB
    
    # WAL settings
    wal_level = replica
    max_wal_size = 1GB
    min_wal_size = 80MB
    checkpoint_completion_target = 0.9
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
    
    # Performance
    random_page_cost = 1.1
    effective_io_concurrency = 200
    
  pg_hba.conf: |
    # TYPE  DATABASE        USER            ADDRESS                 METHOD
    local   all             all                                     trust
    host    all             all             127.0.0.1/32            scram-sha-256
    host    all             all             ::1/128                 scram-sha-256
    host    all             all             10.0.0.0/8              scram-sha-256
    host    all             all             **********/12           scram-sha-256
    host    all             all             ***********/16          scram-sha-256

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: trading-bot
  labels:
    app: redis
    component: config
data:
  redis.conf: |
    # Network
    bind 0.0.0.0
    port 6379
    timeout 300
    tcp-keepalive 60
    
    # General
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    
    # Snapshotting
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir ./
    
    # Replication
    replica-serve-stale-data yes
    replica-read-only yes
    
    # Security
    requirepass ${REDIS_PASSWORD}
    
    # Memory management
    maxmemory 1gb
    maxmemory-policy allkeys-lru
    
    # Append only file
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
