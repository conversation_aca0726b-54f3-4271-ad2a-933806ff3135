"""Configuration management for the trading bot."""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic_settings import BaseSettings
from pydantic import Field, field_validator


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    # PostgreSQL (Primary database)
    postgres_host: str = Field(default="localhost", env="POSTGRES_HOST")
    postgres_port: int = Field(default=5432, env="POSTGRES_PORT")
    postgres_user: str = Field(default="trading_bot", env="POSTGRES_USER")
    postgres_password: str = Field(default="", env="POSTGRES_PASSWORD")
    postgres_database: str = Field(default="trading_bot", env="POSTGRES_DATABASE")
    
    # Redis (Caching and real-time data)
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # MongoDB (Unstructured data)
    mongo_host: str = Field(default="localhost", env="MONGO_HOST")
    mongo_port: int = Field(default=27017, env="MONGO_PORT")
    mongo_user: Optional[str] = Field(default=None, env="MONGO_USER")
    mongo_password: Optional[str] = Field(default=None, env="MONGO_PASSWORD")
    mongo_database: str = Field(default="trading_bot", env="MONGO_DATABASE")
    
    @property
    def postgres_url(self) -> str:
        """Get PostgreSQL connection URL."""
        return (
            f"postgresql+asyncpg://{self.postgres_user}:{self.postgres_password}"
            f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_database}"
        )
    
    @property
    def redis_url(self) -> str:
        """Get Redis connection URL."""
        auth = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth}{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    @property
    def mongo_url(self) -> str:
        """Get MongoDB connection URL."""
        if self.mongo_user and self.mongo_password:
            auth = f"{self.mongo_user}:{self.mongo_password}@"
        else:
            auth = ""
        return f"mongodb://{auth}{self.mongo_host}:{self.mongo_port}/{self.mongo_database}"


class WebullSettings(BaseSettings):
    """Webull API configuration settings."""
    
    username: str = Field(default="", env="WEBULL_USERNAME")
    password: str = Field(default="", env="WEBULL_PASSWORD")
    device_id: Optional[str] = Field(default=None, env="WEBULL_DEVICE_ID")
    trade_token: Optional[str] = Field(default=None, env="WEBULL_TRADE_TOKEN")
    access_token: Optional[str] = Field(default=None, env="WEBULL_ACCESS_TOKEN")
    refresh_token: Optional[str] = Field(default=None, env="WEBULL_REFRESH_TOKEN")
    
    # Paper trading settings
    paper_trading: bool = Field(default=True, env="WEBULL_PAPER_TRADING")
    paper_account_id: Optional[str] = Field(default=None, env="WEBULL_PAPER_ACCOUNT_ID")
    
    # Rate limiting
    max_requests_per_minute: int = Field(default=60, env="WEBULL_MAX_REQUESTS_PER_MINUTE")
    request_delay: float = Field(default=1.0, env="WEBULL_REQUEST_DELAY")


class RiskSettings(BaseSettings):
    """Risk management configuration settings."""
    
    # Position sizing
    max_position_size: float = Field(default=0.05, env="RISK_MAX_POSITION_SIZE")  # 5% of portfolio
    max_portfolio_risk: float = Field(default=0.02, env="RISK_MAX_PORTFOLIO_RISK")  # 2% per trade
    kelly_fraction: float = Field(default=0.25, env="RISK_KELLY_FRACTION")  # Conservative Kelly
    
    # Drawdown controls
    max_daily_drawdown: float = Field(default=0.02, env="RISK_MAX_DAILY_DRAWDOWN")  # 2%
    max_weekly_drawdown: float = Field(default=0.05, env="RISK_MAX_WEEKLY_DRAWDOWN")  # 5%
    max_monthly_drawdown: float = Field(default=0.10, env="RISK_MAX_MONTHLY_DRAWDOWN")  # 10%
    
    # Stop loss settings
    default_stop_loss: float = Field(default=0.02, env="RISK_DEFAULT_STOP_LOSS")  # 2%
    trailing_stop_distance: float = Field(default=0.015, env="RISK_TRAILING_STOP_DISTANCE")  # 1.5%
    
    # Correlation limits
    max_correlation: float = Field(default=0.7, env="RISK_MAX_CORRELATION")
    max_sector_exposure: float = Field(default=0.3, env="RISK_MAX_SECTOR_EXPOSURE")  # 30%
    
    @field_validator("max_position_size", "max_portfolio_risk", "max_daily_drawdown")
    @classmethod
    def validate_percentages(cls, v):
        if not 0 < v <= 1:
            raise ValueError("Risk percentages must be between 0 and 1")
        return v


class MLSettings(BaseSettings):
    """Machine learning configuration settings."""
    
    # Model training
    train_test_split: float = Field(default=0.8, env="ML_TRAIN_TEST_SPLIT")
    validation_split: float = Field(default=0.2, env="ML_VALIDATION_SPLIT")
    random_seed: int = Field(default=42, env="ML_RANDOM_SEED")
    
    # Feature engineering
    lookback_periods: List[int] = Field(default=[5, 10, 20, 50, 200], env="ML_LOOKBACK_PERIODS")
    technical_indicators: List[str] = Field(
        default=[
            "sma", "ema", "rsi", "macd", "bollinger_bands", "atr", "stochastic",
            "williams_r", "cci", "adx", "obv", "vwap", "momentum", "roc"
        ],
        env="ML_TECHNICAL_INDICATORS"
    )
    
    # Model parameters
    ensemble_models: List[str] = Field(
        default=["xgboost", "lightgbm", "lstm", "transformer"],
        env="ML_ENSEMBLE_MODELS"
    )
    model_retrain_frequency: str = Field(default="daily", env="ML_MODEL_RETRAIN_FREQUENCY")
    
    # Performance thresholds
    min_sharpe_ratio: float = Field(default=1.5, env="ML_MIN_SHARPE_RATIO")
    min_win_rate: float = Field(default=0.55, env="ML_MIN_WIN_RATE")
    min_profit_factor: float = Field(default=1.5, env="ML_MIN_PROFIT_FACTOR")


class Settings(BaseSettings):
    """Main application settings."""
    
    # Application info
    app_name: str = Field(default="AI Trading Bot", env="APP_NAME")
    app_version: str = Field(default="0.1.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    log_rotation: str = Field(default="1 day", env="LOG_ROTATION")
    log_retention: str = Field(default="30 days", env="LOG_RETENTION")
    
    # API settings
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_workers: int = Field(default=1, env="API_WORKERS")
    
    # Data settings
    data_directory: Path = Field(default=Path("data"), env="DATA_DIRECTORY")
    cache_directory: Path = Field(default=Path("cache"), env="CACHE_DIRECTORY")
    model_directory: Path = Field(default=Path("models"), env="MODEL_DIRECTORY")
    
    # Trading settings
    trading_enabled: bool = Field(default=False, env="TRADING_ENABLED")
    trading_hours_start: str = Field(default="09:30", env="TRADING_HOURS_START")
    trading_hours_end: str = Field(default="16:00", env="TRADING_HOURS_END")
    timezone: str = Field(default="US/Eastern", env="TIMEZONE")
    
    # Subsystem settings
    database: DatabaseSettings = DatabaseSettings()
    webull: WebullSettings = WebullSettings()
    risk: RiskSettings = RiskSettings()
    ml: MLSettings = MLSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    @field_validator("data_directory", "cache_directory", "model_directory")
    @classmethod
    def create_directories(cls, v):
        """Create directories if they don't exist."""
        v.mkdir(parents=True, exist_ok=True)
        return v
    
    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()
    
    def get_database_url(self, db_type: str = "postgres") -> str:
        """Get database connection URL."""
        if db_type == "postgres":
            return self.database.postgres_url
        elif db_type == "redis":
            return self.database.redis_url
        elif db_type == "mongo":
            return self.database.mongo_url
        else:
            raise ValueError(f"Unknown database type: {db_type}")


# Global settings instance
settings = Settings()
