"""Position sizing engine with multiple algorithms."""

import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import Order, Portfolio, Position, Symbol, TechnicalIndicator

logger = get_logger(__name__)


class PositionSizer:
    """Advanced position sizing engine with multiple algorithms."""
    
    def __init__(self):
        self.config = settings.risk
        self.portfolio_value = 100000.0  # Default portfolio value
        self.risk_free_rate = 0.02  # 2% risk-free rate
        
    async def initialize(self, portfolio_value: float):
        """Initialize position sizer with current portfolio value."""
        self.portfolio_value = portfolio_value
        logger.info(f"Position sizer initialized with portfolio value: ${portfolio_value:,.2f}")
    
    def calculate_kelly_position_size(
        self,
        symbol: str,
        entry_price: float,
        stop_loss: float,
        win_rate: float,
        avg_win: float,
        avg_loss: float,
        safety_factor: float = 0.25
    ) -> int:
        """
        Calculate position size using Kelly Criterion with safety factor.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price
            stop_loss: Stop loss price
            win_rate: Historical win rate (0-1)
            avg_win: Average win amount
            avg_loss: Average loss amount
            safety_factor: Safety factor to reduce Kelly fraction (default 0.25)
            
        Returns:
            Position size in shares
        """
        try:
            if avg_loss <= 0 or win_rate <= 0 or win_rate >= 1:
                logger.warning(f"Invalid Kelly parameters for {symbol}, using basic sizing")
                return self._calculate_basic_position_size(symbol, entry_price, stop_loss)
            
            # Calculate Kelly fraction
            win_loss_ratio = avg_win / avg_loss
            kelly_fraction = win_rate - ((1 - win_rate) / win_loss_ratio)
            
            # Apply safety factor and cap at reasonable limits
            kelly_fraction = max(0.0, min(kelly_fraction * safety_factor, 0.25))
            
            # Calculate position size
            risk_per_share = abs(entry_price - stop_loss)
            if risk_per_share <= 0:
                return 0
            
            kelly_amount = self.portfolio_value * kelly_fraction
            position_size = int(kelly_amount / entry_price)
            
            # Apply maximum position size limit
            max_position_value = self.portfolio_value * self.config.max_position_size
            max_position_size = int(max_position_value / entry_price)
            position_size = min(position_size, max_position_size)
            
            logger.info(
                f"Kelly position size for {symbol}: {position_size} shares "
                f"(Kelly fraction: {kelly_fraction:.4f}, Value: ${position_size * entry_price:,.2f})"
            )
            
            return max(position_size, 1)  # Minimum 1 share
            
        except Exception as e:
            logger.error(f"Error calculating Kelly position size for {symbol}: {e}")
            return self._calculate_basic_position_size(symbol, entry_price, stop_loss)
    
    async def calculate_volatility_adjusted_size(
        self,
        symbol: str,
        entry_price: float,
        stop_loss: float,
        lookback_days: int = 20
    ) -> int:
        """
        Calculate position size adjusted for volatility using ATR.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price
            stop_loss: Stop loss price
            lookback_days: Days to look back for volatility calculation
            
        Returns:
            Position size in shares
        """
        try:
            # Get ATR for volatility measurement
            atr = await self._get_atr(symbol, lookback_days)
            if not atr:
                logger.warning(f"No ATR data for {symbol}, using basic sizing")
                return self._calculate_basic_position_size(symbol, entry_price, stop_loss)
            
            # Calculate volatility-adjusted risk
            base_risk = abs(entry_price - stop_loss)
            volatility_multiplier = min(2.0, max(0.5, atr / (entry_price * 0.02)))  # Normalize ATR
            
            adjusted_risk = base_risk * volatility_multiplier
            max_risk_amount = self.portfolio_value * self.config.max_portfolio_risk
            
            position_size = int(max_risk_amount / adjusted_risk)
            
            # Apply maximum position size limit
            max_position_value = self.portfolio_value * self.config.max_position_size
            max_position_size = int(max_position_value / entry_price)
            position_size = min(position_size, max_position_size)
            
            logger.info(
                f"Volatility-adjusted position size for {symbol}: {position_size} shares "
                f"(ATR: {atr:.4f}, Volatility multiplier: {volatility_multiplier:.2f})"
            )
            
            return max(position_size, 1)
            
        except Exception as e:
            logger.error(f"Error calculating volatility-adjusted position size for {symbol}: {e}")
            return self._calculate_basic_position_size(symbol, entry_price, stop_loss)
    
    async def calculate_risk_parity_size(
        self,
        symbol: str,
        entry_price: float,
        current_positions: Dict[str, Dict],
        target_risk_contribution: float = None
    ) -> int:
        """
        Calculate position size for risk parity allocation.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price
            current_positions: Current portfolio positions
            target_risk_contribution: Target risk contribution (default: equal weight)
            
        Returns:
            Position size in shares
        """
        try:
            if not current_positions:
                # First position, use basic sizing
                return self._calculate_basic_position_size(symbol, entry_price, entry_price * 0.98)
            
            # Calculate current portfolio risk
            total_portfolio_risk = 0.0
            position_risks = {}
            
            for pos_symbol, position in current_positions.items():
                volatility = await self._get_volatility(pos_symbol)
                if volatility:
                    position_value = position["quantity"] * position["current_price"]
                    position_risk = position_value * volatility
                    position_risks[pos_symbol] = position_risk
                    total_portfolio_risk += position_risk
            
            # Calculate target risk for new position
            num_positions = len(current_positions) + 1
            if target_risk_contribution is None:
                target_risk_contribution = 1.0 / num_positions
            
            target_risk = total_portfolio_risk * target_risk_contribution / (1 - target_risk_contribution)
            
            # Calculate position size based on target risk
            new_volatility = await self._get_volatility(symbol)
            if not new_volatility:
                return self._calculate_basic_position_size(symbol, entry_price, entry_price * 0.98)
            
            target_position_value = target_risk / new_volatility
            position_size = int(target_position_value / entry_price)
            
            # Apply limits
            max_position_value = self.portfolio_value * self.config.max_position_size
            max_position_size = int(max_position_value / entry_price)
            position_size = min(position_size, max_position_size)
            
            logger.info(
                f"Risk parity position size for {symbol}: {position_size} shares "
                f"(Target risk contribution: {target_risk_contribution:.2%})"
            )
            
            return max(position_size, 1)
            
        except Exception as e:
            logger.error(f"Error calculating risk parity position size for {symbol}: {e}")
            return self._calculate_basic_position_size(symbol, entry_price, entry_price * 0.98)
    
    def calculate_dynamic_size(
        self,
        symbol: str,
        entry_price: float,
        stop_loss: float,
        signal_strength: float,
        strategy_performance: Dict[str, float]
    ) -> int:
        """
        Calculate dynamic position size based on signal strength and strategy performance.
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price
            stop_loss: Stop loss price
            signal_strength: Signal strength (0-1)
            strategy_performance: Strategy performance metrics
            
        Returns:
            Position size in shares
        """
        try:
            # Base position size
            base_size = self._calculate_basic_position_size(symbol, entry_price, stop_loss)
            
            # Signal strength multiplier (0.5 to 1.5)
            signal_multiplier = 0.5 + signal_strength
            
            # Strategy performance multiplier
            win_rate = strategy_performance.get("win_rate", 0.5)
            profit_factor = strategy_performance.get("profit_factor", 1.0)
            sharpe_ratio = strategy_performance.get("sharpe_ratio", 0.0)
            
            # Performance score (0.5 to 1.5)
            performance_score = (
                (win_rate * 0.4) +
                (min(profit_factor / 2.0, 1.0) * 0.4) +
                (min(max(sharpe_ratio, 0) / 2.0, 1.0) * 0.2)
            )
            performance_multiplier = 0.5 + performance_score
            
            # Combined multiplier
            total_multiplier = signal_multiplier * performance_multiplier
            dynamic_size = int(base_size * total_multiplier)
            
            # Apply limits
            max_position_value = self.portfolio_value * self.config.max_position_size
            max_position_size = int(max_position_value / entry_price)
            dynamic_size = min(dynamic_size, max_position_size)
            
            logger.info(
                f"Dynamic position size for {symbol}: {dynamic_size} shares "
                f"(Signal: {signal_strength:.2f}, Performance: {performance_score:.2f}, "
                f"Multiplier: {total_multiplier:.2f})"
            )
            
            return max(dynamic_size, 1)
            
        except Exception as e:
            logger.error(f"Error calculating dynamic position size for {symbol}: {e}")
            return self._calculate_basic_position_size(symbol, entry_price, stop_loss)
    
    def _calculate_basic_position_size(self, symbol: str, entry_price: float, stop_loss: float) -> int:
        """Calculate basic position size based on fixed risk percentage."""
        try:
            risk_per_share = abs(entry_price - stop_loss)
            if risk_per_share <= 0:
                return 1
            
            max_risk_amount = self.portfolio_value * self.config.max_portfolio_risk
            position_size = int(max_risk_amount / risk_per_share)
            
            # Apply maximum position size limit
            max_position_value = self.portfolio_value * self.config.max_position_size
            max_position_size = int(max_position_value / entry_price)
            position_size = min(position_size, max_position_size)
            
            return max(position_size, 1)
            
        except Exception as e:
            logger.error(f"Error calculating basic position size for {symbol}: {e}")
            return 1
    
    async def _get_atr(self, symbol: str, lookback_days: int = 20) -> Optional[float]:
        """Get Average True Range for the symbol."""
        try:
            async with get_postgres_session() as session:
                # Get symbol ID
                stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                result = await session.execute(stmt)
                symbol_id = result.scalar_one_or_none()
                
                if not symbol_id:
                    return None
                
                # Get latest ATR value
                stmt = (
                    select(TechnicalIndicator.value)
                    .where(
                        and_(
                            TechnicalIndicator.symbol_id == symbol_id,
                            TechnicalIndicator.indicator_name == "atr"
                        )
                    )
                    .order_by(TechnicalIndicator.timestamp.desc())
                    .limit(1)
                )
                result = await session.execute(stmt)
                atr_value = result.scalar_one_or_none()
                
                return float(atr_value) if atr_value else None
                
        except Exception as e:
            logger.error(f"Error getting ATR for {symbol}: {e}")
            return None
    
    async def _get_volatility(self, symbol: str, lookback_days: int = 30) -> Optional[float]:
        """Get historical volatility for the symbol."""
        try:
            async with get_postgres_session() as session:
                # Get symbol ID
                stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                result = await session.execute(stmt)
                symbol_id = result.scalar_one_or_none()
                
                if not symbol_id:
                    return None
                
                # Get recent close prices for volatility calculation
                from ..data.models import Bar
                cutoff_date = datetime.utcnow() - timedelta(days=lookback_days)
                
                stmt = (
                    select(Bar.close)
                    .where(
                        and_(
                            Bar.symbol_id == symbol_id,
                            Bar.timeframe == "1d",
                            Bar.timestamp >= cutoff_date
                        )
                    )
                    .order_by(Bar.timestamp.asc())
                )
                result = await session.execute(stmt)
                prices = [float(row[0]) for row in result.fetchall()]
                
                if len(prices) < 10:  # Need at least 10 data points
                    return None
                
                # Calculate daily returns and volatility
                returns = np.diff(np.log(prices))
                volatility = np.std(returns) * np.sqrt(252)  # Annualized volatility
                
                return float(volatility)
                
        except Exception as e:
            logger.error(f"Error calculating volatility for {symbol}: {e}")
            return None
