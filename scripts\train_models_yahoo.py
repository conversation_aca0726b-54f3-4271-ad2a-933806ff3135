"""
Train ML Models using Yahoo Finance Data
This integrates with your existing ML pipeline
"""

import asyncio
import sys
from pathlib import Path
import pandas as pd
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.trading_bot.core.config import settings
from src.trading_bot.core.logger import get_logger
from src.trading_bot.data.yahoo_data_source import YahooDataSource
from src.trading_bot.ml.pipeline import TradingMLPipeline
from src.trading_bot.ml.features.feature_engineering import FeatureEngineering

logger = get_logger(__name__)


class YahooMLTrainer:
    """Train ML models using Yahoo Finance data."""
    
    def __init__(self):
        self.config = settings
        self.data_source = None
        self.ml_pipeline = None
        self.feature_engineer = FeatureEngineering()
        
    async def initialize(self):
        """Initialize data source and ML pipeline."""
        logger.info("Initializing Yahoo ML Trainer...")
        
        # Initialize data source
        self.data_source = YahooDataSource(self.config)
        if not await self.data_source.initialize():
            raise Exception("Failed to initialize Yahoo Finance data source")
        
        # Initialize ML pipeline
        self.ml_pipeline = TradingMLPipeline(config=self.config)
        
        logger.info("✅ Yahoo ML Trainer initialized")
    
    async def get_training_data(self, symbols: list, start_date: str, end_date: str) -> pd.DataFrame:
        """Get training data for the specified symbols and date range."""
        logger.info(f"Fetching training data for {len(symbols)} symbols from {start_date} to {end_date}")
        
        all_data = []
        
        for symbol in symbols:
            try:
                logger.info(f"Fetching data for {symbol}...")
                
                # Get historical bars (daily data for training)
                bars = await self.data_source.get_historical_bars(
                    symbol=symbol,
                    interval="1d",
                    period="2y"  # Get 2 years of data
                )
                
                if not bars:
                    logger.warning(f"No data found for {symbol}")
                    continue
                
                # Convert to DataFrame
                df_data = []
                for bar in bars:
                    df_data.append({
                        'timestamp': bar.timestamp,
                        'symbol': symbol,
                        'open': float(bar.open),
                        'high': float(bar.high),
                        'low': float(bar.low),
                        'close': float(bar.close),
                        'volume': bar.volume
                    })
                
                df = pd.DataFrame(df_data)
                df.set_index('timestamp', inplace=True)
                
                # Filter by date range
                start_dt = pd.to_datetime(start_date)
                end_dt = pd.to_datetime(end_date)
                df = df[(df.index >= start_dt) & (df.index <= end_dt)]
                
                if df.empty:
                    logger.warning(f"No data in date range for {symbol}")
                    continue
                
                # Get additional features from Yahoo
                symbol_info = await self.data_source.get_symbol_info(symbol)
                if symbol_info:
                    df['market_cap'] = symbol_info.market_cap or 0
                    df['sector'] = symbol_info.sector or 'Unknown'
                    df['industry'] = symbol_info.industry or 'Unknown'
                
                # Engineer features
                try:
                    features_df = self.feature_engineer.engineer_features(df, symbol=symbol)
                    all_data.append(features_df)
                    logger.info(f"✅ Processed {len(features_df)} records for {symbol}")
                except Exception as e:
                    logger.error(f"Error engineering features for {symbol}: {e}")
                    # Add basic data without advanced features
                    all_data.append(df)
                
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
                continue
        
        if not all_data:
            raise Exception("No training data collected")
        
        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        logger.info(f"✅ Combined training data: {len(combined_df)} total records")
        
        return combined_df
    
    async def train_models(self, symbols: list, start_date: str, end_date: str) -> dict:
        """Train all ML models with Yahoo Finance data."""
        logger.info("🎯 Starting ML model training with Yahoo Finance data...")
        
        try:
            # Get training data
            training_data = await self.get_training_data(symbols, start_date, end_date)
            
            if training_data.empty:
                raise Exception("No training data available")
            
            logger.info(f"Training with {len(training_data)} data points")
            
            # Train models using the ML pipeline
            results = {}
            
            # Define models to train
            models_to_train = ['lstm', 'xgboost', 'transformer']
            
            for model_name in models_to_train:
                try:
                    logger.info(f"Training {model_name} model...")
                    
                    # Train the model
                    model_results = await self.ml_pipeline.train_model(
                        model_name=model_name,
                        training_data=training_data,
                        symbols=symbols
                    )
                    
                    results[model_name] = model_results
                    logger.info(f"✅ {model_name} model training completed")
                    
                except Exception as e:
                    logger.error(f"Error training {model_name} model: {e}")
                    results[model_name] = {"error": str(e)}
            
            return results
            
        except Exception as e:
            logger.error(f"Error in model training: {e}")
            raise
    
    async def evaluate_models(self, symbols: list, test_start: str, test_end: str) -> dict:
        """Evaluate trained models on test data."""
        logger.info("📊 Evaluating trained models...")
        
        try:
            # Get test data
            test_data = await self.get_training_data(symbols, test_start, test_end)
            
            if test_data.empty:
                logger.warning("No test data available for evaluation")
                return {}
            
            # Evaluate models
            evaluation_results = {}
            
            if self.ml_pipeline:
                evaluation_results = await self.ml_pipeline.evaluate_models(test_data)
            
            return evaluation_results
            
        except Exception as e:
            logger.error(f"Error in model evaluation: {e}")
            return {}
    
    async def save_models(self):
        """Save trained models to disk."""
        try:
            if self.ml_pipeline:
                await self.ml_pipeline.save_models()
                logger.info("✅ Models saved successfully")
        except Exception as e:
            logger.error(f"Error saving models: {e}")


async def main():
    """Main training function."""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║              ML Model Training with Yahoo Finance            ║
    ║                                                              ║
    ║  🤖 Training LSTM, XGBoost, and Transformer models          ║
    ║  📊 Using reliable Yahoo Finance data                       ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    trainer = YahooMLTrainer()
    
    try:
        # Initialize trainer
        await trainer.initialize()
        
        # Get symbols from config or use default watchlist
        symbols = getattr(settings.external_apis.webull.browser_automation, 'watchlist', [
            "AAPL", "MSFT", "GOOGL", "TSLA", "NVDA", "AMZN", "META", "NFLX"
        ])
        
        logger.info(f"📊 Training models for: {symbols}")
        
        # Define training period
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=365*2)).strftime("%Y-%m-%d")  # 2 years
        
        logger.info(f"Training period: {start_date} to {end_date}")
        
        # Train models
        results = await trainer.train_models(symbols, start_date, end_date)
        
        # Print results
        print("\n✅ Training Results:")
        print("=" * 50)
        
        for model_name, metrics in results.items():
            print(f"\n{model_name.upper()}:")
            if "error" in metrics:
                print(f"  ❌ Error: {metrics['error']}")
            else:
                print(f"  Accuracy: {metrics.get('accuracy', 0):.2%}")
                print(f"  Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
                print(f"  Max Drawdown: {metrics.get('max_drawdown', 0):.2%}")
                print(f"  Win Rate: {metrics.get('win_rate', 0):.2%}")
        
        # Save models
        await trainer.save_models()
        
        # Evaluate on recent data
        test_start = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        test_end = datetime.now().strftime("%Y-%m-%d")
        
        logger.info(f"Evaluating on recent data: {test_start} to {test_end}")
        evaluation_results = await trainer.evaluate_models(symbols, test_start, test_end)
        
        if evaluation_results:
            print("\n📊 Evaluation Results (Last 30 Days):")
            print("=" * 50)
            for model_name, metrics in evaluation_results.items():
                print(f"\n{model_name.upper()}:")
                for metric, value in metrics.items():
                    print(f"  {metric}: {value}")
        
        print("\n🎉 Training completed successfully!")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
