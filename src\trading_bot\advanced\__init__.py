"""Advanced Trading Features Module.

This module provides sophisticated trading capabilities including:
- Sentiment analysis from news and social media
- Economic indicators integration
- Options trading strategies
- Cryptocurrency support
- Advanced portfolio optimization
"""

from .sentiment import (
    NewsAnalyzer,
    SocialMediaAnalyzer,
    EarningsAnalyzer,
    MarketSentimentAggregator
)

from .economic import (
    IndicatorsFetcher,
    EventCalendar,
    MacroAnalyzer,
    SectorRotationAnalyzer
)

from .options import (
    OptionsPricer,
    VolatilityTrader,
    SpreadsBuilder,
    GammaHedger
)

from .crypto import (
    DeFiIntegrator,
    OnChainAnalyzer,
    ArbitrageFinder,
    YieldOptimizer
)

from .portfolio import (
    AdvancedPortfolioOptimizer,
    FactorModelAnalyzer,
    RiskParityOptimizer,
    KellyOptimizer
)

from .advanced_trading_system import AdvancedTradingSystem

__all__ = [
    # Sentiment Analysis
    'NewsAnalyzer',
    'SocialMediaAnalyzer', 
    'EarningsAnalyzer',
    'MarketSentimentAggregator',
    
    # Economic Indicators
    'IndicatorsFetcher',
    'EventCalendar',
    'MacroAnalyzer',
    'SectorRotationAnalyzer',
    
    # Options Trading
    'OptionsPricer',
    'VolatilityTrader',
    'SpreadsBuilder',
    'GammaHedger',
    
    # Crypto Trading
    'DeFiIntegrator',
    'OnChainAnalyzer',
    'ArbitrageFinder',
    'YieldOptimizer',
    
    # Portfolio Optimization
    'AdvancedPortfolioOptimizer',
    'FactorModelAnalyzer',
    'RiskParityOptimizer',
    'KellyOptimizer',
    
    # Main System
    'AdvancedTradingSystem'
]
