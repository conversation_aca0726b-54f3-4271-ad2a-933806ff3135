"""Base API client with common functionality."""

import asyncio
import time
import uuid
from typing import Any, Dict, List, Optional, Union

import aiohttp

from ..core.config import settings
from ..models.enums import APIEndpointType, RateLimitType
from ..utils.cache import cache_manager
from ..utils.exceptions import APIConnectionError, APIRateLimitError, AuthenticationError
from ..utils.logger import get_structured_logger
from ..utils.rate_limiter import rate_limiter
from ..utils.retry import retry_manager
from .endpoints import ENDPOINT_METADATA, WebullEndpoints

logger = get_structured_logger(__name__)


class BaseAPIClient:
    """Base API client with common functionality."""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.device_id: str = settings.webull.device_id or self._generate_device_id()
        
        # Authentication tokens
        self.access_token: Optional[str] = settings.webull.access_token
        self.refresh_token: Optional[str] = settings.webull.refresh_token
        self.trade_token: Optional[str] = settings.webull.trade_token
        
        # Session management
        self.session_timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.max_connections = 100
        self.max_connections_per_host = 30
        
        # Request tracking
        self.request_count = 0
        self.last_request_time = 0.0

        # Circuit breaker service name
        self.service_name = "webull_api"

        # Token refresh tracking
        self.token_refresh_in_progress = False
        self.token_refresh_lock = asyncio.Lock()
    
    def _generate_device_id(self) -> str:
        """Generate a unique device ID."""
        return str(uuid.uuid4())
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_session()
    
    async def start_session(self):
        """Start the HTTP session with connection pooling."""
        if not self.session or self.session.closed:
            connector = aiohttp.TCPConnector(
                limit=self.max_connections,
                limit_per_host=self.max_connections_per_host,
                keepalive_timeout=30,
                enable_cleanup_closed=True,
                use_dns_cache=True,
                ttl_dns_cache=300,
            )
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=self.session_timeout,
                headers=self._get_default_headers(),
            )
            
            logger.info("HTTP session started", extra={"max_connections": self.max_connections})
    
    async def close_session(self):
        """Close the HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
            logger.info("HTTP session closed")
    
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers for all requests."""
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "did": self.device_id,
        }
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers."""
        headers = {}
        
        if self.access_token:
            headers["access_token"] = self.access_token
        
        if self.trade_token:
            headers["t_token"] = self.trade_token
        
        return headers
    
    async def _make_request(
        self,
        method: str,
        endpoint_type: APIEndpointType,
        endpoint_name: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        auth_required: bool = True,
        use_cache: bool = True,
        cache_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Make an API request with full error handling and caching.
        
        Args:
            method: HTTP method
            endpoint_type: Type of API endpoint
            endpoint_name: Name of the endpoint
            data: Request body data
            params: Query parameters
            headers: Additional headers
            auth_required: Whether authentication is required
            use_cache: Whether to use caching
            cache_key: Custom cache key
            
        Returns:
            API response data
            
        Raises:
            APIConnectionError: For connection issues
            AuthenticationError: For auth failures
            APIRateLimitError: For rate limit issues
        """
        if not self.session:
            await self.start_session()
        
        # Get endpoint URL and rate limit type
        url = WebullEndpoints.get_endpoint(endpoint_type, endpoint_name)
        rate_limit_type = WebullEndpoints.get_rate_limit_type(endpoint_type, endpoint_name)
        
        # Check cache first
        if use_cache and method == "GET":
            cache_key = cache_key or self._generate_cache_key(endpoint_name, params)
            cached_data = await cache_manager.get("api_response", cache_key)
            if cached_data:
                logger.debug(
                    "Cache hit for API request",
                    extra={"endpoint": endpoint_name, "cache_key": cache_key}
                )
                return cached_data
        
        # Apply rate limiting
        await rate_limiter.acquire(rate_limit_type, endpoint_name)
        
        # Prepare headers
        request_headers = self._get_default_headers()
        if auth_required:
            request_headers.update(self._get_auth_headers())
        if headers:
            request_headers.update(headers)
        
        # Make request with retry logic
        start_time = time.time()
        
        try:
            response_data = await retry_manager.retry(
                self._execute_request,
                method=method,
                url=url,
                data=data,
                params=params,
                headers=request_headers,
                service_name=self.service_name,
            )
            
            response_time = time.time() - start_time
            
            # Log successful request
            logger.api_request(
                method=method,
                endpoint=f"{endpoint_type.value}.{endpoint_name}",
                status_code=200,
                response_time=response_time,
            )
            
            # Cache successful GET responses
            if use_cache and method == "GET" and response_data:
                cache_key = cache_key or self._generate_cache_key(endpoint_name, params)
                metadata = ENDPOINT_METADATA.get((endpoint_type.value, endpoint_name), {})
                cache_ttl = metadata.get("cache_ttl", 300)
                
                if cache_ttl > 0:
                    await cache_manager.set("api_response", cache_key, response_data, cache_ttl)
            
            # Update request tracking
            self.request_count += 1
            self.last_request_time = time.time()
            
            return response_data
            
        except Exception as e:
            response_time = time.time() - start_time
            
            # Log failed request
            logger.api_request(
                method=method,
                endpoint=f"{endpoint_type.value}.{endpoint_name}",
                response_time=response_time,
                error=str(e),
            )
            
            # Re-raise with context
            if isinstance(e, (APIConnectionError, AuthenticationError, APIRateLimitError)):
                raise e
            else:
                raise APIConnectionError(
                    f"Request failed: {str(e)}",
                    endpoint=f"{endpoint_type.value}.{endpoint_name}",
                )
    
    async def _execute_request(
        self,
        method: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Execute the actual HTTP request."""
        try:
            async with self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=headers,
            ) as response:
                
                # Handle different response types
                content_type = response.headers.get("content-type", "")
                
                if "application/json" in content_type:
                    response_data = await response.json()
                else:
                    text_data = await response.text()
                    response_data = {"data": text_data}
                
                # Handle HTTP errors
                if response.status == 401:
                    raise AuthenticationError(
                        "Authentication failed",
                        auth_type="token",
                        requires_reauth=True,
                    )
                elif response.status == 429:
                    raise APIRateLimitError(
                        "Rate limit exceeded",
                        rate_limit_type="api",
                        reset_time=time.time() + 60,
                    )
                elif response.status >= 400:
                    error_msg = response_data.get("msg", f"HTTP {response.status}")
                    raise APIConnectionError(
                        f"API error: {error_msg}",
                        endpoint=url,
                    )
                
                # Check for API-level errors
                if isinstance(response_data, dict):
                    if not response_data.get("success", True) and "msg" in response_data:
                        raise APIConnectionError(
                            f"API error: {response_data['msg']}",
                            endpoint=url,
                        )
                
                return response_data
                
        except aiohttp.ClientError as e:
            raise APIConnectionError(f"Network error: {str(e)}", endpoint=url)
        except asyncio.TimeoutError:
            raise APIConnectionError("Request timeout", endpoint=url)
    
    def _generate_cache_key(self, endpoint_name: str, params: Optional[Dict[str, Any]] = None) -> str:
        """Generate cache key for request."""
        key_parts = [endpoint_name]
        
        if params:
            # Sort params for consistent cache keys
            sorted_params = sorted(params.items())
            param_str = "&".join(f"{k}={v}" for k, v in sorted_params)
            key_parts.append(param_str)
        
        return ":".join(key_parts)
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the API connection."""
        try:
            # Simple endpoint that doesn't require auth
            await self._make_request(
                "GET",
                APIEndpointType.QUOTE,
                "get_quote",
                params={"symbol": "AAPL"},
                auth_required=False,
                use_cache=False,
            )
            
            return {
                "status": "healthy",
                "request_count": self.request_count,
                "last_request_time": self.last_request_time,
                "session_active": self.session is not None and not self.session.closed,
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "request_count": self.request_count,
                "last_request_time": self.last_request_time,
            }
    
    async def get_rate_limit_status(self) -> Dict[str, Any]:
        """Get current rate limit status."""
        return {
            "data": rate_limiter.get_status(RateLimitType.DATA),
            "trading": rate_limiter.get_status(RateLimitType.TRADING),
            "auth": rate_limiter.get_status(RateLimitType.AUTH),
        }
