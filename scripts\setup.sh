#!/bin/bash

# AI Trading Bot Setup Script
# This script sets up the complete development environment

set -e

echo "🚀 Setting up AI Trading Bot..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if running on Windows (Git Bash/WSL)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    print_warning "Detected Windows environment. Some features may require WSL or Docker Desktop."
fi

# Check prerequisites
print_header "📋 Checking Prerequisites..."

# Check Python
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    print_status "Python $PYTHON_VERSION found"
else
    print_error "Python 3.9+ is required but not found"
    exit 1
fi

# Check pip
if command -v pip &> /dev/null; then
    print_status "pip found"
else
    print_error "pip is required but not found"
    exit 1
fi

# Check Docker (optional)
if command -v docker &> /dev/null; then
    print_status "Docker found"
    DOCKER_AVAILABLE=true
else
    print_warning "Docker not found. Manual database setup will be required."
    DOCKER_AVAILABLE=false
fi

# Create project directories
print_header "📁 Creating Project Structure..."

directories=(
    "data"
    "data/historical"
    "data/real_time"
    "cache"
    "models"
    "models/trained"
    "models/checkpoints"
    "logs"
    "config"
    "notebooks"
    "scripts/sql"
    "monitoring/grafana/dashboards"
    "monitoring/grafana/provisioning"
    "tests/unit"
    "tests/integration"
)

for dir in "${directories[@]}"; do
    mkdir -p "$dir"
    print_status "Created directory: $dir"
done

# Create .env file if it doesn't exist
print_header "⚙️ Setting up Configuration..."

if [ ! -f ".env" ]; then
    cp ".env.example" ".env"
    print_status "Created .env file from template"
    print_warning "Please edit .env file with your configuration"
else
    print_status ".env file already exists"
fi

# Set up Python virtual environment
print_header "🐍 Setting up Python Environment..."

if [ ! -d "venv" ]; then
    python3 -m venv venv
    print_status "Created virtual environment"
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    source venv/Scripts/activate
else
    source venv/bin/activate
fi

print_status "Activated virtual environment"

# Upgrade pip
pip install --upgrade pip
print_status "Upgraded pip"

# Install dependencies
print_status "Installing Python dependencies..."
pip install -e .
pip install -e ".[dev]"

print_status "Python dependencies installed"

# Install pre-commit hooks
print_header "🔧 Setting up Development Tools..."

if command -v pre-commit &> /dev/null; then
    pre-commit install
    print_status "Pre-commit hooks installed"
else
    print_warning "pre-commit not found, skipping hooks setup"
fi

# Set up databases with Docker
if [ "$DOCKER_AVAILABLE" = true ]; then
    print_header "🐳 Setting up Databases with Docker..."
    
    # Check if Docker is running
    if docker info &> /dev/null; then
        print_status "Docker is running"
        
        # Start databases
        docker-compose up -d postgres redis mongo
        print_status "Started databases with Docker Compose"
        
        # Wait for databases to be ready
        print_status "Waiting for databases to be ready..."
        sleep 10
        
        # Test database connections
        print_status "Testing database connections..."
        
        # Test PostgreSQL
        if docker-compose exec -T postgres pg_isready -U trading_bot &> /dev/null; then
            print_status "PostgreSQL is ready"
        else
            print_warning "PostgreSQL connection test failed"
        fi
        
        # Test Redis
        if docker-compose exec -T redis redis-cli ping &> /dev/null; then
            print_status "Redis is ready"
        else
            print_warning "Redis connection test failed"
        fi
        
        # Test MongoDB
        if docker-compose exec -T mongo mongosh --eval "db.runCommand('ping')" &> /dev/null; then
            print_status "MongoDB is ready"
        else
            print_warning "MongoDB connection test failed"
        fi
        
    else
        print_error "Docker is not running. Please start Docker and run this script again."
        exit 1
    fi
else
    print_header "📊 Manual Database Setup Required..."
    print_warning "Docker not available. Please set up databases manually:"
    echo "  1. PostgreSQL 13+ on localhost:5432"
    echo "  2. Redis 6+ on localhost:6379"
    echo "  3. MongoDB 5+ on localhost:27017"
    echo ""
    echo "Update .env file with your database credentials."
fi

# Initialize database schema
print_header "🗄️ Initializing Database Schema..."

python -c "
import asyncio
from src.trading_bot.data.database import startup_databases
asyncio.run(startup_databases())
print('Database schema initialized')
" 2>/dev/null || print_warning "Database schema initialization failed. Check your database connections."

# Run tests
print_header "🧪 Running Tests..."

if python -m pytest tests/ -v; then
    print_status "All tests passed"
else
    print_warning "Some tests failed. Check the output above."
fi

# Test CLI
print_header "🖥️ Testing CLI..."

if python -m trading_bot.cli info; then
    print_status "CLI is working"
else
    print_warning "CLI test failed"
fi

# Create sample configuration files
print_header "📝 Creating Sample Configuration..."

# Create sample strategy config
cat > config/strategies.json << EOF
{
  "strategies": [
    {
      "name": "simple_momentum",
      "type": "momentum",
      "enabled": false,
      "parameters": {
        "lookback_period": 20,
        "threshold": 0.02,
        "max_positions": 5
      }
    },
    {
      "name": "mean_reversion",
      "type": "mean_reversion",
      "enabled": false,
      "parameters": {
        "lookback_period": 50,
        "std_threshold": 2.0,
        "max_positions": 3
      }
    }
  ]
}
EOF

print_status "Created sample strategy configuration"

# Create sample watchlist
cat > config/watchlist.json << EOF
{
  "watchlist": [
    "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA",
    "NVDA", "META", "NFLX", "AMD", "CRM"
  ],
  "sectors": [
    "Technology", "Healthcare", "Financial Services"
  ]
}
EOF

print_status "Created sample watchlist"

# Final instructions
print_header "🎉 Setup Complete!"

echo ""
echo "Next steps:"
echo "1. Edit .env file with your Webull credentials and API keys"
echo "2. Test Webull connection: trading-bot test-webull"
echo "3. Login to Webull: trading-bot login"
echo "4. Start development: trading-bot run"
echo ""
echo "Development tools:"
echo "- Jupyter notebooks: docker-compose --profile dev up jupyter"
echo "- Monitoring: docker-compose up prometheus grafana"
echo "- Full stack: docker-compose up"
echo ""
echo "Documentation:"
echo "- README.md - Getting started guide"
echo "- ROADMAP.md - Development roadmap"
echo "- src/trading_bot/ - Source code"
echo ""

if [ "$DOCKER_AVAILABLE" = true ]; then
    echo "🐳 Docker services are running:"
    echo "- PostgreSQL: localhost:5432"
    echo "- Redis: localhost:6379"
    echo "- MongoDB: localhost:27017"
    echo ""
    echo "To stop services: docker-compose down"
fi

print_status "Setup completed successfully! 🚀"
