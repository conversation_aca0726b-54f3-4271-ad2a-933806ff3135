"""
Production monitoring module for AI Trading Bot.

This module provides comprehensive production monitoring capabilities including:
- Real-time system monitoring and metrics collection
- Alert management and notification system
- Centralized logging and log aggregation
- Trading audit trail and compliance logging
- Performance monitoring and analysis

Components:
- ProductionMonitor: Real-time system monitoring and metrics
- AlertManager: Alert management and notification system
- LogAggregator: Centralized logging and log aggregation
- AuditTrail: Trading audit trail and compliance logging
"""

from .production_monitor import ProductionMonitor, MonitoringMetrics
from .alert_manager import AlertManager, Alert, AlertRule
from .log_aggregator import LogAggregator, LogEntry
from .audit_trail import AuditTrail, AuditEvent

__all__ = [
    'ProductionMonitor',
    'MonitoringMetrics',
    'AlertManager',
    'Alert',
    'AlertRule',
    'LogAggregator',
    'LogEntry',
    'AuditTrail',
    'AuditEvent',
]
