"""DeFi protocol integration and yield optimization."""

import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from decimal import Decimal
import json

from ...core.config import settings
from ...core.logger import get_logger
from ...utils.cache import cache_manager
from ...utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


class DeFiProtocol(Enum):
    """DeFi protocol types."""
    UNISWAP_V3 = "uniswap_v3"
    SUSHISWAP = "sushiswap"
    AAVE = "aave"
    COMPOUND = "compound"
    YEARN = "yearn"
    CURVE = "curve"
    BALANCER = "balancer"


@dataclass
class LiquidityPool:
    """Liquidity pool information."""
    protocol: DeFiProtocol
    pool_address: str
    token0: str
    token1: str
    fee_tier: float
    tvl: float  # Total Value Locked
    volume_24h: float
    apy: float
    il_risk: float  # Impermanent Loss risk score


@dataclass
class YieldOpportunity:
    """Yield farming opportunity."""
    protocol: DeFiProtocol
    pool: LiquidityPool
    strategy: str
    apy: float
    risk_score: float  # 0-10 scale
    min_deposit: float
    lock_period: int  # days
    auto_compound: bool
    rewards_tokens: List[str]


@dataclass
class DeFiPosition:
    """DeFi position tracking."""
    protocol: DeFiProtocol
    position_id: str
    tokens: Dict[str, float]
    value_usd: float
    entry_date: datetime
    current_yield: float
    impermanent_loss: float
    fees_earned: float


class DeFiIntegrator:
    """DeFi protocol integration and management."""
    
    def __init__(self):
        self.rate_limiter = RateLimiter(max_requests=100, time_window=60)
        self.protocol_apis = self._load_protocol_apis()
        self.positions = {}  # Track active positions
        
    async def find_yield_opportunities(
        self,
        tokens: List[str],
        min_apy: float = 0.05,
        max_risk: float = 5.0,
        min_tvl: float = 1000000
    ) -> List[YieldOpportunity]:
        """
        Find optimal yield farming opportunities.
        
        Args:
            tokens: List of tokens to consider
            min_apy: Minimum APY requirement
            max_risk: Maximum risk score
            min_tvl: Minimum TVL requirement
            
        Returns:
            List of yield opportunities
        """
        try:
            opportunities = []
            
            # Fetch opportunities from different protocols
            protocol_tasks = [
                self._fetch_uniswap_opportunities(tokens, min_tvl),
                self._fetch_aave_opportunities(tokens),
                self._fetch_compound_opportunities(tokens),
                self._fetch_yearn_opportunities(tokens),
                self._fetch_curve_opportunities(tokens)
            ]
            
            results = await asyncio.gather(*protocol_tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, list):
                    opportunities.extend(result)
                elif isinstance(result, Exception):
                    logger.warning(f"Protocol fetch failed: {result}")
            
            # Filter by criteria
            filtered_opportunities = [
                opp for opp in opportunities
                if opp.apy >= min_apy and opp.risk_score <= max_risk and opp.pool.tvl >= min_tvl
            ]
            
            # Sort by risk-adjusted return
            filtered_opportunities.sort(
                key=lambda x: x.apy / (1 + x.risk_score),
                reverse=True
            )
            
            return filtered_opportunities[:20]  # Top 20 opportunities
            
        except Exception as e:
            logger.error(f"Error finding yield opportunities: {e}")
            return []
    
    async def optimize_yield_allocation(
        self,
        capital: float,
        tokens: List[str],
        risk_tolerance: float = 5.0
    ) -> Dict[str, Any]:
        """
        Optimize capital allocation across yield opportunities.
        
        Args:
            capital: Available capital in USD
            tokens: Preferred tokens
            risk_tolerance: Risk tolerance (0-10)
            
        Returns:
            Optimal allocation strategy
        """
        try:
            # Find opportunities
            opportunities = await self.find_yield_opportunities(
                tokens, min_apy=0.01, max_risk=risk_tolerance
            )
            
            if not opportunities:
                return {'error': 'No suitable opportunities found'}
            
            # Simple allocation strategy (equal risk-weighted)
            total_score = sum(opp.apy / (1 + opp.risk_score) for opp in opportunities[:5])
            
            allocations = []
            remaining_capital = capital
            
            for opp in opportunities[:5]:  # Top 5 opportunities
                if remaining_capital <= 0:
                    break
                
                score = opp.apy / (1 + opp.risk_score)
                allocation_pct = score / total_score
                allocation_amount = min(capital * allocation_pct, remaining_capital)
                
                if allocation_amount >= opp.min_deposit:
                    allocations.append({
                        'protocol': opp.protocol.value,
                        'strategy': opp.strategy,
                        'amount': allocation_amount,
                        'percentage': allocation_amount / capital,
                        'expected_apy': opp.apy,
                        'risk_score': opp.risk_score
                    })
                    remaining_capital -= allocation_amount
            
            # Calculate portfolio metrics
            total_allocated = sum(alloc['amount'] for alloc in allocations)
            weighted_apy = sum(
                alloc['amount'] * alloc['expected_apy'] for alloc in allocations
            ) / total_allocated if total_allocated > 0 else 0
            
            weighted_risk = sum(
                alloc['amount'] * alloc['risk_score'] for alloc in allocations
            ) / total_allocated if total_allocated > 0 else 0
            
            return {
                'allocations': allocations,
                'total_allocated': total_allocated,
                'allocation_percentage': total_allocated / capital,
                'expected_portfolio_apy': weighted_apy,
                'portfolio_risk_score': weighted_risk,
                'diversification_score': len(allocations) / 5  # Max 5 positions
            }
            
        except Exception as e:
            logger.error(f"Error optimizing yield allocation: {e}")
            return {'error': str(e)}
    
    async def monitor_positions(self) -> Dict[str, Any]:
        """Monitor active DeFi positions."""
        try:
            position_updates = {}
            
            for position_id, position in self.positions.items():
                # Update position value and metrics
                updated_position = await self._update_position(position)
                position_updates[position_id] = {
                    'current_value': updated_position.value_usd,
                    'pnl': updated_position.value_usd - position.value_usd,
                    'yield_earned': updated_position.current_yield,
                    'impermanent_loss': updated_position.impermanent_loss,
                    'fees_earned': updated_position.fees_earned,
                    'days_active': (datetime.now() - position.entry_date).days
                }
            
            # Calculate portfolio summary
            total_value = sum(pos.value_usd for pos in self.positions.values())
            total_yield = sum(pos.current_yield for pos in self.positions.values())
            total_il = sum(pos.impermanent_loss for pos in self.positions.values())
            
            return {
                'timestamp': datetime.now(),
                'positions': position_updates,
                'portfolio_summary': {
                    'total_value': total_value,
                    'total_yield_earned': total_yield,
                    'total_impermanent_loss': total_il,
                    'net_performance': total_yield - total_il,
                    'position_count': len(self.positions)
                }
            }
            
        except Exception as e:
            logger.error(f"Error monitoring positions: {e}")
            return {'error': str(e)}
    
    async def _fetch_uniswap_opportunities(
        self,
        tokens: List[str],
        min_tvl: float
    ) -> List[YieldOpportunity]:
        """Fetch Uniswap V3 liquidity opportunities."""
        try:
            opportunities = []
            
            # Uniswap V3 subgraph query
            query = """
            {
                pools(first: 50, orderBy: totalValueLockedUSD, orderDirection: desc) {
                    id
                    token0 { symbol }
                    token1 { symbol }
                    feeTier
                    totalValueLockedUSD
                    volumeUSD
                }
            }
            """
            
            url = "https://api.thegraph.com/subgraphs/name/uniswap/uniswap-v3"
            
            await self.rate_limiter.acquire()
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json={'query': query}) as response:
                    data = await response.json()
            
            if 'data' in data and 'pools' in data['data']:
                for pool_data in data['data']['pools']:
                    token0 = pool_data['token0']['symbol']
                    token1 = pool_data['token1']['symbol']
                    
                    # Check if tokens are in our list
                    if any(token in tokens for token in [token0, token1]):
                        tvl = float(pool_data['totalValueLockedUSD'])
                        volume = float(pool_data['volumeUSD'])
                        
                        if tvl >= min_tvl:
                            # Estimate APY based on fees
                            fee_tier = float(pool_data['feeTier']) / 10000  # Convert to percentage
                            estimated_apy = (volume * fee_tier * 365) / tvl if tvl > 0 else 0
                            
                            pool = LiquidityPool(
                                protocol=DeFiProtocol.UNISWAP_V3,
                                pool_address=pool_data['id'],
                                token0=token0,
                                token1=token1,
                                fee_tier=fee_tier,
                                tvl=tvl,
                                volume_24h=volume,
                                apy=estimated_apy,
                                il_risk=self._calculate_il_risk(token0, token1)
                            )
                            
                            opportunity = YieldOpportunity(
                                protocol=DeFiProtocol.UNISWAP_V3,
                                pool=pool,
                                strategy="Liquidity Provision",
                                apy=estimated_apy,
                                risk_score=self._calculate_risk_score(pool),
                                min_deposit=1000,  # $1000 minimum
                                lock_period=0,  # No lock
                                auto_compound=False,
                                rewards_tokens=[token0, token1]
                            )
                            opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Error fetching Uniswap opportunities: {e}")
            return []
    
    async def _fetch_aave_opportunities(self, tokens: List[str]) -> List[YieldOpportunity]:
        """Fetch Aave lending opportunities."""
        try:
            opportunities = []
            
            # Aave API endpoint
            url = "https://aave-api-v2.aave.com/data/markets-data"
            
            await self.rate_limiter.acquire()
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    data = await response.json()
            
            for market in data:
                for reserve in market.get('reserves', []):
                    symbol = reserve.get('symbol', '')
                    
                    if symbol in tokens:
                        supply_apy = float(reserve.get('liquidityRate', 0)) / 1e27 * 100  # Convert from ray
                        
                        if supply_apy > 0:
                            # Create synthetic pool for Aave
                            pool = LiquidityPool(
                                protocol=DeFiProtocol.AAVE,
                                pool_address=reserve.get('aTokenAddress', ''),
                                token0=symbol,
                                token1='',
                                fee_tier=0,
                                tvl=float(reserve.get('totalLiquidity', 0)),
                                volume_24h=0,
                                apy=supply_apy,
                                il_risk=0  # No IL risk for lending
                            )
                            
                            opportunity = YieldOpportunity(
                                protocol=DeFiProtocol.AAVE,
                                pool=pool,
                                strategy="Lending",
                                apy=supply_apy,
                                risk_score=2.0,  # Low risk for established tokens
                                min_deposit=100,
                                lock_period=0,
                                auto_compound=True,
                                rewards_tokens=[symbol]
                            )
                            opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Error fetching Aave opportunities: {e}")
            return []
    
    async def _fetch_compound_opportunities(self, tokens: List[str]) -> List[YieldOpportunity]:
        """Fetch Compound lending opportunities."""
        # Similar to Aave implementation
        return []
    
    async def _fetch_yearn_opportunities(self, tokens: List[str]) -> List[YieldOpportunity]:
        """Fetch Yearn vault opportunities."""
        # Implementation for Yearn vaults
        return []
    
    async def _fetch_curve_opportunities(self, tokens: List[str]) -> List[YieldOpportunity]:
        """Fetch Curve pool opportunities."""
        # Implementation for Curve pools
        return []
    
    def _calculate_il_risk(self, token0: str, token1: str) -> float:
        """Calculate impermanent loss risk score."""
        # Simplified IL risk calculation
        # In practice, would analyze historical correlation and volatility
        
        stable_coins = ['USDC', 'USDT', 'DAI', 'BUSD']
        
        if token0 in stable_coins and token1 in stable_coins:
            return 0.1  # Very low IL risk
        elif token0 in stable_coins or token1 in stable_coins:
            return 0.3  # Medium IL risk
        else:
            return 0.6  # High IL risk
    
    def _calculate_risk_score(self, pool: LiquidityPool) -> float:
        """Calculate overall risk score for a pool."""
        risk_score = 0.0
        
        # TVL risk (lower TVL = higher risk)
        if pool.tvl < 1000000:
            risk_score += 2.0
        elif pool.tvl < 10000000:
            risk_score += 1.0
        
        # IL risk
        risk_score += pool.il_risk * 5
        
        # Protocol risk
        protocol_risks = {
            DeFiProtocol.UNISWAP_V3: 1.0,
            DeFiProtocol.AAVE: 0.5,
            DeFiProtocol.COMPOUND: 0.5,
            DeFiProtocol.CURVE: 1.0,
            DeFiProtocol.YEARN: 1.5
        }
        risk_score += protocol_risks.get(pool.protocol, 2.0)
        
        return min(risk_score, 10.0)  # Cap at 10
    
    async def _update_position(self, position: DeFiPosition) -> DeFiPosition:
        """Update position with current values."""
        # This would fetch current position data from the blockchain
        # For now, return the position as-is
        return position
    
    def _load_protocol_apis(self) -> Dict[DeFiProtocol, str]:
        """Load protocol API endpoints."""
        return {
            DeFiProtocol.UNISWAP_V3: "https://api.thegraph.com/subgraphs/name/uniswap/uniswap-v3",
            DeFiProtocol.AAVE: "https://aave-api-v2.aave.com",
            DeFiProtocol.COMPOUND: "https://api.compound.finance",
            DeFiProtocol.YEARN: "https://api.yearn.finance",
            DeFiProtocol.CURVE: "https://api.curve.fi"
        }
