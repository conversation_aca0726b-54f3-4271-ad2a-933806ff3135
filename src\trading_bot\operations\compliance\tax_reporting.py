"""
Tax Reporting and Optimization System

Automated tax reporting, optimization strategies, and compliance
with tax regulations across multiple jurisdictions.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta, date
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from decimal import Decimal
import json
from pathlib import Path

from ...core.logger import get_logger
from ...data.models import Trade, Position

logger = get_logger(__name__)

class TaxJurisdiction(Enum):
    """Tax jurisdictions"""
    US_FEDERAL = "us_federal"
    US_STATE = "us_state"
    UK = "uk"
    CANADA = "canada"
    AUSTRALIA = "australia"
    GERMANY = "germany"
    SINGAPORE = "singapore"

class TaxEventType(Enum):
    """Types of taxable events"""
    CAPITAL_GAIN = "capital_gain"
    CAPITAL_LOSS = "capital_loss"
    DIVIDEND = "dividend"
    INTEREST = "interest"
    WASH_SALE = "wash_sale"
    SECTION_1256 = "section_1256"  # US futures/options
    MARK_TO_MARKET = "mark_to_market"

class TaxLotMethod(Enum):
    """Tax lot identification methods"""
    FIFO = "fifo"  # First In, First Out
    LIFO = "lifo"  # Last In, First Out
    SPECIFIC_ID = "specific_id"  # Specific identification
    AVERAGE_COST = "average_cost"
    HIFO = "hifo"  # Highest In, First Out

@dataclass
class TaxLot:
    """Represents a tax lot for position tracking"""
    symbol: str
    quantity: float
    acquisition_date: datetime
    acquisition_price: float
    cost_basis: float
    lot_id: str

@dataclass
class TaxEvent:
    """Represents a taxable event"""
    event_id: str
    event_type: TaxEventType
    symbol: str
    quantity: float
    date: datetime
    proceeds: float
    cost_basis: float
    gain_loss: float
    holding_period: timedelta
    is_long_term: bool
    jurisdiction: TaxJurisdiction
    related_trade_id: str
    wash_sale_adjustment: float = 0.0

@dataclass
class TaxOptimization:
    """Tax optimization opportunity"""
    opportunity_id: str
    strategy: str
    description: str
    potential_savings: float
    implementation_deadline: Optional[datetime]
    risk_level: str
    requirements: List[str]

class TaxReporter:
    """Automated tax reporting and optimization"""
    
    def __init__(self, db_manager, default_jurisdiction: TaxJurisdiction = TaxJurisdiction.US_FEDERAL):
        self.db_manager = db_manager
        self.default_jurisdiction = default_jurisdiction
        self.tax_lots: Dict[str, List[TaxLot]] = {}  # symbol -> list of lots
        self.tax_events: List[TaxEvent] = []
        self.tax_rates = self._initialize_tax_rates()
        self.wash_sale_period = timedelta(days=61)  # 30 days before + 30 days after + trade date
        
    def _initialize_tax_rates(self) -> Dict[TaxJurisdiction, Dict[str, float]]:
        """Initialize tax rates for different jurisdictions"""
        return {
            TaxJurisdiction.US_FEDERAL: {
                'short_term_capital_gains': 0.37,  # Ordinary income rates (max)
                'long_term_capital_gains': 0.20,   # Long-term capital gains (max)
                'qualified_dividends': 0.20,
                'ordinary_dividends': 0.37,
                'section_1256_60_40': True  # 60% long-term, 40% short-term
            },
            TaxJurisdiction.UK: {
                'capital_gains_basic': 0.10,
                'capital_gains_higher': 0.20,
                'dividend_basic': 0.075,
                'dividend_higher': 0.325,
                'annual_exemption': 12300  # £12,300 for 2022-23
            }
        }
    
    async def process_trade_for_tax(self, trade: Trade) -> List[TaxEvent]:
        """Process a completed trade for tax implications"""
        logger.info(f"Processing trade {trade.id} for tax implications")
        
        tax_events = []
        
        if trade.side.lower() == 'buy':
            # Opening position - create tax lot
            await self._create_tax_lot(trade)
        else:
            # Closing position - realize gains/losses
            tax_events = await self._realize_gains_losses(trade)
        
        # Check for wash sale rules
        for event in tax_events:
            if event.gain_loss < 0:  # Only losses can be wash sales
                await self._check_wash_sale(event)
        
        # Add events to our records
        self.tax_events.extend(tax_events)
        
        return tax_events
    
    async def generate_tax_report(self, year: int, jurisdiction: TaxJurisdiction = None) -> Dict[str, Any]:
        """Generate comprehensive tax report for a given year"""
        
        if jurisdiction is None:
            jurisdiction = self.default_jurisdiction
        
        logger.info(f"Generating tax report for {year} ({jurisdiction.value})")
        
        # Get trades for the year
        year_start = datetime(year, 1, 1)
        year_end = datetime(year, 12, 31, 23, 59, 59)
        
        year_events = [
            event for event in self.tax_events
            if year_start <= event.date <= year_end and event.jurisdiction == jurisdiction
        ]
        
        # Calculate gains and losses
        short_term_gains = sum(event.gain_loss for event in year_events 
                              if not event.is_long_term and event.gain_loss > 0)
        short_term_losses = sum(event.gain_loss for event in year_events 
                               if not event.is_long_term and event.gain_loss < 0)
        long_term_gains = sum(event.gain_loss for event in year_events 
                             if event.is_long_term and event.gain_loss > 0)
        long_term_losses = sum(event.gain_loss for event in year_events 
                              if event.is_long_term and event.gain_loss < 0)
        
        # Calculate net gains/losses
        net_short_term = short_term_gains + short_term_losses
        net_long_term = long_term_gains + long_term_losses
        total_net_gain_loss = net_short_term + net_long_term
        
        # Calculate tax liability
        tax_liability = await self._calculate_tax_liability(
            net_short_term, net_long_term, jurisdiction
        )
        
        # Generate required forms
        forms = await self._generate_tax_forms(year_events, jurisdiction)
        
        # Identify wash sales
        wash_sales = [event for event in year_events if event.wash_sale_adjustment != 0]
        
        # Calculate quarterly estimates for next year
        quarterly_estimates = await self._calculate_quarterly_estimates(total_net_gain_loss)
        
        return {
            'year': year,
            'jurisdiction': jurisdiction.value,
            'summary': {
                'total_trades': len(year_events),
                'short_term_gains': short_term_gains,
                'short_term_losses': abs(short_term_losses),
                'net_short_term': net_short_term,
                'long_term_gains': long_term_gains,
                'long_term_losses': abs(long_term_losses),
                'net_long_term': net_long_term,
                'total_net_gain_loss': total_net_gain_loss,
                'estimated_tax_liability': tax_liability,
                'wash_sales_count': len(wash_sales),
                'wash_sales_adjustment': sum(ws.wash_sale_adjustment for ws in wash_sales)
            },
            'forms': forms,
            'wash_sales': [
                {
                    'symbol': ws.symbol,
                    'date': ws.date.isoformat(),
                    'loss_disallowed': ws.wash_sale_adjustment,
                    'trade_id': ws.related_trade_id
                }
                for ws in wash_sales
            ],
            'quarterly_estimates': quarterly_estimates,
            'generated_date': datetime.now().isoformat()
        }
    
    async def identify_tax_optimization_opportunities(self) -> List[TaxOptimization]:
        """Identify opportunities to reduce tax liability"""
        logger.info("Identifying tax optimization opportunities")
        
        opportunities = []
        current_positions = await self._get_current_positions()
        
        # Tax loss harvesting opportunities
        losing_positions = [pos for pos in current_positions if pos.unrealized_pnl < 0]
        
        for position in losing_positions:
            if await self._can_harvest_loss(position):
                potential_savings = abs(position.unrealized_pnl) * self.tax_rates[self.default_jurisdiction]['short_term_capital_gains']
                
                opportunities.append(TaxOptimization(
                    opportunity_id=f"tlh_{position.symbol}_{datetime.now().strftime('%Y%m%d')}",
                    strategy="tax_loss_harvesting",
                    description=f"Harvest tax loss on {position.symbol} position",
                    potential_savings=potential_savings,
                    implementation_deadline=datetime(datetime.now().year, 12, 31),
                    risk_level="low",
                    requirements=[
                        "Close position",
                        "Wait 31 days before repurchasing (wash sale rule)",
                        "Consider substantially identical securities rule"
                    ]
                ))
        
        # Long-term vs short-term timing
        opportunities.extend(await self._identify_timing_opportunities(current_positions))
        
        # Section 1256 election opportunities (for US)
        if self.default_jurisdiction == TaxJurisdiction.US_FEDERAL:
            opportunities.extend(await self._identify_section_1256_opportunities())
        
        # Year-end planning
        if datetime.now().month >= 10:  # Q4
            opportunities.extend(await self._identify_year_end_opportunities())
        
        return opportunities
    
    async def _create_tax_lot(self, trade: Trade):
        """Create a tax lot for a buy trade"""
        
        lot = TaxLot(
            symbol=trade.symbol,
            quantity=trade.quantity,
            acquisition_date=trade.entry_time,
            acquisition_price=trade.entry_price,
            cost_basis=trade.entry_price * trade.quantity,
            lot_id=f"{trade.symbol}_{trade.entry_time.strftime('%Y%m%d_%H%M%S')}"
        )
        
        if trade.symbol not in self.tax_lots:
            self.tax_lots[trade.symbol] = []
        
        self.tax_lots[trade.symbol].append(lot)
        
        logger.debug(f"Created tax lot: {lot.lot_id}")
    
    async def _realize_gains_losses(self, trade: Trade) -> List[TaxEvent]:
        """Realize gains/losses for a sell trade"""
        
        if trade.symbol not in self.tax_lots:
            logger.warning(f"No tax lots found for {trade.symbol}")
            return []
        
        lots = self.tax_lots[trade.symbol]
        remaining_quantity = trade.quantity
        tax_events = []
        
        # Use FIFO method by default (can be configured)
        lots.sort(key=lambda x: x.acquisition_date)
        
        for lot in lots[:]:
            if remaining_quantity <= 0:
                break
            
            # Determine quantity to close from this lot
            quantity_to_close = min(remaining_quantity, lot.quantity)
            
            # Calculate proceeds and cost basis
            proceeds = quantity_to_close * trade.exit_price
            cost_basis = quantity_to_close * lot.acquisition_price
            gain_loss = proceeds - cost_basis
            
            # Determine holding period
            holding_period = trade.exit_time - lot.acquisition_date
            is_long_term = holding_period >= timedelta(days=365)
            
            # Create tax event
            event = TaxEvent(
                event_id=f"{trade.id}_{lot.lot_id}",
                event_type=TaxEventType.CAPITAL_GAIN if gain_loss >= 0 else TaxEventType.CAPITAL_LOSS,
                symbol=trade.symbol,
                quantity=quantity_to_close,
                date=trade.exit_time,
                proceeds=proceeds,
                cost_basis=cost_basis,
                gain_loss=gain_loss,
                holding_period=holding_period,
                is_long_term=is_long_term,
                jurisdiction=self.default_jurisdiction,
                related_trade_id=trade.id
            )
            
            tax_events.append(event)
            
            # Update lot quantity
            lot.quantity -= quantity_to_close
            remaining_quantity -= quantity_to_close
            
            # Remove lot if fully closed
            if lot.quantity <= 0:
                lots.remove(lot)
        
        return tax_events
    
    async def _check_wash_sale(self, loss_event: TaxEvent):
        """Check if a loss event triggers wash sale rules"""
        
        # Look for purchases of the same security within wash sale period
        wash_sale_start = loss_event.date - timedelta(days=30)
        wash_sale_end = loss_event.date + timedelta(days=30)
        
        # Check for purchases in the wash sale period
        wash_sale_purchases = []
        
        for event in self.tax_events:
            if (event.symbol == loss_event.symbol and
                event.quantity > 0 and  # Purchase
                wash_sale_start <= event.date <= wash_sale_end and
                event.date != loss_event.date):
                
                wash_sale_purchases.append(event)
        
        if wash_sale_purchases:
            # Disallow the loss (simplified - actual rules are more complex)
            loss_event.wash_sale_adjustment = abs(loss_event.gain_loss)
            loss_event.gain_loss = 0  # Loss is disallowed
            
            logger.info(f"Wash sale detected for {loss_event.symbol}: loss of {loss_event.wash_sale_adjustment} disallowed")
    
    async def _calculate_tax_liability(self, 
                                     net_short_term: float, 
                                     net_long_term: float,
                                     jurisdiction: TaxJurisdiction) -> float:
        """Calculate estimated tax liability"""
        
        if jurisdiction == TaxJurisdiction.US_FEDERAL:
            rates = self.tax_rates[jurisdiction]
            
            # Net short-term gains taxed as ordinary income
            short_term_tax = max(0, net_short_term) * rates['short_term_capital_gains']
            
            # Net long-term gains taxed at preferential rates
            long_term_tax = max(0, net_long_term) * rates['long_term_capital_gains']
            
            # Capital loss limitations (simplified)
            total_net = net_short_term + net_long_term
            if total_net < 0:
                # Can only deduct $3,000 per year against ordinary income
                deductible_loss = min(abs(total_net), 3000)
                tax_savings = deductible_loss * rates['short_term_capital_gains']
                return -tax_savings  # Negative indicates tax savings
            
            return short_term_tax + long_term_tax
        
        return 0.0  # Placeholder for other jurisdictions
    
    async def _generate_tax_forms(self, events: List[TaxEvent], jurisdiction: TaxJurisdiction) -> Dict[str, Any]:
        """Generate required tax forms"""
        
        forms = {}
        
        if jurisdiction == TaxJurisdiction.US_FEDERAL:
            # Form 8949 - Sales and Other Dispositions of Capital Assets
            forms['form_8949'] = await self._generate_form_8949(events)
            
            # Schedule D - Capital Gains and Losses
            forms['schedule_d'] = await self._generate_schedule_d(events)
        
        return forms
    
    async def _generate_form_8949(self, events: List[TaxEvent]) -> Dict[str, Any]:
        """Generate Form 8949 data"""
        
        short_term_transactions = []
        long_term_transactions = []
        
        for event in events:
            if event.event_type in [TaxEventType.CAPITAL_GAIN, TaxEventType.CAPITAL_LOSS]:
                transaction = {
                    'description': f"{event.quantity} shares {event.symbol}",
                    'date_acquired': (event.date - event.holding_period).strftime('%m/%d/%Y'),
                    'date_sold': event.date.strftime('%m/%d/%Y'),
                    'proceeds': round(event.proceeds, 2),
                    'cost_basis': round(event.cost_basis, 2),
                    'gain_loss': round(event.gain_loss, 2),
                    'wash_sale_adjustment': round(event.wash_sale_adjustment, 2)
                }
                
                if event.is_long_term:
                    long_term_transactions.append(transaction)
                else:
                    short_term_transactions.append(transaction)
        
        return {
            'short_term_transactions': short_term_transactions,
            'long_term_transactions': long_term_transactions,
            'short_term_total': sum(t['gain_loss'] for t in short_term_transactions),
            'long_term_total': sum(t['gain_loss'] for t in long_term_transactions)
        }
    
    async def _generate_schedule_d(self, events: List[TaxEvent]) -> Dict[str, Any]:
        """Generate Schedule D data"""
        
        form_8949 = await self._generate_form_8949(events)
        
        short_term_total = form_8949['short_term_total']
        long_term_total = form_8949['long_term_total']
        
        # Net capital gain/loss
        net_capital_gain_loss = short_term_total + long_term_total
        
        return {
            'short_term_capital_gain_loss': short_term_total,
            'long_term_capital_gain_loss': long_term_total,
            'net_capital_gain_loss': net_capital_gain_loss,
            'capital_loss_carryover': 0,  # Would need to track from previous years
            'taxable_capital_gain': max(0, net_capital_gain_loss)
        }
    
    async def _calculate_quarterly_estimates(self, annual_gain_loss: float) -> Dict[str, float]:
        """Calculate quarterly estimated tax payments"""
        
        if annual_gain_loss <= 0:
            return {'q1': 0, 'q2': 0, 'q3': 0, 'q4': 0}
        
        # Simplified calculation
        estimated_tax = annual_gain_loss * self.tax_rates[self.default_jurisdiction]['short_term_capital_gains']
        quarterly_payment = estimated_tax / 4
        
        return {
            'q1': quarterly_payment,
            'q2': quarterly_payment,
            'q3': quarterly_payment,
            'q4': quarterly_payment
        }
    
    async def _get_current_positions(self) -> List[Position]:
        """Get current open positions"""
        # This would query the actual position database
        # For now, return empty list
        return []
    
    async def _can_harvest_loss(self, position: Position) -> bool:
        """Check if a position can be harvested for tax losses"""
        
        # Check wash sale rules
        recent_purchases = [
            event for event in self.tax_events
            if (event.symbol == position.symbol and
                event.quantity > 0 and
                event.date > datetime.now() - timedelta(days=30))
        ]
        
        return len(recent_purchases) == 0
    
    async def _identify_timing_opportunities(self, positions: List[Position]) -> List[TaxOptimization]:
        """Identify opportunities to optimize gain/loss timing"""
        
        opportunities = []
        
        for position in positions:
            if position.unrealized_pnl > 0:  # Gains
                # Check if holding until long-term would be beneficial
                lots = self.tax_lots.get(position.symbol, [])
                
                for lot in lots:
                    days_held = (datetime.now() - lot.acquisition_date).days
                    
                    if 300 <= days_held < 365:  # Close to long-term
                        days_to_long_term = 365 - days_held
                        
                        short_term_tax = position.unrealized_pnl * self.tax_rates[self.default_jurisdiction]['short_term_capital_gains']
                        long_term_tax = position.unrealized_pnl * self.tax_rates[self.default_jurisdiction]['long_term_capital_gains']
                        potential_savings = short_term_tax - long_term_tax
                        
                        if potential_savings > 100:  # Minimum threshold
                            opportunities.append(TaxOptimization(
                                opportunity_id=f"timing_{position.symbol}_{lot.lot_id}",
                                strategy="long_term_timing",
                                description=f"Hold {position.symbol} for {days_to_long_term} more days for long-term treatment",
                                potential_savings=potential_savings,
                                implementation_deadline=lot.acquisition_date + timedelta(days=365),
                                risk_level="medium",
                                requirements=[f"Hold position for {days_to_long_term} more days"]
                            ))
        
        return opportunities
    
    async def _identify_section_1256_opportunities(self) -> List[TaxOptimization]:
        """Identify Section 1256 contract opportunities (US)"""
        
        # Section 1256 contracts (futures, options on futures, etc.) get 60/40 treatment
        # 60% long-term, 40% short-term regardless of holding period
        
        opportunities = []
        
        # This would analyze current derivatives positions
        # For now, return empty list
        
        return opportunities
    
    async def _identify_year_end_opportunities(self) -> List[TaxOptimization]:
        """Identify year-end tax planning opportunities"""
        
        opportunities = []
        
        # Calculate current year gains/losses
        year_start = datetime(datetime.now().year, 1, 1)
        ytd_events = [event for event in self.tax_events if event.date >= year_start]
        
        ytd_gains = sum(event.gain_loss for event in ytd_events if event.gain_loss > 0)
        ytd_losses = sum(event.gain_loss for event in ytd_events if event.gain_loss < 0)
        net_ytd = ytd_gains + ytd_losses
        
        if net_ytd > 0:  # Net gains - look for loss harvesting
            opportunities.append(TaxOptimization(
                opportunity_id=f"year_end_harvest_{datetime.now().year}",
                strategy="year_end_loss_harvesting",
                description=f"Harvest losses to offset ${net_ytd:.2f} in gains",
                potential_savings=net_ytd * self.tax_rates[self.default_jurisdiction]['short_term_capital_gains'],
                implementation_deadline=datetime(datetime.now().year, 12, 31),
                risk_level="low",
                requirements=["Identify losing positions", "Execute sales before year-end"]
            ))
        
        return opportunities
    
    def get_tax_summary(self) -> Dict[str, Any]:
        """Get current tax situation summary"""
        
        current_year = datetime.now().year
        ytd_events = [event for event in self.tax_events 
                     if event.date.year == current_year]
        
        ytd_gains = sum(event.gain_loss for event in ytd_events if event.gain_loss > 0)
        ytd_losses = sum(event.gain_loss for event in ytd_events if event.gain_loss < 0)
        
        return {
            'current_year': current_year,
            'ytd_realized_gains': ytd_gains,
            'ytd_realized_losses': abs(ytd_losses),
            'ytd_net_gain_loss': ytd_gains + ytd_losses,
            'total_tax_events': len(ytd_events),
            'wash_sales_ytd': len([e for e in ytd_events if e.wash_sale_adjustment != 0]),
            'estimated_tax_liability': await self._calculate_tax_liability(
                sum(e.gain_loss for e in ytd_events if not e.is_long_term),
                sum(e.gain_loss for e in ytd_events if e.is_long_term),
                self.default_jurisdiction
            )
        }
