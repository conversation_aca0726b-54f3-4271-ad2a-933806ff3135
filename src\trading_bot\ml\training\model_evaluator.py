"""Model evaluation and performance assessment."""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import logging
from dataclasses import dataclass, field
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_auc_score,
    mean_squared_error, mean_absolute_error, r2_score
)
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
import warnings

logger = logging.getLogger(__name__)


@dataclass
class EvaluationConfig:
    """Configuration for model evaluation."""
    # Metrics to calculate
    classification_metrics: List[str] = field(default_factory=lambda: [
        'accuracy', 'precision', 'recall', 'f1', 'auc'
    ])
    regression_metrics: List[str] = field(default_factory=lambda: [
        'mse', 'mae', 'r2', 'directional_accuracy'
    ])
    
    # Trading-specific metrics
    trading_metrics: List[str] = field(default_factory=lambda: [
        'sharpe_ratio', 'sortino_ratio', 'calmar_ratio', 'max_drawdown',
        'hit_rate', 'profit_factor', 'avg_return_per_trade'
    ])
    
    # Evaluation settings
    bootstrap_samples: int = 1000
    confidence_level: float = 0.95
    
    # Cross-validation
    cv_folds: int = 5
    
    # Plotting
    create_plots: bool = True
    save_plots: bool = False
    plot_dir: str = "evaluation_plots"


@dataclass
class ModelPerformance:
    """Container for model performance metrics."""
    model_name: str
    model_type: str
    
    # Classification metrics
    accuracy: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    f1_score: Optional[float] = None
    auc_score: Optional[float] = None
    
    # Regression metrics
    mse: Optional[float] = None
    mae: Optional[float] = None
    r2_score: Optional[float] = None
    directional_accuracy: Optional[float] = None
    
    # Trading metrics
    sharpe_ratio: Optional[float] = None
    sortino_ratio: Optional[float] = None
    calmar_ratio: Optional[float] = None
    max_drawdown: Optional[float] = None
    hit_rate: Optional[float] = None
    profit_factor: Optional[float] = None
    avg_return_per_trade: Optional[float] = None
    
    # Statistical tests
    statistical_significance: Optional[Dict[str, Any]] = None
    confidence_intervals: Optional[Dict[str, Tuple[float, float]]] = None
    
    # Additional info
    predictions: Optional[np.ndarray] = None
    probabilities: Optional[np.ndarray] = None
    returns: Optional[np.ndarray] = None
    trades: Optional[List[Dict]] = None


class ModelEvaluator:
    """Comprehensive model evaluation framework."""
    
    def __init__(self, config: EvaluationConfig):
        self.config = config
        self.results = {}
        
        # Setup plotting
        if config.create_plots:
            import os
            os.makedirs(config.plot_dir, exist_ok=True)
            
        logger.info("Initialized ModelEvaluator")
    
    def evaluate_model(self, 
                      model,
                      X_test: Union[pd.DataFrame, np.ndarray],
                      y_test: Union[pd.Series, np.ndarray],
                      model_name: str,
                      model_type: str = 'classification',
                      price_data: Optional[pd.DataFrame] = None) -> ModelPerformance:
        """Evaluate a single model comprehensively."""
        
        logger.info(f"Evaluating model: {model_name}")
        
        # Get predictions
        predictions, probabilities = self._get_model_predictions(model, X_test)
        
        # Initialize performance object
        performance = ModelPerformance(
            model_name=model_name,
            model_type=model_type,
            predictions=predictions,
            probabilities=probabilities
        )
        
        # Calculate appropriate metrics
        if model_type == 'classification':
            self._calculate_classification_metrics(performance, y_test, predictions, probabilities)
        elif model_type == 'regression':
            self._calculate_regression_metrics(performance, y_test, predictions)
        
        # Calculate trading metrics if price data available
        if price_data is not None:
            returns = self._calculate_strategy_returns(predictions, price_data, y_test)
            performance.returns = returns
            self._calculate_trading_metrics(performance, returns)
        
        # Statistical analysis
        self._perform_statistical_analysis(performance, y_test, predictions)
        
        # Store results
        self.results[model_name] = performance
        
        # Create plots
        if self.config.create_plots:
            self._create_evaluation_plots(performance, y_test, model_name)
        
        logger.info(f"Evaluation completed for {model_name}")
        return performance
    
    def _get_model_predictions(self, model, X_test) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """Get predictions and probabilities from model."""
        
        predictions = None
        probabilities = None
        
        try:
            # Try different prediction methods
            if hasattr(model, 'predict'):
                pred_result = model.predict(X_test)
                
                if isinstance(pred_result, dict):
                    # Custom model format
                    predictions = np.array([pred_result.get('signal', 0)])
                    if 'probabilities' in pred_result:
                        probabilities = np.array([list(pred_result['probabilities'].values())])
                elif isinstance(pred_result, (list, np.ndarray)):
                    predictions = np.array(pred_result)
                else:
                    predictions = np.array([pred_result])
                
                # Get probabilities if available
                if hasattr(model, 'predict_proba') and probabilities is None:
                    try:
                        probabilities = model.predict_proba(X_test)
                    except:
                        pass
                        
            else:
                logger.warning(f"Model does not have predict method")
                predictions = np.zeros(len(X_test))
                
        except Exception as e:
            logger.error(f"Error getting predictions: {e}")
            predictions = np.zeros(len(X_test))
        
        return predictions, probabilities
    
    def _calculate_classification_metrics(self, 
                                        performance: ModelPerformance,
                                        y_true: np.ndarray,
                                        y_pred: np.ndarray,
                                        y_prob: Optional[np.ndarray]):
        """Calculate classification metrics."""
        
        try:
            # Convert string predictions to numerical if needed
            if len(y_pred) > 0 and isinstance(y_pred[0], str):
                label_map = {'SELL': 0, 'HOLD': 1, 'BUY': 2}
                y_pred_num = np.array([label_map.get(pred, 1) for pred in y_pred])
            else:
                y_pred_num = y_pred
            
            # Ensure same length
            min_len = min(len(y_true), len(y_pred_num))
            y_true = y_true[:min_len]
            y_pred_num = y_pred_num[:min_len]
            
            if len(y_true) == 0:
                return
            
            # Basic metrics
            if 'accuracy' in self.config.classification_metrics:
                performance.accuracy = accuracy_score(y_true, y_pred_num)
            
            if 'precision' in self.config.classification_metrics:
                performance.precision = precision_score(y_true, y_pred_num, average='weighted', zero_division=0)
            
            if 'recall' in self.config.classification_metrics:
                performance.recall = recall_score(y_true, y_pred_num, average='weighted', zero_division=0)
            
            if 'f1' in self.config.classification_metrics:
                performance.f1_score = f1_score(y_true, y_pred_num, average='weighted', zero_division=0)
            
            # AUC (if probabilities available and binary/multiclass)
            if 'auc' in self.config.classification_metrics and y_prob is not None:
                try:
                    if len(np.unique(y_true)) == 2:  # Binary
                        if y_prob.ndim == 2 and y_prob.shape[1] == 2:
                            performance.auc_score = roc_auc_score(y_true, y_prob[:, 1])
                        elif y_prob.ndim == 1:
                            performance.auc_score = roc_auc_score(y_true, y_prob)
                    else:  # Multiclass
                        if y_prob.ndim == 2:
                            performance.auc_score = roc_auc_score(y_true, y_prob, multi_class='ovr')
                except Exception as e:
                    logger.warning(f"Could not calculate AUC: {e}")
                    
        except Exception as e:
            logger.error(f"Error calculating classification metrics: {e}")
    
    def _calculate_regression_metrics(self, 
                                    performance: ModelPerformance,
                                    y_true: np.ndarray,
                                    y_pred: np.ndarray):
        """Calculate regression metrics."""
        
        try:
            # Ensure same length
            min_len = min(len(y_true), len(y_pred))
            y_true = y_true[:min_len]
            y_pred = y_pred[:min_len]
            
            if len(y_true) == 0:
                return
            
            if 'mse' in self.config.regression_metrics:
                performance.mse = mean_squared_error(y_true, y_pred)
            
            if 'mae' in self.config.regression_metrics:
                performance.mae = mean_absolute_error(y_true, y_pred)
            
            if 'r2' in self.config.regression_metrics:
                performance.r2_score = r2_score(y_true, y_pred)
            
            # Directional accuracy
            if 'directional_accuracy' in self.config.regression_metrics:
                y_true_direction = np.sign(y_true)
                y_pred_direction = np.sign(y_pred)
                performance.directional_accuracy = accuracy_score(y_true_direction, y_pred_direction)
                
        except Exception as e:
            logger.error(f"Error calculating regression metrics: {e}")
    
    def _calculate_strategy_returns(self, 
                                   predictions: np.ndarray,
                                   price_data: pd.DataFrame,
                                   y_true: np.ndarray) -> np.ndarray:
        """Calculate strategy returns based on predictions."""
        
        try:
            # Convert predictions to position signals
            if len(predictions) > 0 and isinstance(predictions[0], str):
                # String signals
                position_map = {'SELL': -1, 'HOLD': 0, 'BUY': 1}
                positions = np.array([position_map.get(pred, 0) for pred in predictions])
            else:
                # Numerical signals
                positions = np.sign(predictions)
            
            # Ensure same length as price data
            min_len = min(len(positions), len(price_data), len(y_true))
            positions = positions[:min_len]
            
            # Calculate returns
            if 'close' in price_data.columns:
                market_returns = price_data['close'].pct_change().iloc[1:min_len+1].values
            else:
                # Use target returns if available
                market_returns = y_true[:min_len]
            
            # Strategy returns = position * market_returns
            strategy_returns = positions[:-1] * market_returns[:len(positions)-1]
            
            return strategy_returns
            
        except Exception as e:
            logger.error(f"Error calculating strategy returns: {e}")
            return np.array([])
    
    def _calculate_trading_metrics(self, 
                                  performance: ModelPerformance,
                                  returns: np.ndarray):
        """Calculate trading-specific metrics."""
        
        if len(returns) == 0:
            return
        
        try:
            # Annualization factor (assuming daily returns)
            annual_factor = np.sqrt(252)
            
            # Sharpe ratio
            if 'sharpe_ratio' in self.config.trading_metrics:
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                performance.sharpe_ratio = (mean_return / std_return) * annual_factor if std_return > 0 else 0
            
            # Sortino ratio
            if 'sortino_ratio' in self.config.trading_metrics:
                mean_return = np.mean(returns)
                negative_returns = returns[returns < 0]
                downside_std = np.std(negative_returns) if len(negative_returns) > 0 else 0
                performance.sortino_ratio = (mean_return / downside_std) * annual_factor if downside_std > 0 else 0
            
            # Maximum drawdown
            if 'max_drawdown' in self.config.trading_metrics:
                cumulative_returns = np.cumprod(1 + returns)
                peak = np.maximum.accumulate(cumulative_returns)
                drawdown = (peak - cumulative_returns) / peak
                performance.max_drawdown = np.max(drawdown)
            
            # Calmar ratio
            if 'calmar_ratio' in self.config.trading_metrics:
                annual_return = (np.prod(1 + returns) ** (252 / len(returns))) - 1
                performance.calmar_ratio = annual_return / (performance.max_drawdown + 1e-8)
            
            # Hit rate
            if 'hit_rate' in self.config.trading_metrics:
                positive_returns = returns[returns > 0]
                performance.hit_rate = len(positive_returns) / len(returns)
            
            # Profit factor
            if 'profit_factor' in self.config.trading_metrics:
                gross_profit = np.sum(returns[returns > 0])
                gross_loss = abs(np.sum(returns[returns < 0]))
                performance.profit_factor = gross_profit / (gross_loss + 1e-8)
            
            # Average return per trade
            if 'avg_return_per_trade' in self.config.trading_metrics:
                performance.avg_return_per_trade = np.mean(returns)
                
        except Exception as e:
            logger.error(f"Error calculating trading metrics: {e}")
    
    def _perform_statistical_analysis(self, 
                                    performance: ModelPerformance,
                                    y_true: np.ndarray,
                                    y_pred: np.ndarray):
        """Perform statistical significance tests."""
        
        try:
            # Bootstrap confidence intervals
            if self.config.bootstrap_samples > 0:
                confidence_intervals = self._bootstrap_confidence_intervals(
                    y_true, y_pred, performance
                )
                performance.confidence_intervals = confidence_intervals
            
            # Statistical significance tests
            statistical_tests = {}
            
            # T-test for mean difference (if applicable)
            if performance.returns is not None and len(performance.returns) > 1:
                t_stat, p_value = stats.ttest_1samp(performance.returns, 0)
                statistical_tests['t_test'] = {
                    't_statistic': t_stat,
                    'p_value': p_value,
                    'significant': p_value < (1 - self.config.confidence_level)
                }
            
            performance.statistical_significance = statistical_tests
            
        except Exception as e:
            logger.error(f"Error in statistical analysis: {e}")
    
    def _bootstrap_confidence_intervals(self, 
                                      y_true: np.ndarray,
                                      y_pred: np.ndarray,
                                      performance: ModelPerformance) -> Dict[str, Tuple[float, float]]:
        """Calculate bootstrap confidence intervals for metrics."""
        
        confidence_intervals = {}
        
        try:
            # Ensure same length
            min_len = min(len(y_true), len(y_pred))
            y_true = y_true[:min_len]
            y_pred = y_pred[:min_len]
            
            if min_len < 10:  # Not enough data for bootstrap
                return confidence_intervals
            
            # Bootstrap samples
            n_samples = self.config.bootstrap_samples
            alpha = 1 - self.config.confidence_level
            
            # Accuracy bootstrap
            if performance.accuracy is not None:
                accuracies = []
                for _ in range(n_samples):
                    indices = np.random.choice(min_len, min_len, replace=True)
                    boot_accuracy = accuracy_score(y_true[indices], y_pred[indices])
                    accuracies.append(boot_accuracy)
                
                confidence_intervals['accuracy'] = (
                    np.percentile(accuracies, 100 * alpha / 2),
                    np.percentile(accuracies, 100 * (1 - alpha / 2))
                )
            
            # Sharpe ratio bootstrap (if returns available)
            if performance.returns is not None and len(performance.returns) > 1:
                sharpe_ratios = []
                returns = performance.returns
                
                for _ in range(n_samples):
                    indices = np.random.choice(len(returns), len(returns), replace=True)
                    boot_returns = returns[indices]
                    
                    mean_ret = np.mean(boot_returns)
                    std_ret = np.std(boot_returns)
                    sharpe = (mean_ret / std_ret) * np.sqrt(252) if std_ret > 0 else 0
                    sharpe_ratios.append(sharpe)
                
                confidence_intervals['sharpe_ratio'] = (
                    np.percentile(sharpe_ratios, 100 * alpha / 2),
                    np.percentile(sharpe_ratios, 100 * (1 - alpha / 2))
                )
                
        except Exception as e:
            logger.error(f"Error calculating confidence intervals: {e}")
        
        return confidence_intervals
    
    def _create_evaluation_plots(self, 
                               performance: ModelPerformance,
                               y_true: np.ndarray,
                               model_name: str):
        """Create evaluation plots."""
        
        try:
            # Set up the plots
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'Model Evaluation: {model_name}', fontsize=16)
            
            # 1. Confusion Matrix (if classification)
            if performance.accuracy is not None and performance.predictions is not None:
                min_len = min(len(y_true), len(performance.predictions))
                y_true_plot = y_true[:min_len]
                y_pred_plot = performance.predictions[:min_len]
                
                # Convert string predictions if needed
                if len(y_pred_plot) > 0 and isinstance(y_pred_plot[0], str):
                    label_map = {'SELL': 0, 'HOLD': 1, 'BUY': 2}
                    y_pred_plot = np.array([label_map.get(pred, 1) for pred in y_pred_plot])
                
                cm = confusion_matrix(y_true_plot, y_pred_plot)
                sns.heatmap(cm, annot=True, fmt='d', ax=axes[0, 0])
                axes[0, 0].set_title('Confusion Matrix')
                axes[0, 0].set_xlabel('Predicted')
                axes[0, 0].set_ylabel('Actual')
            
            # 2. Prediction vs Actual scatter plot
            if performance.predictions is not None:
                min_len = min(len(y_true), len(performance.predictions))
                y_true_plot = y_true[:min_len]
                y_pred_plot = performance.predictions[:min_len]
                
                # Convert string predictions for plotting
                if len(y_pred_plot) > 0 and isinstance(y_pred_plot[0], str):
                    label_map = {'SELL': -1, 'HOLD': 0, 'BUY': 1}
                    y_pred_plot = np.array([label_map.get(pred, 0) for pred in y_pred_plot])
                
                axes[0, 1].scatter(y_true_plot, y_pred_plot, alpha=0.6)
                axes[0, 1].plot([y_true_plot.min(), y_true_plot.max()], 
                               [y_true_plot.min(), y_true_plot.max()], 'r--', lw=2)
                axes[0, 1].set_xlabel('Actual')
                axes[0, 1].set_ylabel('Predicted')
                axes[0, 1].set_title('Predictions vs Actual')
            
            # 3. Returns distribution (if available)
            if performance.returns is not None and len(performance.returns) > 0:
                axes[1, 0].hist(performance.returns, bins=50, alpha=0.7, edgecolor='black')
                axes[1, 0].axvline(np.mean(performance.returns), color='red', linestyle='--', 
                                  label=f'Mean: {np.mean(performance.returns):.4f}')
                axes[1, 0].set_xlabel('Returns')
                axes[1, 0].set_ylabel('Frequency')
                axes[1, 0].set_title('Returns Distribution')
                axes[1, 0].legend()
            
            # 4. Cumulative returns (if available)
            if performance.returns is not None and len(performance.returns) > 0:
                cumulative_returns = np.cumprod(1 + performance.returns)
                axes[1, 1].plot(cumulative_returns)
                axes[1, 1].set_xlabel('Time')
                axes[1, 1].set_ylabel('Cumulative Returns')
                axes[1, 1].set_title('Cumulative Returns')
                axes[1, 1].grid(True)
            
            plt.tight_layout()
            
            # Save plot if requested
            if self.config.save_plots:
                plot_path = f"{self.config.plot_dir}/{model_name}_evaluation.png"
                plt.savefig(plot_path, dpi=300, bbox_inches='tight')
                logger.info(f"Evaluation plot saved: {plot_path}")
            
            plt.show()
            
        except Exception as e:
            logger.error(f"Error creating evaluation plots: {e}")
    
    def compare_models(self, model_names: Optional[List[str]] = None) -> pd.DataFrame:
        """Compare multiple models."""
        
        if model_names is None:
            model_names = list(self.results.keys())
        
        if not model_names:
            logger.warning("No models to compare")
            return pd.DataFrame()
        
        # Collect metrics
        comparison_data = []
        
        for model_name in model_names:
            if model_name not in self.results:
                continue
                
            performance = self.results[model_name]
            
            row = {
                'Model': model_name,
                'Type': performance.model_type,
                'Accuracy': performance.accuracy,
                'Precision': performance.precision,
                'Recall': performance.recall,
                'F1 Score': performance.f1_score,
                'AUC': performance.auc_score,
                'MSE': performance.mse,
                'MAE': performance.mae,
                'R²': performance.r2_score,
                'Directional Accuracy': performance.directional_accuracy,
                'Sharpe Ratio': performance.sharpe_ratio,
                'Sortino Ratio': performance.sortino_ratio,
                'Max Drawdown': performance.max_drawdown,
                'Hit Rate': performance.hit_rate,
                'Profit Factor': performance.profit_factor
            }
            
            comparison_data.append(row)
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # Create comparison plot
        if self.config.create_plots and len(comparison_df) > 1:
            self._create_comparison_plot(comparison_df)
        
        return comparison_df
    
    def _create_comparison_plot(self, comparison_df: pd.DataFrame):
        """Create model comparison plot."""
        
        try:
            # Select key metrics for comparison
            metrics_to_plot = []
            
            # Add available metrics
            if 'Accuracy' in comparison_df.columns and comparison_df['Accuracy'].notna().any():
                metrics_to_plot.append('Accuracy')
            if 'Sharpe Ratio' in comparison_df.columns and comparison_df['Sharpe Ratio'].notna().any():
                metrics_to_plot.append('Sharpe Ratio')
            if 'Directional Accuracy' in comparison_df.columns and comparison_df['Directional Accuracy'].notna().any():
                metrics_to_plot.append('Directional Accuracy')
            if 'Hit Rate' in comparison_df.columns and comparison_df['Hit Rate'].notna().any():
                metrics_to_plot.append('Hit Rate')
            
            if not metrics_to_plot:
                return
            
            # Create subplots
            n_metrics = len(metrics_to_plot)
            n_cols = min(3, n_metrics)
            n_rows = (n_metrics + n_cols - 1) // n_cols
            
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
            if n_metrics == 1:
                axes = [axes]
            elif n_rows == 1:
                axes = axes.reshape(1, -1)
            
            for i, metric in enumerate(metrics_to_plot):
                row = i // n_cols
                col = i % n_cols
                ax = axes[row, col] if n_rows > 1 else axes[col]
                
                # Filter out NaN values
                data_to_plot = comparison_df[['Model', metric]].dropna()
                
                if len(data_to_plot) > 0:
                    bars = ax.bar(data_to_plot['Model'], data_to_plot[metric])
                    ax.set_title(f'{metric} Comparison')
                    ax.set_ylabel(metric)
                    
                    # Add value labels on bars
                    for bar, value in zip(bars, data_to_plot[metric]):
                        height = bar.get_height()
                        ax.text(bar.get_x() + bar.get_width()/2., height,
                               f'{value:.3f}', ha='center', va='bottom')
                    
                    # Rotate x-axis labels if needed
                    if len(data_to_plot) > 3:
                        ax.tick_params(axis='x', rotation=45)
            
            # Remove empty subplots
            for i in range(n_metrics, n_rows * n_cols):
                row = i // n_cols
                col = i % n_cols
                if n_rows > 1:
                    fig.delaxes(axes[row, col])
                else:
                    fig.delaxes(axes[col])
            
            plt.tight_layout()
            
            # Save plot if requested
            if self.config.save_plots:
                plot_path = f"{self.config.plot_dir}/model_comparison.png"
                plt.savefig(plot_path, dpi=300, bbox_inches='tight')
                logger.info(f"Comparison plot saved: {plot_path}")
            
            plt.show()
            
        except Exception as e:
            logger.error(f"Error creating comparison plot: {e}")
    
    def get_evaluation_summary(self) -> Dict[str, Any]:
        """Get summary of all evaluations."""
        
        summary = {
            'n_models_evaluated': len(self.results),
            'models': list(self.results.keys()),
            'config': self.config.__dict__
        }
        
        # Add best models for key metrics
        if self.results:
            comparison_df = self.compare_models()
            
            if not comparison_df.empty:
                best_models = {}
                
                for metric in ['Accuracy', 'Sharpe Ratio', 'Directional Accuracy', 'Hit Rate']:
                    if metric in comparison_df.columns:
                        best_idx = comparison_df[metric].idxmax()
                        if not pd.isna(best_idx):
                            best_models[f'best_{metric.lower().replace(" ", "_")}'] = {
                                'model': comparison_df.loc[best_idx, 'Model'],
                                'value': comparison_df.loc[best_idx, metric]
                            }
                
                summary['best_models'] = best_models
        
        return summary
    
    def save_results(self, filepath: str):
        """Save evaluation results."""
        
        # Prepare serializable results
        serializable_results = {}
        
        for model_name, performance in self.results.items():
            serializable_results[model_name] = {
                'model_name': performance.model_name,
                'model_type': performance.model_type,
                'accuracy': performance.accuracy,
                'precision': performance.precision,
                'recall': performance.recall,
                'f1_score': performance.f1_score,
                'auc_score': performance.auc_score,
                'mse': performance.mse,
                'mae': performance.mae,
                'r2_score': performance.r2_score,
                'directional_accuracy': performance.directional_accuracy,
                'sharpe_ratio': performance.sharpe_ratio,
                'sortino_ratio': performance.sortino_ratio,
                'calmar_ratio': performance.calmar_ratio,
                'max_drawdown': performance.max_drawdown,
                'hit_rate': performance.hit_rate,
                'profit_factor': performance.profit_factor,
                'avg_return_per_trade': performance.avg_return_per_trade,
                'statistical_significance': performance.statistical_significance,
                'confidence_intervals': performance.confidence_intervals
            }
        
        # Save to file
        import json
        with open(filepath, 'w') as f:
            json.dump({
                'config': self.config.__dict__,
                'results': serializable_results,
                'summary': self.get_evaluation_summary()
            }, f, indent=2, default=str)
        
        logger.info(f"Evaluation results saved to {filepath}")