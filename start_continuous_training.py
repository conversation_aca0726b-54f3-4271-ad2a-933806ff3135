#!/usr/bin/env python3
"""
Startup script for continuous ML training
"""

import asyncio
import sys
import argparse
from pathlib import Path
from continuous_ml_trainer import ContinuousMLTrainer

def main():
    parser = argparse.ArgumentParser(description='Start continuous ML training')
    parser.add_argument('--symbols', nargs='+', default=['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN'],
                        help='Stock symbols to train on')
    parser.add_argument('--training-window', type=int, default=90,
                        help='Training window in days')
    parser.add_argument('--retrain-frequency', type=int, default=24,
                        help='Retrain frequency in hours')
    parser.add_argument('--performance-threshold', type=float, default=0.55,
                        help='Minimum performance threshold')
    parser.add_argument('--run-once', action='store_true',
                        help='Run training once and exit')
    
    args = parser.parse_args()
    
    print(f"""
    🚀 Starting Continuous ML Training System
    ==========================================
    
    Symbols: {args.symbols}
    Training window: {args.training_window} days
    Retrain frequency: {args.retrain_frequency} hours
    Performance threshold: {args.performance_threshold}
    Run once: {args.run_once}
    """)
    
    # Create trainer
    trainer = ContinuousMLTrainer(
        symbols=args.symbols,
        training_window_days=args.training_window,
        retrain_frequency_hours=args.retrain_frequency,
        performance_threshold=args.performance_threshold
    )
    
    async def run_training():
        if args.run_once:
            # Run single training cycle
            await trainer.single_training_cycle()
        else:
            # Run continuous training
            await trainer.run_continuous_training()
    
    try:
        asyncio.run(run_training())
    except KeyboardInterrupt:
        print("\n⏹️  Training stopped by user")
        trainer.stop_training()
    except Exception as e:
        print(f"\n💥 Training failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()