"""
Paper Trading System for Testing Strategies
Simulates real trading with virtual money
"""
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional
import json
import os
from dataclasses import dataclass, asdict
import asyncio
import yfinance as yf
import sys

# Add project path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from test_volatility_hunter import VolatilityHunter, VolatilityOpportunity

@dataclass
class PaperTrade:
    """Represents a paper trade"""
    trade_id: str
    symbol: str
    strategy: str
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: int
    entry_time: datetime
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    status: str = "OPEN"  # OPEN, CLOSED, STOPPED
    pnl: float = 0.0
    pnl_percent: float = 0.0

class PaperTradingEngine:
    def __init__(self, starting_capital: float = 10000):
        self.capital = starting_capital
        self.starting_capital = starting_capital
        self.positions: Dict[str, PaperTrade] = {}
        self.closed_trades: List[PaperTrade] = []
        self.trade_log_file = "paper_trades.json"
        self.volatility_hunter = VolatilityHunter()
        
        # Load existing trades if any
        self._load_trades()
    
    async def run_paper_trading(self, max_iterations: int = 5):
        """Main paper trading loop"""
        print(f"🚀 Starting Paper Trading with ${self.capital:,.2f}")
        
        iteration = 0
        while iteration < max_iterations:
            try:
                print(f"\n🔄 Trading iteration {iteration + 1}/{max_iterations}")
                
                # 1. Update existing positions
                await self._update_positions()
                
                # 2. Find new opportunities
                opportunities = self.volatility_hunter.scan_for_opportunities(
                    portfolio_value=self.capital
                )
                
                # 3. Execute new trades
                for opp in opportunities[:3]:  # Take top 3 opportunities
                    if self._can_trade(opp):
                        await self._execute_trade(opp)
                
                # 4. Display status
                self._display_status()
                
                # 5. Save state
                self._save_trades()
                
                iteration += 1
                
                # Simulate time passage
                if iteration < max_iterations:
                    print(f"⏳ Waiting 10 seconds before next iteration...")
                    await asyncio.sleep(10)
                
            except KeyboardInterrupt:
                print("\n📊 Paper trading stopped by user")
                break
            except Exception as e:
                print(f"Error in paper trading loop: {e}")
                iteration += 1
                await asyncio.sleep(5)
        
        print(f"\n🏁 Paper trading completed after {iteration} iterations")
        self._display_final_summary()
    
    async def _update_positions(self):
        """Update all open positions with current prices"""
        for symbol, trade in list(self.positions.items()):
            try:
                # Get current price
                ticker = yf.Ticker(symbol)
                info = ticker.info
                current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
                
                if current_price == 0:
                    # Try history
                    hist = ticker.history(period="1d", interval="1m")
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                
                if current_price == 0:
                    continue
                
                # Check stop loss
                if current_price <= trade.stop_loss:
                    await self._close_position(trade, current_price, "STOPPED")
                
                # Check target
                elif current_price >= trade.target_price:
                    await self._close_position(trade, current_price, "TARGET")
                
                # Update unrealized P&L
                else:
                    trade.pnl = (current_price - trade.entry_price) * trade.position_size
                    trade.pnl_percent = ((current_price / trade.entry_price) - 1) * 100
                    
            except Exception as e:
                print(f"Error updating position {symbol}: {e}")
    
    def _can_trade(self, opportunity: VolatilityOpportunity) -> bool:
        """Check if we can take this trade"""
        # Check if already in position
        if opportunity.symbol in self.positions:
            return False
        
        # Check capital requirements
        required_capital = opportunity.entry_price * opportunity.position_size
        if required_capital > self.capital * 0.25:  # Max 25% per position
            return False
        
        # Check max positions
        if len(self.positions) >= 5:  # Max 5 concurrent positions
            return False
        
        return True
    
    async def _execute_trade(self, opportunity: VolatilityOpportunity):
        """Execute a paper trade"""
        trade = PaperTrade(
            trade_id=f"{opportunity.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            symbol=opportunity.symbol,
            strategy=opportunity.strategy_type,
            entry_price=opportunity.entry_price,
            target_price=opportunity.target_price,
            stop_loss=opportunity.stop_loss,
            position_size=opportunity.position_size,
            entry_time=datetime.now()
        )
        
        # Add to positions
        self.positions[opportunity.symbol] = trade
        
        # Update capital
        self.capital -= trade.entry_price * trade.position_size
        
        print(f"\n✅ PAPER TRADE EXECUTED:")
        print(f"   Symbol: {trade.symbol}")
        print(f"   Strategy: {trade.strategy}")
        print(f"   Entry: ${trade.entry_price:.2f}")
        print(f"   Target: ${trade.target_price:.2f}")
        print(f"   Stop: ${trade.stop_loss:.2f}")
        print(f"   Size: {trade.position_size} shares")
    
    async def _close_position(self, trade: PaperTrade, exit_price: float, reason: str):
        """Close a paper position"""
        trade.exit_time = datetime.now()
        trade.exit_price = exit_price
        trade.status = reason
        trade.pnl = (exit_price - trade.entry_price) * trade.position_size
        trade.pnl_percent = ((exit_price / trade.entry_price) - 1) * 100
        
        # Update capital
        self.capital += exit_price * trade.position_size
        
        # Move to closed trades
        self.closed_trades.append(trade)
        del self.positions[trade.symbol]
        
        print(f"\n💰 POSITION CLOSED:")
        print(f"   Symbol: {trade.symbol}")
        print(f"   Reason: {reason}")
        print(f"   P&L: ${trade.pnl:.2f} ({trade.pnl_percent:.1f}%)")
    
    def _display_status(self):
        """Display current trading status"""
        total_value = self.capital + sum(
            t.entry_price * t.position_size + t.pnl 
            for t in self.positions.values()
        )
        
        print(f"\n📊 PAPER TRADING STATUS")
        print(f"{'='*50}")
        print(f"Capital: ${self.capital:,.2f}")
        print(f"Positions: {len(self.positions)}")
        print(f"Total Value: ${total_value:,.2f}")
        print(f"Total Return: {((total_value/self.starting_capital)-1)*100:.1f}%")
        
        if self.closed_trades:
            wins = sum(1 for t in self.closed_trades if t.pnl > 0)
            total_trades = len(self.closed_trades)
            win_rate = (wins / total_trades) * 100 if total_trades > 0 else 0
            
            print(f"\nClosed Trades: {total_trades}")
            print(f"Win Rate: {win_rate:.1f}%")
            print(f"Total P&L: ${sum(t.pnl for t in self.closed_trades):.2f}")
    
    def _display_final_summary(self):
        """Display final trading summary"""
        total_value = self.capital + sum(
            t.entry_price * t.position_size + t.pnl 
            for t in self.positions.values()
        )
        
        print(f"\n🎯 FINAL PAPER TRADING SUMMARY")
        print(f"{'='*60}")
        print(f"Starting Capital: ${self.starting_capital:,.2f}")
        print(f"Final Value: ${total_value:,.2f}")
        print(f"Total Return: {((total_value/self.starting_capital)-1)*100:.2f}%")
        
        if self.closed_trades:
            wins = sum(1 for t in self.closed_trades if t.pnl > 0)
            total_trades = len(self.closed_trades)
            win_rate = (wins / total_trades) * 100 if total_trades > 0 else 0
            avg_win = np.mean([t.pnl for t in self.closed_trades if t.pnl > 0]) if wins > 0 else 0
            avg_loss = np.mean([t.pnl for t in self.closed_trades if t.pnl < 0]) if total_trades > wins else 0
            
            print(f"\nTrade Statistics:")
            print(f"Total Trades: {total_trades}")
            print(f"Wins: {wins} | Losses: {total_trades - wins}")
            print(f"Win Rate: {win_rate:.1f}%")
            print(f"Average Win: ${avg_win:.2f}")
            print(f"Average Loss: ${avg_loss:.2f}")
            print(f"Profit Factor: {abs(avg_win/avg_loss):.2f}" if avg_loss != 0 else "N/A")
            
            print(f"\nTop 3 Trades:")
            sorted_trades = sorted(self.closed_trades, key=lambda x: x.pnl, reverse=True)
            for i, trade in enumerate(sorted_trades[:3], 1):
                print(f"{i}. {trade.symbol}: ${trade.pnl:.2f} ({trade.pnl_percent:.1f}%)")
    
    def _save_trades(self):
        """Save trades to file"""
        data = {
            'capital': self.capital,
            'starting_capital': self.starting_capital,
            'positions': [asdict(t) for t in self.positions.values()],
            'closed_trades': [asdict(t) for t in self.closed_trades]
        }
        
        with open(self.trade_log_file, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    
    def _load_trades(self):
        """Load trades from file"""
        if os.path.exists(self.trade_log_file):
            try:
                with open(self.trade_log_file, 'r') as f:
                    data = json.load(f)
                    self.capital = data.get('capital', self.starting_capital)
                    # Load closed trades history for statistics
                    for trade_data in data.get('closed_trades', []):
                        trade = PaperTrade(**trade_data)
                        # Convert string datetime back to datetime objects
                        if isinstance(trade.entry_time, str):
                            trade.entry_time = datetime.fromisoformat(trade.entry_time.replace('Z', '+00:00'))
                        if trade.exit_time and isinstance(trade.exit_time, str):
                            trade.exit_time = datetime.fromisoformat(trade.exit_time.replace('Z', '+00:00'))
                        self.closed_trades.append(trade)
            except Exception as e:
                print(f"Error loading trades: {e}")

# Main execution
async def run_paper_trading():
    """Run the paper trading system"""
    engine = PaperTradingEngine(starting_capital=10000)
    await engine.run_paper_trading()

if __name__ == "__main__":
    asyncio.run(run_paper_trading())