"""
Strategy Evolution using Genetic Algorithms

Automatically evolves trading strategy parameters using genetic algorithms
to find optimal configurations based on historical performance.
"""

from typing import Dict, List, Any, Tuple, Optional
import numpy as np
import random
import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

from ...core.logger import get_logger
from ...data.models import Trade
from .performance_analyzer import PerformanceAnalyzer

logger = get_logger(__name__)

@dataclass
class StrategyGenome:
    """Represents a strategy configuration as a genome"""
    parameters: Dict[str, float]
    fitness: float = 0.0
    generation: int = 0
    parent_ids: List[str] = None
    
    def __post_init__(self):
        if self.parent_ids is None:
            self.parent_ids = []

@dataclass
class EvolutionConfig:
    """Configuration for genetic algorithm evolution"""
    population_size: int = 50
    elite_size: int = 10
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    max_generations: int = 100
    convergence_threshold: float = 0.001
    fitness_metric: str = 'sharpe_ratio'
    
class StrategyEvolution:
    """Genetic algorithm-based strategy improvement"""
    
    def __init__(self, performance_analyzer: PerformanceAnalyzer, config: EvolutionConfig = None):
        self.performance_analyzer = performance_analyzer
        self.config = config or EvolutionConfig()
        self.population: List[StrategyGenome] = []
        self.generation = 0
        self.best_fitness_history = []
        self.convergence_count = 0
        
    async def evolve_strategy(self, 
                            base_strategy: Dict[str, Any], 
                            parameter_bounds: Dict[str, Tuple[float, float]],
                            fitness_metric: str = 'sharpe_ratio') -> StrategyGenome:
        """
        Use genetic algorithms to evolve better strategy parameters
        
        Args:
            base_strategy: Base strategy configuration
            parameter_bounds: Min/max bounds for each parameter
            fitness_metric: Metric to optimize (sharpe_ratio, total_return, etc.)
            
        Returns:
            Best evolved strategy genome
        """
        logger.info(f"Starting strategy evolution with {self.config.population_size} individuals")
        
        # Initialize population
        self.population = await self._create_initial_population(base_strategy, parameter_bounds)
        
        best_genome = None
        
        for generation in range(self.config.max_generations):
            self.generation = generation
            
            # Evaluate fitness for all individuals
            await self._evaluate_population_fitness(fitness_metric)
            
            # Sort by fitness (descending)
            self.population.sort(key=lambda x: x.fitness, reverse=True)
            
            # Track best fitness
            best_fitness = self.population[0].fitness
            self.best_fitness_history.append(best_fitness)
            
            # Check for convergence
            if await self._check_convergence():
                logger.info(f"Converged at generation {generation}")
                break
            
            # Select elite individuals
            elite = self.population[:self.config.elite_size]
            
            # Create new generation
            new_population = elite.copy()
            
            while len(new_population) < self.config.population_size:
                # Selection
                parent1 = await self._tournament_selection()
                parent2 = await self._tournament_selection()
                
                # Crossover
                if random.random() < self.config.crossover_rate:
                    child1, child2 = await self._crossover(parent1, parent2, parameter_bounds)
                else:
                    child1, child2 = parent1, parent2
                
                # Mutation
                if random.random() < self.config.mutation_rate:
                    child1 = await self._mutate(child1, parameter_bounds)
                if random.random() < self.config.mutation_rate:
                    child2 = await self._mutate(child2, parameter_bounds)
                
                new_population.extend([child1, child2])
            
            # Trim to population size
            self.population = new_population[:self.config.population_size]
            
            # Log progress
            logger.info(f"Generation {generation}: Best fitness = {best_fitness:.4f}")
            
            # Update generation for all individuals
            for genome in self.population:
                genome.generation = generation
        
        # Return best individual
        best_genome = max(self.population, key=lambda x: x.fitness)
        logger.info(f"Evolution complete. Best fitness: {best_genome.fitness:.4f}")
        
        return best_genome
    
    async def _create_initial_population(self, 
                                       base_strategy: Dict[str, Any],
                                       parameter_bounds: Dict[str, Tuple[float, float]]) -> List[StrategyGenome]:
        """Create initial population with random variations"""
        population = []
        
        # Add base strategy as one individual
        base_genome = StrategyGenome(
            parameters=base_strategy.copy(),
            generation=0
        )
        population.append(base_genome)
        
        # Generate random variations
        for i in range(self.config.population_size - 1):
            parameters = {}
            
            for param_name, (min_val, max_val) in parameter_bounds.items():
                # Random value within bounds
                if param_name in base_strategy:
                    # Start near base value with some variation
                    base_val = base_strategy[param_name]
                    variation = (max_val - min_val) * 0.2  # 20% variation
                    value = np.clip(
                        np.random.normal(base_val, variation),
                        min_val, max_val
                    )
                else:
                    # Completely random within bounds
                    value = np.random.uniform(min_val, max_val)
                
                parameters[param_name] = value
            
            genome = StrategyGenome(
                parameters=parameters,
                generation=0
            )
            population.append(genome)
        
        return population
    
    async def _evaluate_population_fitness(self, fitness_metric: str):
        """Evaluate fitness for all individuals in population"""
        tasks = []
        
        for genome in self.population:
            if genome.fitness == 0.0:  # Only evaluate if not already evaluated
                task = self._evaluate_individual_fitness(genome, fitness_metric)
                tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks)
    
    async def _evaluate_individual_fitness(self, genome: StrategyGenome, fitness_metric: str):
        """Evaluate fitness for a single individual"""
        try:
            # This would run a backtest with the genome's parameters
            # For now, we'll simulate this with a simplified calculation
            
            # In a real implementation, you would:
            # 1. Apply the genome's parameters to the strategy
            # 2. Run a backtest over historical data
            # 3. Calculate the fitness metric
            
            fitness = await self._simulate_strategy_performance(genome.parameters, fitness_metric)
            genome.fitness = fitness
            
        except Exception as e:
            logger.error(f"Error evaluating genome fitness: {e}")
            genome.fitness = -1.0  # Penalty for failed evaluation
    
    async def _simulate_strategy_performance(self, parameters: Dict[str, float], metric: str) -> float:
        """Simulate strategy performance with given parameters"""
        # This is a simplified simulation
        # In reality, you would run a full backtest
        
        # Simulate some performance metrics based on parameters
        base_performance = 0.5
        
        # Add some parameter-based adjustments
        for param_name, value in parameters.items():
            if 'threshold' in param_name.lower():
                # Higher thresholds might reduce trades but improve quality
                base_performance += (value - 0.5) * 0.1
            elif 'size' in param_name.lower():
                # Position size affects risk/return
                base_performance += (value - 0.02) * 2.0
        
        # Add some randomness to simulate market uncertainty
        noise = np.random.normal(0, 0.1)
        performance = base_performance + noise
        
        # Convert to requested metric
        if metric == 'sharpe_ratio':
            return max(performance, -2.0)  # Cap at -2.0
        elif metric == 'total_return':
            return performance * 100  # Convert to percentage
        else:
            return performance
    
    async def _tournament_selection(self, tournament_size: int = 3) -> StrategyGenome:
        """Select individual using tournament selection"""
        tournament = random.sample(self.population, min(tournament_size, len(self.population)))
        return max(tournament, key=lambda x: x.fitness)
    
    async def _crossover(self, 
                        parent1: StrategyGenome, 
                        parent2: StrategyGenome,
                        parameter_bounds: Dict[str, Tuple[float, float]]) -> Tuple[StrategyGenome, StrategyGenome]:
        """Create offspring using crossover"""
        child1_params = {}
        child2_params = {}
        
        for param_name in parent1.parameters:
            if param_name in parent2.parameters:
                # Blend crossover
                alpha = random.random()
                val1 = parent1.parameters[param_name]
                val2 = parent2.parameters[param_name]
                
                child1_val = alpha * val1 + (1 - alpha) * val2
                child2_val = alpha * val2 + (1 - alpha) * val1
                
                # Ensure within bounds
                if param_name in parameter_bounds:
                    min_val, max_val = parameter_bounds[param_name]
                    child1_val = np.clip(child1_val, min_val, max_val)
                    child2_val = np.clip(child2_val, min_val, max_val)
                
                child1_params[param_name] = child1_val
                child2_params[param_name] = child2_val
            else:
                # Parameter only in one parent
                child1_params[param_name] = parent1.parameters[param_name]
                if param_name in parent2.parameters:
                    child2_params[param_name] = parent2.parameters[param_name]
        
        child1 = StrategyGenome(
            parameters=child1_params,
            generation=self.generation + 1,
            parent_ids=[id(parent1), id(parent2)]
        )
        
        child2 = StrategyGenome(
            parameters=child2_params,
            generation=self.generation + 1,
            parent_ids=[id(parent1), id(parent2)]
        )
        
        return child1, child2

    async def _mutate(self,
                     genome: StrategyGenome,
                     parameter_bounds: Dict[str, Tuple[float, float]]) -> StrategyGenome:
        """Apply mutation to a genome"""
        mutated_params = genome.parameters.copy()

        for param_name, value in mutated_params.items():
            if random.random() < 0.1:  # 10% chance to mutate each parameter
                if param_name in parameter_bounds:
                    min_val, max_val = parameter_bounds[param_name]

                    # Gaussian mutation
                    mutation_strength = (max_val - min_val) * 0.05  # 5% of range
                    mutated_value = value + np.random.normal(0, mutation_strength)

                    # Ensure within bounds
                    mutated_params[param_name] = np.clip(mutated_value, min_val, max_val)

        return StrategyGenome(
            parameters=mutated_params,
            generation=self.generation + 1,
            parent_ids=[id(genome)]
        )

    async def _check_convergence(self) -> bool:
        """Check if population has converged"""
        if len(self.best_fitness_history) < 10:
            return False

        # Check if fitness hasn't improved significantly in last 10 generations
        recent_fitness = self.best_fitness_history[-10:]
        fitness_std = np.std(recent_fitness)

        if fitness_std < self.config.convergence_threshold:
            self.convergence_count += 1
        else:
            self.convergence_count = 0

        return self.convergence_count >= 5  # Converged if stable for 5 generations

    def get_evolution_statistics(self) -> Dict[str, Any]:
        """Get statistics about the evolution process"""
        if not self.best_fitness_history:
            return {}

        return {
            'generations_completed': len(self.best_fitness_history),
            'best_fitness': max(self.best_fitness_history),
            'initial_fitness': self.best_fitness_history[0],
            'improvement': max(self.best_fitness_history) - self.best_fitness_history[0],
            'convergence_generation': len(self.best_fitness_history) if self.convergence_count >= 5 else None,
            'fitness_history': self.best_fitness_history.copy()
        }

    async def save_evolution_results(self, filepath: str, best_genome: StrategyGenome):
        """Save evolution results to file"""
        results = {
            'best_genome': {
                'parameters': best_genome.parameters,
                'fitness': best_genome.fitness,
                'generation': best_genome.generation
            },
            'evolution_config': {
                'population_size': self.config.population_size,
                'elite_size': self.config.elite_size,
                'mutation_rate': self.config.mutation_rate,
                'crossover_rate': self.config.crossover_rate,
                'max_generations': self.config.max_generations
            },
            'statistics': self.get_evolution_statistics(),
            'timestamp': datetime.now().isoformat()
        }

        try:
            with open(filepath, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Evolution results saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving evolution results: {e}")

    async def load_evolution_results(self, filepath: str) -> Optional[StrategyGenome]:
        """Load previously saved evolution results"""
        try:
            with open(filepath, 'r') as f:
                results = json.load(f)

            best_genome_data = results['best_genome']
            genome = StrategyGenome(
                parameters=best_genome_data['parameters'],
                fitness=best_genome_data['fitness'],
                generation=best_genome_data['generation']
            )

            logger.info(f"Loaded evolution results from {filepath}")
            return genome

        except Exception as e:
            logger.error(f"Error loading evolution results: {e}")
            return None
