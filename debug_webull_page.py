#!/usr/bin/env python3
"""
Debug Webull page content to understand structure
"""

import asyncio
import sys
import os
import re
from datetime import datetime

# Simple imports
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait

class WebullPageDebugger:
    """Debug Webull page content"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        self.base_url = "https://app.webull.com"
        
    async def initialize(self):
        """Initialize the browser driver"""
        try:
            print("Initializing browser driver...")
            
            # Simple Chrome options that work
            self.driver = uc.Chrome(version_main=None)
            self.wait = WebDriverWait(self.driver, 10)
            
            # Set page load timeout
            self.driver.set_page_load_timeout(30)
            
            print("Browser driver initialized successfully")
            return True
            
        except Exception as e:
            print(f"Failed to initialize browser: {e}")
            return False
    
    async def debug_page(self, symbol: str):
        """Debug page content for a symbol"""
        try:
            print(f"\n🔍 Debugging page for {symbol}")
            print("=" * 50)
            
            # Navigate to stock page
            url = f"{self.base_url}/stocks/{symbol}"
            print(f"Navigating to: {url}")
            self.driver.get(url)
            
            # Wait for page to load
            await asyncio.sleep(8)
            
            # Get page title
            title = self.driver.title
            print(f"Page title: {title}")
            
            # Get page text
            page_text = self.driver.find_element(By.TAG_NAME, 'body').text
            
            # Save full page text to file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_{symbol}_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(page_text)
            print(f"Full page text saved to: {filename}")
            
            # Look for lines containing the symbol
            lines = page_text.split('\n')
            symbol_lines = [line for line in lines if symbol in line]
            
            print(f"\nLines containing '{symbol}':")
            for i, line in enumerate(symbol_lines[:10]):  # Show first 10 matches
                print(f"  {i+1}: {line.strip()}")
            
            # Look for price patterns
            price_patterns = [
                r'\$\d+\.\d{2}',  # $123.45
                r'\d+\.\d{2}',    # 123.45
                r'\d{1,4}\.\d{2}' # 1234.56
            ]
            
            print(f"\nPrice patterns found:")
            for pattern in price_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    print(f"  Pattern {pattern}: {matches[:5]}")  # Show first 5 matches
            
            # Look for percentage patterns
            percent_matches = re.findall(r'[+-]?\d+\.\d+%', page_text)
            if percent_matches:
                print(f"\nPercentage patterns: {percent_matches[:10]}")
            
            # Look for volume patterns
            volume_matches = re.findall(r'Volume.*?([0-9,.]+[MKB]?)', page_text, re.IGNORECASE)
            if volume_matches:
                print(f"\nVolume patterns: {volume_matches[:5]}")
            
            # Take screenshot
            screenshot_name = f"debug_{symbol}_{timestamp}.png"
            self.driver.save_screenshot(screenshot_name)
            print(f"Screenshot saved: {screenshot_name}")
            
            # Show first 20 lines of page text
            print(f"\nFirst 20 lines of page:")
            for i, line in enumerate(lines[:20]):
                if line.strip():
                    print(f"  {i+1:2d}: {line.strip()}")
            
        except Exception as e:
            print(f"Failed to debug page for {symbol}: {e}")
            import traceback
            traceback.print_exc()
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
            print("\nBrowser driver closed")
        except Exception as e:
            print(f"Error during cleanup: {e}")

async def debug_extraction():
    """Debug the extraction"""
    debugger = WebullPageDebugger()
    
    try:
        print("🚀 Initializing browser for debugging...")
        if await debugger.initialize():
            print("✅ Browser initialized successfully")
            
            # Debug one symbol thoroughly
            symbol = 'AAPL'
            await debugger.debug_page(symbol)
            
        else:
            print("❌ Failed to initialize browser")
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await debugger.cleanup()
        print("\n🧹 Debug complete")

if __name__ == "__main__":
    print("🔬 Debugging Webull Page Structure")
    print("=" * 50)
    asyncio.run(debug_extraction())
