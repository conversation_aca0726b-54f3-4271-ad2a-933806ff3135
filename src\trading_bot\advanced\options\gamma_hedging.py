"""Gamma hedging and delta-neutral strategies."""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from ...core.config import settings
from ...core.logger import get_logger
from .options_pricing import OptionsPricer, OptionContract, OptionType, Greeks

logger = get_logger(__name__)


@dataclass
class Position:
    """Trading position data structure."""
    symbol: str
    quantity: float
    price: float
    position_type: str  # 'stock', 'option'
    greeks: Optional[Greeks] = None


@dataclass
class Portfolio:
    """Portfolio of positions."""
    positions: List[Position]
    total_delta: float
    total_gamma: float
    total_theta: float
    total_vega: float
    net_value: float


@dataclass
class HedgeRecommendation:
    """Hedge recommendation."""
    action: str  # 'buy', 'sell'
    symbol: str
    quantity: float
    reasoning: str
    expected_delta_change: float
    cost: float
    urgency: str  # 'low', 'medium', 'high'


class GammaHedger:
    """Gamma hedging and delta-neutral portfolio management."""
    
    def __init__(self):
        self.pricer = OptionsPricer()
        self.target_delta = 0.0  # Delta-neutral target
        self.delta_tolerance = 0.05  # 5% tolerance
        self.gamma_threshold = 0.1  # Gamma threshold for hedging
        
    async def analyze_portfolio_greeks(
        self,
        positions: List[Position],
        spot_price: float
    ) -> Portfolio:
        """
        Analyze portfolio Greeks and risk metrics.
        
        Args:
            positions: List of portfolio positions
            spot_price: Current underlying price
            
        Returns:
            Portfolio analysis with Greeks
        """
        try:
            total_delta = 0.0
            total_gamma = 0.0
            total_theta = 0.0
            total_vega = 0.0
            net_value = 0.0
            
            for position in positions:
                if position.position_type == 'stock':
                    # Stock has delta of 1, no other Greeks
                    total_delta += position.quantity
                    net_value += position.quantity * position.price
                    
                elif position.position_type == 'option':
                    if position.greeks:
                        total_delta += position.quantity * position.greeks.delta
                        total_gamma += position.quantity * position.greeks.gamma
                        total_theta += position.quantity * position.greeks.theta
                        total_vega += position.quantity * position.greeks.vega
                        net_value += position.quantity * position.price
            
            return Portfolio(
                positions=positions,
                total_delta=total_delta,
                total_gamma=total_gamma,
                total_theta=total_theta,
                total_vega=total_vega,
                net_value=net_value
            )
            
        except Exception as e:
            logger.error(f"Error analyzing portfolio Greeks: {e}")
            return Portfolio([], 0, 0, 0, 0, 0)
    
    async def calculate_hedge_requirements(
        self,
        portfolio: Portfolio,
        spot_price: float,
        available_instruments: List[OptionContract] = None
    ) -> List[HedgeRecommendation]:
        """
        Calculate hedge requirements to maintain delta neutrality.
        
        Args:
            portfolio: Current portfolio
            spot_price: Current underlying price
            available_instruments: Available hedging instruments
            
        Returns:
            List of hedge recommendations
        """
        try:
            recommendations = []
            
            # Check if delta hedging is needed
            delta_exposure = portfolio.total_delta
            
            if abs(delta_exposure) > self.delta_tolerance:
                # Calculate stock hedge
                stock_hedge = self._calculate_stock_hedge(delta_exposure, spot_price)
                if stock_hedge:
                    recommendations.append(stock_hedge)
            
            # Check if gamma hedging is needed
            if abs(portfolio.total_gamma) > self.gamma_threshold:
                gamma_hedge = await self._calculate_gamma_hedge(
                    portfolio, spot_price, available_instruments
                )
                if gamma_hedge:
                    recommendations.extend(gamma_hedge)
            
            # Check for theta decay management
            if portfolio.total_theta < -50:  # Significant theta decay
                theta_hedge = self._calculate_theta_hedge(portfolio, spot_price)
                if theta_hedge:
                    recommendations.append(theta_hedge)
            
            # Sort by urgency
            urgency_order = {'high': 3, 'medium': 2, 'low': 1}
            recommendations.sort(key=lambda x: urgency_order.get(x.urgency, 0), reverse=True)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error calculating hedge requirements: {e}")
            return []
    
    async def simulate_portfolio_pnl(
        self,
        portfolio: Portfolio,
        spot_price: float,
        price_scenarios: List[float],
        time_decay_days: int = 1
    ) -> Dict[float, float]:
        """
        Simulate portfolio P&L under different price scenarios.
        
        Args:
            portfolio: Current portfolio
            spot_price: Current underlying price
            price_scenarios: List of price scenarios to test
            time_decay_days: Days of time decay to simulate
            
        Returns:
            Dictionary mapping price scenarios to P&L
        """
        try:
            pnl_scenarios = {}
            
            for scenario_price in price_scenarios:
                total_pnl = 0.0
                price_change = scenario_price - spot_price
                
                for position in portfolio.positions:
                    if position.position_type == 'stock':
                        # Stock P&L
                        stock_pnl = position.quantity * price_change
                        total_pnl += stock_pnl
                        
                    elif position.position_type == 'option' and position.greeks:
                        # Option P&L using Greeks approximation
                        delta_pnl = position.quantity * position.greeks.delta * price_change
                        gamma_pnl = position.quantity * position.greeks.gamma * (price_change ** 2) / 2
                        theta_pnl = position.quantity * position.greeks.theta * time_decay_days
                        
                        option_pnl = delta_pnl + gamma_pnl + theta_pnl
                        total_pnl += option_pnl
                
                pnl_scenarios[scenario_price] = total_pnl
            
            return pnl_scenarios
            
        except Exception as e:
            logger.error(f"Error simulating portfolio P&L: {e}")
            return {}
    
    async def optimize_delta_neutral_portfolio(
        self,
        target_positions: List[Position],
        spot_price: float,
        available_options: List[OptionContract]
    ) -> List[Position]:
        """
        Optimize portfolio to achieve delta neutrality.
        
        Args:
            target_positions: Desired option positions
            spot_price: Current underlying price
            available_options: Available options for hedging
            
        Returns:
            Optimized portfolio positions
        """
        try:
            # Calculate target portfolio Greeks
            target_portfolio = await self.analyze_portfolio_greeks(target_positions, spot_price)
            
            # Find optimal hedge
            hedge_recommendations = await self.calculate_hedge_requirements(
                target_portfolio, spot_price, available_options
            )
            
            # Apply hedge recommendations
            optimized_positions = target_positions.copy()
            
            for hedge in hedge_recommendations:
                if hedge.symbol == 'STOCK':
                    # Add stock position
                    stock_position = Position(
                        symbol=hedge.symbol,
                        quantity=hedge.quantity,
                        price=spot_price,
                        position_type='stock'
                    )
                    optimized_positions.append(stock_position)
                else:
                    # Add option position
                    matching_option = next(
                        (opt for opt in available_options if opt.symbol == hedge.symbol),
                        None
                    )
                    if matching_option:
                        option_position = Position(
                            symbol=hedge.symbol,
                            quantity=hedge.quantity,
                            price=(matching_option.bid + matching_option.ask) / 2,
                            position_type='option',
                            greeks=self._calculate_option_greeks(matching_option, spot_price)
                        )
                        optimized_positions.append(option_position)
            
            return optimized_positions
            
        except Exception as e:
            logger.error(f"Error optimizing delta neutral portfolio: {e}")
            return target_positions
    
    def _calculate_stock_hedge(
        self,
        delta_exposure: float,
        spot_price: float
    ) -> Optional[HedgeRecommendation]:
        """Calculate stock hedge to neutralize delta."""
        try:
            # Stock has delta of 1, so hedge quantity = -delta_exposure
            hedge_quantity = -delta_exposure
            
            if abs(hedge_quantity) < 1:  # Skip small hedges
                return None
            
            action = 'buy' if hedge_quantity > 0 else 'sell'
            cost = abs(hedge_quantity) * spot_price
            
            # Determine urgency based on delta size
            if abs(delta_exposure) > 0.2:
                urgency = 'high'
            elif abs(delta_exposure) > 0.1:
                urgency = 'medium'
            else:
                urgency = 'low'
            
            return HedgeRecommendation(
                action=action,
                symbol='STOCK',
                quantity=abs(hedge_quantity),
                reasoning=f"Delta hedge: neutralize {delta_exposure:.3f} delta exposure",
                expected_delta_change=-delta_exposure,
                cost=cost,
                urgency=urgency
            )
            
        except Exception as e:
            logger.error(f"Error calculating stock hedge: {e}")
            return None
    
    async def _calculate_gamma_hedge(
        self,
        portfolio: Portfolio,
        spot_price: float,
        available_options: List[OptionContract]
    ) -> List[HedgeRecommendation]:
        """Calculate gamma hedge using options."""
        try:
            if not available_options:
                return []
            
            recommendations = []
            target_gamma_reduction = -portfolio.total_gamma * 0.5  # Reduce gamma by 50%
            
            # Find options with high gamma for hedging
            gamma_options = []
            for option in available_options:
                greeks = self._calculate_option_greeks(option, spot_price)
                if greeks and abs(greeks.gamma) > 0.01:  # Minimum gamma threshold
                    gamma_options.append((option, greeks))
            
            # Sort by gamma (highest first)
            gamma_options.sort(key=lambda x: abs(x[1].gamma), reverse=True)
            
            # Select best gamma hedge
            if gamma_options:
                best_option, best_greeks = gamma_options[0]
                
                # Calculate quantity needed
                if best_greeks.gamma != 0:
                    hedge_quantity = target_gamma_reduction / best_greeks.gamma
                    
                    if abs(hedge_quantity) >= 1:  # Minimum position size
                        action = 'buy' if hedge_quantity > 0 else 'sell'
                        cost = abs(hedge_quantity) * (best_option.bid + best_option.ask) / 2
                        
                        recommendation = HedgeRecommendation(
                            action=action,
                            symbol=best_option.symbol,
                            quantity=abs(hedge_quantity),
                            reasoning=f"Gamma hedge: reduce gamma exposure by {target_gamma_reduction:.3f}",
                            expected_delta_change=hedge_quantity * best_greeks.delta,
                            cost=cost,
                            urgency='medium'
                        )
                        recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error calculating gamma hedge: {e}")
            return []
    
    def _calculate_theta_hedge(
        self,
        portfolio: Portfolio,
        spot_price: float
    ) -> Optional[HedgeRecommendation]:
        """Calculate hedge for theta decay."""
        try:
            # Simplified theta hedge using stock position
            # In practice, would use calendar spreads or other strategies
            
            if portfolio.total_theta < -100:  # Significant theta decay
                # Suggest reducing position size
                return HedgeRecommendation(
                    action='sell',
                    symbol='PORTFOLIO',
                    quantity=0.2,  # Reduce by 20%
                    reasoning=f"High theta decay: {portfolio.total_theta:.2f} per day",
                    expected_delta_change=0,
                    cost=0,
                    urgency='medium'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error calculating theta hedge: {e}")
            return None
    
    def _calculate_option_greeks(
        self,
        option: OptionContract,
        spot_price: float
    ) -> Optional[Greeks]:
        """Calculate Greeks for an option."""
        try:
            time_to_expiry = (option.expiration - datetime.now()).total_seconds() / (365.25 * 24 * 3600)
            
            if time_to_expiry <= 0:
                return None
            
            # Use mid price for IV calculation
            mid_price = (option.bid + option.ask) / 2
            if mid_price <= 0:
                return None
            
            # Calculate implied volatility
            implied_vol = self.pricer.calculate_implied_volatility(
                mid_price, spot_price, option.strike, time_to_expiry, option.option_type
            )
            
            # Calculate Greeks
            greeks = self.pricer.calculate_greeks(
                spot_price, option.strike, time_to_expiry, implied_vol, option.option_type
            )
            
            return greeks
            
        except Exception as e:
            logger.error(f"Error calculating option Greeks: {e}")
            return None
    
    async def monitor_portfolio_risk(
        self,
        portfolio: Portfolio,
        spot_price: float,
        risk_limits: Dict[str, float] = None
    ) -> Dict[str, Any]:
        """
        Monitor portfolio risk metrics.
        
        Args:
            portfolio: Current portfolio
            spot_price: Current underlying price
            risk_limits: Risk limits dictionary
            
        Returns:
            Risk monitoring report
        """
        try:
            if risk_limits is None:
                risk_limits = {
                    'max_delta': 0.1,
                    'max_gamma': 0.2,
                    'max_theta': -100,
                    'max_vega': 50
                }
            
            risk_report = {
                'timestamp': datetime.now(),
                'current_greeks': {
                    'delta': portfolio.total_delta,
                    'gamma': portfolio.total_gamma,
                    'theta': portfolio.total_theta,
                    'vega': portfolio.total_vega
                },
                'risk_limits': risk_limits,
                'violations': [],
                'recommendations': []
            }
            
            # Check for limit violations
            if abs(portfolio.total_delta) > risk_limits['max_delta']:
                risk_report['violations'].append(f"Delta limit exceeded: {portfolio.total_delta:.3f}")
            
            if abs(portfolio.total_gamma) > risk_limits['max_gamma']:
                risk_report['violations'].append(f"Gamma limit exceeded: {portfolio.total_gamma:.3f}")
            
            if portfolio.total_theta < risk_limits['max_theta']:
                risk_report['violations'].append(f"Theta limit exceeded: {portfolio.total_theta:.2f}")
            
            if abs(portfolio.total_vega) > risk_limits['max_vega']:
                risk_report['violations'].append(f"Vega limit exceeded: {portfolio.total_vega:.2f}")
            
            # Generate recommendations based on violations
            if risk_report['violations']:
                hedge_recs = await self.calculate_hedge_requirements(portfolio, spot_price)
                risk_report['recommendations'] = [rec.reasoning for rec in hedge_recs]
            
            return risk_report
            
        except Exception as e:
            logger.error(f"Error monitoring portfolio risk: {e}")
            return {'error': str(e)}
