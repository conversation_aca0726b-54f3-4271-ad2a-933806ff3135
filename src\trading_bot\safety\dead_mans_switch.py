"""
Dead Man's Switch - Critical Safety System

This module implements a comprehensive dead man's switch system that automatically
shuts down trading operations if the system becomes unresponsive or if operators
fail to provide regular heartbeats.

Features:
- Multiple heartbeat sources (human operators, automated systems)
- Configurable timeout periods
- Graceful shutdown procedures
- Emergency contact notifications
- System health monitoring
- Automatic position unwinding
"""

import asyncio
import json
import smtplib
import requests
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import aiofiles

from ..core.logger import get_logger
from ..core.config import settings
from ..emergency.kill_switch import EmergencyKillSwitch, EmergencyLevel, EmergencyTrigger

logger = get_logger(__name__)


class HeartbeatSource(Enum):
    """Sources of heartbeat signals."""
    HUMAN_OPERATOR = "human_operator"
    AUTOMATED_SYSTEM = "automated_system"
    HEALTH_MONITOR = "health_monitor"
    EXTERNAL_WATCHDOG = "external_watchdog"
    MOBILE_APP = "mobile_app"
    WEB_DASHBOARD = "web_dashboard"


class SwitchState(Enum):
    """Dead man's switch states."""
    ACTIVE = "active"           # Normal operation
    WARNING = "warning"         # Approaching timeout
    TRIGGERED = "triggered"     # Switch activated
    SHUTDOWN = "shutdown"       # System shutdown complete
    DISABLED = "disabled"       # Switch disabled (maintenance mode)


@dataclass
class HeartbeatConfig:
    """Configuration for heartbeat monitoring."""
    source: HeartbeatSource
    timeout_seconds: int
    warning_threshold_seconds: int
    required: bool = True
    enabled: bool = True


@dataclass
class HeartbeatRecord:
    """Record of a heartbeat signal."""
    source: HeartbeatSource
    timestamp: datetime
    operator_id: Optional[str] = None
    message: Optional[str] = None
    system_status: Optional[Dict[str, Any]] = None


@dataclass
class EmergencyContact:
    """Emergency contact information."""
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    sms: Optional[str] = None
    priority: int = 1  # 1 = highest priority


class DeadMansSwitch:
    """Dead man's switch implementation for trading system safety."""
    
    def __init__(self, 
                 emergency_kill_switch: EmergencyKillSwitch,
                 heartbeat_configs: Optional[List[HeartbeatConfig]] = None):
        
        self.emergency_kill_switch = emergency_kill_switch
        self.state = SwitchState.ACTIVE
        self.is_monitoring = False
        
        # Heartbeat configuration
        self.heartbeat_configs = heartbeat_configs or self._default_heartbeat_configs()
        self.heartbeat_history: Dict[HeartbeatSource, List[HeartbeatRecord]] = {
            source.source: [] for source in self.heartbeat_configs
        }
        self.last_heartbeats: Dict[HeartbeatSource, datetime] = {}
        
        # Emergency contacts
        self.emergency_contacts = self._load_emergency_contacts()
        
        # Monitoring tasks
        self.monitoring_tasks: List[asyncio.Task] = []
        
        # Callbacks for custom actions
        self.pre_shutdown_callbacks: List[Callable] = []
        self.post_shutdown_callbacks: List[Callable] = []
        
        # Configuration
        self.config = {
            'check_interval_seconds': 10,
            'notification_retry_attempts': 3,
            'notification_retry_delay_seconds': 30,
            'graceful_shutdown_timeout_seconds': 300,  # 5 minutes
            'position_unwind_timeout_seconds': 600,    # 10 minutes
            'heartbeat_history_retention_hours': 24
        }
        
        logger.info("Dead man's switch initialized")
    
    def _default_heartbeat_configs(self) -> List[HeartbeatConfig]:
        """Default heartbeat configurations."""
        return [
            HeartbeatConfig(
                source=HeartbeatSource.HUMAN_OPERATOR,
                timeout_seconds=1800,  # 30 minutes
                warning_threshold_seconds=1200,  # 20 minutes
                required=True
            ),
            HeartbeatConfig(
                source=HeartbeatSource.AUTOMATED_SYSTEM,
                timeout_seconds=300,   # 5 minutes
                warning_threshold_seconds=240,   # 4 minutes
                required=True
            ),
            HeartbeatConfig(
                source=HeartbeatSource.HEALTH_MONITOR,
                timeout_seconds=120,   # 2 minutes
                warning_threshold_seconds=90,    # 1.5 minutes
                required=True
            ),
            HeartbeatConfig(
                source=HeartbeatSource.MOBILE_APP,
                timeout_seconds=3600,  # 1 hour
                warning_threshold_seconds=2700,  # 45 minutes
                required=False
            )
        ]
    
    def _load_emergency_contacts(self) -> List[EmergencyContact]:
        """Load emergency contact information."""
        # In production, this would load from secure configuration
        return [
            EmergencyContact(
                name="Primary Operator",
                email="<EMAIL>",
                phone="+**********",
                sms="+**********",
                priority=1
            ),
            EmergencyContact(
                name="Secondary Operator", 
                email="<EMAIL>",
                phone="+**********",
                sms="+**********",
                priority=2
            ),
            EmergencyContact(
                name="Emergency Response Team",
                email="<EMAIL>",
                phone="+1234567892",
                priority=1
            )
        ]
    
    async def start_monitoring(self):
        """Start dead man's switch monitoring."""
        if self.is_monitoring:
            logger.warning("Dead man's switch already monitoring")
            return
        
        self.is_monitoring = True
        self.state = SwitchState.ACTIVE
        
        logger.info("Starting dead man's switch monitoring...")
        
        # Initialize heartbeat timestamps
        now = datetime.utcnow()
        for config in self.heartbeat_configs:
            if config.enabled:
                self.last_heartbeats[config.source] = now
        
        # Start monitoring tasks
        self.monitoring_tasks = [
            asyncio.create_task(self._heartbeat_monitor()),
            asyncio.create_task(self._system_health_monitor()),
            asyncio.create_task(self._cleanup_old_heartbeats())
        ]
        
        logger.info("Dead man's switch monitoring started")
        
        try:
            await asyncio.gather(*self.monitoring_tasks)
        except Exception as e:
            logger.error(f"Dead man's switch monitoring error: {e}")
        finally:
            self.is_monitoring = False
    
    async def stop_monitoring(self):
        """Stop dead man's switch monitoring."""
        self.is_monitoring = False
        
        # Cancel monitoring tasks
        for task in self.monitoring_tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete
        if self.monitoring_tasks:
            await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
        
        self.monitoring_tasks.clear()
        logger.info("Dead man's switch monitoring stopped")
    
    async def send_heartbeat(self, 
                            source: HeartbeatSource,
                            operator_id: Optional[str] = None,
                            message: Optional[str] = None,
                            system_status: Optional[Dict[str, Any]] = None):
        """Send a heartbeat signal."""
        
        if not self.is_monitoring:
            logger.warning("Dead man's switch not monitoring - heartbeat ignored")
            return
        
        now = datetime.utcnow()
        
        # Record heartbeat
        heartbeat = HeartbeatRecord(
            source=source,
            timestamp=now,
            operator_id=operator_id,
            message=message,
            system_status=system_status
        )
        
        # Update tracking
        self.last_heartbeats[source] = now
        if source not in self.heartbeat_history:
            self.heartbeat_history[source] = []
        self.heartbeat_history[source].append(heartbeat)
        
        logger.debug(f"Heartbeat received from {source.value}: {operator_id or 'system'}")
        
        # If we were in warning state, check if we can return to active
        if self.state == SwitchState.WARNING:
            if await self._check_all_heartbeats_healthy():
                self.state = SwitchState.ACTIVE
                logger.info("Dead man's switch returned to ACTIVE state")
    
    async def _heartbeat_monitor(self):
        """Monitor heartbeat timeouts."""
        while self.is_monitoring:
            try:
                now = datetime.utcnow()
                warning_triggered = False
                timeout_triggered = False
                
                for config in self.heartbeat_configs:
                    if not config.enabled or not config.required:
                        continue
                    
                    last_heartbeat = self.last_heartbeats.get(config.source)
                    if not last_heartbeat:
                        continue
                    
                    time_since_heartbeat = (now - last_heartbeat).total_seconds()
                    
                    # Check for timeout
                    if time_since_heartbeat > config.timeout_seconds:
                        logger.critical(f"HEARTBEAT TIMEOUT: {config.source.value} - {time_since_heartbeat:.0f}s")
                        timeout_triggered = True
                        break
                    
                    # Check for warning threshold
                    elif time_since_heartbeat > config.warning_threshold_seconds:
                        logger.warning(f"Heartbeat warning: {config.source.value} - {time_since_heartbeat:.0f}s")
                        warning_triggered = True
                
                # Handle state transitions
                if timeout_triggered and self.state != SwitchState.TRIGGERED:
                    await self._trigger_dead_mans_switch()
                elif warning_triggered and self.state == SwitchState.ACTIVE:
                    await self._enter_warning_state()
                
                await asyncio.sleep(self.config['check_interval_seconds'])
                
            except Exception as e:
                logger.error(f"Error in heartbeat monitoring: {e}")
                await asyncio.sleep(30)
    
    async def _trigger_dead_mans_switch(self):
        """Trigger the dead man's switch - EMERGENCY SHUTDOWN."""
        if self.state == SwitchState.TRIGGERED:
            return  # Already triggered
        
        logger.critical("🚨 DEAD MAN'S SWITCH TRIGGERED - INITIATING EMERGENCY SHUTDOWN 🚨")
        
        self.state = SwitchState.TRIGGERED
        
        try:
            # Send immediate emergency notifications
            await self._send_emergency_notifications("DEAD MAN'S SWITCH TRIGGERED")
            
            # Execute pre-shutdown callbacks
            for callback in self.pre_shutdown_callbacks:
                try:
                    await callback()
                except Exception as e:
                    logger.error(f"Pre-shutdown callback failed: {e}")
            
            # Activate emergency kill switch
            success = await self.emergency_kill_switch.activate_emergency(
                level=EmergencyLevel.FULL_SHUTDOWN,
                trigger=EmergencyTrigger.DEAD_MANS_SWITCH,
                reason="Dead man's switch timeout - no operator heartbeat",
                user_id="dead_mans_switch"
            )
            
            if success:
                self.state = SwitchState.SHUTDOWN
                logger.critical("Dead man's switch shutdown completed successfully")
            else:
                logger.critical("Dead man's switch shutdown FAILED - manual intervention required")
            
            # Execute post-shutdown callbacks
            for callback in self.post_shutdown_callbacks:
                try:
                    await callback()
                except Exception as e:
                    logger.error(f"Post-shutdown callback failed: {e}")
            
        except Exception as e:
            logger.critical(f"CRITICAL ERROR in dead man's switch: {e}")
            # Last resort - try to send notification about the failure
            await self._send_emergency_notifications(f"DEAD MAN'S SWITCH FAILURE: {e}")
    
    async def _enter_warning_state(self):
        """Enter warning state when heartbeats are approaching timeout."""
        if self.state != SwitchState.ACTIVE:
            return
        
        logger.warning("Dead man's switch entering WARNING state")
        self.state = SwitchState.WARNING
        
        # Send warning notifications
        await self._send_warning_notifications()
    
    async def _check_all_heartbeats_healthy(self) -> bool:
        """Check if all required heartbeats are healthy."""
        now = datetime.utcnow()
        
        for config in self.heartbeat_configs:
            if not config.enabled or not config.required:
                continue
            
            last_heartbeat = self.last_heartbeats.get(config.source)
            if not last_heartbeat:
                return False
            
            time_since_heartbeat = (now - last_heartbeat).total_seconds()
            if time_since_heartbeat > config.warning_threshold_seconds:
                return False
        
        return True

    async def _send_emergency_notifications(self, message: str):
        """Send emergency notifications to all contacts."""
        logger.critical(f"Sending emergency notifications: {message}")

        # Sort contacts by priority
        sorted_contacts = sorted(self.emergency_contacts, key=lambda c: c.priority)

        for contact in sorted_contacts:
            try:
                # Send email notification
                if contact.email:
                    await self._send_email_notification(contact, message, urgent=True)

                # Send SMS notification
                if contact.sms:
                    await self._send_sms_notification(contact, message)

                # Make phone call (placeholder)
                if contact.phone:
                    await self._make_emergency_call(contact, message)

            except Exception as e:
                logger.error(f"Failed to notify {contact.name}: {e}")

    async def _send_warning_notifications(self):
        """Send warning notifications about approaching timeout."""
        message = "WARNING: Trading system heartbeat approaching timeout. Please check system status."

        # Send to primary contacts only for warnings
        primary_contacts = [c for c in self.emergency_contacts if c.priority == 1]

        for contact in primary_contacts:
            try:
                if contact.email:
                    await self._send_email_notification(contact, message, urgent=False)

            except Exception as e:
                logger.error(f"Failed to send warning to {contact.name}: {e}")

    async def _send_email_notification(self, contact: EmergencyContact, message: str, urgent: bool = False):
        """Send email notification."""
        try:
            # Email configuration (would be in settings in production)
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
            smtp_username = "<EMAIL>"
            smtp_password = "app_password"  # Use app password or OAuth

            # Create message
            msg = MimeMultipart()
            msg['From'] = smtp_username
            msg['To'] = contact.email
            msg['Subject'] = f"{'🚨 URGENT' if urgent else '⚠️ WARNING'} - Trading Bot Alert"

            body = f"""
            {'EMERGENCY ALERT' if urgent else 'WARNING ALERT'}

            Time: {datetime.utcnow().isoformat()}
            System: Trading Bot Dead Man's Switch

            Message: {message}

            {'IMMEDIATE ACTION REQUIRED' if urgent else 'Please check system status'}

            Contact: {contact.name}
            """

            msg.attach(MimeText(body, 'plain'))

            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()

            logger.info(f"Email sent to {contact.email}")

        except Exception as e:
            logger.error(f"Failed to send email to {contact.email}: {e}")

    async def _send_sms_notification(self, contact: EmergencyContact, message: str):
        """Send SMS notification using Twilio or similar service."""
        try:
            # Twilio configuration (would be in settings)
            account_sid = "your_account_sid"
            auth_token = "your_auth_token"
            from_number = "+**********"

            # Truncate message for SMS
            sms_message = f"TRADING BOT ALERT: {message[:100]}..."

            # In production, use actual Twilio client
            # from twilio.rest import Client
            # client = Client(account_sid, auth_token)
            # client.messages.create(
            #     body=sms_message,
            #     from_=from_number,
            #     to=contact.sms
            # )

            logger.info(f"SMS sent to {contact.sms} (simulated)")

        except Exception as e:
            logger.error(f"Failed to send SMS to {contact.sms}: {e}")

    async def _make_emergency_call(self, contact: EmergencyContact, message: str):
        """Make emergency phone call (placeholder implementation)."""
        try:
            # In production, integrate with voice calling service
            # like Twilio Voice, AWS Connect, etc.

            logger.info(f"Emergency call to {contact.phone} (simulated)")
            logger.info(f"Call message: {message}")

        except Exception as e:
            logger.error(f"Failed to make emergency call to {contact.phone}: {e}")

    async def _system_health_monitor(self):
        """Monitor overall system health and send automated heartbeats."""
        while self.is_monitoring:
            try:
                # Collect system health metrics
                health_status = await self._collect_health_metrics()

                # Send automated system heartbeat
                await self.send_heartbeat(
                    source=HeartbeatSource.AUTOMATED_SYSTEM,
                    operator_id="system_monitor",
                    message="Automated health check",
                    system_status=health_status
                )

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Error in system health monitoring: {e}")
                await asyncio.sleep(60)

    async def _collect_health_metrics(self) -> Dict[str, Any]:
        """Collect system health metrics."""
        import psutil

        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
                'timestamp': datetime.utcnow().isoformat(),
                'process_count': len(psutil.pids()),
                'boot_time': psutil.boot_time()
            }
        except Exception as e:
            logger.error(f"Failed to collect health metrics: {e}")
            return {'error': str(e), 'timestamp': datetime.utcnow().isoformat()}

    async def _cleanup_old_heartbeats(self):
        """Clean up old heartbeat records."""
        while self.is_monitoring:
            try:
                cutoff_time = datetime.utcnow() - timedelta(hours=self.config['heartbeat_history_retention_hours'])

                for source in self.heartbeat_history:
                    self.heartbeat_history[source] = [
                        hb for hb in self.heartbeat_history[source]
                        if hb.timestamp > cutoff_time
                    ]

                await asyncio.sleep(3600)  # Clean up every hour

            except Exception as e:
                logger.error(f"Error in heartbeat cleanup: {e}")
                await asyncio.sleep(3600)

    def add_pre_shutdown_callback(self, callback: Callable):
        """Add callback to execute before shutdown."""
        self.pre_shutdown_callbacks.append(callback)

    def add_post_shutdown_callback(self, callback: Callable):
        """Add callback to execute after shutdown."""
        self.post_shutdown_callbacks.append(callback)

    def get_status(self) -> Dict[str, Any]:
        """Get current dead man's switch status."""
        now = datetime.utcnow()

        heartbeat_status = {}
        for config in self.heartbeat_configs:
            if config.enabled:
                last_heartbeat = self.last_heartbeats.get(config.source)
                if last_heartbeat:
                    seconds_since = (now - last_heartbeat).total_seconds()
                    heartbeat_status[config.source.value] = {
                        'last_heartbeat': last_heartbeat.isoformat(),
                        'seconds_since': seconds_since,
                        'timeout_seconds': config.timeout_seconds,
                        'warning_threshold': config.warning_threshold_seconds,
                        'status': 'healthy' if seconds_since < config.warning_threshold_seconds else 'warning' if seconds_since < config.timeout_seconds else 'timeout'
                    }
                else:
                    heartbeat_status[config.source.value] = {
                        'status': 'no_heartbeat'
                    }

        return {
            'switch_state': self.state.value,
            'is_monitoring': self.is_monitoring,
            'heartbeat_status': heartbeat_status,
            'emergency_contacts_count': len(self.emergency_contacts),
            'monitoring_tasks_count': len(self.monitoring_tasks)
        }

    async def disable_switch(self, reason: str = "Maintenance mode"):
        """Temporarily disable the dead man's switch."""
        logger.warning(f"Disabling dead man's switch: {reason}")
        self.state = SwitchState.DISABLED
        await self.stop_monitoring()

    async def enable_switch(self):
        """Re-enable the dead man's switch."""
        logger.info("Re-enabling dead man's switch")
        await self.start_monitoring()


# Global instance
_dead_mans_switch = None

def get_dead_mans_switch(emergency_kill_switch=None) -> Optional[DeadMansSwitch]:
    """Get global dead man's switch instance."""
    global _dead_mans_switch

    if _dead_mans_switch is None and emergency_kill_switch:
        _dead_mans_switch = DeadMansSwitch(emergency_kill_switch)

    return _dead_mans_switch
