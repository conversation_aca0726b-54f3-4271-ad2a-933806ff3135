<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 Emergency Trading Bot Control</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .status-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .status-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 5px;
        }
        
        .status-active {
            background: rgba(255,0,0,0.3);
            border: 2px solid #ff4444;
        }
        
        .status-normal {
            background: rgba(0,255,0,0.3);
            border: 2px solid #44ff44;
        }
        
        .emergency-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .emergency-button {
            padding: 30px;
            border: none;
            border-radius: 15px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .emergency-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.3);
        }
        
        .emergency-button:active {
            transform: translateY(0);
        }
        
        .btn-stop {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }
        
        .btn-close {
            background: linear-gradient(45deg, #ff9ff3, #f368e0);
            color: white;
        }
        
        .btn-shutdown {
            background: linear-gradient(45deg, #ff3838, #c0392b);
            color: white;
        }
        
        .btn-lock {
            background: linear-gradient(45deg, #2c2c54, #40407a);
            color: white;
        }
        
        .btn-deactivate {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
            color: white;
        }
        
        .emergency-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .emergency-button:hover::before {
            left: 100%;
        }
        
        .auth-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .auth-input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .log-panel {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            padding: 10px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-success {
            background: rgba(0,255,0,0.2);
            border: 1px solid #44ff44;
        }
        
        .alert-error {
            background: rgba(255,0,0,0.2);
            border: 1px solid #ff4444;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .emergency-controls {
                grid-template-columns: 1fr;
            }
            
            .emergency-button {
                padding: 20px;
                font-size: 1em;
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 EMERGENCY TRADING BOT CONTROL 🚨</h1>
            <p>Use these controls to immediately stop trading, close positions, or shutdown the system</p>
        </div>
        
        <div id="alert-container"></div>
        
        <div class="status-panel">
            <h2>System Status</h2>
            <div class="status-grid">
                <div class="status-item" id="emergency-status">
                    <div>Emergency Mode</div>
                    <div class="status-value" id="emergency-value">
                        {{ 'ACTIVE' if status.emergency_active else 'NORMAL' }}
                    </div>
                </div>
                <div class="status-item" id="trading-status">
                    <div>Trading Status</div>
                    <div class="status-value" id="trading-value">
                        {{ 'HALTED' if status.trading_halted else 'ACTIVE' }}
                    </div>
                </div>
                <div class="status-item" id="system-status">
                    <div>System Status</div>
                    <div class="status-value" id="system-value">
                        {{ 'LOCKED' if status.system_locked else 'OPERATIONAL' }}
                    </div>
                </div>
                <div class="status-item">
                    <div>Last Update</div>
                    <div class="status-value" id="last-update">
                        <span id="current-time"></span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="auth-panel">
            <h3>Authentication Required</h3>
            <input type="password" id="auth-password" class="auth-input" placeholder="Enter emergency password">
            <input type="text" id="user-id" class="auth-input" placeholder="Your user ID" value="web_user">
        </div>
        
        <div class="emergency-controls">
            <button class="emergency-button btn-stop" onclick="stopTrading()">
                🛑 STOP TRADING
                <br><small>Stop all new trades immediately</small>
            </button>
            
            <button class="emergency-button btn-close" onclick="closePositions()">
                🔴 CLOSE ALL POSITIONS
                <br><small>Close all open positions at market</small>
            </button>
            
            <button class="emergency-button btn-shutdown" onclick="fullShutdown()">
                ⚡ FULL SHUTDOWN
                <br><small>Complete system shutdown</small>
            </button>
            
            <button class="emergency-button btn-lock" onclick="systemLock()">
                🔒 LOCK SYSTEM
                <br><small>Lock system completely</small>
            </button>
            
            <button class="emergency-button btn-deactivate" onclick="deactivateEmergency()">
                ✅ DEACTIVATE EMERGENCY
                <br><small>Return to normal operation</small>
            </button>
        </div>
        
        <div class="log-panel">
            <h3>Emergency Log</h3>
            <div id="emergency-log">
                <div class="log-entry">System initialized - No emergency events</div>
            </div>
        </div>
    </div>
    
    <script>
        // Update current time
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        
        // Update status
        async function updateStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                // Update emergency status
                const emergencyStatus = document.getElementById('emergency-status');
                const emergencyValue = document.getElementById('emergency-value');
                if (status.emergency_active) {
                    emergencyStatus.className = 'status-item status-active pulse';
                    emergencyValue.textContent = 'ACTIVE';
                } else {
                    emergencyStatus.className = 'status-item status-normal';
                    emergencyValue.textContent = 'NORMAL';
                }
                
                // Update trading status
                const tradingStatus = document.getElementById('trading-status');
                const tradingValue = document.getElementById('trading-value');
                if (status.trading_halted) {
                    tradingStatus.className = 'status-item status-active';
                    tradingValue.textContent = 'HALTED';
                } else {
                    tradingStatus.className = 'status-item status-normal';
                    tradingValue.textContent = 'ACTIVE';
                }
                
                // Update system status
                const systemStatus = document.getElementById('system-status');
                const systemValue = document.getElementById('system-value');
                if (status.system_locked) {
                    systemStatus.className = 'status-item status-active';
                    systemValue.textContent = 'LOCKED';
                } else {
                    systemStatus.className = 'status-item status-normal';
                    systemValue.textContent = 'OPERATIONAL';
                }
                
            } catch (error) {
                console.error('Error updating status:', error);
            }
        }
        
        // Update emergency log
        async function updateLog() {
            try {
                const response = await fetch('/api/emergency/log');
                const data = await response.json();
                
                const logContainer = document.getElementById('emergency-log');
                logContainer.innerHTML = '';
                
                if (data.log && data.log.length > 0) {
                    data.log.forEach(entry => {
                        const logEntry = document.createElement('div');
                        logEntry.className = 'log-entry';
                        logEntry.innerHTML = `
                            <strong>${entry.timestamp}</strong> - 
                            ${entry.level || entry.action} 
                            ${entry.reason ? '- ' + entry.reason : ''}
                        `;
                        logContainer.appendChild(logEntry);
                    });
                } else {
                    logContainer.innerHTML = '<div class="log-entry">No emergency events</div>';
                }
                
            } catch (error) {
                console.error('Error updating log:', error);
            }
        }
        
        // Show alert
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }
        
        // Get auth headers
        function getAuthHeaders() {
            const password = document.getElementById('auth-password').value;
            const userId = document.getElementById('user-id').value;
            
            if (!password) {
                showAlert('Please enter the emergency password', 'error');
                return null;
            }
            
            return {
                'Authorization': 'Basic ' + btoa(':' + password),
                'Content-Type': 'application/json'
            };
        }
        
        // Emergency functions
        async function stopTrading() {
            if (!confirm('Are you sure you want to STOP ALL TRADING?')) return;
            
            const headers = getAuthHeaders();
            if (!headers) return;
            
            try {
                const response = await fetch('/api/emergency/stop_trading', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        reason: 'Manual stop from web dashboard',
                        user_id: document.getElementById('user-id').value
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('✅ Trading stopped successfully', 'success');
                } else {
                    showAlert('❌ ' + result.error, 'error');
                }
                
                updateStatus();
                updateLog();
                
            } catch (error) {
                showAlert('❌ Error: ' + error.message, 'error');
            }
        }
        
        async function closePositions() {
            if (!confirm('Are you sure you want to CLOSE ALL POSITIONS? This will sell everything at market prices!')) return;
            
            const headers = getAuthHeaders();
            if (!headers) return;
            
            try {
                const response = await fetch('/api/emergency/close_positions', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        reason: 'Manual close all from web dashboard',
                        user_id: document.getElementById('user-id').value
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('✅ All positions closed successfully', 'success');
                } else {
                    showAlert('❌ ' + result.error, 'error');
                }
                
                updateStatus();
                updateLog();
                
            } catch (error) {
                showAlert('❌ Error: ' + error.message, 'error');
            }
        }
        
        async function fullShutdown() {
            if (!confirm('Are you sure you want to SHUTDOWN THE ENTIRE SYSTEM? This will stop everything!')) return;
            
            const headers = getAuthHeaders();
            if (!headers) return;
            
            try {
                const response = await fetch('/api/emergency/full_shutdown', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        reason: 'Manual shutdown from web dashboard',
                        user_id: document.getElementById('user-id').value
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('✅ System shutdown initiated', 'success');
                } else {
                    showAlert('❌ ' + result.error, 'error');
                }
                
                updateStatus();
                updateLog();
                
            } catch (error) {
                showAlert('❌ Error: ' + error.message, 'error');
            }
        }
        
        async function systemLock() {
            if (!confirm('Are you sure you want to LOCK THE SYSTEM? This will prevent all operations!')) return;
            
            const headers = getAuthHeaders();
            if (!headers) return;
            
            try {
                const response = await fetch('/api/emergency/system_lock', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        reason: 'Manual system lock from web dashboard',
                        user_id: document.getElementById('user-id').value
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('✅ System locked successfully', 'success');
                } else {
                    showAlert('❌ ' + result.error, 'error');
                }
                
                updateStatus();
                updateLog();
                
            } catch (error) {
                showAlert('❌ Error: ' + error.message, 'error');
            }
        }
        
        async function deactivateEmergency() {
            if (!confirm('Are you sure you want to deactivate emergency mode?')) return;
            
            const headers = getAuthHeaders();
            if (!headers) return;
            
            try {
                const response = await fetch('/api/emergency/deactivate', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        user_id: document.getElementById('user-id').value
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('✅ Emergency mode deactivated', 'success');
                } else {
                    showAlert('❌ ' + result.error, 'error');
                }
                
                updateStatus();
                updateLog();
                
            } catch (error) {
                showAlert('❌ Error: ' + error.message, 'error');
            }
        }
        
        // Initialize
        updateTime();
        updateStatus();
        updateLog();
        
        // Update every 5 seconds
        setInterval(() => {
            updateTime();
            updateStatus();
            updateLog();
        }, 5000);
    </script>
</body>
</html>
