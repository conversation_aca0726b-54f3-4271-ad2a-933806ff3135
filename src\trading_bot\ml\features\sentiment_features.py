"""Sentiment Features from news, social media, and market sentiment indicators."""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
import logging
from datetime import datetime, timedelta
import re
from textblob import TextBlob
import requests
from collections import defaultdict

logger = logging.getLogger(__name__)


class SentimentFeatures:
    """Extract sentiment features from various sources."""
    
    def __init__(self, api_keys: Optional[Dict[str, str]] = None):
        self.api_keys = api_keys or {}
        self.sentiment_cache = {}
        
        # Sentiment lexicons
        self.financial_lexicon = self._load_financial_lexicon()
        self.volatility_keywords = self._load_volatility_keywords()
        
    def calculate_sentiment_features(self, 
                                   price_data: pd.DataFrame,
                                   news_data: Optional[pd.DataFrame] = None,
                                   social_data: Optional[pd.DataFrame] = None,
                                   symbol: Optional[str] = None) -> pd.DataFrame:
        """Calculate all sentiment features."""
        df = price_data.copy()
        
        # Market-based sentiment indicators
        df = self._add_market_sentiment_indicators(df)
        
        # Fear and greed indicators
        df = self._add_fear_greed_indicators(df)
        
        # Options-based sentiment (if available)
        df = self._add_options_sentiment(df)
        
        # News sentiment (if available)
        if news_data is not None:
            df = self._add_news_sentiment(df, news_data)
        
        # Social media sentiment (if available)
        if social_data is not None:
            df = self._add_social_sentiment(df, social_data)
        
        # Economic sentiment proxies
        df = self._add_economic_sentiment_proxies(df)
        
        return df
    
    def _add_market_sentiment_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add market-based sentiment indicators."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Price momentum sentiment
        df['momentum_sentiment'] = close.pct_change(10).rolling(5).mean()
        
        # Volume sentiment
        avg_volume = volume.rolling(20).mean()
        df['volume_sentiment'] = (volume - avg_volume) / avg_volume
        
        # Volatility sentiment (high volatility = fear)
        returns = close.pct_change()
        df['volatility_sentiment'] = -returns.rolling(10).std()  # Negative because high vol = negative sentiment
        
        # Range sentiment
        daily_range = (high - low) / close
        avg_range = daily_range.rolling(20).mean()
        df['range_sentiment'] = -(daily_range - avg_range) / avg_range  # Negative because high range = fear
        
        # Gap sentiment
        gap = (close - close.shift(1)) / close.shift(1)
        df['gap_sentiment'] = gap.rolling(5).mean()
        
        # Consecutive moves sentiment
        df['consecutive_up'] = self._calculate_consecutive_moves(close, direction='up')
        df['consecutive_down'] = self._calculate_consecutive_moves(close, direction='down')
        
        return df
    
    def _add_fear_greed_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add fear and greed indicators."""
        close, volume = df['close'], df['volume']
        
        # Custom Fear & Greed Index
        df['fear_greed_index'] = self._calculate_fear_greed_index(df)
        
        # Buying pressure
        df['buying_pressure'] = self._calculate_buying_pressure(df)
        
        # Selling pressure
        df['selling_pressure'] = self._calculate_selling_pressure(df)
        
        # Panic indicator
        df['panic_indicator'] = self._calculate_panic_indicator(df)
        
        # Euphoria indicator
        df['euphoria_indicator'] = self._calculate_euphoria_indicator(df)
        
        # FOMO (Fear of Missing Out) indicator
        df['fomo_indicator'] = self._calculate_fomo_indicator(df)
        
        return df
    
    def _add_options_sentiment(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add options-based sentiment indicators."""
        # These would require options data - using proxies based on price action
        
        # Put/Call ratio proxy
        df['put_call_ratio_proxy'] = self._calculate_put_call_proxy(df)
        
        # VIX proxy (volatility index)
        df['vix_proxy'] = self._calculate_vix_proxy(df)
        
        # Skew indicator
        df['skew_indicator'] = self._calculate_skew_indicator(df)
        
        return df
    
    def _add_news_sentiment(self, df: pd.DataFrame, news_data: pd.DataFrame) -> pd.DataFrame:
        """Add news sentiment features."""
        # Process news data
        news_sentiment = self._process_news_sentiment(news_data)
        
        # Merge with price data
        df = df.merge(news_sentiment, left_index=True, right_index=True, how='left')
        
        # Fill missing values
        sentiment_cols = ['news_sentiment', 'news_volume', 'news_polarity', 'news_subjectivity']
        for col in sentiment_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0)
        
        return df
    
    def _add_social_sentiment(self, df: pd.DataFrame, social_data: pd.DataFrame) -> pd.DataFrame:
        """Add social media sentiment features."""
        # Process social media data
        social_sentiment = self._process_social_sentiment(social_data)
        
        # Merge with price data
        df = df.merge(social_sentiment, left_index=True, right_index=True, how='left')
        
        # Fill missing values
        social_cols = ['social_sentiment', 'social_volume', 'social_buzz', 'social_polarity']
        for col in social_cols:
            if col in df.columns:
                df[col] = df[col].fillna(0)
        
        return df
    
    def _add_economic_sentiment_proxies(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add economic sentiment proxies."""
        close = df['close']
        
        # Dollar strength sentiment (inverse relationship often)
        df['dollar_sentiment'] = self._calculate_dollar_sentiment_proxy(df)
        
        # Commodity sentiment
        df['commodity_sentiment'] = self._calculate_commodity_sentiment_proxy(df)
        
        # Interest rate sentiment
        df['interest_rate_sentiment'] = self._calculate_interest_rate_sentiment_proxy(df)
        
        # Sector rotation sentiment
        df['sector_rotation_sentiment'] = self._calculate_sector_rotation_sentiment(df)
        
        return df
    
    def _calculate_consecutive_moves(self, prices: pd.Series, direction: str) -> pd.Series:
        """Calculate consecutive moves in one direction."""
        returns = prices.pct_change()
        
        if direction == 'up':
            moves = (returns > 0).astype(int)
        else:
            moves = (returns < 0).astype(int)
        
        # Count consecutive moves
        consecutive = moves * (moves.groupby((moves != moves.shift()).cumsum()).cumcount() + 1)
        
        return consecutive
    
    def _calculate_fear_greed_index(self, df: pd.DataFrame) -> pd.Series:
        """Calculate custom Fear & Greed Index."""
        close, volume = df['close'], df['volume']
        
        # Components (normalized to 0-100 scale)
        
        # 1. Price momentum (0 = extreme fear, 100 = extreme greed)
        momentum = close.pct_change(10).rolling(5).mean()
        momentum_score = self._normalize_to_0_100(momentum, momentum.rolling(50).quantile(0.05), momentum.rolling(50).quantile(0.95))
        
        # 2. Volume sentiment
        vol_ratio = volume / volume.rolling(20).mean()
        vol_score = self._normalize_to_0_100(vol_ratio, 0.5, 2.0)
        
        # 3. Volatility (inverted - high vol = fear)
        volatility = close.pct_change().rolling(10).std()
        vol_fear_score = 100 - self._normalize_to_0_100(volatility, volatility.rolling(50).quantile(0.05), volatility.rolling(50).quantile(0.95))
        
        # 4. Market breadth proxy
        up_days = (close.pct_change() > 0).rolling(10).sum()
        breadth_score = self._normalize_to_0_100(up_days, 0, 10)
        
        # Weighted average
        fear_greed = (momentum_score * 0.3 + vol_score * 0.2 + vol_fear_score * 0.3 + breadth_score * 0.2)
        
        return fear_greed
    
    def _calculate_buying_pressure(self, df: pd.DataFrame) -> pd.Series:
        """Calculate buying pressure indicator."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Money Flow Multiplier
        mfm = ((close - low) - (high - close)) / (high - low)
        mfm = mfm.fillna(0)
        
        # Money Flow Volume
        mfv = mfm * volume
        
        # Buying pressure (positive MFV)
        buying_pressure = np.where(mfv > 0, mfv, 0)
        
        # Normalize by total volume
        total_volume = volume.rolling(20).sum()
        buying_pressure_ratio = pd.Series(buying_pressure).rolling(20).sum() / total_volume
        
        return buying_pressure_ratio
    
    def _calculate_selling_pressure(self, df: pd.DataFrame) -> pd.Series:
        """Calculate selling pressure indicator."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Money Flow Multiplier
        mfm = ((close - low) - (high - close)) / (high - low)
        mfm = mfm.fillna(0)
        
        # Money Flow Volume
        mfv = mfm * volume
        
        # Selling pressure (negative MFV)
        selling_pressure = np.where(mfv < 0, abs(mfv), 0)
        
        # Normalize by total volume
        total_volume = volume.rolling(20).sum()
        selling_pressure_ratio = pd.Series(selling_pressure).rolling(20).sum() / total_volume
        
        return selling_pressure_ratio
    
    def _calculate_panic_indicator(self, df: pd.DataFrame) -> pd.Series:
        """Calculate panic selling indicator."""
        close, volume = df['close'], df['volume']
        
        # Large negative moves with high volume
        returns = close.pct_change()
        volume_spike = volume / volume.rolling(20).mean()
        
        # Panic conditions
        panic_threshold = returns.rolling(50).quantile(0.05)  # Bottom 5% of returns
        volume_threshold = 1.5  # 50% above average volume
        
        panic_indicator = ((returns < panic_threshold) & (volume_spike > volume_threshold)).astype(int)
        
        # Smooth the indicator
        panic_score = panic_indicator.rolling(5).sum()
        
        return panic_score
    
    def _calculate_euphoria_indicator(self, df: pd.DataFrame) -> pd.Series:
        """Calculate euphoria/greed indicator."""
        close, volume = df['close'], df['volume']
        
        # Large positive moves with high volume
        returns = close.pct_change()
        volume_spike = volume / volume.rolling(20).mean()
        
        # Euphoria conditions
        euphoria_threshold = returns.rolling(50).quantile(0.95)  # Top 5% of returns
        volume_threshold = 1.5
        
        euphoria_indicator = ((returns > euphoria_threshold) & (volume_spike > volume_threshold)).astype(int)
        
        # Smooth the indicator
        euphoria_score = euphoria_indicator.rolling(5).sum()
        
        return euphoria_score
    
    def _calculate_fomo_indicator(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Fear of Missing Out indicator."""
        close, volume = df['close'], df['volume']
        
        # Consecutive up days with increasing volume
        up_days = (close.pct_change() > 0).astype(int)
        volume_increasing = (volume > volume.shift(1)).astype(int)
        
        # FOMO conditions
        fomo_signal = up_days & volume_increasing
        
        # Count consecutive FOMO days
        fomo_streak = fomo_signal * (fomo_signal.groupby((fomo_signal != fomo_signal.shift()).cumsum()).cumcount() + 1)
        
        return fomo_streak
    
    def _calculate_put_call_proxy(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Put/Call ratio proxy from price action."""
        close, volume = df['close'], df['volume']
        
        # Use down volume vs up volume as proxy
        returns = close.pct_change()
        
        down_volume = np.where(returns < 0, volume, 0)
        up_volume = np.where(returns > 0, volume, 0)
        
        # Rolling put/call ratio proxy
        put_call_ratio = pd.Series(down_volume).rolling(10).sum() / (pd.Series(up_volume).rolling(10).sum() + 1e-8)
        
        return put_call_ratio
    
    def _calculate_vix_proxy(self, df: pd.DataFrame) -> pd.Series:
        """Calculate VIX proxy from realized volatility."""
        close = df['close']
        
        # 30-day realized volatility as VIX proxy
        returns = close.pct_change()
        realized_vol = returns.rolling(30).std() * np.sqrt(252) * 100
        
        return realized_vol
    
    def _calculate_skew_indicator(self, df: pd.DataFrame) -> pd.Series:
        """Calculate skew indicator from return distribution."""
        close = df['close']
        
        # Rolling skewness of returns
        returns = close.pct_change()
        skew = returns.rolling(30).skew()
        
        return skew
    
    def _process_news_sentiment(self, news_data: pd.DataFrame) -> pd.DataFrame:
        """Process news data to extract sentiment."""
        if 'text' not in news_data.columns or 'timestamp' not in news_data.columns:
            logger.warning("News data missing required columns")
            return pd.DataFrame()
        
        results = []
        
        for date, group in news_data.groupby(pd.to_datetime(news_data['timestamp']).dt.date):
            daily_sentiment = self._analyze_text_sentiment(group['text'].tolist())
            
            results.append({
                'date': date,
                'news_sentiment': daily_sentiment['compound_score'],
                'news_volume': len(group),
                'news_polarity': daily_sentiment['polarity'],
                'news_subjectivity': daily_sentiment['subjectivity']
            })
        
        sentiment_df = pd.DataFrame(results)
        sentiment_df.set_index('date', inplace=True)
        
        return sentiment_df
    
    def _process_social_sentiment(self, social_data: pd.DataFrame) -> pd.DataFrame:
        """Process social media data to extract sentiment."""
        if 'text' not in social_data.columns or 'timestamp' not in social_data.columns:
            logger.warning("Social data missing required columns")
            return pd.DataFrame()
        
        results = []
        
        for date, group in social_data.groupby(pd.to_datetime(social_data['timestamp']).dt.date):
            daily_sentiment = self._analyze_text_sentiment(group['text'].tolist())
            
            # Social media specific metrics
            buzz_score = len(group)
            engagement = group.get('likes', 0).sum() + group.get('shares', 0).sum()
            
            results.append({
                'date': date,
                'social_sentiment': daily_sentiment['compound_score'],
                'social_volume': len(group),
                'social_buzz': buzz_score,
                'social_polarity': daily_sentiment['polarity'],
                'social_engagement': engagement
            })
        
        sentiment_df = pd.DataFrame(results)
        sentiment_df.set_index('date', inplace=True)
        
        return sentiment_df
    
    def _analyze_text_sentiment(self, texts: List[str]) -> Dict[str, float]:
        """Analyze sentiment of text list."""
        if not texts:
            return {'compound_score': 0, 'polarity': 0, 'subjectivity': 0}
        
        # Combine all texts
        combined_text = ' '.join(texts)
        
        # Basic sentiment analysis using TextBlob
        blob = TextBlob(combined_text)
        
        # Financial keyword analysis
        financial_score = self._calculate_financial_sentiment_score(combined_text)
        
        # Combine scores
        compound_score = (blob.sentiment.polarity + financial_score) / 2
        
        return {
            'compound_score': compound_score,
            'polarity': blob.sentiment.polarity,
            'subjectivity': blob.sentiment.subjectivity
        }
    
    def _calculate_financial_sentiment_score(self, text: str) -> float:
        """Calculate sentiment score using financial lexicon."""
        text = text.lower()
        
        positive_score = 0
        negative_score = 0
        
        for word, score in self.financial_lexicon.items():
            count = text.count(word)
            if score > 0:
                positive_score += count * score
            else:
                negative_score += count * abs(score)
        
        if positive_score + negative_score == 0:
            return 0
        
        return (positive_score - negative_score) / (positive_score + negative_score)
    
    def _load_financial_lexicon(self) -> Dict[str, float]:
        """Load financial sentiment lexicon."""
        # Simplified financial lexicon
        lexicon = {
            # Positive words
            'bullish': 1.0, 'bull': 0.8, 'rally': 0.8, 'surge': 0.9, 'soar': 0.9,
            'gain': 0.7, 'rise': 0.6, 'jump': 0.8, 'climb': 0.6, 'advance': 0.6,
            'outperform': 0.8, 'beat': 0.7, 'exceed': 0.7, 'strong': 0.6, 'robust': 0.6,
            'optimistic': 0.7, 'positive': 0.6, 'upgrade': 0.8, 'buy': 0.9,
            
            # Negative words
            'bearish': -1.0, 'bear': -0.8, 'crash': -0.9, 'plunge': -0.9, 'dive': -0.8,
            'fall': -0.6, 'drop': -0.6, 'decline': -0.6, 'tumble': -0.8, 'slide': -0.6,
            'underperform': -0.8, 'miss': -0.7, 'weak': -0.6, 'poor': -0.6, 'disappointing': -0.7,
            'pessimistic': -0.7, 'negative': -0.6, 'downgrade': -0.8, 'sell': -0.9,
            'loss': -0.7, 'losses': -0.7, 'risk': -0.5, 'concern': -0.5, 'worry': -0.6
        }
        
        return lexicon
    
    def _load_volatility_keywords(self) -> List[str]:
        """Load volatility-related keywords."""
        return [
            'volatile', 'volatility', 'uncertain', 'uncertainty', 'swing', 'swings',
            'turbulent', 'choppy', 'erratic', 'unstable', 'fluctuate', 'fluctuation'
        ]
    
    def _normalize_to_0_100(self, series: pd.Series, min_val: float, max_val: float) -> pd.Series:
        """Normalize series to 0-100 scale."""
        normalized = (series - min_val) / (max_val - min_val) * 100
        return normalized.clip(0, 100)
    
    def _calculate_dollar_sentiment_proxy(self, df: pd.DataFrame) -> pd.Series:
        """Calculate dollar strength sentiment proxy."""
        # Simplified - in reality would use DXY or currency data
        close = df['close']
        
        # Use inverse correlation assumption
        dollar_proxy = -close.pct_change(10).rolling(5).mean()
        
        return dollar_proxy
    
    def _calculate_commodity_sentiment_proxy(self, df: pd.DataFrame) -> pd.Series:
        """Calculate commodity sentiment proxy."""
        # Simplified - would use actual commodity indices
        close = df['close']
        
        # Use volatility as proxy for commodity stress
        volatility = close.pct_change().rolling(10).std()
        commodity_sentiment = -volatility  # High volatility = negative sentiment
        
        return commodity_sentiment
    
    def _calculate_interest_rate_sentiment_proxy(self, df: pd.DataFrame) -> pd.Series:
        """Calculate interest rate sentiment proxy."""
        # Simplified - would use actual yield data
        close = df['close']
        
        # Use price momentum as proxy
        rate_sentiment = close.pct_change(20).rolling(5).mean()
        
        return rate_sentiment
    
    def _calculate_sector_rotation_sentiment(self, df: pd.DataFrame) -> pd.Series:
        """Calculate sector rotation sentiment."""
        close, volume = df['close'], df['volume']
        
        # Use relative performance and volume as proxy
        returns = close.pct_change()
        volume_ratio = volume / volume.rolling(20).mean()
        
        # Sector rotation indicator
        rotation_sentiment = returns.rolling(10).mean() * volume_ratio.rolling(10).mean()
        
        return rotation_sentiment
    
    def get_sentiment_feature_list(self) -> List[str]:
        """Get list of all sentiment features."""
        return [
            # Market sentiment
            'momentum_sentiment', 'volume_sentiment', 'volatility_sentiment',
            'range_sentiment', 'gap_sentiment', 'consecutive_up', 'consecutive_down',
            
            # Fear & Greed
            'fear_greed_index', 'buying_pressure', 'selling_pressure',
            'panic_indicator', 'euphoria_indicator', 'fomo_indicator',
            
            # Options proxies
            'put_call_ratio_proxy', 'vix_proxy', 'skew_indicator',
            
            # News sentiment (if available)
            'news_sentiment', 'news_volume', 'news_polarity', 'news_subjectivity',
            
            # Social sentiment (if available)
            'social_sentiment', 'social_volume', 'social_buzz', 'social_polarity',
            
            # Economic proxies
            'dollar_sentiment', 'commodity_sentiment', 'interest_rate_sentiment',
            'sector_rotation_sentiment'
        ]