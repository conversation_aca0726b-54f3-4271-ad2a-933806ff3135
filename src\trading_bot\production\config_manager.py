"""
Production configuration management system for AI Trading Bot.

This module provides comprehensive configuration management for production
deployment with environment-specific settings, feature flags, and validation:

Configuration Features:
- Environment-specific configurations (dev, staging, production)
- Feature flags for gradual rollouts
- Configuration validation and schema enforcement
- Dynamic configuration updates
- Configuration versioning and rollback
- Secure secrets management
- Configuration drift detection
"""

import os
import yaml
import json
import logging
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pathlib import Path
import hashlib

from ....core.config import Config
from ....utils.logger import get_logger

logger = get_logger(__name__)


class Environment(Enum):
    """Deployment environments."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class TradingMode(Enum):
    """Trading modes."""
    PAPER = "paper"
    LIVE = "live"
    SIMULATION = "simulation"


@dataclass
class FeatureFlag:
    """Feature flag configuration."""
    name: str
    enabled: bool
    description: str
    rollout_percentage: float = 100.0
    target_users: List[str] = field(default_factory=list)
    target_environments: List[Environment] = field(default_factory=list)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    dependencies: List[str] = field(default_factory=list)


@dataclass
class DeploymentConfig:
    """Production deployment configuration."""
    environment: Environment
    trading_mode: TradingMode
    version: str
    build_date: datetime
    git_commit: str
    
    # Resource limits
    max_memory_gb: float = 8.0
    max_cpu_cores: float = 4.0
    max_disk_gb: float = 200.0
    max_connections: int = 200
    max_concurrent_orders: int = 50
    
    # Performance targets
    max_latency_ms: float = 100.0
    min_throughput_ops_sec: float = 1000.0
    max_error_rate: float = 0.01
    
    # Safety limits
    max_daily_loss_percent: float = 2.0
    max_position_size_percent: float = 10.0
    max_sector_exposure_percent: float = 30.0
    
    # Feature flags
    feature_flags: Dict[str, FeatureFlag] = field(default_factory=dict)


@dataclass
class ConfigValidationResult:
    """Configuration validation result."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)


class ProductionConfigManager:
    """
    Production configuration management system.
    
    Manages environment-specific configurations, feature flags,
    and provides validation and deployment safety checks.
    """
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.current_config: Optional[DeploymentConfig] = None
        self.config_history: List[DeploymentConfig] = []
        
        # Configuration file paths
        self.config_files = {
            Environment.DEVELOPMENT: self.config_dir / "development.yaml",
            Environment.STAGING: self.config_dir / "staging.yaml", 
            Environment.PRODUCTION: self.config_dir / "production.yaml"
        }
        
        # Feature flags file
        self.feature_flags_file = self.config_dir / "feature_flags.yaml"
        
        # Secrets management
        self.secrets_file = self.config_dir / "secrets.yaml"
        
        # Configuration schema for validation
        self.config_schema = self._load_config_schema()
    
    async def initialize(self, environment: Environment):
        """Initialize configuration manager for specific environment."""
        logger.info(f"Initializing configuration manager for {environment.value}")
        
        # Create config directory if it doesn't exist
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Load configuration
        self.current_config = await self._load_config(environment)
        
        # Load feature flags
        await self._load_feature_flags()
        
        # Validate configuration
        validation_result = await self.validate_config()
        if not validation_result.is_valid:
            raise ValueError(f"Invalid configuration: {validation_result.errors}")
        
        # Log warnings and recommendations
        for warning in validation_result.warnings:
            logger.warning(f"Configuration warning: {warning}")
        
        for recommendation in validation_result.recommendations:
            logger.info(f"Configuration recommendation: {recommendation}")
        
        logger.info("Configuration manager initialized successfully")
    
    async def get_config(self) -> DeploymentConfig:
        """Get current deployment configuration."""
        if not self.current_config:
            raise ValueError("Configuration not initialized")
        return self.current_config
    
    async def update_config(self, updates: Dict[str, Any]) -> bool:
        """Update configuration with new values."""
        if not self.current_config:
            raise ValueError("Configuration not initialized")
        
        # Create backup of current config
        self.config_history.append(self.current_config)
        
        # Apply updates
        updated_config = self._apply_config_updates(self.current_config, updates)
        
        # Validate updated configuration
        validation_result = await self._validate_config_object(updated_config)
        if not validation_result.is_valid:
            logger.error(f"Configuration update failed validation: {validation_result.errors}")
            return False
        
        # Save updated configuration
        self.current_config = updated_config
        await self._save_config()
        
        logger.info("Configuration updated successfully")
        return True
    
    async def rollback_config(self) -> bool:
        """Rollback to previous configuration."""
        if not self.config_history:
            logger.error("No configuration history available for rollback")
            return False
        
        # Restore previous configuration
        self.current_config = self.config_history.pop()
        await self._save_config()
        
        logger.info("Configuration rolled back successfully")
        return True
    
    def is_feature_enabled(self, feature_name: str, user_id: Optional[str] = None) -> bool:
        """Check if a feature flag is enabled."""
        if not self.current_config:
            return False
        
        feature = self.current_config.feature_flags.get(feature_name)
        if not feature:
            return False
        
        # Check if feature is globally enabled
        if not feature.enabled:
            return False
        
        # Check environment targeting
        if feature.target_environments and self.current_config.environment not in feature.target_environments:
            return False
        
        # Check user targeting
        if feature.target_users and user_id and user_id not in feature.target_users:
            return False
        
        # Check date range
        now = datetime.utcnow()
        if feature.start_date and now < feature.start_date:
            return False
        if feature.end_date and now > feature.end_date:
            return False
        
        # Check rollout percentage
        if feature.rollout_percentage < 100.0:
            # Use deterministic hash for consistent rollout
            hash_input = f"{feature_name}:{user_id or 'anonymous'}"
            hash_value = int(hashlib.md5(hash_input.encode()).hexdigest(), 16)
            rollout_threshold = (hash_value % 100) + 1
            return rollout_threshold <= feature.rollout_percentage
        
        return True
    
    async def enable_feature(self, feature_name: str, rollout_percentage: float = 100.0) -> bool:
        """Enable a feature flag."""
        if not self.current_config:
            return False
        
        if feature_name in self.current_config.feature_flags:
            self.current_config.feature_flags[feature_name].enabled = True
            self.current_config.feature_flags[feature_name].rollout_percentage = rollout_percentage
        else:
            self.current_config.feature_flags[feature_name] = FeatureFlag(
                name=feature_name,
                enabled=True,
                description=f"Feature flag for {feature_name}",
                rollout_percentage=rollout_percentage
            )
        
        await self._save_feature_flags()
        logger.info(f"Feature {feature_name} enabled with {rollout_percentage}% rollout")
        return True
    
    async def disable_feature(self, feature_name: str) -> bool:
        """Disable a feature flag."""
        if not self.current_config:
            return False
        
        if feature_name in self.current_config.feature_flags:
            self.current_config.feature_flags[feature_name].enabled = False
            await self._save_feature_flags()
            logger.info(f"Feature {feature_name} disabled")
            return True
        
        return False
    
    async def validate_config(self) -> ConfigValidationResult:
        """Validate current configuration."""
        if not self.current_config:
            return ConfigValidationResult(
                is_valid=False,
                errors=["Configuration not loaded"]
            )
        
        return await self._validate_config_object(self.current_config)
    
    async def get_config_diff(self, other_environment: Environment) -> Dict[str, Any]:
        """Get configuration differences between environments."""
        if not self.current_config:
            return {}
        
        other_config = await self._load_config(other_environment)
        return self._calculate_config_diff(self.current_config, other_config)
    
    def get_config_checksum(self) -> str:
        """Get checksum of current configuration."""
        if not self.current_config:
            return ""
        
        config_str = json.dumps(self._config_to_dict(self.current_config), sort_keys=True)
        return hashlib.sha256(config_str.encode()).hexdigest()
    
    async def _load_config(self, environment: Environment) -> DeploymentConfig:
        """Load configuration for specific environment."""
        config_file = self.config_files[environment]
        
        if not config_file.exists():
            # Create default configuration
            default_config = self._create_default_config(environment)
            await self._save_config_to_file(default_config, config_file)
            return default_config
        
        # Load from file
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
        
        return self._dict_to_config(config_data, environment)
    
    async def _load_feature_flags(self):
        """Load feature flags from file."""
        if not self.feature_flags_file.exists():
            # Create default feature flags
            default_flags = self._create_default_feature_flags()
            await self._save_feature_flags_to_file(default_flags)
            return
        
        with open(self.feature_flags_file, 'r') as f:
            flags_data = yaml.safe_load(f)
        
        # Convert to FeatureFlag objects
        for flag_name, flag_data in flags_data.get('feature_flags', {}).items():
            self.current_config.feature_flags[flag_name] = FeatureFlag(
                name=flag_name,
                enabled=flag_data.get('enabled', False),
                description=flag_data.get('description', ''),
                rollout_percentage=flag_data.get('rollout_percentage', 100.0),
                target_users=flag_data.get('target_users', []),
                target_environments=[Environment(env) for env in flag_data.get('target_environments', [])],
                start_date=datetime.fromisoformat(flag_data['start_date']) if flag_data.get('start_date') else None,
                end_date=datetime.fromisoformat(flag_data['end_date']) if flag_data.get('end_date') else None,
                dependencies=flag_data.get('dependencies', [])
            )

    def _create_default_config(self, environment: Environment) -> DeploymentConfig:
        """Create default configuration for environment."""
        return DeploymentConfig(
            environment=environment,
            trading_mode=TradingMode.PAPER if environment != Environment.PRODUCTION else TradingMode.PAPER,
            version="1.0.0",
            build_date=datetime.utcnow(),
            git_commit="unknown",
            max_memory_gb=8.0 if environment == Environment.PRODUCTION else 4.0,
            max_cpu_cores=4.0 if environment == Environment.PRODUCTION else 2.0,
            max_disk_gb=200.0 if environment == Environment.PRODUCTION else 100.0,
            max_connections=200 if environment == Environment.PRODUCTION else 100,
            max_concurrent_orders=50 if environment == Environment.PRODUCTION else 20,
            max_latency_ms=100.0,
            min_throughput_ops_sec=1000.0,
            max_error_rate=0.01,
            max_daily_loss_percent=2.0 if environment == Environment.PRODUCTION else 5.0,
            max_position_size_percent=10.0,
            max_sector_exposure_percent=30.0
        )

    def _create_default_feature_flags(self) -> Dict[str, FeatureFlag]:
        """Create default feature flags."""
        return {
            "sentiment_analysis": FeatureFlag(
                name="sentiment_analysis",
                enabled=True,
                description="Enable sentiment analysis for trading decisions",
                rollout_percentage=100.0
            ),
            "options_trading": FeatureFlag(
                name="options_trading",
                enabled=False,
                description="Enable options trading capabilities",
                rollout_percentage=0.0
            ),
            "crypto_trading": FeatureFlag(
                name="crypto_trading",
                enabled=False,
                description="Enable cryptocurrency trading",
                rollout_percentage=0.0
            ),
            "advanced_analytics": FeatureFlag(
                name="advanced_analytics",
                enabled=True,
                description="Enable advanced analytics and reporting",
                rollout_percentage=100.0
            ),
            "real_time_alerts": FeatureFlag(
                name="real_time_alerts",
                enabled=True,
                description="Enable real-time trading alerts",
                rollout_percentage=100.0
            ),
            "portfolio_optimization": FeatureFlag(
                name="portfolio_optimization",
                enabled=True,
                description="Enable portfolio optimization algorithms",
                rollout_percentage=100.0
            ),
            "high_frequency_trading": FeatureFlag(
                name="high_frequency_trading",
                enabled=False,
                description="Enable high-frequency trading strategies",
                rollout_percentage=0.0,
                target_environments=[Environment.PRODUCTION]
            )
        }

    async def _validate_config_object(self, config: DeploymentConfig) -> ConfigValidationResult:
        """Validate a configuration object."""
        errors = []
        warnings = []
        recommendations = []

        # Validate resource limits
        if config.max_memory_gb < 2.0:
            errors.append("Minimum memory requirement is 2GB")
        elif config.max_memory_gb < 4.0:
            warnings.append("Recommended minimum memory is 4GB")

        if config.max_cpu_cores < 1.0:
            errors.append("Minimum CPU requirement is 1 core")
        elif config.max_cpu_cores < 2.0:
            warnings.append("Recommended minimum CPU is 2 cores")

        if config.max_disk_gb < 50.0:
            errors.append("Minimum disk space requirement is 50GB")
        elif config.max_disk_gb < 100.0:
            warnings.append("Recommended minimum disk space is 100GB")

        # Validate performance targets
        if config.max_latency_ms > 1000.0:
            warnings.append("High latency target may impact trading performance")

        if config.min_throughput_ops_sec < 100.0:
            warnings.append("Low throughput target may limit trading capacity")

        if config.max_error_rate > 0.05:
            errors.append("Error rate target too high for production trading")

        # Validate safety limits
        if config.max_daily_loss_percent > 5.0:
            errors.append("Daily loss limit too high for safe trading")
        elif config.max_daily_loss_percent > 2.0 and config.environment == Environment.PRODUCTION:
            warnings.append("Consider lower daily loss limit for production")

        if config.max_position_size_percent > 20.0:
            errors.append("Position size limit too high for risk management")
        elif config.max_position_size_percent > 10.0:
            warnings.append("Consider lower position size limit")

        if config.max_sector_exposure_percent > 50.0:
            errors.append("Sector exposure limit too high for diversification")

        # Validate trading mode for environment
        if config.environment == Environment.PRODUCTION and config.trading_mode == TradingMode.LIVE:
            if not self._validate_production_readiness():
                errors.append("System not ready for live trading in production")

        # Generate recommendations
        if config.environment == Environment.PRODUCTION:
            recommendations.extend([
                "Enable comprehensive monitoring and alerting",
                "Implement automated backup procedures",
                "Set up disaster recovery protocols",
                "Configure security monitoring"
            ])

        return ConfigValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            recommendations=recommendations
        )

    def _validate_production_readiness(self) -> bool:
        """Validate system readiness for production live trading."""
        # This would check various system components
        # For now, return True as a placeholder
        return True

    def _apply_config_updates(self, config: DeploymentConfig, updates: Dict[str, Any]) -> DeploymentConfig:
        """Apply updates to configuration object."""
        # Create a copy of the current config
        updated_config = DeploymentConfig(
            environment=config.environment,
            trading_mode=config.trading_mode,
            version=config.version,
            build_date=config.build_date,
            git_commit=config.git_commit,
            max_memory_gb=config.max_memory_gb,
            max_cpu_cores=config.max_cpu_cores,
            max_disk_gb=config.max_disk_gb,
            max_connections=config.max_connections,
            max_concurrent_orders=config.max_concurrent_orders,
            max_latency_ms=config.max_latency_ms,
            min_throughput_ops_sec=config.min_throughput_ops_sec,
            max_error_rate=config.max_error_rate,
            max_daily_loss_percent=config.max_daily_loss_percent,
            max_position_size_percent=config.max_position_size_percent,
            max_sector_exposure_percent=config.max_sector_exposure_percent,
            feature_flags=config.feature_flags.copy()
        )

        # Apply updates
        for key, value in updates.items():
            if hasattr(updated_config, key):
                setattr(updated_config, key, value)

        return updated_config

    def _calculate_config_diff(self, config1: DeploymentConfig, config2: DeploymentConfig) -> Dict[str, Any]:
        """Calculate differences between two configurations."""
        diff = {}

        config1_dict = self._config_to_dict(config1)
        config2_dict = self._config_to_dict(config2)

        for key in config1_dict:
            if key in config2_dict:
                if config1_dict[key] != config2_dict[key]:
                    diff[key] = {
                        'current': config1_dict[key],
                        'other': config2_dict[key]
                    }
            else:
                diff[key] = {
                    'current': config1_dict[key],
                    'other': None
                }

        for key in config2_dict:
            if key not in config1_dict:
                diff[key] = {
                    'current': None,
                    'other': config2_dict[key]
                }

        return diff

    def _config_to_dict(self, config: DeploymentConfig) -> Dict[str, Any]:
        """Convert configuration object to dictionary."""
        return {
            'environment': config.environment.value,
            'trading_mode': config.trading_mode.value,
            'version': config.version,
            'build_date': config.build_date.isoformat(),
            'git_commit': config.git_commit,
            'max_memory_gb': config.max_memory_gb,
            'max_cpu_cores': config.max_cpu_cores,
            'max_disk_gb': config.max_disk_gb,
            'max_connections': config.max_connections,
            'max_concurrent_orders': config.max_concurrent_orders,
            'max_latency_ms': config.max_latency_ms,
            'min_throughput_ops_sec': config.min_throughput_ops_sec,
            'max_error_rate': config.max_error_rate,
            'max_daily_loss_percent': config.max_daily_loss_percent,
            'max_position_size_percent': config.max_position_size_percent,
            'max_sector_exposure_percent': config.max_sector_exposure_percent,
            'feature_flags': {name: {
                'enabled': flag.enabled,
                'description': flag.description,
                'rollout_percentage': flag.rollout_percentage
            } for name, flag in config.feature_flags.items()}
        }

    def _dict_to_config(self, config_data: Dict[str, Any], environment: Environment) -> DeploymentConfig:
        """Convert dictionary to configuration object."""
        return DeploymentConfig(
            environment=environment,
            trading_mode=TradingMode(config_data.get('trading_mode', 'paper')),
            version=config_data.get('version', '1.0.0'),
            build_date=datetime.fromisoformat(config_data.get('build_date', datetime.utcnow().isoformat())),
            git_commit=config_data.get('git_commit', 'unknown'),
            max_memory_gb=config_data.get('max_memory_gb', 8.0),
            max_cpu_cores=config_data.get('max_cpu_cores', 4.0),
            max_disk_gb=config_data.get('max_disk_gb', 200.0),
            max_connections=config_data.get('max_connections', 200),
            max_concurrent_orders=config_data.get('max_concurrent_orders', 50),
            max_latency_ms=config_data.get('max_latency_ms', 100.0),
            min_throughput_ops_sec=config_data.get('min_throughput_ops_sec', 1000.0),
            max_error_rate=config_data.get('max_error_rate', 0.01),
            max_daily_loss_percent=config_data.get('max_daily_loss_percent', 2.0),
            max_position_size_percent=config_data.get('max_position_size_percent', 10.0),
            max_sector_exposure_percent=config_data.get('max_sector_exposure_percent', 30.0)
        )

    async def _save_config(self):
        """Save current configuration to file."""
        if not self.current_config:
            return

        config_file = self.config_files[self.current_config.environment]
        await self._save_config_to_file(self.current_config, config_file)

    async def _save_config_to_file(self, config: DeploymentConfig, file_path: Path):
        """Save configuration to specific file."""
        config_dict = self._config_to_dict(config)

        with open(file_path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)

    async def _save_feature_flags(self):
        """Save feature flags to file."""
        if not self.current_config:
            return

        await self._save_feature_flags_to_file(self.current_config.feature_flags)

    async def _save_feature_flags_to_file(self, feature_flags: Dict[str, FeatureFlag]):
        """Save feature flags to specific file."""
        flags_data = {
            'feature_flags': {}
        }

        for name, flag in feature_flags.items():
            flags_data['feature_flags'][name] = {
                'enabled': flag.enabled,
                'description': flag.description,
                'rollout_percentage': flag.rollout_percentage,
                'target_users': flag.target_users,
                'target_environments': [env.value for env in flag.target_environments],
                'start_date': flag.start_date.isoformat() if flag.start_date else None,
                'end_date': flag.end_date.isoformat() if flag.end_date else None,
                'dependencies': flag.dependencies
            }

        with open(self.feature_flags_file, 'w') as f:
            yaml.dump(flags_data, f, default_flow_style=False, indent=2)

    def _load_config_schema(self) -> Dict[str, Any]:
        """Load configuration schema for validation."""
        # This would typically load from a JSON schema file
        # For now, return a basic schema
        return {
            "type": "object",
            "properties": {
                "environment": {"type": "string", "enum": ["development", "staging", "production"]},
                "trading_mode": {"type": "string", "enum": ["paper", "live", "simulation"]},
                "version": {"type": "string"},
                "max_memory_gb": {"type": "number", "minimum": 2.0},
                "max_cpu_cores": {"type": "number", "minimum": 1.0},
                "max_disk_gb": {"type": "number", "minimum": 50.0},
                "max_daily_loss_percent": {"type": "number", "minimum": 0.1, "maximum": 10.0},
                "max_position_size_percent": {"type": "number", "minimum": 1.0, "maximum": 25.0}
            },
            "required": ["environment", "trading_mode", "version"]
        }

    async def export_config(self, format: str = "yaml") -> str:
        """Export current configuration in specified format."""
        if not self.current_config:
            return ""

        config_dict = self._config_to_dict(self.current_config)

        if format.lower() == "yaml":
            return yaml.dump(config_dict, default_flow_style=False, indent=2)
        elif format.lower() == "json":
            return json.dumps(config_dict, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")

    async def import_config(self, config_str: str, format: str = "yaml") -> bool:
        """Import configuration from string."""
        try:
            if format.lower() == "yaml":
                config_data = yaml.safe_load(config_str)
            elif format.lower() == "json":
                config_data = json.loads(config_str)
            else:
                raise ValueError(f"Unsupported import format: {format}")

            # Convert to config object
            environment = Environment(config_data.get('environment', 'development'))
            imported_config = self._dict_to_config(config_data, environment)

            # Validate imported configuration
            validation_result = await self._validate_config_object(imported_config)
            if not validation_result.is_valid:
                logger.error(f"Imported configuration validation failed: {validation_result.errors}")
                return False

            # Backup current config and apply imported config
            if self.current_config:
                self.config_history.append(self.current_config)

            self.current_config = imported_config
            await self._save_config()

            logger.info("Configuration imported successfully")
            return True

        except Exception as e:
            logger.error(f"Configuration import failed: {e}")
            return False

    def get_feature_flags_summary(self) -> Dict[str, Any]:
        """Get summary of all feature flags."""
        if not self.current_config:
            return {}

        summary = {
            'total_flags': len(self.current_config.feature_flags),
            'enabled_flags': len([f for f in self.current_config.feature_flags.values() if f.enabled]),
            'disabled_flags': len([f for f in self.current_config.feature_flags.values() if not f.enabled]),
            'flags': {}
        }

        for name, flag in self.current_config.feature_flags.items():
            summary['flags'][name] = {
                'enabled': flag.enabled,
                'rollout_percentage': flag.rollout_percentage,
                'description': flag.description
            }

        return summary

    async def shutdown(self):
        """Shutdown configuration manager."""
        logger.info("Shutting down configuration manager...")

        # Save current configuration
        if self.current_config:
            await self._save_config()
            await self._save_feature_flags()

        logger.info("Configuration manager shutdown completed")
