"""Risk limit enforcement system with circuit breakers."""

from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
from typing import Dict, List, Optional, Tuple

import numpy as np
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import Order, Portfolio, Position, Symbol

logger = get_logger(__name__)


class RiskLimitType(Enum):
    """Types of risk limits."""
    DAILY_LOSS = "daily_loss"
    POSITION_SIZE = "position_size"
    SECTOR_EXPOSURE = "sector_exposure"
    CORRELATION = "correlation"
    DRAWDOWN = "drawdown"
    VAR = "var"
    CONCENTRATION = "concentration"


class CircuitBreakerLevel(Enum):
    """Circuit breaker levels."""
    LEVEL_1 = "level_1"  # Warning
    LEVEL_2 = "level_2"  # Reduce positions
    LEVEL_3 = "level_3"  # Stop trading


class RiskLimitEnforcer:
    """Comprehensive risk limit enforcement system."""
    
    def __init__(self):
        self.config = settings.risk
        self.circuit_breakers = {
            CircuitBreakerLevel.LEVEL_1: 0.02,  # 2% daily loss
            CircuitBreakerLevel.LEVEL_2: 0.05,  # 5% daily loss
            CircuitBreakerLevel.LEVEL_3: 0.10,  # 10% daily loss
        }
        self.active_limits = {}
        self.breach_history = []
        
    async def initialize(self):
        """Initialize risk limit enforcer."""
        await self._load_active_limits()
        logger.info("Risk limit enforcer initialized")
    
    async def _load_active_limits(self):
        """Load active risk limits from configuration."""
        self.active_limits = {
            RiskLimitType.DAILY_LOSS: {
                "limit": self.config.max_daily_drawdown,
                "current": 0.0,
                "breached": False
            },
            RiskLimitType.POSITION_SIZE: {
                "limit": self.config.max_position_size,
                "current": 0.0,
                "breached": False
            },
            RiskLimitType.SECTOR_EXPOSURE: {
                "limit": self.config.max_sector_exposure,
                "current": {},
                "breached": False
            },
            RiskLimitType.CORRELATION: {
                "limit": self.config.max_correlation,
                "current": 0.0,
                "breached": False
            }
        }
    
    async def check_all_limits(
        self,
        portfolio_value: float,
        positions: Dict[str, Dict],
        daily_pnl: float
    ) -> Dict[str, any]:
        """
        Check all risk limits and return enforcement actions.
        
        Args:
            portfolio_value: Current portfolio value
            positions: Current positions
            daily_pnl: Daily P&L
            
        Returns:
            Dictionary with limit check results and actions
        """
        try:
            results = {
                "breaches": [],
                "warnings": [],
                "actions": [],
                "circuit_breaker_level": None
            }
            
            # Check daily loss limits
            daily_loss_check = await self._check_daily_loss_limit(portfolio_value, daily_pnl)
            if daily_loss_check["breached"]:
                results["breaches"].append(daily_loss_check)
                results["actions"].extend(daily_loss_check.get("actions", []))
            
            # Check position size limits
            position_size_check = await self._check_position_size_limits(portfolio_value, positions)
            if position_size_check["breached"]:
                results["breaches"].append(position_size_check)
                results["actions"].extend(position_size_check.get("actions", []))
            
            # Check sector exposure limits
            sector_check = await self._check_sector_exposure_limits(portfolio_value, positions)
            if sector_check["breached"]:
                results["breaches"].append(sector_check)
                results["actions"].extend(sector_check.get("actions", []))
            
            # Check correlation limits
            correlation_check = await self._check_correlation_limits(positions)
            if correlation_check["breached"]:
                results["breaches"].append(correlation_check)
                results["actions"].extend(correlation_check.get("actions", []))
            
            # Check concentration limits
            concentration_check = await self._check_concentration_limits(portfolio_value, positions)
            if concentration_check["breached"]:
                results["breaches"].append(concentration_check)
                results["actions"].extend(concentration_check.get("actions", []))
            
            # Determine circuit breaker level
            results["circuit_breaker_level"] = self._determine_circuit_breaker_level(daily_pnl, portfolio_value)
            
            # Log results
            if results["breaches"]:
                logger.warning(f"Risk limit breaches detected: {len(results['breaches'])}")
                for breach in results["breaches"]:
                    logger.warning(f"Breach: {breach['type']} - {breach['message']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return {"breaches": [], "warnings": [], "actions": [], "circuit_breaker_level": None}
    
    async def _check_daily_loss_limit(self, portfolio_value: float, daily_pnl: float) -> Dict:
        """Check daily loss limits."""
        try:
            if daily_pnl >= 0:
                return {"breached": False, "type": RiskLimitType.DAILY_LOSS}
            
            daily_loss_pct = abs(daily_pnl) / portfolio_value
            limit = self.active_limits[RiskLimitType.DAILY_LOSS]["limit"]
            
            if daily_loss_pct >= limit:
                actions = []
                
                # Determine severity and actions
                if daily_loss_pct >= self.circuit_breakers[CircuitBreakerLevel.LEVEL_3]:
                    actions.append("STOP_ALL_TRADING")
                    actions.append("CLOSE_ALL_POSITIONS")
                elif daily_loss_pct >= self.circuit_breakers[CircuitBreakerLevel.LEVEL_2]:
                    actions.append("REDUCE_ALL_POSITIONS_50")
                    actions.append("STOP_NEW_POSITIONS")
                else:
                    actions.append("REDUCE_POSITION_SIZES")
                    actions.append("INCREASE_MONITORING")
                
                return {
                    "breached": True,
                    "type": RiskLimitType.DAILY_LOSS,
                    "current": daily_loss_pct,
                    "limit": limit,
                    "message": f"Daily loss limit breached: {daily_loss_pct:.2%} > {limit:.2%}",
                    "actions": actions
                }
            
            return {"breached": False, "type": RiskLimitType.DAILY_LOSS}
            
        except Exception as e:
            logger.error(f"Error checking daily loss limit: {e}")
            return {"breached": False, "type": RiskLimitType.DAILY_LOSS}
    
    async def _check_position_size_limits(self, portfolio_value: float, positions: Dict[str, Dict]) -> Dict:
        """Check individual position size limits."""
        try:
            limit = self.active_limits[RiskLimitType.POSITION_SIZE]["limit"]
            max_position_value = portfolio_value * limit
            
            oversized_positions = []
            
            for symbol, position in positions.items():
                position_value = position["quantity"] * position["current_price"]
                if position_value > max_position_value:
                    oversized_positions.append({
                        "symbol": symbol,
                        "value": position_value,
                        "percentage": position_value / portfolio_value,
                        "excess": position_value - max_position_value
                    })
            
            if oversized_positions:
                actions = []
                for pos in oversized_positions:
                    actions.append(f"REDUCE_POSITION_{pos['symbol']}")
                
                return {
                    "breached": True,
                    "type": RiskLimitType.POSITION_SIZE,
                    "oversized_positions": oversized_positions,
                    "limit": limit,
                    "message": f"Position size limit breached for {len(oversized_positions)} positions",
                    "actions": actions
                }
            
            return {"breached": False, "type": RiskLimitType.POSITION_SIZE}
            
        except Exception as e:
            logger.error(f"Error checking position size limits: {e}")
            return {"breached": False, "type": RiskLimitType.POSITION_SIZE}
    
    async def _check_sector_exposure_limits(self, portfolio_value: float, positions: Dict[str, Dict]) -> Dict:
        """Check sector exposure limits."""
        try:
            limit = self.active_limits[RiskLimitType.SECTOR_EXPOSURE]["limit"]
            sector_exposures = {}
            
            async with get_postgres_session() as session:
                for symbol, position in positions.items():
                    # Get sector for symbol
                    stmt = select(Symbol.sector).where(Symbol.symbol == symbol)
                    result = await session.execute(stmt)
                    sector = result.scalar_one_or_none()
                    
                    if sector:
                        position_value = position["quantity"] * position["current_price"]
                        if sector not in sector_exposures:
                            sector_exposures[sector] = 0.0
                        sector_exposures[sector] += position_value
            
            # Check for breaches
            breached_sectors = []
            for sector, exposure in sector_exposures.items():
                exposure_pct = exposure / portfolio_value
                if exposure_pct > limit:
                    breached_sectors.append({
                        "sector": sector,
                        "exposure": exposure,
                        "percentage": exposure_pct,
                        "excess": exposure - (portfolio_value * limit)
                    })
            
            if breached_sectors:
                actions = []
                for sector_info in breached_sectors:
                    actions.append(f"REDUCE_SECTOR_EXPOSURE_{sector_info['sector']}")
                
                return {
                    "breached": True,
                    "type": RiskLimitType.SECTOR_EXPOSURE,
                    "breached_sectors": breached_sectors,
                    "limit": limit,
                    "message": f"Sector exposure limit breached for {len(breached_sectors)} sectors",
                    "actions": actions
                }
            
            return {"breached": False, "type": RiskLimitType.SECTOR_EXPOSURE}
            
        except Exception as e:
            logger.error(f"Error checking sector exposure limits: {e}")
            return {"breached": False, "type": RiskLimitType.SECTOR_EXPOSURE}
    
    async def _check_correlation_limits(self, positions: Dict[str, Dict]) -> Dict:
        """Check correlation limits between positions."""
        try:
            if len(positions) < 2:
                return {"breached": False, "type": RiskLimitType.CORRELATION}
            
            limit = self.active_limits[RiskLimitType.CORRELATION]["limit"]
            high_correlations = []
            
            # Get correlation data (simplified - in production would use actual correlation calculation)
            symbols = list(positions.keys())
            
            # For now, implement a simple sector-based correlation check
            async with get_postgres_session() as session:
                sector_groups = {}
                for symbol in symbols:
                    stmt = select(Symbol.sector).where(Symbol.symbol == symbol)
                    result = await session.execute(stmt)
                    sector = result.scalar_one_or_none()
                    
                    if sector:
                        if sector not in sector_groups:
                            sector_groups[sector] = []
                        sector_groups[sector].append(symbol)
                
                # Check for high concentration in same sector (proxy for correlation)
                for sector, sector_symbols in sector_groups.items():
                    if len(sector_symbols) > 3:  # More than 3 positions in same sector
                        total_sector_value = sum(
                            positions[symbol]["quantity"] * positions[symbol]["current_price"]
                            for symbol in sector_symbols
                        )
                        total_portfolio_value = sum(
                            pos["quantity"] * pos["current_price"] 
                            for pos in positions.values()
                        )
                        
                        sector_concentration = total_sector_value / total_portfolio_value
                        if sector_concentration > 0.5:  # 50% in one sector indicates high correlation risk
                            high_correlations.append({
                                "sector": sector,
                                "symbols": sector_symbols,
                                "concentration": sector_concentration
                            })
            
            if high_correlations:
                actions = []
                for corr_info in high_correlations:
                    actions.append(f"REDUCE_CORRELATION_RISK_{corr_info['sector']}")
                
                return {
                    "breached": True,
                    "type": RiskLimitType.CORRELATION,
                    "high_correlations": high_correlations,
                    "limit": limit,
                    "message": f"High correlation risk detected in {len(high_correlations)} sectors",
                    "actions": actions
                }
            
            return {"breached": False, "type": RiskLimitType.CORRELATION}
            
        except Exception as e:
            logger.error(f"Error checking correlation limits: {e}")
            return {"breached": False, "type": RiskLimitType.CORRELATION}
    
    async def _check_concentration_limits(self, portfolio_value: float, positions: Dict[str, Dict]) -> Dict:
        """Check portfolio concentration limits."""
        try:
            if not positions:
                return {"breached": False, "type": RiskLimitType.CONCENTRATION}
            
            # Calculate concentration metrics
            position_values = [
                pos["quantity"] * pos["current_price"] 
                for pos in positions.values()
            ]
            
            # Top 3 positions concentration
            sorted_values = sorted(position_values, reverse=True)
            top_3_concentration = sum(sorted_values[:3]) / portfolio_value if len(sorted_values) >= 3 else 1.0
            
            # Single position concentration
            max_position_concentration = max(position_values) / portfolio_value
            
            # Check limits
            breaches = []
            
            if top_3_concentration > 0.6:  # Top 3 positions > 60%
                breaches.append({
                    "type": "top_3_concentration",
                    "value": top_3_concentration,
                    "limit": 0.6
                })
            
            if max_position_concentration > 0.25:  # Single position > 25%
                breaches.append({
                    "type": "single_position_concentration",
                    "value": max_position_concentration,
                    "limit": 0.25
                })
            
            if breaches:
                actions = ["DIVERSIFY_PORTFOLIO", "REDUCE_LARGEST_POSITIONS"]
                
                return {
                    "breached": True,
                    "type": RiskLimitType.CONCENTRATION,
                    "breaches": breaches,
                    "message": f"Portfolio concentration limits breached: {len(breaches)} violations",
                    "actions": actions
                }
            
            return {"breached": False, "type": RiskLimitType.CONCENTRATION}
            
        except Exception as e:
            logger.error(f"Error checking concentration limits: {e}")
            return {"breached": False, "type": RiskLimitType.CONCENTRATION}
    
    def _determine_circuit_breaker_level(self, daily_pnl: float, portfolio_value: float) -> Optional[CircuitBreakerLevel]:
        """Determine circuit breaker level based on daily P&L."""
        try:
            if daily_pnl >= 0:
                return None
            
            daily_loss_pct = abs(daily_pnl) / portfolio_value
            
            if daily_loss_pct >= self.circuit_breakers[CircuitBreakerLevel.LEVEL_3]:
                return CircuitBreakerLevel.LEVEL_3
            elif daily_loss_pct >= self.circuit_breakers[CircuitBreakerLevel.LEVEL_2]:
                return CircuitBreakerLevel.LEVEL_2
            elif daily_loss_pct >= self.circuit_breakers[CircuitBreakerLevel.LEVEL_1]:
                return CircuitBreakerLevel.LEVEL_1
            
            return None
            
        except Exception as e:
            logger.error(f"Error determining circuit breaker level: {e}")
            return None
    
    async def enforce_actions(self, actions: List[str], positions: Dict[str, Dict]) -> Dict[str, any]:
        """
        Enforce risk management actions.
        
        Args:
            actions: List of actions to enforce
            positions: Current positions
            
        Returns:
            Dictionary with enforcement results
        """
        try:
            results = {
                "executed_actions": [],
                "failed_actions": [],
                "position_changes": []
            }
            
            for action in actions:
                try:
                    if action == "STOP_ALL_TRADING":
                        # Set trading disabled flag
                        results["executed_actions"].append(action)
                        logger.critical("CIRCUIT BREAKER: All trading stopped")
                    
                    elif action == "CLOSE_ALL_POSITIONS":
                        # Mark all positions for closure
                        for symbol in positions.keys():
                            results["position_changes"].append({
                                "symbol": symbol,
                                "action": "CLOSE",
                                "percentage": 100
                            })
                        results["executed_actions"].append(action)
                        logger.critical("CIRCUIT BREAKER: All positions marked for closure")
                    
                    elif action.startswith("REDUCE_POSITION_"):
                        symbol = action.split("_")[-1]
                        if symbol in positions:
                            results["position_changes"].append({
                                "symbol": symbol,
                                "action": "REDUCE",
                                "percentage": 50
                            })
                            results["executed_actions"].append(action)
                    
                    elif action == "REDUCE_ALL_POSITIONS_50":
                        for symbol in positions.keys():
                            results["position_changes"].append({
                                "symbol": symbol,
                                "action": "REDUCE",
                                "percentage": 50
                            })
                        results["executed_actions"].append(action)
                        logger.warning("Risk limit breach: Reducing all positions by 50%")
                    
                    else:
                        results["executed_actions"].append(action)
                        logger.info(f"Risk action executed: {action}")
                
                except Exception as action_error:
                    results["failed_actions"].append({
                        "action": action,
                        "error": str(action_error)
                    })
                    logger.error(f"Failed to execute risk action {action}: {action_error}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error enforcing risk actions: {e}")
            return {"executed_actions": [], "failed_actions": [], "position_changes": []}
    
    async def log_breach(self, breach_info: Dict):
        """Log risk limit breach for historical tracking."""
        try:
            breach_record = {
                "timestamp": datetime.utcnow(),
                "type": breach_info["type"],
                "message": breach_info["message"],
                "current_value": breach_info.get("current"),
                "limit_value": breach_info.get("limit"),
                "actions_taken": breach_info.get("actions", [])
            }
            
            self.breach_history.append(breach_record)
            
            # Keep only last 100 breaches in memory
            if len(self.breach_history) > 100:
                self.breach_history = self.breach_history[-100:]
            
            logger.warning(f"Risk breach logged: {breach_record}")
            
        except Exception as e:
            logger.error(f"Error logging breach: {e}")
