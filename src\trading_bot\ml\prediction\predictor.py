"""Real-time prediction pipeline for trading models."""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import time
import queue
import threading
from concurrent.futures import ThreadPoolExecutor
import warnings
import pickle
import joblib

from ..models.ensemble import EnsembleCoordinator, EnsembleConfig
from ..features.feature_engineering import FeatureEngineering

logger = logging.getLogger(__name__)


@dataclass
class PredictionConfig:
    """Configuration for prediction pipeline."""
    # Performance requirements
    max_inference_time_ms: float = 50.0
    batch_size: int = 1
    
    # Model management
    model_reload_interval: int = 3600  # seconds
    auto_reload_models: bool = True
    
    # Feature engineering
    feature_cache_size: int = 1000
    use_feature_cache: bool = True
    
    # Prediction settings
    confidence_threshold: float = 0.7
    min_data_points: int = 200
    
    # Real-time settings
    prediction_frequency: str = '1min'  # pandas frequency string
    max_queue_size: int = 1000
    
    # Monitoring
    track_performance: bool = True
    log_predictions: bool = False
    
    # Risk management
    max_position_change: float = 0.1  # 10% max position change per prediction
    enable_circuit_breaker: bool = True
    circuit_breaker_threshold: int = 10  # consecutive failed predictions


@dataclass
class PredictionResult:
    """Container for prediction results."""
    timestamp: datetime
    symbol: str
    signal: str
    confidence: float
    probabilities: Optional[Dict[str, float]] = None
    
    # Model details
    model_predictions: Optional[Dict[str, Any]] = None
    ensemble_agreement: Optional[float] = None
    
    # Risk metrics
    risk_score: Optional[float] = None
    position_recommendation: Optional[float] = None
    
    # Performance metrics
    inference_time_ms: float = 0.0
    feature_count: int = 0
    
    # Additional context
    market_conditions: Optional[Dict[str, Any]] = None
    alerts: Optional[List[str]] = None


class ModelCache:
    """Cache for loaded models with automatic reloading."""
    
    def __init__(self, config: PredictionConfig):
        self.config = config
        self.models = {}
        self.model_timestamps = {}
        self.ensemble = None
        self.feature_engineer = None
        
    def load_model(self, model_name: str, model_path: str):
        """Load a model into cache."""
        try:
            if model_name == 'ensemble':
                # Load ensemble
                self.ensemble = EnsembleCoordinator(EnsembleConfig())
                self.ensemble.load_ensemble_state(model_path)
                logger.info(f"Loaded ensemble from {model_path}")
            else:
                # Load individual model
                model = joblib.load(model_path)
                self.models[model_name] = model
                self.model_timestamps[model_name] = time.time()
                logger.info(f"Loaded model {model_name} from {model_path}")
                
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {e}")
    
    def get_model(self, model_name: str):
        """Get model from cache."""
        if model_name == 'ensemble':
            return self.ensemble
        return self.models.get(model_name)
    
    def reload_models_if_needed(self):
        """Reload models if auto-reload is enabled and interval passed."""
        if not self.config.auto_reload_models:
            return
        
        current_time = time.time()
        for model_name, load_time in self.model_timestamps.items():
            if current_time - load_time > self.config.model_reload_interval:
                logger.info(f"Auto-reloading model {model_name}")
                # This would require storing the original path
                # For now, just log that reload is needed
                logger.warning(f"Model {model_name} needs reload (not implemented)")
    
    def get_feature_engineer(self) -> FeatureEngineering:
        """Get or create feature engineer."""
        if self.feature_engineer is None:
            self.feature_engineer = FeatureEngineering()
        return self.feature_engineer


class FeatureCache:
    """Cache for computed features."""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = {}
        self.access_times = {}
        
    def get(self, key: str) -> Optional[pd.DataFrame]:
        """Get features from cache."""
        if key in self.cache:
            self.access_times[key] = time.time()
            return self.cache[key].copy()
        return None
    
    def put(self, key: str, features: pd.DataFrame):
        """Put features in cache."""
        # Remove oldest if cache is full
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.access_times, key=self.access_times.get)
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
        
        self.cache[key] = features.copy()
        self.access_times[key] = time.time()
    
    def clear(self):
        """Clear cache."""
        self.cache.clear()
        self.access_times.clear()


class CircuitBreaker:
    """Circuit breaker for prediction pipeline."""
    
    def __init__(self, threshold: int = 10):
        self.threshold = threshold
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        
    def call(self, func: Callable, *args, **kwargs):
        """Call function with circuit breaker protection."""
        if self.state == 'OPEN':
            # Check if we should try again
            if time.time() - self.last_failure_time > 60:  # 1 minute timeout
                self.state = 'HALF_OPEN'
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
                
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.threshold:
                self.state = 'OPEN'
                
            raise e


class PredictionPipeline:
    """Main prediction pipeline for real-time trading."""
    
    def __init__(self, config: PredictionConfig):
        self.config = config
        self.model_cache = ModelCache(config)
        self.feature_cache = FeatureCache(config.feature_cache_size) if config.use_feature_cache else None
        self.circuit_breaker = CircuitBreaker(config.circuit_breaker_threshold) if config.enable_circuit_breaker else None
        
        # Performance tracking
        self.prediction_times = []
        self.prediction_count = 0
        self.failure_count = 0
        
        # Data queues for real-time processing
        self.data_queue = queue.Queue(maxsize=config.max_queue_size)
        self.prediction_queue = queue.Queue(maxsize=config.max_queue_size)
        
        # Threading
        self.prediction_thread = None
        self.is_running = False
        
        logger.info("Initialized PredictionPipeline")
    
    def load_models(self, model_paths: Dict[str, str]):
        """Load models into cache."""
        logger.info(f"Loading {len(model_paths)} models...")
        
        for model_name, model_path in model_paths.items():
            self.model_cache.load_model(model_name, model_path)
        
        logger.info("Model loading completed")
    
    async def predict(self, 
                     data: pd.DataFrame,
                     symbol: str,
                     timestamp: Optional[datetime] = None) -> PredictionResult:
        """Make a single prediction."""
        
        start_time = time.time()
        timestamp = timestamp or datetime.now()
        
        try:
            # Validate input data
            if len(data) < self.config.min_data_points:
                raise ValueError(f"Insufficient data points: {len(data)} < {self.config.min_data_points}")
            
            # Circuit breaker protection
            if self.circuit_breaker:
                result = self.circuit_breaker.call(self._make_prediction_internal, data, symbol, timestamp)
            else:
                result = await self._make_prediction_internal(data, symbol, timestamp)
            
            # Track performance
            inference_time = (time.time() - start_time) * 1000
            result.inference_time_ms = inference_time
            
            self.prediction_times.append(inference_time)
            self.prediction_count += 1
            
            # Check performance requirement
            if inference_time > self.config.max_inference_time_ms:
                logger.warning(f"Prediction time {inference_time:.2f}ms exceeds limit {self.config.max_inference_time_ms}ms")
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            logger.error(f"Prediction failed: {e}")
            
            # Return default result
            return PredictionResult(
                timestamp=timestamp,
                symbol=symbol,
                signal='HOLD',
                confidence=0.0,
                inference_time_ms=(time.time() - start_time) * 1000,
                alerts=[f"Prediction failed: {str(e)}"]
            )
    
    async def _make_prediction_internal(self, 
                                      data: pd.DataFrame,
                                      symbol: str,
                                      timestamp: datetime) -> PredictionResult:
        """Internal prediction logic."""
        
        # Feature engineering
        features = await self._engineer_features(data, symbol)
        
        # Get predictions from models
        predictions = await self._get_model_predictions(features)
        
        # Ensemble prediction
        ensemble_result = await self._get_ensemble_prediction(features, predictions)
        
        # Risk assessment
        risk_metrics = await self._assess_risk(ensemble_result, data)
        
        # Create result
        result = PredictionResult(
            timestamp=timestamp,
            symbol=symbol,
            signal=ensemble_result.get('signal', 'HOLD'),
            confidence=ensemble_result.get('confidence', 0.0),
            probabilities=ensemble_result.get('probabilities'),
            model_predictions=predictions,
            ensemble_agreement=ensemble_result.get('agreement'),
            risk_score=risk_metrics.get('risk_score'),
            position_recommendation=risk_metrics.get('position_recommendation'),
            feature_count=len(features.columns) if features is not None else 0,
            market_conditions=self._analyze_market_conditions(data),
            alerts=self._generate_alerts(ensemble_result, risk_metrics)
        )
        
        return result
    
    async def _engineer_features(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Engineer features with caching."""
        
        # Create cache key
        cache_key = f"{symbol}_{hash(str(data.tail(10).values.tobytes()))}" if self.feature_cache else None
        
        # Check cache
        if cache_key and self.feature_cache:
            cached_features = self.feature_cache.get(cache_key)
            if cached_features is not None:
                return cached_features
        
        # Engineer features
        feature_engineer = self.model_cache.get_feature_engineer()
        features = feature_engineer.engineer_features(data)
        
        # Cache result
        if cache_key and self.feature_cache:
            self.feature_cache.put(cache_key, features)
        
        return features
    
    async def _get_model_predictions(self, features: pd.DataFrame) -> Dict[str, Any]:
        """Get predictions from individual models."""
        
        predictions = {}
        
        # Get all available models
        for model_name in self.model_cache.models:
            try:
                model = self.model_cache.get_model(model_name)
                if model and hasattr(model, 'predict'):
                    # Use last row for prediction
                    X = features.iloc[[-1]]
                    pred_result = model.predict(X)
                    predictions[model_name] = pred_result
                    
            except Exception as e:
                logger.warning(f"Error getting prediction from {model_name}: {e}")
                continue
        
        return predictions
    
    async def _get_ensemble_prediction(self, 
                                     features: pd.DataFrame,
                                     individual_predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Get ensemble prediction."""
        
        ensemble = self.model_cache.get_model('ensemble')
        
        if ensemble and individual_predictions:
            try:
                # Use last row for prediction
                X = features.iloc[[-1]]
                ensemble_result = ensemble.predict(X)
                return ensemble_result
                
            except Exception as e:
                logger.warning(f"Error getting ensemble prediction: {e}")
        
        # Fallback: simple voting
        if individual_predictions:
            signals = []
            confidences = []
            
            for pred in individual_predictions.values():
                if isinstance(pred, dict):
                    signals.append(pred.get('signal', 'HOLD'))
                    confidences.append(pred.get('confidence', 0.5))
                else:
                    signals.append('HOLD')
                    confidences.append(0.5)
            
            # Simple majority voting
            from collections import Counter
            signal_counts = Counter(signals)
            winning_signal = signal_counts.most_common(1)[0][0]
            avg_confidence = np.mean(confidences)
            
            return {
                'signal': winning_signal,
                'confidence': avg_confidence,
                'agreement': signal_counts[winning_signal] / len(signals),
                'individual_predictions': individual_predictions
            }
        
        # Default result
        return {
            'signal': 'HOLD',
            'confidence': 0.0,
            'agreement': 0.0
        }
    
    async def _assess_risk(self, 
                          prediction: Dict[str, Any],
                          data: pd.DataFrame) -> Dict[str, Any]:
        """Assess risk for the prediction."""
        
        risk_metrics = {}
        
        try:
            # Basic risk score based on confidence and volatility
            confidence = prediction.get('confidence', 0)
            signal = prediction.get('signal', 'HOLD')
            
            # Calculate recent volatility
            if 'close' in data.columns and len(data) > 20:
                returns = data['close'].pct_change().tail(20)
                volatility = returns.std()
                
                # Risk score: lower confidence and higher volatility = higher risk
                risk_score = (1 - confidence) * volatility * 10
                risk_metrics['risk_score'] = min(risk_score, 1.0)
                
                # Position recommendation based on confidence and risk
                if signal == 'BUY':
                    position_rec = confidence * (1 - risk_score) * self.config.max_position_change
                elif signal == 'SELL':
                    position_rec = -confidence * (1 - risk_score) * self.config.max_position_change
                else:  # HOLD
                    position_rec = 0.0
                
                risk_metrics['position_recommendation'] = position_rec
            else:
                risk_metrics['risk_score'] = 0.5
                risk_metrics['position_recommendation'] = 0.0
                
        except Exception as e:
            logger.warning(f"Error assessing risk: {e}")
            risk_metrics['risk_score'] = 1.0  # High risk on error
            risk_metrics['position_recommendation'] = 0.0
        
        return risk_metrics
    
    def _analyze_market_conditions(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze current market conditions."""
        
        conditions = {}
        
        try:
            if 'close' in data.columns and len(data) >= 20:
                close = data['close']
                volume = data.get('volume', pd.Series())
                
                # Price trend
                sma_20 = close.tail(20).mean()
                current_price = close.iloc[-1]
                conditions['trend'] = 'UP' if current_price > sma_20 else 'DOWN'
                
                # Volatility regime
                returns = close.pct_change().tail(20)
                volatility = returns.std()
                vol_percentile = returns.rolling(20).std().rank(pct=True).iloc[-1]
                
                if vol_percentile > 0.8:
                    conditions['volatility_regime'] = 'HIGH'
                elif vol_percentile < 0.2:
                    conditions['volatility_regime'] = 'LOW'
                else:
                    conditions['volatility_regime'] = 'NORMAL'
                
                # Volume condition
                if not volume.empty and len(volume) >= 10:
                    avg_volume = volume.tail(10).mean()
                    current_volume = volume.iloc[-1]
                    
                    if current_volume > avg_volume * 1.5:
                        conditions['volume'] = 'HIGH'
                    elif current_volume < avg_volume * 0.5:
                        conditions['volume'] = 'LOW'
                    else:
                        conditions['volume'] = 'NORMAL'
                        
        except Exception as e:
            logger.warning(f"Error analyzing market conditions: {e}")
        
        return conditions
    
    def _generate_alerts(self, 
                        prediction: Dict[str, Any],
                        risk_metrics: Dict[str, Any]) -> List[str]:
        """Generate alerts based on prediction and risk."""
        
        alerts = []
        
        try:
            # High risk alert
            if risk_metrics.get('risk_score', 0) > 0.8:
                alerts.append("HIGH_RISK: Risk score above 80%")
            
            # Low confidence alert
            if prediction.get('confidence', 0) < self.config.confidence_threshold:
                alerts.append(f"LOW_CONFIDENCE: {prediction.get('confidence', 0):.2f} < {self.config.confidence_threshold}")
            
            # Low agreement alert
            if prediction.get('agreement', 0) < 0.6:
                alerts.append(f"LOW_AGREEMENT: Models disagree ({prediction.get('agreement', 0):.2f})")
            
            # Signal strength alert
            signal = prediction.get('signal', 'HOLD')
            confidence = prediction.get('confidence', 0)
            
            if signal != 'HOLD' and confidence > 0.8:
                alerts.append(f"STRONG_SIGNAL: {signal} with {confidence:.2f} confidence")
                
        except Exception as e:
            logger.warning(f"Error generating alerts: {e}")
        
        return alerts
    
    def start_real_time_predictions(self, prediction_callback: Optional[Callable] = None):
        """Start real-time prediction processing."""
        
        if self.is_running:
            logger.warning("Prediction pipeline already running")
            return
        
        self.is_running = True
        
        def prediction_worker():
            """Worker thread for processing predictions."""
            while self.is_running:
                try:
                    # Get data from queue (with timeout)
                    data_item = self.data_queue.get(timeout=1)
                    
                    if data_item is None:  # Shutdown signal
                        break
                    
                    # Unpack data
                    data, symbol, timestamp = data_item
                    
                    # Make prediction
                    prediction = asyncio.run(self.predict(data, symbol, timestamp))
                    
                    # Put result in output queue
                    if not self.prediction_queue.full():
                        self.prediction_queue.put(prediction)
                    
                    # Call callback if provided
                    if prediction_callback:
                        try:
                            prediction_callback(prediction)
                        except Exception as e:
                            logger.error(f"Error in prediction callback: {e}")
                    
                    # Log prediction if enabled
                    if self.config.log_predictions:
                        logger.info(f"Prediction: {symbol} {prediction.signal} ({prediction.confidence:.2f})")
                        
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Error in prediction worker: {e}")
                    continue
        
        # Start worker thread
        self.prediction_thread = threading.Thread(target=prediction_worker, daemon=True)
        self.prediction_thread.start()
        
        logger.info("Real-time prediction pipeline started")
    
    def stop_real_time_predictions(self):
        """Stop real-time prediction processing."""
        
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Send shutdown signal
        if not self.data_queue.full():
            self.data_queue.put(None)
        
        # Wait for thread to finish
        if self.prediction_thread:
            self.prediction_thread.join(timeout=5)
        
        logger.info("Real-time prediction pipeline stopped")
    
    def add_data(self, data: pd.DataFrame, symbol: str, timestamp: Optional[datetime] = None):
        """Add data to the prediction queue."""
        
        if not self.is_running:
            logger.warning("Prediction pipeline not running")
            return
        
        timestamp = timestamp or datetime.now()
        
        try:
            self.data_queue.put((data, symbol, timestamp), timeout=1)
        except queue.Full:
            logger.warning("Data queue full, dropping data")
    
    def get_prediction_result(self, timeout: float = 1.0) -> Optional[PredictionResult]:
        """Get prediction result from queue."""
        
        try:
            return self.prediction_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get pipeline performance metrics."""
        
        metrics = {
            'prediction_count': self.prediction_count,
            'failure_count': self.failure_count,
            'success_rate': (self.prediction_count - self.failure_count) / max(self.prediction_count, 1),
            'is_running': self.is_running
        }
        
        if self.prediction_times:
            metrics.update({
                'avg_inference_time_ms': np.mean(self.prediction_times),
                'median_inference_time_ms': np.median(self.prediction_times),
                'max_inference_time_ms': np.max(self.prediction_times),
                'p95_inference_time_ms': np.percentile(self.prediction_times, 95),
                'performance_requirement_met': np.percentile(self.prediction_times, 95) <= self.config.max_inference_time_ms
            })
        
        # Circuit breaker status
        if self.circuit_breaker:
            metrics['circuit_breaker_state'] = self.circuit_breaker.state
            metrics['circuit_breaker_failures'] = self.circuit_breaker.failure_count
        
        return metrics
    
    def reset_performance_metrics(self):
        """Reset performance tracking."""
        self.prediction_times.clear()
        self.prediction_count = 0
        self.failure_count = 0
        
        if self.circuit_breaker:
            self.circuit_breaker.failure_count = 0
            self.circuit_breaker.state = 'CLOSED'
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on prediction pipeline."""
        
        health = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'checks': {}
        }
        
        try:
            # Check if models are loaded
            loaded_models = len(self.model_cache.models)
            health['checks']['models_loaded'] = {
                'status': 'pass' if loaded_models > 0 else 'fail',
                'count': loaded_models
            }
            
            # Check circuit breaker
            if self.circuit_breaker:
                health['checks']['circuit_breaker'] = {
                    'status': 'pass' if self.circuit_breaker.state != 'OPEN' else 'fail',
                    'state': self.circuit_breaker.state
                }
            
            # Check performance
            metrics = self.get_performance_metrics()
            health['checks']['performance'] = {
                'status': 'pass' if metrics.get('performance_requirement_met', True) else 'warn',
                'avg_inference_time_ms': metrics.get('avg_inference_time_ms', 0)
            }
            
            # Check queue status
            health['checks']['queues'] = {
                'status': 'pass',
                'data_queue_size': self.data_queue.qsize(),
                'prediction_queue_size': self.prediction_queue.qsize()
            }
            
            # Overall status
            failed_checks = [check for check in health['checks'].values() if check['status'] == 'fail']
            if failed_checks:
                health['status'] = 'unhealthy'
            elif any(check['status'] == 'warn' for check in health['checks'].values()):
                health['status'] = 'degraded'
                
        except Exception as e:
            health['status'] = 'unhealthy'
            health['error'] = str(e)
        
        return health


class Predictor:
    """Main predictor class - simplified interface."""
    
    def __init__(self, config: Optional[PredictionConfig] = None):
        self.config = config or PredictionConfig()
        self.pipeline = PredictionPipeline(self.config)
        
    def load_models(self, model_paths: Dict[str, str]):
        """Load models."""
        self.pipeline.load_models(model_paths)
    
    async def predict(self, data: pd.DataFrame, symbol: str) -> PredictionResult:
        """Make prediction."""
        return await self.pipeline.predict(data, symbol)
    
    def start_real_time(self, callback: Optional[Callable] = None):
        """Start real-time predictions."""
        self.pipeline.start_real_time_predictions(callback)
    
    def stop_real_time(self):
        """Stop real-time predictions."""
        self.pipeline.stop_real_time_predictions()
    
    def get_health(self) -> Dict[str, Any]:
        """Get health status."""
        return self.pipeline.health_check()