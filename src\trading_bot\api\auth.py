"""Authentication manager for Webull API."""

import asyncio
import hashlib
import time
from typing import Dict, Optional, Tuple

from ..core.config import settings
from ..models.enums import APIEndpointType, AccountType, RegionId
from ..utils.exceptions import AuthenticationError
from ..utils.logger import get_structured_logger
from .base import BaseAPIClient

logger = get_structured_logger(__name__)


class AuthenticationManager(BaseAPIClient):
    """Manages authentication with Webull API."""
    
    def __init__(self):
        super().__init__()
        self.username: Optional[str] = settings.webull.username
        self.password: Optional[str] = settings.webull.password
        self.account_id: Optional[str] = None
        self.is_authenticated = False
        self.token_expires_at: Optional[float] = None
        self.mfa_required = False
        
    async def login(
        self,
        username: Optional[str] = None,
        password: Optional[str] = None,
        save_tokens: bool = True,
    ) -> bool:
        """
        Login to Webull with username and password.
        
        Args:
            username: <PERSON>ull username (uses config if None)
            password: Webull password (uses config if None)
            save_tokens: Whether to save tokens to config
            
        Returns:
            True if login successful, False otherwise
            
        Raises:
            AuthenticationError: If login fails
        """
        username = username or self.username
        password = password or self.password
        
        if not username or not password:
            raise AuthenticationError(
                "Username and password are required",
                auth_type="credentials",
                requires_reauth=True,
            )
        
        try:
            logger.info("Starting login process", extra={"username": username})
            
            # Step 1: Login challenge
            challenge_response = await self._login_challenge(username)
            
            # Step 2: Submit password
            auth_response = await self._submit_password(username, password)
            
            # Step 3: Handle MFA if required
            if auth_response.get("mfaRequired"):
                self.mfa_required = True
                logger.warning("MFA required for login")
                raise AuthenticationError(
                    "MFA required - call verify_mfa() with code",
                    auth_type="mfa",
                    requires_reauth=False,
                )
            
            # Extract tokens
            self.access_token = auth_response.get("accessToken")
            self.refresh_token = auth_response.get("refreshToken")
            self.trade_token = auth_response.get("tradeToken")
            
            if not self.access_token:
                raise AuthenticationError(
                    "No access token received",
                    auth_type="token",
                    requires_reauth=True,
                )
            
            # Set token expiration (typically 24 hours)
            expires_in = auth_response.get("expiresIn", 86400)  # Default 24 hours
            self.token_expires_at = time.time() + expires_in
            
            # Get account information
            await self._get_account_info()
            
            self.is_authenticated = True
            
            logger.info(
                "Login successful",
                extra={
                    "username": username,
                    "account_id": self.account_id,
                    "expires_at": self.token_expires_at,
                }
            )
            
            # Save tokens if requested
            if save_tokens:
                await self._save_tokens()
            
            return True
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Login failed: {e}", extra={"username": username})
            raise AuthenticationError(
                f"Login failed: {str(e)}",
                auth_type="login",
                requires_reauth=True,
            )
    
    async def _login_challenge(self, username: str) -> Dict:
        """Send login challenge request."""
        data = {
            "account": username,
            "accountType": AccountType.EMAIL.value,
            "deviceId": self.device_id,
            "grade": 1,
            "regionId": RegionId.US.value,
        }
        
        response = await self._make_request(
            "POST",
            APIEndpointType.PASSPORT,
            "login_challenge",
            data=data,
            auth_required=False,
            use_cache=False,
        )
        
        if not response.get("success"):
            raise AuthenticationError(
                f"Login challenge failed: {response.get('msg', 'Unknown error')}",
                auth_type="challenge",
            )
        
        return response
    
    async def _submit_password(self, username: str, password: str) -> Dict:
        """Submit password for authentication."""
        password_hash = hashlib.md5(password.encode()).hexdigest()
        
        data = {
            "account": username,
            "accountType": AccountType.EMAIL.value,
            "deviceId": self.device_id,
            "pwd": password_hash,
            "regionId": RegionId.US.value,
        }
        
        response = await self._make_request(
            "POST",
            APIEndpointType.PASSPORT,
            "login_submit",
            data=data,
            auth_required=False,
            use_cache=False,
        )
        
        if not response.get("success"):
            error_msg = response.get("msg", "Unknown error")
            if "password" in error_msg.lower():
                raise AuthenticationError(
                    "Invalid password",
                    auth_type="password",
                    requires_reauth=True,
                )
            else:
                raise AuthenticationError(
                    f"Authentication failed: {error_msg}",
                    auth_type="login",
                    requires_reauth=True,
                )
        
        return response
    
    async def verify_mfa(self, mfa_code: str) -> bool:
        """
        Verify MFA code.
        
        Args:
            mfa_code: MFA verification code
            
        Returns:
            True if verification successful
            
        Raises:
            AuthenticationError: If verification fails
        """
        if not self.mfa_required:
            raise AuthenticationError(
                "MFA not required",
                auth_type="mfa",
                requires_reauth=False,
            )
        
        try:
            data = {
                "deviceId": self.device_id,
                "code": mfa_code,
            }
            
            response = await self._make_request(
                "POST",
                APIEndpointType.PASSPORT,
                "verify_mfa",
                data=data,
                auth_required=False,
                use_cache=False,
            )
            
            if not response.get("success"):
                raise AuthenticationError(
                    f"MFA verification failed: {response.get('msg', 'Invalid code')}",
                    auth_type="mfa",
                    requires_reauth=False,
                )
            
            # Extract tokens after MFA
            self.access_token = response.get("accessToken")
            self.refresh_token = response.get("refreshToken")
            self.trade_token = response.get("tradeToken")
            
            if not self.access_token:
                raise AuthenticationError(
                    "No access token received after MFA",
                    auth_type="token",
                    requires_reauth=True,
                )
            
            # Set token expiration
            expires_in = response.get("expiresIn", 86400)
            self.token_expires_at = time.time() + expires_in
            
            # Get account information
            await self._get_account_info()
            
            self.is_authenticated = True
            self.mfa_required = False
            
            logger.info("MFA verification successful")
            
            # Save tokens
            await self._save_tokens()
            
            return True
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"MFA verification failed: {e}")
            raise AuthenticationError(
                f"MFA verification failed: {str(e)}",
                auth_type="mfa",
                requires_reauth=False,
            )
    
    async def refresh_access_token(self) -> bool:
        """
        Refresh access token using refresh token.
        
        Returns:
            True if refresh successful
            
        Raises:
            AuthenticationError: If refresh fails
        """
        if not self.refresh_token:
            raise AuthenticationError(
                "No refresh token available",
                auth_type="refresh",
                requires_reauth=True,
            )
        
        async with self.token_refresh_lock:
            if self.token_refresh_in_progress:
                # Wait for ongoing refresh
                while self.token_refresh_in_progress:
                    await asyncio.sleep(0.1)
                return self.is_authenticated
            
            self.token_refresh_in_progress = True
            
            try:
                logger.info("Refreshing access token")
                
                data = {
                    "refreshToken": self.refresh_token,
                    "deviceId": self.device_id,
                }
                
                response = await self._make_request(
                    "POST",
                    APIEndpointType.PASSPORT,
                    "refresh_token",
                    data=data,
                    auth_required=False,
                    use_cache=False,
                )
                
                if not response.get("success"):
                    raise AuthenticationError(
                        f"Token refresh failed: {response.get('msg', 'Unknown error')}",
                        auth_type="refresh",
                        requires_reauth=True,
                    )
                
                # Update tokens
                self.access_token = response.get("accessToken")
                new_refresh_token = response.get("refreshToken")
                if new_refresh_token:
                    self.refresh_token = new_refresh_token
                
                # Update expiration
                expires_in = response.get("expiresIn", 86400)
                self.token_expires_at = time.time() + expires_in
                
                self.is_authenticated = True
                
                logger.info("Token refresh successful")
                
                # Save updated tokens
                await self._save_tokens()
                
                return True
                
            except AuthenticationError:
                self.is_authenticated = False
                raise
            except Exception as e:
                self.is_authenticated = False
                logger.error(f"Token refresh failed: {e}")
                raise AuthenticationError(
                    f"Token refresh failed: {str(e)}",
                    auth_type="refresh",
                    requires_reauth=True,
                )
            finally:
                self.token_refresh_in_progress = False
    
    async def _get_account_info(self):
        """Get account information after authentication."""
        try:
            # This will be implemented in the trading module
            # For now, just set a placeholder
            self.account_id = "authenticated"
        except Exception as e:
            logger.warning(f"Could not get account info: {e}")
    
    async def _save_tokens(self):
        """Save tokens to configuration (placeholder)."""
        # In a real implementation, you might save to a secure store
        logger.debug("Tokens saved (placeholder)")
    
    def is_token_expired(self) -> bool:
        """Check if access token is expired."""
        if not self.token_expires_at:
            return True
        
        # Consider token expired 5 minutes before actual expiration
        return time.time() >= (self.token_expires_at - 300)
    
    async def ensure_authenticated(self) -> bool:
        """
        Ensure we have a valid authentication token.
        
        Returns:
            True if authenticated, False otherwise
        """
        if not self.is_authenticated or not self.access_token:
            return False
        
        if self.is_token_expired():
            try:
                return await self.refresh_access_token()
            except AuthenticationError:
                self.is_authenticated = False
                return False
        
        return True
    
    async def logout(self) -> bool:
        """
        Logout and invalidate tokens.
        
        Returns:
            True if logout successful
        """
        try:
            if self.access_token:
                await self._make_request(
                    "POST",
                    APIEndpointType.PASSPORT,
                    "logout",
                    auth_required=True,
                    use_cache=False,
                )
            
            # Clear tokens
            self.access_token = None
            self.refresh_token = None
            self.trade_token = None
            self.account_id = None
            self.is_authenticated = False
            self.token_expires_at = None
            self.mfa_required = False
            
            logger.info("Logout successful")
            return True
            
        except Exception as e:
            logger.warning(f"Logout error: {e}")
            # Clear tokens anyway
            self.access_token = None
            self.refresh_token = None
            self.trade_token = None
            self.account_id = None
            self.is_authenticated = False
            self.token_expires_at = None
            self.mfa_required = False
            return False
