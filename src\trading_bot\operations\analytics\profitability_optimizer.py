"""
Profitability Optimizer

Optimizes trading strategies and parameters to maximize risk-adjusted returns
using advanced optimization techniques and machine learning.
"""

from typing import Dict, List, Optional, Any, Tuple, Callable
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
import numpy as np
import pandas as pd
from scipy.optimize import minimize, differential_evolution
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import TimeSeriesSplit
import warnings
warnings.filterwarnings('ignore')

from ...core.logger import get_logger

logger = get_logger(__name__)

@dataclass
class OptimizationTarget:
    """Optimization target definition"""
    metric: str  # 'sharpe_ratio', 'total_return', 'profit_factor', etc.
    weight: float
    constraint_min: Optional[float] = None
    constraint_max: Optional[float] = None

@dataclass
class ParameterBounds:
    """Parameter bounds for optimization"""
    parameter_name: str
    min_value: float
    max_value: float
    current_value: float
    step_size: Optional[float] = None

@dataclass
class OptimizationResult:
    """Result of optimization process"""
    optimization_id: str
    target_metric: str
    original_value: float
    optimized_value: float
    improvement: float
    optimal_parameters: Dict[str, float]
    confidence: float
    backtest_results: Dict[str, Any]
    timestamp: datetime

@dataclass
class ProfitabilityInsight:
    """Insight for improving profitability"""
    insight_id: str
    category: str
    description: str
    potential_improvement: float
    implementation_effort: str  # 'low', 'medium', 'high'
    risk_level: str  # 'low', 'medium', 'high'
    recommended_actions: List[str]
    priority_score: float

class ProfitabilityOptimizer:
    """Optimizes trading strategies for maximum profitability"""
    
    def __init__(self, performance_analyzer, strategy_manager):
        self.performance_analyzer = performance_analyzer
        self.strategy_manager = strategy_manager
        self.optimization_history = []
        self.ml_models = {}
        
        # Optimization configuration
        self.config = {
            'optimization_methods': ['grid_search', 'genetic_algorithm', 'bayesian'],
            'backtest_periods': [30, 90, 180, 365],  # days
            'min_trades_for_optimization': 50,
            'confidence_threshold': 0.7,
            'max_optimization_time': 3600,  # seconds
            'cross_validation_folds': 5
        }
        
        # Default optimization targets
        self.default_targets = [
            OptimizationTarget('sharpe_ratio', 0.4, constraint_min=1.0),
            OptimizationTarget('total_return', 0.3, constraint_min=0.05),
            OptimizationTarget('max_drawdown', 0.2, constraint_max=0.15),
            OptimizationTarget('profit_factor', 0.1, constraint_min=1.2)
        ]
        
    async def optimize_strategy_parameters(self, 
                                         strategy_name: str,
                                         parameter_bounds: List[ParameterBounds],
                                         optimization_targets: Optional[List[OptimizationTarget]] = None,
                                         method: str = 'genetic_algorithm') -> OptimizationResult:
        """Optimize strategy parameters for maximum profitability"""
        
        logger.info(f"Optimizing parameters for strategy: {strategy_name}")
        
        if optimization_targets is None:
            optimization_targets = self.default_targets
        
        # Get historical data for backtesting
        historical_data = await self._get_historical_data(365)  # 1 year
        
        if len(historical_data) < self.config['min_trades_for_optimization']:
            raise ValueError(f"Insufficient historical data for optimization")
        
        # Define objective function
        def objective_function(parameters):
            return self._evaluate_parameter_set(
                strategy_name, parameters, parameter_bounds, 
                optimization_targets, historical_data
            )
        
        # Perform optimization based on method
        if method == 'genetic_algorithm':
            result = await self._optimize_genetic_algorithm(
                objective_function, parameter_bounds
            )
        elif method == 'grid_search':
            result = await self._optimize_grid_search(
                objective_function, parameter_bounds
            )
        elif method == 'bayesian':
            result = await self._optimize_bayesian(
                objective_function, parameter_bounds
            )
        else:
            raise ValueError(f"Unknown optimization method: {method}")
        
        # Validate results with out-of-sample testing
        validation_results = await self._validate_optimization_results(
            strategy_name, result, parameter_bounds
        )
        
        # Create optimization result
        optimization_result = OptimizationResult(
            optimization_id=f"opt_{strategy_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            target_metric=optimization_targets[0].metric,
            original_value=result['original_score'],
            optimized_value=result['optimized_score'],
            improvement=(result['optimized_score'] - result['original_score']) / result['original_score'],
            optimal_parameters=result['optimal_parameters'],
            confidence=validation_results['confidence'],
            backtest_results=validation_results['backtest_results'],
            timestamp=datetime.now()
        )
        
        self.optimization_history.append(optimization_result)
        
        logger.info(f"Optimization complete. Improvement: {optimization_result.improvement:.2%}")
        
        return optimization_result
    
    async def identify_profitability_insights(self) -> List[ProfitabilityInsight]:
        """Identify insights and opportunities to improve profitability"""
        
        logger.info("Identifying profitability insights")
        
        insights = []
        
        # Analyze current performance
        current_performance = await self.performance_analyzer.get_performance_metrics(90)
        
        # Strategy-specific insights
        strategy_insights = await self._analyze_strategy_profitability()
        insights.extend(strategy_insights)
        
        # Risk-adjusted return insights
        risk_insights = await self._analyze_risk_adjusted_returns()
        insights.extend(risk_insights)
        
        # Timing insights
        timing_insights = await self._analyze_timing_optimization()
        insights.extend(timing_insights)
        
        # Position sizing insights
        sizing_insights = await self._analyze_position_sizing()
        insights.extend(sizing_insights)
        
        # Cost optimization insights
        cost_insights = await self._analyze_cost_optimization()
        insights.extend(cost_insights)
        
        # Market regime insights
        regime_insights = await self._analyze_regime_optimization()
        insights.extend(regime_insights)
        
        # Sort by priority score
        insights.sort(key=lambda x: x.priority_score, reverse=True)
        
        return insights
    
    async def continuous_optimization(self):
        """Continuously optimize strategies based on performance"""
        
        logger.info("Starting continuous profitability optimization")
        
        while True:
            try:
                # Check if optimization is needed
                optimization_needed = await self._check_optimization_triggers()
                
                if optimization_needed:
                    # Get strategies that need optimization
                    strategies_to_optimize = await self._identify_strategies_for_optimization()
                    
                    for strategy_name in strategies_to_optimize:
                        try:
                            # Get parameter bounds for strategy
                            parameter_bounds = await self._get_strategy_parameter_bounds(strategy_name)
                            
                            if parameter_bounds:
                                # Optimize strategy
                                result = await self.optimize_strategy_parameters(
                                    strategy_name, parameter_bounds
                                )
                                
                                # Apply optimization if improvement is significant
                                if result.improvement > 0.05 and result.confidence > 0.7:
                                    await self._apply_optimization_results(strategy_name, result)
                                    
                        except Exception as e:
                            logger.error(f"Error optimizing strategy {strategy_name}: {e}")
                
                # Generate and apply insights
                insights = await self.identify_profitability_insights()
                high_priority_insights = [i for i in insights if i.priority_score > 0.8]
                
                for insight in high_priority_insights:
                    await self._apply_insight(insight)
                
                # Sleep before next optimization cycle
                await asyncio.sleep(86400)  # Daily optimization
                
            except Exception as e:
                logger.error(f"Error in continuous optimization: {e}")
                await asyncio.sleep(3600)
    
    def _evaluate_parameter_set(self, 
                               strategy_name: str,
                               parameters: List[float],
                               parameter_bounds: List[ParameterBounds],
                               targets: List[OptimizationTarget],
                               historical_data: pd.DataFrame) -> float:
        """Evaluate a parameter set using backtesting"""
        
        # Map parameters to names
        param_dict = {
            bounds.parameter_name: parameters[i] 
            for i, bounds in enumerate(parameter_bounds)
        }
        
        # Run backtest with parameters
        backtest_results = self._run_backtest(strategy_name, param_dict, historical_data)
        
        # Calculate composite score
        total_score = 0
        total_weight = 0
        
        for target in targets:
            metric_value = backtest_results.get(target.metric, 0)
            
            # Check constraints
            if target.constraint_min and metric_value < target.constraint_min:
                return -1000  # Heavy penalty for constraint violation
            if target.constraint_max and metric_value > target.constraint_max:
                return -1000
            
            # Add to score
            total_score += metric_value * target.weight
            total_weight += target.weight
        
        return total_score / total_weight if total_weight > 0 else 0
    
    def _run_backtest(self, strategy_name: str, parameters: Dict[str, float], data: pd.DataFrame) -> Dict[str, float]:
        """Run backtest with given parameters"""
        
        # This would run actual backtest with the strategy
        # For now, simulate results based on parameters
        
        # Simulate some metrics based on parameters
        base_sharpe = 1.2
        base_return = 0.15
        base_drawdown = 0.08
        base_profit_factor = 1.5
        
        # Add parameter-based adjustments
        param_adjustment = sum(parameters.values()) / len(parameters) if parameters else 1.0
        noise = np.random.normal(0, 0.1)
        
        return {
            'sharpe_ratio': max(0.1, base_sharpe * param_adjustment + noise),
            'total_return': max(0.01, base_return * param_adjustment + noise * 0.5),
            'max_drawdown': max(0.01, base_drawdown / param_adjustment + abs(noise) * 0.3),
            'profit_factor': max(1.0, base_profit_factor * param_adjustment + noise * 0.2),
            'win_rate': max(0.3, 0.55 + noise * 0.1),
            'total_trades': int(100 + noise * 20)
        }
    
    async def _optimize_genetic_algorithm(self, 
                                        objective_function: Callable,
                                        parameter_bounds: List[ParameterBounds]) -> Dict[str, Any]:
        """Optimize using genetic algorithm"""
        
        # Prepare bounds for scipy
        bounds = [(bounds.min_value, bounds.max_value) for bounds in parameter_bounds]
        
        # Get current parameter values
        current_params = [bounds.current_value for bounds in parameter_bounds]
        current_score = objective_function(current_params)
        
        # Run genetic algorithm
        result = differential_evolution(
            lambda x: -objective_function(x),  # Minimize negative
            bounds,
            maxiter=100,
            popsize=15,
            seed=42
        )
        
        optimal_params = {
            parameter_bounds[i].parameter_name: result.x[i]
            for i in range(len(parameter_bounds))
        }
        
        return {
            'optimal_parameters': optimal_params,
            'optimized_score': -result.fun,
            'original_score': current_score,
            'success': result.success
        }
    
    async def _optimize_grid_search(self, 
                                  objective_function: Callable,
                                  parameter_bounds: List[ParameterBounds]) -> Dict[str, Any]:
        """Optimize using grid search"""
        
        # Create parameter grids
        param_grids = []
        for bounds in parameter_bounds:
            if bounds.step_size:
                grid = np.arange(bounds.min_value, bounds.max_value + bounds.step_size, bounds.step_size)
            else:
                grid = np.linspace(bounds.min_value, bounds.max_value, 10)
            param_grids.append(grid)
        
        # Get current score
        current_params = [bounds.current_value for bounds in parameter_bounds]
        current_score = objective_function(current_params)
        
        # Grid search
        best_score = current_score
        best_params = current_params.copy()
        
        # Simplified grid search (would use itertools.product for full grid)
        for i in range(min(1000, np.prod([len(grid) for grid in param_grids]))):
            # Random sampling from grid
            test_params = [np.random.choice(grid) for grid in param_grids]
            score = objective_function(test_params)
            
            if score > best_score:
                best_score = score
                best_params = test_params
        
        optimal_params = {
            parameter_bounds[i].parameter_name: best_params[i]
            for i in range(len(parameter_bounds))
        }
        
        return {
            'optimal_parameters': optimal_params,
            'optimized_score': best_score,
            'original_score': current_score,
            'success': True
        }
    
    async def _optimize_bayesian(self, 
                               objective_function: Callable,
                               parameter_bounds: List[ParameterBounds]) -> Dict[str, Any]:
        """Optimize using Bayesian optimization (simplified)"""
        
        # This would use a proper Bayesian optimization library like scikit-optimize
        # For now, use a simplified approach
        
        current_params = [bounds.current_value for bounds in parameter_bounds]
        current_score = objective_function(current_params)
        
        best_score = current_score
        best_params = current_params.copy()
        
        # Random search with some intelligence
        for iteration in range(50):
            # Generate candidate parameters
            test_params = []
            for i, bounds in enumerate(parameter_bounds):
                if iteration < 10:
                    # Random exploration
                    param = np.random.uniform(bounds.min_value, bounds.max_value)
                else:
                    # Exploitation around best parameters
                    noise = np.random.normal(0, (bounds.max_value - bounds.min_value) * 0.1)
                    param = np.clip(best_params[i] + noise, bounds.min_value, bounds.max_value)
                
                test_params.append(param)
            
            score = objective_function(test_params)
            
            if score > best_score:
                best_score = score
                best_params = test_params
        
        optimal_params = {
            parameter_bounds[i].parameter_name: best_params[i]
            for i in range(len(parameter_bounds))
        }
        
        return {
            'optimal_parameters': optimal_params,
            'optimized_score': best_score,
            'original_score': current_score,
            'success': True
        }
    
    async def _validate_optimization_results(self, 
                                           strategy_name: str,
                                           optimization_result: Dict[str, Any],
                                           parameter_bounds: List[ParameterBounds]) -> Dict[str, Any]:
        """Validate optimization results with out-of-sample testing"""
        
        # Get out-of-sample data
        oos_data = await self._get_historical_data(90)  # Last 90 days
        
        # Test optimized parameters
        optimized_results = self._run_backtest(
            strategy_name, 
            optimization_result['optimal_parameters'], 
            oos_data
        )
        
        # Test original parameters
        original_params = {
            bounds.parameter_name: bounds.current_value 
            for bounds in parameter_bounds
        }
        original_results = self._run_backtest(strategy_name, original_params, oos_data)
        
        # Calculate confidence based on consistency
        in_sample_improvement = optimization_result['optimized_score'] - optimization_result['original_score']
        oos_improvement = optimized_results['sharpe_ratio'] - original_results['sharpe_ratio']
        
        # Confidence based on consistency between in-sample and out-of-sample
        if in_sample_improvement > 0 and oos_improvement > 0:
            confidence = min(0.95, 0.5 + abs(oos_improvement / in_sample_improvement) * 0.4)
        else:
            confidence = 0.3
        
        return {
            'confidence': confidence,
            'backtest_results': {
                'in_sample': optimization_result,
                'out_of_sample': {
                    'optimized': optimized_results,
                    'original': original_results
                }
            }
        }
    
    async def _analyze_strategy_profitability(self) -> List[ProfitabilityInsight]:
        """Analyze strategy-specific profitability opportunities"""
        
        insights = []
        
        # This would analyze actual strategy performance
        # For now, generate sample insights
        
        insights.append(ProfitabilityInsight(
            insight_id="strategy_001",
            category="strategy_allocation",
            description="Momentum strategy showing 15% higher Sharpe ratio than mean reversion",
            potential_improvement=0.12,
            implementation_effort="low",
            risk_level="low",
            recommended_actions=[
                "Increase momentum strategy allocation by 10%",
                "Reduce mean reversion allocation by 5%"
            ],
            priority_score=0.85
        ))
        
        return insights
    
    async def _analyze_risk_adjusted_returns(self) -> List[ProfitabilityInsight]:
        """Analyze risk-adjusted return optimization opportunities"""
        
        insights = []
        
        insights.append(ProfitabilityInsight(
            insight_id="risk_001",
            category="risk_optimization",
            description="Current position sizing could be optimized using Kelly Criterion",
            potential_improvement=0.08,
            implementation_effort="medium",
            risk_level="medium",
            recommended_actions=[
                "Implement Kelly Criterion position sizing",
                "Adjust position sizes based on win probability"
            ],
            priority_score=0.75
        ))
        
        return insights
    
    async def _analyze_timing_optimization(self) -> List[ProfitabilityInsight]:
        """Analyze timing optimization opportunities"""
        
        insights = []
        
        insights.append(ProfitabilityInsight(
            insight_id="timing_001",
            category="timing",
            description="Trading during 10-11 AM shows 20% higher win rate",
            potential_improvement=0.06,
            implementation_effort="low",
            risk_level="low",
            recommended_actions=[
                "Increase trading activity during 10-11 AM",
                "Reduce activity during low-performance hours"
            ],
            priority_score=0.70
        ))
        
        return insights
    
    async def _analyze_position_sizing(self) -> List[ProfitabilityInsight]:
        """Analyze position sizing optimization"""
        
        insights = []
        
        insights.append(ProfitabilityInsight(
            insight_id="sizing_001",
            category="position_sizing",
            description="Volatility-adjusted position sizing could improve risk-adjusted returns",
            potential_improvement=0.10,
            implementation_effort="medium",
            risk_level="low",
            recommended_actions=[
                "Implement volatility-based position sizing",
                "Reduce positions during high volatility periods"
            ],
            priority_score=0.80
        ))
        
        return insights
    
    async def _analyze_cost_optimization(self) -> List[ProfitabilityInsight]:
        """Analyze cost optimization opportunities"""
        
        insights = []
        
        insights.append(ProfitabilityInsight(
            insight_id="cost_001",
            category="cost_optimization",
            description="Transaction costs consuming 15% of gross profits",
            potential_improvement=0.05,
            implementation_effort="high",
            risk_level="low",
            recommended_actions=[
                "Negotiate better commission rates",
                "Optimize order routing",
                "Reduce trade frequency for small positions"
            ],
            priority_score=0.65
        ))
        
        return insights
    
    async def _analyze_regime_optimization(self) -> List[ProfitabilityInsight]:
        """Analyze market regime optimization"""
        
        insights = []
        
        insights.append(ProfitabilityInsight(
            insight_id="regime_001",
            category="market_regime",
            description="Strategy performance varies significantly by market regime",
            potential_improvement=0.15,
            implementation_effort="high",
            risk_level="medium",
            recommended_actions=[
                "Implement regime-aware strategy allocation",
                "Adjust parameters based on market conditions"
            ],
            priority_score=0.90
        ))
        
        return insights
    
    async def _get_historical_data(self, days: int) -> pd.DataFrame:
        """Get historical data for backtesting"""
        
        # This would get actual historical data
        # For now, generate synthetic data
        
        dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
        returns = np.random.normal(0.001, 0.02, days)
        
        return pd.DataFrame({
            'date': dates,
            'returns': returns,
            'price': 100 * np.exp(np.cumsum(returns))
        })
    
    async def _check_optimization_triggers(self) -> bool:
        """Check if optimization should be triggered"""
        
        # Check performance degradation
        recent_performance = await self.performance_analyzer.get_performance_metrics(30)
        
        if recent_performance.sharpe_ratio < 1.0:
            return True
        
        # Check time since last optimization
        if self.optimization_history:
            last_optimization = max(opt.timestamp for opt in self.optimization_history)
            if datetime.now() - last_optimization > timedelta(days=30):
                return True
        
        return False
    
    async def _identify_strategies_for_optimization(self) -> List[str]:
        """Identify strategies that need optimization"""
        
        # This would analyze actual strategy performance
        # For now, return sample strategies
        
        return ['momentum', 'mean_reversion']
    
    async def _get_strategy_parameter_bounds(self, strategy_name: str) -> List[ParameterBounds]:
        """Get parameter bounds for a strategy"""
        
        # This would get actual parameter bounds from strategy configuration
        # For now, return sample bounds
        
        if strategy_name == 'momentum':
            return [
                ParameterBounds('lookback_period', 5, 50, 20),
                ParameterBounds('threshold', 0.01, 0.1, 0.05),
                ParameterBounds('position_size', 0.01, 0.1, 0.05)
            ]
        elif strategy_name == 'mean_reversion':
            return [
                ParameterBounds('mean_period', 10, 100, 50),
                ParameterBounds('std_multiplier', 1.0, 3.0, 2.0),
                ParameterBounds('position_size', 0.01, 0.1, 0.03)
            ]
        
        return []
    
    async def _apply_optimization_results(self, strategy_name: str, result: OptimizationResult):
        """Apply optimization results to strategy"""
        
        logger.info(f"Applying optimization results to {strategy_name}")
        
        # This would update actual strategy parameters
        # For now, just log the action
        
        for param_name, param_value in result.optimal_parameters.items():
            logger.info(f"Setting {strategy_name}.{param_name} = {param_value}")
    
    async def _apply_insight(self, insight: ProfitabilityInsight):
        """Apply a profitability insight"""
        
        logger.info(f"Applying insight: {insight.description}")
        
        # This would implement the actual insight
        # For now, just log the actions
        
        for action in insight.recommended_actions:
            logger.info(f"Action: {action}")
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of optimization activities"""
        
        if not self.optimization_history:
            return {'total_optimizations': 0}
        
        recent_optimizations = [
            opt for opt in self.optimization_history
            if opt.timestamp > datetime.now() - timedelta(days=30)
        ]
        
        avg_improvement = np.mean([opt.improvement for opt in recent_optimizations]) if recent_optimizations else 0
        
        return {
            'total_optimizations': len(self.optimization_history),
            'recent_optimizations': len(recent_optimizations),
            'average_improvement': avg_improvement,
            'best_optimization': max(self.optimization_history, key=lambda x: x.improvement) if self.optimization_history else None,
            'last_optimization': max(self.optimization_history, key=lambda x: x.timestamp) if self.optimization_history else None
        }
