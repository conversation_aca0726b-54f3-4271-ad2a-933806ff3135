"""
Integrated Volatility Trading System
Combines volatility hunting, ML predictions, and paper trading
Ready for immediate deployment and testing
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import time
import os

# Import our custom modules
try:
    from quick_ml_yahoo_standalone import YahooML<PERSON>rainer
    from paper_trading_engine import PaperTradingEngine, PaperTrade
except ImportError as e:
    logging.error(f"Import error: {e}")
    logging.error("Make sure quick_ml_yahoo_standalone.py and paper_trading_engine.py are in the same directory")
    exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegratedVolatilityTrader:
    """
    Complete volatility trading system that:
    1. Scans for volatility opportunities
    2. Uses ML to predict success probability
    3. Executes paper trades with risk management
    4. Tracks performance and learns from results
    """
    
    def __init__(self, initial_capital: float = 10000.0):
        # Initialize components
        self.ml_trainer = YahooMLTrainer()
        self.paper_engine = PaperTradingEngine(initial_capital)
        
        # Load trained models if available
        if os.path.exists("ml_models"):
            logger.info("Loading existing ML models...")
            if self.ml_trainer.load_models():
                self.models_loaded = True
            else:
                self.models_loaded = False
                logger.warning("Could not load models, will use rule-based approach")
        else:
            self.models_loaded = False
            logger.warning("No ML models found, will use rule-based approach")
        
        # Trading parameters
        self.volatility_watchlist = [
            # Leveraged ETFs - High volume, predictable patterns
            'TQQQ', 'SQQQ', 'UVXY', 'SOXL', 'SOXS',
            'LABU', 'LABD', 'TNA', 'TZA', 'UPRO', 'SPXU',
            # Popular meme stocks
            'GME', 'AMC', 'BB', 'NOK',
            # High volatility individual stocks
            'TSLA', 'NVDA', 'AMD', 'PLTR',
            # Crypto-related stocks
            'RIOT', 'MARA', 'COIN', 'MSTR',
            # Biotech volatility
            'MRNA', 'BNTX', 'NVAX'
        ]
        
        # Strategy thresholds
        self.min_confidence = 0.6  # Minimum confidence to trade
        self.min_volume_ratio = 1.5  # Minimum volume surge
        self.max_trades_per_day = 10  # Risk management
        self.min_gap_size = 0.02  # 2% minimum gap
        self.max_gap_size = 0.10  # 10% maximum gap
        
        # Performance tracking
        self.daily_scans = 0
        self.opportunities_found = 0
        self.trades_executed = 0
        self.scan_history = []
    
    def run_daily_scan(self) -> List[Dict]:
        """
        Run complete daily volatility scan with ML-enhanced predictions
        """
        logger.info("🔍 Starting daily volatility scan...")
        self.daily_scans += 1
        
        opportunities = []
        scan_start_time = datetime.now()
        
        for symbol in self.volatility_watchlist:
            try:
                logger.info(f"Scanning {symbol}...")
                
                # Get market data
                opportunity = self._analyze_symbol(symbol)
                
                if opportunity:
                    opportunities.append(opportunity)
                    self.opportunities_found += 1
                    logger.info(f"✅ Found opportunity: {symbol} - {opportunity['strategy']} (confidence: {opportunity['confidence']:.2f})")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        # Sort by confidence score
        opportunities.sort(key=lambda x: x['confidence'], reverse=True)
        
        scan_duration = (datetime.now() - scan_start_time).total_seconds()
        logger.info(f"📊 Scan complete: {len(opportunities)} opportunities found in {scan_duration:.1f}s")
        
        # Store scan results
        self.scan_history.append({
            'timestamp': scan_start_time,
            'opportunities_found': len(opportunities),
            'duration': scan_duration
        })
        
        return opportunities
    
    def _analyze_symbol(self, symbol: str) -> Optional[Dict]:
        """
        Analyze a single symbol for volatility opportunities
        """
        try:
            # Get recent data
            ticker = yf.Ticker(symbol)
            df = ticker.history(period="1mo", interval="1d")
            
            if df.empty or len(df) < 20:
                return None
            
            # Calculate technical indicators
            df = self._calculate_technical_indicators(df)
            latest = df.iloc[-1]
            current_price = latest['Close']
            
            # Get additional info
            info = ticker.info
            
            # Check for opportunities
            # 1. Gap Fill Strategy
            gap_opportunity = self._check_gap_fill_opportunity(df, latest, current_price, symbol)
            if gap_opportunity:
                return gap_opportunity
            
            # 2. Oversold Bounce Strategy
            oversold_opportunity = self._check_oversold_opportunity(df, latest, current_price, symbol)
            if oversold_opportunity:
                return oversold_opportunity
            
            # 3. Breakout Pullback Strategy
            breakout_opportunity = self._check_breakout_opportunity(df, latest, current_price, symbol)
            if breakout_opportunity:
                return breakout_opportunity
            
            return None
            
        except Exception as e:
            logger.error(f"Error in symbol analysis for {symbol}: {e}")
            return None
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        # Price movement
        df['daily_return'] = df['Close'].pct_change()
        df['daily_range'] = (df['High'] - df['Low']) / df['Open']
        df['gap'] = df['Open'] / df['Close'].shift(1) - 1
        
        # Volume
        df['volume_sma'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma']
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['bb_middle'] = df['Close'].rolling(20).mean()
        df['bb_std'] = df['Close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        
        # ATR
        high_low = df['High'] - df['Low']
        high_close = abs(df['High'] - df['Close'].shift())
        low_close = abs(df['Low'] - df['Close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['atr'] = true_range.rolling(14).mean()
        
        # Support/Resistance
        df['resistance'] = df['High'].rolling(20).max()
        df['support'] = df['Low'].rolling(20).min()
        
        return df
    
    def _check_gap_fill_opportunity(self, df: pd.DataFrame, latest: pd.Series, 
                                   current_price: float, symbol: str) -> Optional[Dict]:
        """Check for gap fill opportunities"""
        gap_size = abs(latest['gap'])
        
        # Check gap criteria
        if gap_size < self.min_gap_size or gap_size > self.max_gap_size:
            return None
        
        # Check volume
        if latest['volume_ratio'] < self.min_volume_ratio:
            return None
        
        # Determine direction and prices
        prev_close = df.iloc[-2]['Close']
        is_gap_up = latest['gap'] > 0
        
        entry_price = current_price
        target_price = prev_close
        
        if is_gap_up:
            stop_loss = latest['High'] * 1.01
            if entry_price >= target_price:
                return None
        else:
            stop_loss = latest['Low'] * 0.99
            if entry_price <= target_price:
                return None
        
        # Calculate confidence
        base_confidence = 0.80  # Historical gap fill rate
        
        # ML enhancement
        if self.models_loaded:
            try:
                ml_predictions = self.ml_trainer.predict_opportunities(symbol)
                if 'gap_fill_probability' in ml_predictions:
                    ml_confidence = ml_predictions['gap_fill_probability']
                    # Weighted average: 60% ML, 40% rule-based
                    confidence = 0.6 * ml_confidence + 0.4 * base_confidence
                else:
                    confidence = base_confidence
            except:
                confidence = base_confidence
        else:
            # Volume-based confidence adjustment
            volume_boost = min(0.1, (latest['volume_ratio'] - 1.5) * 0.05)
            confidence = base_confidence + volume_boost
        
        if confidence < self.min_confidence:
            return None
        
        # Calculate expected metrics
        potential_profit = abs(target_price - entry_price) / entry_price
        potential_loss = abs(stop_loss - entry_price) / entry_price
        risk_reward = potential_profit / potential_loss if potential_loss > 0 else 0
        
        return {
            'symbol': symbol,
            'strategy': 'gap_fill',
            'entry_price': entry_price,
            'target_price': target_price,
            'stop_loss': stop_loss,
            'confidence': confidence,
            'risk_reward': risk_reward,
            'gap_size': gap_size,
            'volume_ratio': latest['volume_ratio'],
            'expected_return': confidence * potential_profit - (1 - confidence) * potential_loss,
            'signals': {
                'gap_direction': 'up' if is_gap_up else 'down',
                'volume_surge': latest['volume_ratio'],
                'atr_ratio': latest['atr'] / current_price
            }
        }
    
    def _check_oversold_opportunity(self, df: pd.DataFrame, latest: pd.Series,
                                   current_price: float, symbol: str) -> Optional[Dict]:
        """Check for oversold bounce opportunities"""
        # RSI oversold check
        if latest['rsi'] > 35:
            return None
        
        # Volume surge check
        if latest['volume_ratio'] < 2.0:
            return None
        
        # Volatility check
        if latest['daily_range'] < 0.05:
            return None
        
        # Price setup
        entry_price = current_price
        target_price = latest['bb_middle']
        stop_loss = latest['support'] * 0.98
        
        # Skip if poor risk/reward
        potential_profit = (target_price - entry_price) / entry_price
        potential_loss = (entry_price - stop_loss) / entry_price
        
        if potential_profit < potential_loss * 1.5:
            return None
        
        # Calculate confidence
        base_confidence = 0.65 + (30 - latest['rsi']) * 0.01
        base_confidence = min(base_confidence, 0.75)
        
        # ML enhancement
        if self.models_loaded:
            try:
                ml_predictions = self.ml_trainer.predict_opportunities(symbol)
                if 'oversold_bounce_probability' in ml_predictions:
                    ml_confidence = ml_predictions['oversold_bounce_probability']
                    confidence = 0.6 * ml_confidence + 0.4 * base_confidence
                else:
                    confidence = base_confidence
            except:
                confidence = base_confidence
        else:
            confidence = base_confidence
        
        if confidence < self.min_confidence:
            return None
        
        risk_reward = potential_profit / potential_loss if potential_loss > 0 else 0
        
        return {
            'symbol': symbol,
            'strategy': 'oversold_bounce',
            'entry_price': entry_price,
            'target_price': target_price,
            'stop_loss': stop_loss,
            'confidence': confidence,
            'risk_reward': risk_reward,
            'rsi': latest['rsi'],
            'volume_ratio': latest['volume_ratio'],
            'expected_return': confidence * potential_profit - (1 - confidence) * potential_loss,
            'signals': {
                'rsi_level': latest['rsi'],
                'volume_surge': latest['volume_ratio'],
                'bb_position': (current_price - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
            }
        }
    
    def _check_breakout_opportunity(self, df: pd.DataFrame, latest: pd.Series,
                                   current_price: float, symbol: str) -> Optional[Dict]:
        """Check for breakout pullback opportunities"""
        # Check for recent breakout
        recent_high = df['High'][-5:-2].max()
        breakout_level = df['resistance'][-10:-3].mean()
        
        if recent_high < breakout_level * 1.02:
            return None
        
        # Check pullback distance
        pullback_distance = abs(current_price - breakout_level) / breakout_level
        if pullback_distance > 0.03:
            return None
        
        # Must be above breakout level
        if current_price < breakout_level:
            return None
        
        # Volume confirmation
        if latest['volume_ratio'] < 1.2:
            return None
        
        # Price setup
        entry_price = current_price
        target_price = recent_high * 1.05
        stop_loss = breakout_level * 0.98
        
        # Risk/reward check
        potential_profit = (target_price - entry_price) / entry_price
        potential_loss = (entry_price - stop_loss) / entry_price
        
        if potential_profit < potential_loss * 2:
            return None
        
        # Calculate confidence
        momentum_score = (latest['rsi'] - 50) / 50
        base_confidence = 0.60 + momentum_score * 0.15
        base_confidence = min(max(base_confidence, 0.55), 0.75)
        
        # ML enhancement
        if self.models_loaded:
            try:
                ml_predictions = self.ml_trainer.predict_opportunities(symbol)
                if 'breakout_probability' in ml_predictions:
                    ml_confidence = ml_predictions['breakout_probability']
                    confidence = 0.6 * ml_confidence + 0.4 * base_confidence
                else:
                    confidence = base_confidence
            except:
                confidence = base_confidence
        else:
            confidence = base_confidence
        
        if confidence < self.min_confidence:
            return None
        
        risk_reward = potential_profit / potential_loss if potential_loss > 0 else 0
        
        return {
            'symbol': symbol,
            'strategy': 'breakout_pullback',
            'entry_price': entry_price,
            'target_price': target_price,
            'stop_loss': stop_loss,
            'confidence': confidence,
            'risk_reward': risk_reward,
            'pullback_distance': pullback_distance,
            'volume_ratio': latest['volume_ratio'],
            'expected_return': confidence * potential_profit - (1 - confidence) * potential_loss,
            'signals': {
                'rsi_momentum': latest['rsi'],
                'breakout_strength': pullback_distance,
                'volume_confirmation': latest['volume_ratio']
            }
        }
    
    def execute_top_opportunities(self, opportunities: List[Dict], max_trades: int = 5) -> List[str]:
        """Execute paper trades for top opportunities"""
        if not opportunities:
            logger.info("No opportunities to execute")
            return []
        
        executed_trades = []
        
        # Filter and sort opportunities
        filtered_opportunities = [opp for opp in opportunities if opp['confidence'] >= self.min_confidence]
        filtered_opportunities.sort(key=lambda x: x['expected_return'], reverse=True)
        
        trades_to_execute = min(max_trades, len(filtered_opportunities), 
                               self.max_trades_per_day - self.trades_executed)
        
        logger.info(f"📈 Executing {trades_to_execute} paper trades...")
        
        for i in range(trades_to_execute):
            opp = filtered_opportunities[i]
            
            trade_id = self.paper_engine.place_order(
                symbol=opp['symbol'],
                strategy=opp['strategy'],
                entry_price=opp['entry_price'],
                target_price=opp['target_price'],
                stop_loss=opp['stop_loss'],
                confidence=opp['confidence']
            )
            
            if trade_id:
                executed_trades.append(trade_id)
                self.trades_executed += 1
                logger.info(f"✅ Executed: {opp['symbol']} ({opp['strategy']}) - Expected: {opp['expected_return']*100:.1f}%")
        
        return executed_trades
    
    def update_and_monitor(self):
        """Update positions and monitor performance"""
        logger.info("📊 Updating positions and monitoring performance...")
        self.paper_engine.update_positions()
        
        # Show current status
        summary = self.paper_engine.get_portfolio_summary()
        logger.info(f"Portfolio Value: ${summary['portfolio_value']:,.2f} ({summary['total_return_pct']:+.2f}%)")
        logger.info(f"Open Positions: {summary['open_positions']}")
        logger.info(f"Win Rate: {summary['win_rate']:.1f}% ({summary['total_trades']} trades)")
    
    def run_full_trading_session(self):
        """Run a complete trading session"""
        logger.info("🚀 Starting Full Trading Session")
        logger.info("=" * 50)
        
        # 1. Scan for opportunities
        opportunities = self.run_daily_scan()
        
        # 2. Execute top trades
        if opportunities:
            executed_trades = self.execute_top_opportunities(opportunities, max_trades=5)
            logger.info(f"📈 Executed {len(executed_trades)} trades")
        else:
            logger.info("📉 No trading opportunities found")
        
        # 3. Update existing positions
        self.update_and_monitor()
        
        # 4. Generate summary
        print(self.paper_engine.get_daily_summary())
        
        # 5. Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.paper_engine.save_results(f"trading_session_{timestamp}.json")
        
        logger.info("✅ Trading session completed")
    
    def get_session_summary(self) -> str:
        """Get comprehensive session summary"""
        summary = f"\n🎯 INTEGRATED VOLATILITY TRADER SESSION SUMMARY\n"
        summary += "=" * 60 + "\n"
        
        summary += f"\n📊 SCANNING PERFORMANCE:\n"
        summary += f"   Total Scans: {self.daily_scans}\n"
        summary += f"   Opportunities Found: {self.opportunities_found}\n"
        summary += f"   Trades Executed: {self.trades_executed}\n"
        summary += f"   Success Rate: {(self.trades_executed/max(self.opportunities_found,1)*100):.1f}%\n"
        
        # Portfolio performance
        portfolio_summary = self.paper_engine.get_portfolio_summary()
        summary += f"\n💰 PORTFOLIO PERFORMANCE:\n"
        summary += f"   Total Return: {portfolio_summary['total_return_pct']:+.2f}%\n"
        summary += f"   Win Rate: {portfolio_summary['win_rate']:.1f}%\n"
        summary += f"   Sharpe Ratio: {portfolio_summary['sharpe_ratio']:.2f}\n"
        
        # System status
        summary += f"\n⚙️ SYSTEM STATUS:\n"
        summary += f"   ML Models: {'✅ Loaded' if self.models_loaded else '❌ Not Available'}\n"
        summary += f"   Data Source: ✅ Yahoo Finance\n"
        summary += f"   Risk Management: ✅ Active\n"
        
        return summary


def main():
    """Main execution function"""
    print("🎯 INTEGRATED VOLATILITY TRADING SYSTEM")
    print("=" * 50)
    
    # Initialize trader
    trader = IntegratedVolatilityTrader(initial_capital=10000)
    
    try:
        # Check if we should train ML models first
        if not trader.models_loaded:
            print("\n🤖 Training ML models (this may take a few minutes)...")
            df = trader.ml_trainer.collect_training_data(period="1y")
            if not df.empty:
                trader.ml_trainer.train_models(df)
                trader.ml_trainer.save_models()
                trader.models_loaded = True
                print("✅ ML models trained and saved!")
            else:
                print("⚠️ Could not train ML models, continuing with rule-based approach")
        
        # Run trading session
        trader.run_full_trading_session()
        
        # Show final summary
        print(trader.get_session_summary())
        
    except KeyboardInterrupt:
        print("\n⏹️ Trading session interrupted by user")
        trader.paper_engine.force_close_all()
        print(trader.paper_engine.get_daily_summary())
    
    except Exception as e:
        logger.error(f"❌ Error in trading session: {e}")
        print(f"Error: {e}")
    
    finally:
        print("\n📊 Final session statistics:")
        print(trader.get_session_summary())


if __name__ == "__main__":
    main()