#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Integrated Trading Bot with Data Extraction

Combines the working browser automation with your data extractor
for a complete trading solution.
"""

import asyncio
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.trading_bot.automation.webull_data_extractor_fixed import WebullDataExtractor
except ImportError:
    print("Error: Could not import WebullDataExtractor")
    print("Make sure src/trading_bot/automation/webull_data_extractor_fixed.py exists")
    sys.exit(1)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntegratedTradingBot:
    """
    Integrated trading bot that combines:
    - Data extraction from Webull
    - Market analysis
    - Trading decisions
    - Order execution (paper trading)
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.data_extractor = WebullDataExtractor()
        self.watchlist = config.get('watchlist', ['AAPL', 'MSFT', 'GOOGL', 'TSLA'])
        self.running = False
        
        # Trading parameters
        self.min_price_change = config.get('min_price_change', 1.0)  # 1% minimum change
        self.max_position_size = config.get('max_position_size', 100)  # Max 100 shares
        self.paper_trading = config.get('paper_trading', True)  # Always start with paper trading
        
        # Data storage
        self.market_data = {}
        self.positions = {}
        self.orders = []
        
        logger.info("🤖 Integrated Trading Bot initialized")
    
    async def initialize(self):
        """Initialize the trading bot"""
        try:
            logger.info("🚀 Initializing Integrated Trading Bot...")
            
            # Initialize data extractor
            if not self.data_extractor.initialize_driver(headless=self.config.get('headless', False)):
                raise Exception("Failed to initialize data extractor")
            
            logger.info("✅ Trading bot initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize trading bot: {e}")
            return False
    
    async def extract_market_data(self, symbol: str) -> Optional[Dict]:
        """Extract market data for a symbol"""
        try:
            logger.info(f"📊 Extracting market data for {symbol}")
            
            # Use your data extractor
            data = self.data_extractor.extract_stock_data(symbol)
            
            if data and data.get('price'):
                # Store the data
                self.market_data[symbol] = data
                
                logger.info(f"✅ {symbol}: ${data['price']} ({data.get('change_percent', 0):.2f}%)")
                return data
            else:
                logger.warning(f"⚠️ No price data found for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to extract data for {symbol}: {e}")
            return None
    
    async def analyze_market_data(self, symbol: str, data: Dict) -> Dict:
        """Analyze market data and generate trading signals"""
        try:
            # Simple trading logic based on price change
            change_percent = data.get('change_percent', 0)
            price = data.get('price', 0)
            volume = data.get('volume', 0)
            
            signal = {
                'symbol': symbol,
                'action': 'HOLD',
                'confidence': 0.0,
                'reason': 'No clear signal',
                'suggested_quantity': 0,
                'timestamp': datetime.now()
            }
            
            # Simple momentum strategy
            if change_percent > self.min_price_change:
                signal['action'] = 'BUY'
                signal['confidence'] = min(abs(change_percent) / 5.0, 1.0)  # Max confidence at 5% change
                signal['reason'] = f'Positive momentum: +{change_percent:.2f}%'
                signal['suggested_quantity'] = min(int(1000 / price), self.max_position_size)
                
            elif change_percent < -self.min_price_change:
                signal['action'] = 'SELL'
                signal['confidence'] = min(abs(change_percent) / 5.0, 1.0)
                signal['reason'] = f'Negative momentum: {change_percent:.2f}%'
                signal['suggested_quantity'] = min(int(1000 / price), self.max_position_size)
            
            # Volume confirmation
            if volume and volume > 1_000_000:  # High volume
                signal['confidence'] *= 1.2  # Increase confidence
                signal['reason'] += f' (High volume: {volume:,})'
            
            # Cap confidence at 1.0
            signal['confidence'] = min(signal['confidence'], 1.0)
            
            logger.info(f"📈 {symbol} Analysis: {signal['action']} (confidence: {signal['confidence']:.2f})")
            logger.info(f"   Reason: {signal['reason']}")
            
            return signal
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze {symbol}: {e}")
            return {'symbol': symbol, 'action': 'HOLD', 'confidence': 0.0, 'reason': 'Analysis error'}
    
    async def execute_trade(self, signal: Dict) -> bool:
        """Execute a trade based on the signal (paper trading)"""
        try:
            if signal['action'] == 'HOLD' or signal['confidence'] < 0.5:
                return False
            
            symbol = signal['symbol']
            action = signal['action']
            quantity = signal['suggested_quantity']
            confidence = signal['confidence']
            
            logger.info(f"📝 PAPER TRADE: {action} {quantity} shares of {symbol}")
            logger.info(f"   Confidence: {confidence:.2f}")
            logger.info(f"   Reason: {signal['reason']}")
            
            # Simulate order execution
            order = {
                'id': f"paper_{int(time.time())}",
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': self.market_data[symbol]['price'],
                'confidence': confidence,
                'timestamp': datetime.now(),
                'status': 'FILLED' if self.paper_trading else 'PENDING'
            }
            
            self.orders.append(order)
            
            # Update positions (paper trading simulation)
            if symbol not in self.positions:
                self.positions[symbol] = {'quantity': 0, 'avg_price': 0}
            
            if action == 'BUY':
                old_qty = self.positions[symbol]['quantity']
                old_price = self.positions[symbol]['avg_price']
                new_qty = old_qty + quantity
                new_avg_price = ((old_qty * old_price) + (quantity * order['price'])) / new_qty if new_qty > 0 else 0
                
                self.positions[symbol]['quantity'] = new_qty
                self.positions[symbol]['avg_price'] = new_avg_price
                
            elif action == 'SELL':
                self.positions[symbol]['quantity'] = max(0, self.positions[symbol]['quantity'] - quantity)
            
            logger.info(f"✅ Paper trade executed: {order['id']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to execute trade: {e}")
            return False
    
    async def run_trading_loop(self):
        """Main trading loop"""
        logger.info("🚀 Starting integrated trading loop...")
        self.running = True
        
        loop_count = 0
        
        try:
            while self.running:
                loop_count += 1
                logger.info(f"\n🔄 Trading Loop #{loop_count}")
                logger.info("=" * 50)
                
                # Process each symbol in watchlist
                for symbol in self.watchlist:
                    try:
                        # Extract market data
                        data = await self.extract_market_data(symbol)
                        
                        if data:
                            # Analyze the data
                            signal = await self.analyze_market_data(symbol, data)
                            
                            # Execute trade if signal is strong enough
                            if signal['confidence'] > 0.5:
                                await self.execute_trade(signal)
                        
                        # Small delay between symbols
                        await asyncio.sleep(2)
                        
                    except Exception as e:
                        logger.error(f"Error processing {symbol}: {e}")
                        continue
                
                # Show current status
                await self.show_status()
                
                # Wait before next loop
                loop_interval = self.config.get('loop_interval', 300)  # 5 minutes default
                logger.info(f"⏳ Waiting {loop_interval} seconds before next loop...")
                
                for i in range(loop_interval):
                    if not self.running:
                        break
                    await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("\n⏹️ Trading loop stopped by user")
        except Exception as e:
            logger.error(f"❌ Trading loop error: {e}")
        finally:
            self.running = False
    
    async def show_status(self):
        """Show current trading status"""
        logger.info("\n📊 CURRENT STATUS")
        logger.info("-" * 30)
        
        # Show positions
        if self.positions:
            logger.info("💼 Positions:")
            for symbol, pos in self.positions.items():
                if pos['quantity'] > 0:
                    current_price = self.market_data.get(symbol, {}).get('price', 0)
                    pnl = (current_price - pos['avg_price']) * pos['quantity'] if current_price else 0
                    logger.info(f"   {symbol}: {pos['quantity']} shares @ ${pos['avg_price']:.2f} (P&L: ${pnl:.2f})")
        else:
            logger.info("💼 No positions")
        
        # Show recent orders
        recent_orders = self.orders[-5:] if self.orders else []
        if recent_orders:
            logger.info("📝 Recent Orders:")
            for order in recent_orders:
                logger.info(f"   {order['action']} {order['quantity']} {order['symbol']} @ ${order['price']:.2f}")
        
        logger.info(f"📈 Total Orders: {len(self.orders)}")
    
    async def cleanup(self):
        """Clean up resources"""
        logger.info("🧹 Cleaning up...")
        self.running = False
        
        if self.data_extractor:
            self.data_extractor.cleanup()
        
        logger.info("✅ Cleanup completed")

async def main():
    """Main function"""
    print("🤖 Integrated Trading Bot with Data Extraction")
    print("=" * 60)
    print("This bot will:")
    print("1. Extract real-time data from Webull")
    print("2. Analyze market conditions")
    print("3. Generate trading signals")
    print("4. Execute paper trades")
    print("5. Monitor positions and performance")
    print()
    print("⚠️ PAPER TRADING MODE ONLY - No real money used!")
    print()
    
    # Configuration
    config = {
        'watchlist': ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA'],
        'headless': False,  # Show browser for debugging
        'paper_trading': True,  # Always paper trading
        'min_price_change': 1.0,  # 1% minimum change to trade
        'max_position_size': 100,  # Max 100 shares per position
        'loop_interval': 300  # 5 minutes between loops
    }
    
    # Create and run bot
    bot = IntegratedTradingBot(config)
    
    try:
        # Initialize
        if not await bot.initialize():
            print("❌ Failed to initialize bot")
            return
        
        print("✅ Bot initialized successfully!")
        print("\nPress Ctrl+C to stop the bot\n")
        
        # Run trading loop
        await bot.run_trading_loop()
        
    except KeyboardInterrupt:
        print("\n⏹️ Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Bot error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await bot.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
