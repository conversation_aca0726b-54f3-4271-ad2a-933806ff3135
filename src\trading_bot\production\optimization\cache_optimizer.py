"""
Cache optimization system for AI Trading Bot production deployment.

This module provides intelligent caching strategies to optimize performance
and reduce latency for critical trading operations:

Cache Types:
- In-memory caching for frequently accessed data
- Redis distributed caching for shared data
- ML model prediction caching
- Market data caching with TTL
- Database query result caching
- API response caching

Optimization Features:
- Cache hit rate monitoring
- Automatic cache warming
- Cache eviction policies
- Cache size optimization
- Performance impact analysis
"""

import asyncio
import time
import hashlib
import pickle
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
import redis.asyncio as redis
import json

from ....core.config import Config
from ....utils.logger import get_logger

logger = get_logger(__name__)


class CacheType(Enum):
    """Types of caches."""
    MEMORY = "memory"
    REDIS = "redis"
    HYBRID = "hybrid"


class EvictionPolicy(Enum):
    """Cache eviction policies."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time To Live
    FIFO = "fifo"  # First In First Out


@dataclass
class CacheMetrics:
    """Cache performance metrics."""
    cache_name: str
    cache_type: CacheType
    
    # Hit/Miss statistics
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    hit_rate: float = 0.0
    
    # Performance metrics
    avg_hit_time_ms: float = 0.0
    avg_miss_time_ms: float = 0.0
    avg_write_time_ms: float = 0.0
    
    # Size metrics
    current_size: int = 0
    max_size: int = 0
    memory_usage_mb: float = 0.0
    
    # Eviction statistics
    evictions: int = 0
    expired_keys: int = 0
    
    # Time window
    window_start: datetime = field(default_factory=datetime.utcnow)
    window_duration: timedelta = field(default_factory=lambda: timedelta(hours=1))


@dataclass
class CacheConfig:
    """Cache configuration."""
    name: str
    cache_type: CacheType
    max_size: int = 10000
    ttl_seconds: int = 3600
    eviction_policy: EvictionPolicy = EvictionPolicy.LRU
    enable_compression: bool = False
    enable_encryption: bool = False
    redis_url: Optional[str] = None


class CacheOptimizer:
    """
    Intelligent cache optimization system for production trading bot.
    
    Provides multi-level caching with automatic optimization based on
    access patterns, performance metrics, and resource constraints.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.caches: Dict[str, 'SmartCache'] = {}
        self.metrics: Dict[str, CacheMetrics] = {}
        self.redis_client: Optional[redis.Redis] = None
        
        # Cache configurations for different data types
        self.cache_configs = {
            'market_data': CacheConfig(
                name='market_data',
                cache_type=CacheType.MEMORY,
                max_size=50000,
                ttl_seconds=60,  # 1 minute for market data
                eviction_policy=EvictionPolicy.TTL
            ),
            'ml_predictions': CacheConfig(
                name='ml_predictions',
                cache_type=CacheType.HYBRID,
                max_size=10000,
                ttl_seconds=300,  # 5 minutes for predictions
                eviction_policy=EvictionPolicy.LRU
            ),
            'risk_calculations': CacheConfig(
                name='risk_calculations',
                cache_type=CacheType.MEMORY,
                max_size=5000,
                ttl_seconds=30,  # 30 seconds for risk data
                eviction_policy=EvictionPolicy.TTL
            ),
            'database_queries': CacheConfig(
                name='database_queries',
                cache_type=CacheType.REDIS,
                max_size=20000,
                ttl_seconds=1800,  # 30 minutes for DB queries
                eviction_policy=EvictionPolicy.LRU
            ),
            'api_responses': CacheConfig(
                name='api_responses',
                cache_type=CacheType.REDIS,
                max_size=15000,
                ttl_seconds=600,  # 10 minutes for API responses
                eviction_policy=EvictionPolicy.LRU
            )
        }
    
    async def initialize(self):
        """Initialize cache optimizer and create caches."""
        logger.info("Initializing cache optimizer...")
        
        # Initialize Redis connection if needed
        redis_configs = [c for c in self.cache_configs.values() 
                        if c.cache_type in [CacheType.REDIS, CacheType.HYBRID]]
        
        if redis_configs:
            redis_url = self.config.redis.url if hasattr(self.config, 'redis') else "redis://localhost:6379"
            self.redis_client = redis.from_url(redis_url)
            
            # Test Redis connection
            try:
                await self.redis_client.ping()
                logger.info("Redis connection established")
            except Exception as e:
                logger.error(f"Redis connection failed: {e}")
                # Fallback to memory-only caching
                for config in redis_configs:
                    config.cache_type = CacheType.MEMORY
        
        # Create caches
        for cache_config in self.cache_configs.values():
            cache = SmartCache(cache_config, self.redis_client)
            await cache.initialize()
            self.caches[cache_config.name] = cache
            self.metrics[cache_config.name] = CacheMetrics(
                cache_name=cache_config.name,
                cache_type=cache_config.cache_type
            )
        
        # Start optimization background task
        asyncio.create_task(self._optimization_loop())
        
        logger.info(f"Cache optimizer initialized with {len(self.caches)} caches")
    
    async def get(self, cache_name: str, key: str) -> Optional[Any]:
        """Get value from cache."""
        if cache_name not in self.caches:
            logger.warning(f"Cache {cache_name} not found")
            return None
        
        cache = self.caches[cache_name]
        metrics = self.metrics[cache_name]
        
        start_time = time.perf_counter()
        value = await cache.get(key)
        end_time = time.perf_counter()
        
        # Update metrics
        metrics.total_requests += 1
        if value is not None:
            metrics.cache_hits += 1
            metrics.avg_hit_time_ms = self._update_avg_time(
                metrics.avg_hit_time_ms, (end_time - start_time) * 1000, metrics.cache_hits
            )
        else:
            metrics.cache_misses += 1
            metrics.avg_miss_time_ms = self._update_avg_time(
                metrics.avg_miss_time_ms, (end_time - start_time) * 1000, metrics.cache_misses
            )
        
        metrics.hit_rate = metrics.cache_hits / metrics.total_requests
        
        return value
    
    async def set(self, cache_name: str, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        if cache_name not in self.caches:
            logger.warning(f"Cache {cache_name} not found")
            return False
        
        cache = self.caches[cache_name]
        metrics = self.metrics[cache_name]
        
        start_time = time.perf_counter()
        success = await cache.set(key, value, ttl)
        end_time = time.perf_counter()
        
        # Update metrics
        if success:
            metrics.avg_write_time_ms = self._update_avg_time(
                metrics.avg_write_time_ms, (end_time - start_time) * 1000, 
                metrics.total_requests + 1
            )
        
        return success

    async def delete(self, cache_name: str, key: str) -> bool:
        """Delete value from cache."""
        if cache_name not in self.caches:
            return False

        cache = self.caches[cache_name]
        return await cache.delete(key)

    async def clear(self, cache_name: str) -> bool:
        """Clear all values from cache."""
        if cache_name not in self.caches:
            return False

        cache = self.caches[cache_name]
        return await cache.clear()

    async def warm_cache(self, cache_name: str, warm_data: Dict[str, Any]):
        """Warm up cache with frequently accessed data."""
        if cache_name not in self.caches:
            logger.warning(f"Cache {cache_name} not found")
            return

        logger.info(f"Warming up cache {cache_name} with {len(warm_data)} items")

        cache = self.caches[cache_name]
        for key, value in warm_data.items():
            await cache.set(key, value)

        logger.info(f"Cache {cache_name} warmed up successfully")

    def get_metrics(self, cache_name: Optional[str] = None) -> Union[CacheMetrics, Dict[str, CacheMetrics]]:
        """Get cache metrics."""
        if cache_name:
            return self.metrics.get(cache_name)
        return self.metrics.copy()

    async def optimize_all_caches(self):
        """Optimize all caches based on current metrics."""
        logger.info("Optimizing all caches...")

        for cache_name, cache in self.caches.items():
            await self._optimize_cache(cache_name, cache)

        logger.info("Cache optimization completed")

    async def _optimize_cache(self, cache_name: str, cache: 'SmartCache'):
        """Optimize a specific cache."""
        metrics = self.metrics[cache_name]

        # Analyze hit rate and adjust cache size
        if metrics.hit_rate < 0.5 and metrics.total_requests > 1000:
            # Low hit rate - consider increasing cache size
            new_size = min(cache.config.max_size * 2, 100000)
            logger.info(f"Increasing {cache_name} cache size to {new_size}")
            await cache.resize(new_size)

        elif metrics.hit_rate > 0.9 and cache.current_size < cache.config.max_size * 0.5:
            # High hit rate but low utilization - consider decreasing size
            new_size = max(cache.config.max_size // 2, 1000)
            logger.info(f"Decreasing {cache_name} cache size to {new_size}")
            await cache.resize(new_size)

        # Optimize TTL based on access patterns
        await self._optimize_ttl(cache_name, cache, metrics)

        # Clean up expired entries
        await cache.cleanup_expired()

    async def _optimize_ttl(self, cache_name: str, cache: 'SmartCache', metrics: CacheMetrics):
        """Optimize TTL based on access patterns."""
        # This is a simplified optimization - in production, you'd analyze
        # access patterns more thoroughly

        if metrics.hit_rate > 0.8:
            # High hit rate - data is being reused, consider longer TTL
            new_ttl = min(cache.config.ttl_seconds * 1.5, 7200)  # Max 2 hours
            cache.config.ttl_seconds = int(new_ttl)
        elif metrics.hit_rate < 0.3:
            # Low hit rate - data might be stale, consider shorter TTL
            new_ttl = max(cache.config.ttl_seconds * 0.7, 30)  # Min 30 seconds
            cache.config.ttl_seconds = int(new_ttl)

    async def _optimization_loop(self):
        """Background optimization loop."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self.optimize_all_caches()
                await self._update_cache_metrics()
            except Exception as e:
                logger.error(f"Cache optimization loop error: {e}")

    async def _update_cache_metrics(self):
        """Update cache size and memory metrics."""
        for cache_name, cache in self.caches.items():
            metrics = self.metrics[cache_name]
            metrics.current_size = await cache.size()
            metrics.memory_usage_mb = await cache.memory_usage()

    def _update_avg_time(self, current_avg: float, new_time: float, count: int) -> float:
        """Update running average time."""
        if count <= 1:
            return new_time
        return ((current_avg * (count - 1)) + new_time) / count

    async def shutdown(self):
        """Shutdown cache optimizer."""
        logger.info("Shutting down cache optimizer...")

        for cache in self.caches.values():
            await cache.shutdown()

        if self.redis_client:
            await self.redis_client.close()

        logger.info("Cache optimizer shutdown completed")


class SmartCache:
    """
    Smart cache implementation with multiple backends and optimization features.
    """

    def __init__(self, config: CacheConfig, redis_client: Optional[redis.Redis] = None):
        self.config = config
        self.redis_client = redis_client
        self.memory_cache: Dict[str, Any] = {}
        self.access_times: Dict[str, float] = {}
        self.access_counts: Dict[str, int] = {}
        self.expiry_times: Dict[str, float] = {}
        self.current_size = 0

    async def initialize(self):
        """Initialize the cache."""
        logger.info(f"Initializing cache: {self.config.name}")

        if self.config.cache_type == CacheType.REDIS and not self.redis_client:
            logger.warning(f"Redis client not available for cache {self.config.name}, falling back to memory")
            self.config.cache_type = CacheType.MEMORY

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        # Check expiry first
        if await self._is_expired(key):
            await self.delete(key)
            return None

        value = None

        # Try memory cache first (for hybrid and memory caches)
        if self.config.cache_type in [CacheType.MEMORY, CacheType.HYBRID]:
            value = self.memory_cache.get(key)

        # Try Redis cache if not found in memory
        if value is None and self.config.cache_type in [CacheType.REDIS, CacheType.HYBRID]:
            if self.redis_client:
                try:
                    redis_value = await self.redis_client.get(f"{self.config.name}:{key}")
                    if redis_value:
                        value = self._deserialize(redis_value)

                        # Store in memory cache for hybrid mode
                        if self.config.cache_type == CacheType.HYBRID:
                            await self._store_in_memory(key, value)
                except Exception as e:
                    logger.error(f"Redis get error: {e}")

        # Update access statistics
        if value is not None:
            self._update_access_stats(key)

        return value

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        ttl = ttl or self.config.ttl_seconds
        expiry_time = time.time() + ttl

        try:
            # Store in memory cache
            if self.config.cache_type in [CacheType.MEMORY, CacheType.HYBRID]:
                await self._store_in_memory(key, value, expiry_time)

            # Store in Redis cache
            if self.config.cache_type in [CacheType.REDIS, CacheType.HYBRID]:
                if self.redis_client:
                    serialized_value = self._serialize(value)
                    await self.redis_client.setex(f"{self.config.name}:{key}", ttl, serialized_value)

            return True

        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            # Delete from memory cache
            if self.config.cache_type in [CacheType.MEMORY, CacheType.HYBRID]:
                self.memory_cache.pop(key, None)
                self.access_times.pop(key, None)
                self.access_counts.pop(key, None)
                self.expiry_times.pop(key, None)
                self.current_size = max(0, self.current_size - 1)

            # Delete from Redis cache
            if self.config.cache_type in [CacheType.REDIS, CacheType.HYBRID]:
                if self.redis_client:
                    await self.redis_client.delete(f"{self.config.name}:{key}")

            return True

        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False

    async def clear(self) -> bool:
        """Clear all values from cache."""
        try:
            # Clear memory cache
            if self.config.cache_type in [CacheType.MEMORY, CacheType.HYBRID]:
                self.memory_cache.clear()
                self.access_times.clear()
                self.access_counts.clear()
                self.expiry_times.clear()
                self.current_size = 0

            # Clear Redis cache
            if self.config.cache_type in [CacheType.REDIS, CacheType.HYBRID]:
                if self.redis_client:
                    pattern = f"{self.config.name}:*"
                    keys = await self.redis_client.keys(pattern)
                    if keys:
                        await self.redis_client.delete(*keys)

            return True

        except Exception as e:
            logger.error(f"Cache clear error: {e}")
            return False

    async def size(self) -> int:
        """Get current cache size."""
        if self.config.cache_type == CacheType.MEMORY:
            return len(self.memory_cache)
        elif self.config.cache_type == CacheType.REDIS:
            if self.redis_client:
                pattern = f"{self.config.name}:*"
                keys = await self.redis_client.keys(pattern)
                return len(keys)
        else:  # HYBRID
            memory_size = len(self.memory_cache)
            redis_size = 0
            if self.redis_client:
                pattern = f"{self.config.name}:*"
                keys = await self.redis_client.keys(pattern)
                redis_size = len(keys)
            return max(memory_size, redis_size)

        return 0

    async def memory_usage(self) -> float:
        """Get memory usage in MB."""
        total_size = 0

        # Calculate memory cache size
        for key, value in self.memory_cache.items():
            try:
                total_size += len(pickle.dumps(value))
                total_size += len(key.encode('utf-8'))
            except Exception:
                total_size += 1024  # Estimate 1KB per item if serialization fails

        return total_size / (1024 * 1024)  # Convert to MB

    async def resize(self, new_max_size: int):
        """Resize cache to new maximum size."""
        old_size = self.config.max_size
        self.config.max_size = new_max_size

        # If reducing size, evict items
        if new_max_size < old_size:
            await self._evict_to_size(new_max_size)

        logger.info(f"Cache {self.config.name} resized from {old_size} to {new_max_size}")

    async def cleanup_expired(self):
        """Clean up expired entries."""
        current_time = time.time()
        expired_keys = []

        for key, expiry_time in self.expiry_times.items():
            if expiry_time <= current_time:
                expired_keys.append(key)

        for key in expired_keys:
            await self.delete(key)

        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired entries from {self.config.name}")

    async def _store_in_memory(self, key: str, value: Any, expiry_time: Optional[float] = None):
        """Store value in memory cache with eviction if needed."""
        # Check if we need to evict
        if len(self.memory_cache) >= self.config.max_size:
            await self._evict_items(1)

        self.memory_cache[key] = value
        self.expiry_times[key] = expiry_time or (time.time() + self.config.ttl_seconds)
        self.current_size = len(self.memory_cache)

        # Initialize access stats
        if key not in self.access_counts:
            self.access_counts[key] = 0
        self._update_access_stats(key)

    async def _evict_items(self, count: int):
        """Evict items based on eviction policy."""
        if not self.memory_cache:
            return

        keys_to_evict = []

        if self.config.eviction_policy == EvictionPolicy.LRU:
            # Evict least recently used
            sorted_keys = sorted(self.access_times.items(), key=lambda x: x[1])
            keys_to_evict = [key for key, _ in sorted_keys[:count]]

        elif self.config.eviction_policy == EvictionPolicy.LFU:
            # Evict least frequently used
            sorted_keys = sorted(self.access_counts.items(), key=lambda x: x[1])
            keys_to_evict = [key for key, _ in sorted_keys[:count]]

        elif self.config.eviction_policy == EvictionPolicy.TTL:
            # Evict items closest to expiry
            current_time = time.time()
            sorted_keys = sorted(self.expiry_times.items(), key=lambda x: x[1])
            keys_to_evict = [key for key, _ in sorted_keys[:count]]

        elif self.config.eviction_policy == EvictionPolicy.FIFO:
            # Evict oldest items (first inserted)
            keys_to_evict = list(self.memory_cache.keys())[:count]

        # Remove evicted items
        for key in keys_to_evict:
            self.memory_cache.pop(key, None)
            self.access_times.pop(key, None)
            self.access_counts.pop(key, None)
            self.expiry_times.pop(key, None)

        self.current_size = len(self.memory_cache)

    async def _evict_to_size(self, target_size: int):
        """Evict items until cache is at target size."""
        current_size = len(self.memory_cache)
        if current_size <= target_size:
            return

        items_to_evict = current_size - target_size
        await self._evict_items(items_to_evict)

    async def _is_expired(self, key: str) -> bool:
        """Check if key is expired."""
        if key not in self.expiry_times:
            return False

        return time.time() > self.expiry_times[key]

    def _update_access_stats(self, key: str):
        """Update access statistics for key."""
        current_time = time.time()
        self.access_times[key] = current_time
        self.access_counts[key] = self.access_counts.get(key, 0) + 1

    def _serialize(self, value: Any) -> bytes:
        """Serialize value for storage."""
        if self.config.enable_compression:
            # Could add compression here
            pass

        if self.config.enable_encryption:
            # Could add encryption here
            pass

        if isinstance(value, (str, int, float, bool)):
            return json.dumps(value).encode('utf-8')
        else:
            return pickle.dumps(value)

    def _deserialize(self, data: bytes) -> Any:
        """Deserialize value from storage."""
        try:
            # Try JSON first (for simple types)
            return json.loads(data.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # Fall back to pickle
            return pickle.loads(data)

    async def shutdown(self):
        """Shutdown cache."""
        await self.clear()
        logger.info(f"Cache {self.config.name} shutdown completed")
