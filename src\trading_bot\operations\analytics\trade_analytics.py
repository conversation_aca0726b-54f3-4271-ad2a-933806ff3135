"""
Trade Analytics System

Deep analysis of trading performance with attribution analysis,
pattern recognition, and performance optimization insights.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
import pandas as pd
import numpy as np
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import json

from ...core.logger import get_logger
from ...data.models import Trade, Position

logger = get_logger(__name__)

@dataclass
class TradeMetrics:
    """Comprehensive trade metrics"""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    avg_trade_duration: timedelta
    total_pnl: float
    total_return: float

@dataclass
class AttributionAnalysis:
    """Performance attribution analysis"""
    strategy_attribution: Dict[str, float]
    sector_attribution: Dict[str, float]
    time_attribution: Dict[str, float]
    market_attribution: Dict[str, float]
    alpha: float
    beta: float
    tracking_error: float

@dataclass
class TradePattern:
    """Identified trading pattern"""
    pattern_id: str
    pattern_type: str
    description: str
    frequency: int
    success_rate: float
    avg_profit: float
    confidence: float
    conditions: Dict[str, Any]

class TradeAnalytics:
    """Deep analysis of trading performance and patterns"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.trades_cache = []
        self.analysis_cache = {}
        self.patterns_cache = []
        
        # Analysis configuration
        self.config = {
            'min_trades_for_analysis': 10,
            'pattern_min_frequency': 5,
            'confidence_threshold': 0.7,
            'lookback_periods': [7, 30, 90, 365]
        }
        
    async def analyze_trading_performance(self, 
                                        start_date: Optional[datetime] = None,
                                        end_date: Optional[datetime] = None,
                                        strategy: Optional[str] = None) -> Dict[str, Any]:
        """Comprehensive trading performance analysis"""
        
        logger.info("Starting comprehensive trading performance analysis")
        
        # Get trades for analysis
        trades = await self._get_trades(start_date, end_date, strategy)
        
        if len(trades) < self.config['min_trades_for_analysis']:
            return {'error': f'Insufficient trades for analysis (minimum {self.config["min_trades_for_analysis"]})'}
        
        # Calculate basic metrics
        basic_metrics = await self._calculate_basic_metrics(trades)
        
        # Performance attribution
        attribution = await self._calculate_attribution(trades)
        
        # Risk metrics
        risk_metrics = await self._calculate_risk_metrics(trades)
        
        # Time-based analysis
        time_analysis = await self._analyze_time_patterns(trades)
        
        # Strategy analysis
        strategy_analysis = await self._analyze_strategy_performance(trades)
        
        # Market condition analysis
        market_analysis = await self._analyze_market_conditions(trades)
        
        # Trade distribution analysis
        distribution_analysis = await self._analyze_trade_distribution(trades)
        
        return {
            'analysis_period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None,
                'total_trades': len(trades)
            },
            'basic_metrics': basic_metrics,
            'attribution': attribution,
            'risk_metrics': risk_metrics,
            'time_analysis': time_analysis,
            'strategy_analysis': strategy_analysis,
            'market_analysis': market_analysis,
            'distribution_analysis': distribution_analysis
        }
    
    async def identify_trading_patterns(self, lookback_days: int = 90) -> List[TradePattern]:
        """Identify recurring trading patterns"""
        
        logger.info(f"Identifying trading patterns over {lookback_days} days")
        
        trades = await self._get_trades(
            start_date=datetime.now() - timedelta(days=lookback_days)
        )
        
        patterns = []
        
        # Time-based patterns
        patterns.extend(await self._identify_time_patterns(trades))
        
        # Volatility patterns
        patterns.extend(await self._identify_volatility_patterns(trades))
        
        # Momentum patterns
        patterns.extend(await self._identify_momentum_patterns(trades))
        
        # Mean reversion patterns
        patterns.extend(await self._identify_mean_reversion_patterns(trades))
        
        # Sector rotation patterns
        patterns.extend(await self._identify_sector_patterns(trades))
        
        # Filter patterns by confidence and frequency
        filtered_patterns = [
            pattern for pattern in patterns
            if (pattern.confidence >= self.config['confidence_threshold'] and
                pattern.frequency >= self.config['pattern_min_frequency'])
        ]
        
        # Sort by success rate and confidence
        filtered_patterns.sort(
            key=lambda p: (p.success_rate * p.confidence),
            reverse=True
        )
        
        self.patterns_cache = filtered_patterns
        return filtered_patterns
    
    async def generate_performance_report(self, period: str = "monthly") -> Dict[str, Any]:
        """Generate detailed performance report"""
        
        # Determine date range
        if period == "daily":
            start_date = datetime.now() - timedelta(days=1)
        elif period == "weekly":
            start_date = datetime.now() - timedelta(days=7)
        elif period == "monthly":
            start_date = datetime.now() - timedelta(days=30)
        elif period == "quarterly":
            start_date = datetime.now() - timedelta(days=90)
        elif period == "yearly":
            start_date = datetime.now() - timedelta(days=365)
        else:
            start_date = datetime.now() - timedelta(days=30)
        
        # Get comprehensive analysis
        analysis = await self.analyze_trading_performance(start_date=start_date)
        
        # Add period-specific insights
        insights = await self._generate_insights(analysis)
        
        # Add recommendations
        recommendations = await self._generate_recommendations(analysis)
        
        # Add visualizations data
        charts_data = await self._prepare_chart_data(start_date)
        
        return {
            'report_period': period,
            'generated_at': datetime.now().isoformat(),
            'analysis': analysis,
            'insights': insights,
            'recommendations': recommendations,
            'charts_data': charts_data
        }
    
    async def _get_trades(self, 
                         start_date: Optional[datetime] = None,
                         end_date: Optional[datetime] = None,
                         strategy: Optional[str] = None) -> List[Trade]:
        """Get trades for analysis"""
        
        # This would query the actual database
        # For now, return cached trades or generate sample data
        
        if not self.trades_cache:
            self.trades_cache = await self._generate_sample_trades()
        
        trades = self.trades_cache.copy()
        
        # Apply filters
        if start_date:
            trades = [t for t in trades if t.entry_time >= start_date]
        
        if end_date:
            trades = [t for t in trades if t.entry_time <= end_date]
        
        if strategy:
            trades = [t for t in trades if t.strategy == strategy]
        
        return trades
    
    async def _generate_sample_trades(self) -> List[Trade]:
        """Generate sample trades for demonstration"""
        
        trades = []
        strategies = ['momentum', 'mean_reversion', 'breakout', 'arbitrage']
        symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'META', 'NVDA']
        
        for i in range(200):
            entry_time = datetime.now() - timedelta(days=np.random.randint(1, 365))
            exit_time = entry_time + timedelta(hours=np.random.randint(1, 48))
            
            entry_price = np.random.uniform(50, 500)
            price_change = np.random.normal(0, 0.02)  # 2% volatility
            exit_price = entry_price * (1 + price_change)
            
            quantity = np.random.randint(10, 1000)
            profit_loss = (exit_price - entry_price) * quantity
            
            trade = Trade(
                id=f"trade_{i:04d}",
                symbol=np.random.choice(symbols),
                side='buy',  # Simplified
                quantity=quantity,
                entry_price=entry_price,
                exit_price=exit_price,
                entry_time=entry_time,
                exit_time=exit_time,
                strategy=np.random.choice(strategies),
                profit_loss=profit_loss
            )
            
            trades.append(trade)
        
        return trades
    
    async def _calculate_basic_metrics(self, trades: List[Trade]) -> TradeMetrics:
        """Calculate basic trading metrics"""
        
        if not trades:
            return TradeMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, timedelta(0), 0, 0)
        
        # Basic counts
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t.profit_loss > 0])
        losing_trades = len([t for t in trades if t.profit_loss < 0])
        
        # Win rate
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Average win/loss
        wins = [t.profit_loss for t in trades if t.profit_loss > 0]
        losses = [t.profit_loss for t in trades if t.profit_loss < 0]
        
        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 0
        
        # Profit factor
        gross_profit = sum(wins) if wins else 0
        gross_loss = abs(sum(losses)) if losses else 1
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        
        # Returns for ratio calculations
        returns = [t.profit_loss for t in trades]
        total_pnl = sum(returns)
        
        # Sharpe ratio (simplified)
        if len(returns) > 1:
            returns_std = np.std(returns)
            sharpe_ratio = np.mean(returns) / returns_std if returns_std > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Sortino ratio (downside deviation)
        downside_returns = [r for r in returns if r < 0]
        if downside_returns:
            downside_std = np.std(downside_returns)
            sortino_ratio = np.mean(returns) / downside_std if downside_std > 0 else 0
        else:
            sortino_ratio = sharpe_ratio
        
        # Max drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(min(drawdowns)) if len(drawdowns) > 0 else 0
        
        # Consecutive wins/losses
        max_consecutive_wins = self._calculate_max_consecutive(trades, True)
        max_consecutive_losses = self._calculate_max_consecutive(trades, False)
        
        # Average trade duration
        durations = [t.exit_time - t.entry_time for t in trades if t.exit_time]
        avg_duration = sum(durations, timedelta(0)) / len(durations) if durations else timedelta(0)
        
        # Total return (simplified)
        initial_capital = 100000  # Assume $100k starting capital
        total_return = total_pnl / initial_capital if initial_capital > 0 else 0
        
        return TradeMetrics(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            max_consecutive_wins=max_consecutive_wins,
            max_consecutive_losses=max_consecutive_losses,
            avg_trade_duration=avg_duration,
            total_pnl=total_pnl,
            total_return=total_return
        )
    
    def _calculate_max_consecutive(self, trades: List[Trade], wins: bool) -> int:
        """Calculate maximum consecutive wins or losses"""
        
        if not trades:
            return 0
        
        max_consecutive = 0
        current_consecutive = 0
        
        for trade in sorted(trades, key=lambda t: t.entry_time):
            is_win = trade.profit_loss > 0
            
            if (wins and is_win) or (not wins and not is_win):
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    async def _calculate_attribution(self, trades: List[Trade]) -> AttributionAnalysis:
        """Calculate performance attribution"""
        
        # Strategy attribution
        strategy_pnl = defaultdict(float)
        for trade in trades:
            strategy_pnl[trade.strategy] += trade.profit_loss
        
        total_pnl = sum(strategy_pnl.values())
        strategy_attribution = {
            strategy: pnl / total_pnl if total_pnl != 0 else 0
            for strategy, pnl in strategy_pnl.items()
        }
        
        # Sector attribution (simplified)
        sector_map = {
            'AAPL': 'Technology', 'GOOGL': 'Technology', 'MSFT': 'Technology',
            'TSLA': 'Automotive', 'AMZN': 'Consumer', 'META': 'Technology',
            'NVDA': 'Technology'
        }
        
        sector_pnl = defaultdict(float)
        for trade in trades:
            sector = sector_map.get(trade.symbol, 'Other')
            sector_pnl[sector] += trade.profit_loss
        
        sector_attribution = {
            sector: pnl / total_pnl if total_pnl != 0 else 0
            for sector, pnl in sector_pnl.items()
        }
        
        # Time attribution (by hour)
        time_pnl = defaultdict(float)
        for trade in trades:
            hour = trade.entry_time.hour
            time_pnl[f"hour_{hour}"] += trade.profit_loss
        
        time_attribution = {
            time_period: pnl / total_pnl if total_pnl != 0 else 0
            for time_period, pnl in time_pnl.items()
        }
        
        # Market attribution (simplified)
        market_attribution = {
            'market_timing': 0.6,  # 60% from timing
            'security_selection': 0.4  # 40% from selection
        }
        
        # Alpha and Beta (simplified calculations)
        returns = [trade.profit_loss for trade in trades]
        market_returns = np.random.normal(0.001, 0.02, len(returns))  # Simulated market
        
        if len(returns) > 1:
            beta, alpha, r_value, p_value, std_err = stats.linregress(market_returns, returns)
            tracking_error = np.std(np.array(returns) - beta * np.array(market_returns))
        else:
            alpha, beta, tracking_error = 0, 1, 0
        
        return AttributionAnalysis(
            strategy_attribution=strategy_attribution,
            sector_attribution=sector_attribution,
            time_attribution=time_attribution,
            market_attribution=market_attribution,
            alpha=alpha,
            beta=beta,
            tracking_error=tracking_error
        )
    
    async def _calculate_risk_metrics(self, trades: List[Trade]) -> Dict[str, float]:
        """Calculate risk metrics"""
        
        returns = [trade.profit_loss for trade in trades]
        
        if len(returns) < 2:
            return {'var_95': 0, 'var_99': 0, 'cvar_95': 0, 'volatility': 0}
        
        # Value at Risk (VaR)
        var_95 = np.percentile(returns, 5)  # 95% VaR
        var_99 = np.percentile(returns, 1)  # 99% VaR
        
        # Conditional VaR (Expected Shortfall)
        cvar_95 = np.mean([r for r in returns if r <= var_95])
        
        # Volatility
        volatility = np.std(returns)
        
        return {
            'var_95': var_95,
            'var_99': var_99,
            'cvar_95': cvar_95,
            'volatility': volatility
        }
    
    async def _analyze_time_patterns(self, trades: List[Trade]) -> Dict[str, Any]:
        """Analyze time-based trading patterns"""
        
        # Performance by hour of day
        hourly_pnl = defaultdict(list)
        for trade in trades:
            hour = trade.entry_time.hour
            hourly_pnl[hour].append(trade.profit_loss)
        
        hourly_performance = {
            hour: {
                'avg_pnl': np.mean(pnls),
                'trade_count': len(pnls),
                'win_rate': len([p for p in pnls if p > 0]) / len(pnls) if pnls else 0
            }
            for hour, pnls in hourly_pnl.items()
        }
        
        # Performance by day of week
        daily_pnl = defaultdict(list)
        for trade in trades:
            day = trade.entry_time.weekday()  # 0=Monday, 6=Sunday
            daily_pnl[day].append(trade.profit_loss)
        
        daily_performance = {
            day: {
                'avg_pnl': np.mean(pnls),
                'trade_count': len(pnls),
                'win_rate': len([p for p in pnls if p > 0]) / len(pnls) if pnls else 0
            }
            for day, pnls in daily_pnl.items()
        }
        
        # Best and worst performing times
        best_hour = max(hourly_performance.items(), key=lambda x: x[1]['avg_pnl'])[0] if hourly_performance else None
        worst_hour = min(hourly_performance.items(), key=lambda x: x[1]['avg_pnl'])[0] if hourly_performance else None
        
        return {
            'hourly_performance': hourly_performance,
            'daily_performance': daily_performance,
            'best_trading_hour': best_hour,
            'worst_trading_hour': worst_hour
        }
    
    async def _analyze_strategy_performance(self, trades: List[Trade]) -> Dict[str, Any]:
        """Analyze performance by strategy"""
        
        strategy_metrics = {}
        
        for strategy in set(trade.strategy for trade in trades):
            strategy_trades = [t for t in trades if t.strategy == strategy]
            
            if strategy_trades:
                metrics = await self._calculate_basic_metrics(strategy_trades)
                strategy_metrics[strategy] = {
                    'total_trades': metrics.total_trades,
                    'win_rate': metrics.win_rate,
                    'total_pnl': metrics.total_pnl,
                    'avg_pnl_per_trade': metrics.total_pnl / metrics.total_trades,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'max_drawdown': metrics.max_drawdown
                }
        
        # Rank strategies by Sharpe ratio
        ranked_strategies = sorted(
            strategy_metrics.items(),
            key=lambda x: x[1]['sharpe_ratio'],
            reverse=True
        )
        
        return {
            'strategy_metrics': strategy_metrics,
            'best_strategy': ranked_strategies[0][0] if ranked_strategies else None,
            'worst_strategy': ranked_strategies[-1][0] if ranked_strategies else None
        }
    
    async def _analyze_market_conditions(self, trades: List[Trade]) -> Dict[str, Any]:
        """Analyze performance under different market conditions"""
        
        # This would analyze trades based on market volatility, trend, etc.
        # For now, simplified implementation
        
        # Simulate market conditions
        conditions = ['bull_market', 'bear_market', 'sideways_market', 'high_volatility']
        
        condition_performance = {}
        for condition in conditions:
            # Randomly assign trades to conditions for demonstration
            condition_trades = np.random.choice(trades, size=len(trades)//4, replace=False)
            
            if condition_trades:
                total_pnl = sum(trade.profit_loss for trade in condition_trades)
                win_rate = len([t for t in condition_trades if t.profit_loss > 0]) / len(condition_trades)
                
                condition_performance[condition] = {
                    'total_pnl': total_pnl,
                    'trade_count': len(condition_trades),
                    'win_rate': win_rate,
                    'avg_pnl_per_trade': total_pnl / len(condition_trades)
                }
        
        return {
            'market_condition_performance': condition_performance,
            'best_market_condition': max(condition_performance.items(), key=lambda x: x[1]['avg_pnl_per_trade'])[0] if condition_performance else None
        }
    
    async def _analyze_trade_distribution(self, trades: List[Trade]) -> Dict[str, Any]:
        """Analyze distribution of trade outcomes"""
        
        returns = [trade.profit_loss for trade in trades]
        
        if not returns:
            return {}
        
        return {
            'mean_return': np.mean(returns),
            'median_return': np.median(returns),
            'std_return': np.std(returns),
            'skewness': stats.skew(returns),
            'kurtosis': stats.kurtosis(returns),
            'percentiles': {
                '5th': np.percentile(returns, 5),
                '25th': np.percentile(returns, 25),
                '75th': np.percentile(returns, 75),
                '95th': np.percentile(returns, 95)
            }
        }
