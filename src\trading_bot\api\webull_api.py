"""Unified Webull API client combining all functionality."""

from typing import Any, Callable, Dict, List, Optional, Union

from ..core.config import settings
from ..models.account import AccountSummary, Portfolio
from ..models.enums import Interval, OrderStatus
from ..models.market import Bar, MarketHours, Quote, Symbol
from ..models.orders import Order, OrderRequest, Position
from ..utils.cache import cache_manager
from ..utils.logger import get_structured_logger
from .paper_trading import PaperTradingClient
from .trading import TradingClient
from .websocket import WebSocketClient

logger = get_structured_logger(__name__)


class WebullAPI:
    """
    Unified Webull API client with all functionality.
    
    This is the main entry point for all Webull API operations including:
    - Authentication and session management
    - Market data (quotes, bars, news)
    - Trading operations (orders, positions, portfolio)
    - Paper trading simulation
    - Real-time WebSocket data streams
    """
    
    def __init__(self, paper_trading: Optional[bool] = None):
        """
        Initialize the unified API client.
        
        Args:
            paper_trading: Force paper trading mode (uses config if None)
        """
        self.paper_trading = paper_trading if paper_trading is not None else settings.webull.paper_trading
        
        # Initialize appropriate client based on trading mode
        if self.paper_trading:
            self.client = PaperTradingClient()
            logger.info("Initialized in paper trading mode")
        else:
            self.client = TradingClient()
            logger.info("Initialized in live trading mode")
        
        # WebSocket client for real-time data
        self.websocket = WebSocketClient()
        
        # Track initialization state
        self.is_initialized = False
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def initialize(self) -> None:
        """Initialize the API client and all subsystems."""
        try:
            # Start HTTP session
            await self.client.start_session()
            
            # Initialize cache manager
            await cache_manager.initialize()
            
            self.is_initialized = True
            
            logger.info(
                "API client initialized successfully",
                extra={
                    "paper_trading": self.paper_trading,
                    "cache_enabled": await cache_manager.health_check(),
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to initialize API client: {e}")
            raise
    
    async def close(self) -> None:
        """Close the API client and cleanup resources."""
        try:
            # Close WebSocket connection
            if self.websocket.is_connected:
                await self.websocket.disconnect()
            
            # Close HTTP session
            await self.client.close_session()
            
            # Close cache manager
            await cache_manager.close()
            
            self.is_initialized = False
            
            logger.info("API client closed successfully")
            
        except Exception as e:
            logger.warning(f"Error during cleanup: {e}")
    
    # Authentication methods
    async def login(self, username: Optional[str] = None, password: Optional[str] = None) -> bool:
        """
        Login to Webull.
        
        Args:
            username: Webull username (uses config if None)
            password: Webull password (uses config if None)
            
        Returns:
            True if login successful
        """
        return await self.client.login(username, password)
    
    async def logout(self) -> bool:
        """Logout from Webull."""
        return await self.client.logout()
    
    async def verify_mfa(self, mfa_code: str) -> bool:
        """
        Verify MFA code.
        
        Args:
            mfa_code: MFA verification code
            
        Returns:
            True if verification successful
        """
        return await self.client.verify_mfa(mfa_code)
    
    @property
    def is_authenticated(self) -> bool:
        """Check if client is authenticated."""
        return self.client.is_authenticated
    
    # Market data methods
    async def get_quote(self, symbol: str, use_cache: bool = True) -> Quote:
        """Get real-time quote for a symbol."""
        return await self.client.get_quote(symbol, use_cache)
    
    async def get_quotes(self, symbols: List[str], use_cache: bool = True) -> Dict[str, Quote]:
        """Get real-time quotes for multiple symbols."""
        return await self.client.get_quotes(symbols, use_cache)
    
    async def get_bars(
        self,
        symbol: str,
        interval: Union[str, Interval] = Interval.ONE_MINUTE,
        count: int = 100,
        extend_trading: bool = False,
        use_cache: bool = True,
    ) -> List[Bar]:
        """Get historical bars for a symbol."""
        return await self.client.get_bars(symbol, interval, count, extend_trading, use_cache)
    
    async def search_stocks(self, query: str, limit: int = 20) -> List[Symbol]:
        """Search for stocks by keyword."""
        return await self.client.search_stocks(query, limit)
    
    async def get_market_hours(self, use_cache: bool = True) -> MarketHours:
        """Get current market hours information."""
        return await self.client.get_market_hours(use_cache)
    
    async def get_movers(
        self,
        direction: str = "up",
        change_type: str = "percent",
        limit: int = 20,
    ) -> List[Dict]:
        """Get market movers (top gainers/losers)."""
        return await self.client.get_movers(direction, change_type, limit)
    
    async def get_news(
        self,
        symbol: Optional[str] = None,
        limit: int = 20,
        use_cache: bool = True,
    ) -> List[Dict]:
        """Get news articles."""
        return await self.client.get_news(symbol, limit, use_cache)
    
    # Trading methods
    async def get_account_summary(self, use_cache: bool = True) -> AccountSummary:
        """Get complete account summary."""
        return await self.client.get_account_summary(use_cache)
    
    async def get_portfolio(self, use_cache: bool = True) -> Portfolio:
        """Get complete portfolio information."""
        return await self.client.get_portfolio(use_cache)
    
    async def get_positions(self, use_cache: bool = True) -> List[Position]:
        """Get current positions."""
        return await self.client.get_positions(use_cache)
    
    async def get_orders(
        self,
        status: Optional[OrderStatus] = None,
        use_cache: bool = True,
    ) -> List[Order]:
        """Get orders."""
        return await self.client.get_orders(status, use_cache)
    
    async def place_order(self, order_request: OrderRequest) -> Order:
        """Place a new order."""
        return await self.client.place_order(order_request)
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an existing order."""
        return await self.client.cancel_order(order_id)
    
    async def modify_order(
        self,
        order_id: str,
        quantity: Optional[int] = None,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
    ) -> Order:
        """Modify an existing order."""
        return await self.client.modify_order(order_id, quantity, price, stop_price)
    
    async def get_buying_power(self) -> Dict[str, float]:
        """Get current buying power."""
        return await self.client.get_buying_power()
    
    # Paper trading specific methods (only available in paper mode)
    async def reset_paper_account(self, initial_balance: Optional[float] = None) -> bool:
        """Reset paper trading account (paper trading only)."""
        if not self.paper_trading:
            raise ValueError("Reset account is only available in paper trading mode")
        return await self.client.reset_account(initial_balance)
    
    async def get_paper_performance(self) -> Dict[str, float]:
        """Get paper trading performance metrics (paper trading only)."""
        if not self.paper_trading:
            raise ValueError("Paper performance is only available in paper trading mode")
        return await self.client.get_paper_performance()
    
    async def simulate_order_scenarios(
        self,
        order_request: OrderRequest,
        scenarios: List[Dict[str, float]],
    ) -> List[Dict[str, any]]:
        """Simulate order execution scenarios (paper trading only)."""
        if not self.paper_trading:
            raise ValueError("Order simulation is only available in paper trading mode")
        return await self.client.simulate_order_scenarios(order_request, scenarios)
    
    # WebSocket methods
    async def connect_websocket(self, auto_reconnect: bool = True) -> bool:
        """Connect to WebSocket for real-time data."""
        return await self.websocket.connect(auto_reconnect)
    
    async def disconnect_websocket(self) -> None:
        """Disconnect from WebSocket."""
        await self.websocket.disconnect()
    
    async def subscribe_quotes(self, symbols: List[str], callback: Optional[Callable] = None) -> bool:
        """Subscribe to real-time quotes."""
        return await self.websocket.subscribe_quotes(symbols, callback)
    
    async def unsubscribe_quotes(self, symbols: List[str]) -> bool:
        """Unsubscribe from real-time quotes."""
        return await self.websocket.unsubscribe_quotes(symbols)
    
    @property
    def is_websocket_connected(self) -> bool:
        """Check if WebSocket is connected."""
        return self.websocket.is_connected
    
    # Utility methods
    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        try:
            # Check HTTP API
            api_health = await self.client.health_check()
            
            # Check cache
            cache_health = await cache_manager.health_check()
            
            # Check WebSocket
            ws_health = self.websocket.is_connected
            
            # Check rate limits
            rate_limits = await self.client.get_rate_limit_status()
            
            health = {
                "status": "healthy" if api_health.get("status") == "healthy" else "unhealthy",
                "api": api_health,
                "cache": cache_health,
                "websocket": ws_health,
                "rate_limits": rate_limits,
                "paper_trading": self.paper_trading,
                "authenticated": self.is_authenticated,
                "initialized": self.is_initialized,
            }
            
            logger.info("Health check completed", extra=health)
            
            return health
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "paper_trading": self.paper_trading,
                "authenticated": self.is_authenticated,
                "initialized": self.is_initialized,
            }
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return await cache_manager.get_stats()
    
    async def clear_cache(self, cache_type: Optional[str] = None) -> int:
        """Clear cache entries."""
        if cache_type:
            return await cache_manager.clear_type(cache_type)
        else:
            # Clear all cache types
            total_cleared = 0
            for cache_type in ["quote", "bars", "account", "positions", "orders"]:
                total_cleared += await cache_manager.clear_type(cache_type)
            return total_cleared
