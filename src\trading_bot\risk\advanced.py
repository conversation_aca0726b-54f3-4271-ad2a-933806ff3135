"""Advanced risk management features: <PERSON>, stress testing, and optimization."""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from scipy import stats
from scipy.optimize import minimize
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import Bar, Portfolio, Position, Symbol

logger = get_logger(__name__)


class MonteCarloSimulator:
    """Monte Carlo simulation for portfolio risk analysis."""
    
    def __init__(self):
        self.config = settings.risk
        self.num_simulations = 10000
        self.time_horizon_days = 252  # 1 year
        
    async def simulate_portfolio_returns(
        self,
        positions: Dict[str, Dict],
        num_simulations: Optional[int] = None,
        time_horizon: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Run Monte Carlo simulation for portfolio returns.
        
        Args:
            positions: Current portfolio positions
            num_simulations: Number of simulations to run
            time_horizon: Time horizon in days
            
        Returns:
            Simulation results with statistics
        """
        try:
            if not positions:
                return {"error": "No positions provided"}
            
            num_sims = num_simulations or self.num_simulations
            horizon = time_horizon or self.time_horizon_days
            
            # Get historical returns for all symbols
            returns_data = await self._get_historical_returns(positions, lookback_days=252)
            if returns_data.empty:
                return {"error": "Insufficient historical data"}
            
            # Calculate portfolio weights
            total_value = sum(pos["quantity"] * pos["current_price"] for pos in positions.values())
            weights = np.array([
                (pos["quantity"] * pos["current_price"]) / total_value 
                for pos in positions.values()
            ])
            
            # Calculate expected returns and covariance matrix
            mean_returns = returns_data.mean().values
            cov_matrix = returns_data.cov().values
            
            # Run Monte Carlo simulation
            portfolio_returns = []
            
            for _ in range(num_sims):
                # Generate random returns for each asset
                random_returns = np.random.multivariate_normal(
                    mean_returns * horizon,
                    cov_matrix * horizon,
                    size=1
                )[0]
                
                # Calculate portfolio return
                portfolio_return = np.dot(weights, random_returns)
                portfolio_returns.append(portfolio_return)
            
            portfolio_returns = np.array(portfolio_returns)
            
            # Calculate statistics
            results = {
                "num_simulations": num_sims,
                "time_horizon_days": horizon,
                "mean_return": float(np.mean(portfolio_returns)),
                "std_return": float(np.std(portfolio_returns)),
                "var_95": float(np.percentile(portfolio_returns, 5)),
                "var_99": float(np.percentile(portfolio_returns, 1)),
                "cvar_95": float(np.mean(portfolio_returns[portfolio_returns <= np.percentile(portfolio_returns, 5)])),
                "cvar_99": float(np.mean(portfolio_returns[portfolio_returns <= np.percentile(portfolio_returns, 1)])),
                "max_loss": float(np.min(portfolio_returns)),
                "max_gain": float(np.max(portfolio_returns)),
                "probability_of_loss": float(np.sum(portfolio_returns < 0) / num_sims),
                "probability_of_large_loss": float(np.sum(portfolio_returns < -0.1) / num_sims),  # >10% loss
                "percentiles": {
                    "1": float(np.percentile(portfolio_returns, 1)),
                    "5": float(np.percentile(portfolio_returns, 5)),
                    "10": float(np.percentile(portfolio_returns, 10)),
                    "25": float(np.percentile(portfolio_returns, 25)),
                    "50": float(np.percentile(portfolio_returns, 50)),
                    "75": float(np.percentile(portfolio_returns, 75)),
                    "90": float(np.percentile(portfolio_returns, 90)),
                    "95": float(np.percentile(portfolio_returns, 95)),
                    "99": float(np.percentile(portfolio_returns, 99))
                }
            }
            
            logger.info(f"Monte Carlo simulation completed: {num_sims} simulations over {horizon} days")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in Monte Carlo simulation: {e}")
            return {"error": str(e)}
    
    async def _get_historical_returns(
        self,
        positions: Dict[str, Dict],
        lookback_days: int = 252
    ) -> pd.DataFrame:
        """Get historical returns for portfolio symbols."""
        try:
            returns_data = {}
            cutoff_date = datetime.utcnow() - timedelta(days=lookback_days)
            
            async with get_postgres_session() as session:
                for symbol in positions.keys():
                    # Get symbol ID
                    stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                    result = await session.execute(stmt)
                    symbol_id = result.scalar_one_or_none()
                    
                    if not symbol_id:
                        continue
                    
                    # Get historical prices
                    stmt = (
                        select(Bar.timestamp, Bar.close)
                        .where(
                            and_(
                                Bar.symbol_id == symbol_id,
                                Bar.timeframe == "1d",
                                Bar.timestamp >= cutoff_date
                            )
                        )
                        .order_by(Bar.timestamp.asc())
                    )
                    result = await session.execute(stmt)
                    price_data = result.fetchall()
                    
                    if len(price_data) < 50:  # Need sufficient data
                        continue
                    
                    # Calculate returns
                    prices = [float(row[1]) for row in price_data]
                    returns = np.diff(np.log(prices))
                    
                    # Create date index
                    dates = [row[0] for row in price_data[1:]]  # Skip first date
                    
                    returns_data[symbol] = pd.Series(returns, index=dates)
            
            if not returns_data:
                return pd.DataFrame()
            
            # Align all series and fill missing values
            returns_df = pd.DataFrame(returns_data)
            returns_df = returns_df.dropna()
            
            return returns_df
            
        except Exception as e:
            logger.error(f"Error getting historical returns: {e}")
            return pd.DataFrame()


class StressTester:
    """Stress testing for extreme market scenarios."""
    
    def __init__(self):
        self.config = settings.risk
        
    async def run_stress_tests(
        self,
        positions: Dict[str, Dict],
        scenarios: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        Run stress tests on portfolio.
        
        Args:
            positions: Current portfolio positions
            scenarios: Custom stress scenarios
            
        Returns:
            Stress test results
        """
        try:
            if not positions:
                return {"error": "No positions provided"}
            
            # Default stress scenarios
            if scenarios is None:
                scenarios = self._get_default_scenarios()
            
            results = {}
            
            for scenario in scenarios:
                scenario_name = scenario["name"]
                scenario_results = await self._run_single_scenario(positions, scenario)
                results[scenario_name] = scenario_results
            
            # Calculate overall stress test summary
            results["summary"] = self._calculate_stress_summary(results)
            
            logger.info(f"Stress testing completed for {len(scenarios)} scenarios")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in stress testing: {e}")
            return {"error": str(e)}
    
    def _get_default_scenarios(self) -> List[Dict[str, Any]]:
        """Get default stress test scenarios."""
        return [
            {
                "name": "market_crash_2008",
                "description": "2008 Financial Crisis scenario",
                "market_shock": -0.37,  # 37% market decline
                "volatility_multiplier": 2.5,
                "correlation_increase": 0.3
            },
            {
                "name": "covid_crash_2020",
                "description": "COVID-19 market crash",
                "market_shock": -0.34,  # 34% market decline
                "volatility_multiplier": 3.0,
                "correlation_increase": 0.4
            },
            {
                "name": "flash_crash",
                "description": "Flash crash scenario",
                "market_shock": -0.20,  # 20% rapid decline
                "volatility_multiplier": 5.0,
                "correlation_increase": 0.5
            },
            {
                "name": "interest_rate_shock",
                "description": "Sudden interest rate increase",
                "market_shock": -0.15,  # 15% decline
                "volatility_multiplier": 1.8,
                "correlation_increase": 0.2
            },
            {
                "name": "sector_rotation",
                "description": "Major sector rotation",
                "market_shock": -0.10,  # 10% decline
                "volatility_multiplier": 1.5,
                "correlation_increase": -0.1  # Decreased correlation
            }
        ]
    
    async def _run_single_scenario(
        self,
        positions: Dict[str, Dict],
        scenario: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run a single stress test scenario."""
        try:
            total_value = sum(pos["quantity"] * pos["current_price"] for pos in positions.values())
            
            # Apply market shock to all positions
            market_shock = scenario.get("market_shock", 0)
            volatility_multiplier = scenario.get("volatility_multiplier", 1)
            
            stressed_positions = {}
            total_stressed_value = 0
            
            for symbol, position in positions.items():
                current_price = position["current_price"]
                quantity = position["quantity"]
                
                # Apply base market shock
                stressed_price = current_price * (1 + market_shock)
                
                # Add additional volatility-based shock
                additional_shock = np.random.normal(0, 0.02 * volatility_multiplier)
                stressed_price *= (1 + additional_shock)
                
                stressed_value = quantity * stressed_price
                total_stressed_value += stressed_value
                
                stressed_positions[symbol] = {
                    "original_price": current_price,
                    "stressed_price": stressed_price,
                    "price_change": (stressed_price - current_price) / current_price,
                    "position_value": stressed_value,
                    "pnl": stressed_value - (quantity * current_price)
                }
            
            # Calculate scenario results
            total_pnl = total_stressed_value - total_value
            portfolio_return = total_pnl / total_value
            
            return {
                "scenario": scenario,
                "total_portfolio_value": total_value,
                "stressed_portfolio_value": total_stressed_value,
                "total_pnl": total_pnl,
                "portfolio_return": portfolio_return,
                "positions": stressed_positions,
                "worst_position": min(
                    stressed_positions.items(),
                    key=lambda x: x[1]["pnl"]
                )[0] if stressed_positions else None,
                "best_position": max(
                    stressed_positions.items(),
                    key=lambda x: x[1]["pnl"]
                )[0] if stressed_positions else None
            }
            
        except Exception as e:
            logger.error(f"Error running scenario {scenario.get('name', 'unknown')}: {e}")
            return {"error": str(e)}
    
    def _calculate_stress_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate summary statistics from stress test results."""
        try:
            scenario_returns = []
            scenario_names = []
            
            for name, result in results.items():
                if name == "summary" or "error" in result:
                    continue
                
                scenario_returns.append(result["portfolio_return"])
                scenario_names.append(name)
            
            if not scenario_returns:
                return {"error": "No valid scenario results"}
            
            return {
                "worst_case_return": min(scenario_returns),
                "best_case_return": max(scenario_returns),
                "average_return": np.mean(scenario_returns),
                "worst_scenario": scenario_names[scenario_returns.index(min(scenario_returns))],
                "scenarios_with_loss": sum(1 for r in scenario_returns if r < 0),
                "scenarios_with_large_loss": sum(1 for r in scenario_returns if r < -0.1),
                "total_scenarios": len(scenario_returns)
            }
            
        except Exception as e:
            logger.error(f"Error calculating stress summary: {e}")
            return {"error": str(e)}


class PortfolioOptimizer:
    """Portfolio optimization using modern portfolio theory."""
    
    def __init__(self):
        self.config = settings.risk
        
    async def optimize_portfolio(
        self,
        symbols: List[str],
        target_return: Optional[float] = None,
        risk_tolerance: float = 0.15,
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Optimize portfolio allocation using mean-variance optimization.
        
        Args:
            symbols: List of symbols to include
            target_return: Target annual return (optional)
            risk_tolerance: Maximum acceptable volatility
            constraints: Additional constraints
            
        Returns:
            Optimal portfolio weights and metrics
        """
        try:
            # Get historical data
            returns_data = await self._get_returns_data(symbols)
            if returns_data.empty:
                return {"error": "Insufficient data for optimization"}
            
            # Calculate expected returns and covariance matrix
            mean_returns = returns_data.mean() * 252  # Annualized
            cov_matrix = returns_data.cov() * 252  # Annualized
            
            num_assets = len(symbols)
            
            # Optimization constraints
            constraints_list = [
                {"type": "eq", "fun": lambda x: np.sum(x) - 1}  # Weights sum to 1
            ]
            
            # Add custom constraints
            if constraints:
                if "max_weight" in constraints:
                    max_weight = constraints["max_weight"]
                    for i in range(num_assets):
                        constraints_list.append({
                            "type": "ineq",
                            "fun": lambda x, i=i: max_weight - x[i]
                        })
                
                if "min_weight" in constraints:
                    min_weight = constraints["min_weight"]
                    for i in range(num_assets):
                        constraints_list.append({
                            "type": "ineq",
                            "fun": lambda x, i=i: x[i] - min_weight
                        })
            
            # Bounds for weights (0 to 1)
            bounds = tuple((0, 1) for _ in range(num_assets))
            
            # Initial guess (equal weights)
            x0 = np.array([1.0 / num_assets] * num_assets)
            
            # Optimization objectives
            results = {}
            
            # 1. Minimum variance portfolio
            def portfolio_variance(weights):
                return np.dot(weights.T, np.dot(cov_matrix, weights))
            
            min_var_result = minimize(
                portfolio_variance,
                x0,
                method="SLSQP",
                bounds=bounds,
                constraints=constraints_list
            )
            
            if min_var_result.success:
                min_var_weights = min_var_result.x
                results["minimum_variance"] = {
                    "weights": dict(zip(symbols, min_var_weights)),
                    "expected_return": float(np.dot(min_var_weights, mean_returns)),
                    "volatility": float(np.sqrt(portfolio_variance(min_var_weights))),
                    "sharpe_ratio": float(
                        (np.dot(min_var_weights, mean_returns) - 0.02) /
                        np.sqrt(portfolio_variance(min_var_weights))
                    )
                }
            
            # 2. Maximum Sharpe ratio portfolio
            def negative_sharpe(weights):
                portfolio_return = np.dot(weights, mean_returns)
                portfolio_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
                return -(portfolio_return - 0.02) / portfolio_vol  # Risk-free rate = 2%
            
            max_sharpe_result = minimize(
                negative_sharpe,
                x0,
                method="SLSQP",
                bounds=bounds,
                constraints=constraints_list
            )
            
            if max_sharpe_result.success:
                max_sharpe_weights = max_sharpe_result.x
                results["maximum_sharpe"] = {
                    "weights": dict(zip(symbols, max_sharpe_weights)),
                    "expected_return": float(np.dot(max_sharpe_weights, mean_returns)),
                    "volatility": float(np.sqrt(portfolio_variance(max_sharpe_weights))),
                    "sharpe_ratio": float(-max_sharpe_result.fun)
                }
            
            # 3. Target return portfolio (if specified)
            if target_return:
                target_constraints = constraints_list + [
                    {"type": "eq", "fun": lambda x: np.dot(x, mean_returns) - target_return}
                ]
                
                target_result = minimize(
                    portfolio_variance,
                    x0,
                    method="SLSQP",
                    bounds=bounds,
                    constraints=target_constraints
                )
                
                if target_result.success:
                    target_weights = target_result.x
                    results["target_return"] = {
                        "weights": dict(zip(symbols, target_weights)),
                        "expected_return": float(np.dot(target_weights, mean_returns)),
                        "volatility": float(np.sqrt(portfolio_variance(target_weights))),
                        "sharpe_ratio": float(
                            (np.dot(target_weights, mean_returns) - 0.02) /
                            np.sqrt(portfolio_variance(target_weights))
                        )
                    }
            
            logger.info(f"Portfolio optimization completed for {len(symbols)} assets")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in portfolio optimization: {e}")
            return {"error": str(e)}
    
    async def _get_returns_data(self, symbols: List[str], lookback_days: int = 252) -> pd.DataFrame:
        """Get historical returns data for optimization."""
        try:
            returns_data = {}
            cutoff_date = datetime.utcnow() - timedelta(days=lookback_days)
            
            async with get_postgres_session() as session:
                for symbol in symbols:
                    # Get symbol ID
                    stmt = select(Symbol.id).where(Symbol.symbol == symbol)
                    result = await session.execute(stmt)
                    symbol_id = result.scalar_one_or_none()
                    
                    if not symbol_id:
                        continue
                    
                    # Get historical prices
                    stmt = (
                        select(Bar.timestamp, Bar.close)
                        .where(
                            and_(
                                Bar.symbol_id == symbol_id,
                                Bar.timeframe == "1d",
                                Bar.timestamp >= cutoff_date
                            )
                        )
                        .order_by(Bar.timestamp.asc())
                    )
                    result = await session.execute(stmt)
                    price_data = result.fetchall()
                    
                    if len(price_data) < 100:  # Need sufficient data
                        continue
                    
                    # Calculate returns
                    prices = [float(row[1]) for row in price_data]
                    returns = np.diff(np.log(prices))
                    
                    # Create date index
                    dates = [row[0] for row in price_data[1:]]
                    
                    returns_data[symbol] = pd.Series(returns, index=dates)
            
            if not returns_data:
                return pd.DataFrame()
            
            # Align all series
            returns_df = pd.DataFrame(returns_data)
            returns_df = returns_df.dropna()
            
            return returns_df
            
        except Exception as e:
            logger.error(f"Error getting returns data: {e}")
            return pd.DataFrame()


# Global instances
monte_carlo_simulator = MonteCarloSimulator()
stress_tester = StressTester()
portfolio_optimizer = PortfolioOptimizer()
