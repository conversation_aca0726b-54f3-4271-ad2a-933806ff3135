"""Volatility trading strategies and analysis."""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from scipy import stats
from sklearn.preprocessing import StandardScaler

from ...core.config import settings
from ...core.logger import get_logger
from .options_pricing import OptionsPricer, OptionContract, OptionType

logger = get_logger(__name__)


class VolatilityRegime(Enum):
    """Volatility regime types."""
    LOW_VOL = "low_volatility"
    NORMAL_VOL = "normal_volatility"
    HIGH_VOL = "high_volatility"
    EXTREME_VOL = "extreme_volatility"


@dataclass
class VolatilitySurface:
    """Volatility surface data structure."""
    underlying: str
    timestamp: datetime
    strikes: List[float]
    expirations: List[datetime]
    implied_vols: np.ndarray  # 2D array: strikes x expirations
    atm_vol: float
    vol_skew: float
    term_structure: Dict[datetime, float]


@dataclass
class VolatilitySignal:
    """Volatility trading signal."""
    underlying: str
    signal_type: str  # 'long_vol', 'short_vol', 'vol_arb'
    strategy: str
    entry_price: float
    target_price: float
    stop_loss: float
    confidence: float
    expected_return: float
    max_risk: float
    reasoning: List[str]


class VolatilityTrader:
    """Volatility trading strategies and surface analysis."""
    
    def __init__(self):
        self.pricer = OptionsPricer()
        self.vol_history = {}  # Cache for historical volatility data
        
    async def analyze_volatility_surface(
        self,
        underlying: str,
        option_chain: List[OptionContract],
        spot_price: float
    ) -> VolatilitySurface:
        """
        Analyze volatility surface from option chain.
        
        Args:
            underlying: Underlying symbol
            option_chain: List of option contracts
            spot_price: Current spot price
            
        Returns:
            Volatility surface analysis
        """
        try:
            if not option_chain:
                return self._empty_surface(underlying)
            
            # Group options by expiration and strike
            expirations = sorted(list(set(opt.expiration for opt in option_chain)))
            strikes = sorted(list(set(opt.strike for opt in option_chain)))
            
            # Calculate implied volatilities
            implied_vols = np.full((len(strikes), len(expirations)), np.nan)
            
            for i, strike in enumerate(strikes):
                for j, expiration in enumerate(expirations):
                    # Find option for this strike/expiration
                    matching_options = [
                        opt for opt in option_chain 
                        if opt.strike == strike and opt.expiration == expiration
                    ]
                    
                    if matching_options:
                        option = matching_options[0]
                        time_to_expiry = (expiration - datetime.now()).total_seconds() / (365.25 * 24 * 3600)
                        
                        if time_to_expiry > 0:
                            mid_price = (option.bid + option.ask) / 2
                            if mid_price > 0:
                                iv = self.pricer.calculate_implied_volatility(
                                    mid_price, spot_price, strike, time_to_expiry, option.option_type
                                )
                                implied_vols[i, j] = iv
            
            # Calculate ATM volatility
            atm_strike_idx = np.argmin(np.abs(np.array(strikes) - spot_price))
            atm_vol = np.nanmean(implied_vols[atm_strike_idx, :])
            
            # Calculate volatility skew (put skew)
            vol_skew = self._calculate_vol_skew(strikes, implied_vols, spot_price)
            
            # Calculate term structure
            term_structure = {}
            for j, expiration in enumerate(expirations):
                term_vol = np.nanmean(implied_vols[:, j])
                if not np.isnan(term_vol):
                    term_structure[expiration] = term_vol
            
            return VolatilitySurface(
                underlying=underlying,
                timestamp=datetime.now(),
                strikes=strikes,
                expirations=expirations,
                implied_vols=implied_vols,
                atm_vol=atm_vol,
                vol_skew=vol_skew,
                term_structure=term_structure
            )
            
        except Exception as e:
            logger.error(f"Error analyzing volatility surface: {e}")
            return self._empty_surface(underlying)
    
    async def detect_volatility_regime(
        self,
        underlying: str,
        lookback_days: int = 252
    ) -> VolatilityRegime:
        """
        Detect current volatility regime.
        
        Args:
            underlying: Underlying symbol
            lookback_days: Days of historical data to analyze
            
        Returns:
            Current volatility regime
        """
        try:
            # Get historical volatility data
            historical_vol = await self._get_historical_volatility(underlying, lookback_days)
            
            if len(historical_vol) < 30:
                return VolatilityRegime.NORMAL_VOL
            
            # Calculate percentiles
            vol_values = list(historical_vol.values())
            current_vol = vol_values[-1]
            
            p25 = np.percentile(vol_values, 25)
            p75 = np.percentile(vol_values, 75)
            p90 = np.percentile(vol_values, 90)
            p10 = np.percentile(vol_values, 10)
            
            # Classify regime
            if current_vol > p90:
                return VolatilityRegime.EXTREME_VOL
            elif current_vol > p75:
                return VolatilityRegime.HIGH_VOL
            elif current_vol < p10:
                return VolatilityRegime.LOW_VOL
            else:
                return VolatilityRegime.NORMAL_VOL
                
        except Exception as e:
            logger.error(f"Error detecting volatility regime: {e}")
            return VolatilityRegime.NORMAL_VOL
    
    async def find_volatility_arbitrage(
        self,
        underlying: str,
        option_chain: List[OptionContract],
        spot_price: float,
        min_edge: float = 0.05
    ) -> List[VolatilitySignal]:
        """
        Find volatility arbitrage opportunities.
        
        Args:
            underlying: Underlying symbol
            option_chain: Option chain data
            spot_price: Current spot price
            min_edge: Minimum edge required
            
        Returns:
            List of volatility arbitrage signals
        """
        try:
            signals = []
            
            # Get volatility surface
            vol_surface = await self.analyze_volatility_surface(underlying, option_chain, spot_price)
            
            # Get historical volatility
            historical_vol = await self._get_realized_volatility(underlying, 30)
            
            if not historical_vol:
                return signals
            
            # Find mispriced options
            for option in option_chain:
                time_to_expiry = (option.expiration - datetime.now()).total_seconds() / (365.25 * 24 * 3600)
                
                if time_to_expiry <= 0 or time_to_expiry > 1:  # Focus on < 1 year
                    continue
                
                mid_price = (option.bid + option.ask) / 2
                if mid_price <= 0:
                    continue
                
                # Calculate implied volatility
                implied_vol = self.pricer.calculate_implied_volatility(
                    mid_price, spot_price, option.strike, time_to_expiry, option.option_type
                )
                
                # Compare with historical volatility
                vol_edge = implied_vol - historical_vol
                
                if abs(vol_edge) > min_edge:
                    signal = self._create_vol_arbitrage_signal(
                        underlying, option, spot_price, implied_vol, historical_vol, vol_edge
                    )
                    if signal:
                        signals.append(signal)
            
            # Sort by expected return
            signals.sort(key=lambda x: x.expected_return, reverse=True)
            
            return signals[:10]  # Top 10 opportunities
            
        except Exception as e:
            logger.error(f"Error finding volatility arbitrage: {e}")
            return []
    
    async def generate_volatility_signals(
        self,
        underlying: str,
        option_chain: List[OptionContract],
        spot_price: float
    ) -> List[VolatilitySignal]:
        """
        Generate comprehensive volatility trading signals.
        
        Args:
            underlying: Underlying symbol
            option_chain: Option chain data
            spot_price: Current spot price
            
        Returns:
            List of volatility trading signals
        """
        try:
            signals = []
            
            # Detect volatility regime
            vol_regime = await self.detect_volatility_regime(underlying)
            
            # Get volatility surface
            vol_surface = await self.analyze_volatility_surface(underlying, option_chain, spot_price)
            
            # Generate regime-based signals
            regime_signals = self._generate_regime_signals(underlying, vol_regime, vol_surface, spot_price)
            signals.extend(regime_signals)
            
            # Find volatility arbitrage
            arb_signals = await self.find_volatility_arbitrage(underlying, option_chain, spot_price)
            signals.extend(arb_signals)
            
            # Generate skew trading signals
            skew_signals = self._generate_skew_signals(underlying, vol_surface, spot_price)
            signals.extend(skew_signals)
            
            # Generate term structure signals
            term_signals = self._generate_term_structure_signals(underlying, vol_surface, spot_price)
            signals.extend(term_signals)
            
            # Sort by confidence and expected return
            signals.sort(key=lambda x: x.confidence * x.expected_return, reverse=True)
            
            return signals[:5]  # Top 5 signals
            
        except Exception as e:
            logger.error(f"Error generating volatility signals: {e}")
            return []

    def _calculate_vol_skew(
        self,
        strikes: List[float],
        implied_vols: np.ndarray,
        spot_price: float
    ) -> float:
        """Calculate volatility skew."""
        try:
            if implied_vols.size == 0:
                return 0.0

            # Find ATM index
            atm_idx = np.argmin(np.abs(np.array(strikes) - spot_price))

            # Calculate skew as difference between OTM put and call volatilities
            otm_put_idx = max(0, atm_idx - 2)  # 2 strikes below ATM
            otm_call_idx = min(len(strikes) - 1, atm_idx + 2)  # 2 strikes above ATM

            # Average across expirations
            put_vol = np.nanmean(implied_vols[otm_put_idx, :])
            call_vol = np.nanmean(implied_vols[otm_call_idx, :])

            if np.isnan(put_vol) or np.isnan(call_vol):
                return 0.0

            return put_vol - call_vol

        except Exception as e:
            logger.error(f"Error calculating vol skew: {e}")
            return 0.0

    async def _get_historical_volatility(
        self,
        underlying: str,
        days: int
    ) -> Dict[datetime, float]:
        """Get historical volatility data."""
        try:
            # This would typically fetch from market data API
            # For now, simulate historical volatility

            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            vol_data = {}
            current_date = start_date

            while current_date <= end_date:
                # Simulate volatility with mean reversion
                base_vol = 0.2
                noise = np.random.normal(0, 0.05)
                vol_data[current_date] = max(0.05, base_vol + noise)
                current_date += timedelta(days=1)

            return vol_data

        except Exception as e:
            logger.error(f"Error getting historical volatility: {e}")
            return {}

    async def _get_realized_volatility(
        self,
        underlying: str,
        days: int
    ) -> float:
        """Get realized volatility over specified period."""
        try:
            # This would calculate from actual price returns
            # For now, return simulated value
            return 0.18  # 18% annualized volatility

        except Exception as e:
            logger.error(f"Error getting realized volatility: {e}")
            return 0.2

    def _create_vol_arbitrage_signal(
        self,
        underlying: str,
        option: OptionContract,
        spot_price: float,
        implied_vol: float,
        historical_vol: float,
        vol_edge: float
    ) -> Optional[VolatilitySignal]:
        """Create volatility arbitrage signal."""
        try:
            mid_price = (option.bid + option.ask) / 2

            if vol_edge > 0:  # Implied vol > historical vol
                signal_type = "short_vol"
                strategy = "Short Straddle"
                target_return = vol_edge * 0.5  # Conservative target
            else:  # Implied vol < historical vol
                signal_type = "long_vol"
                strategy = "Long Straddle"
                target_return = abs(vol_edge) * 0.5

            confidence = min(abs(vol_edge) / 0.1, 1.0)  # Higher edge = higher confidence

            reasoning = [
                f"Implied vol ({implied_vol:.1%}) vs Historical vol ({historical_vol:.1%})",
                f"Vol edge: {vol_edge:.1%}",
                f"Strategy: {strategy}"
            ]

            return VolatilitySignal(
                underlying=underlying,
                signal_type=signal_type,
                strategy=strategy,
                entry_price=mid_price,
                target_price=mid_price * (1 + target_return),
                stop_loss=mid_price * (1 - abs(vol_edge) * 0.3),
                confidence=confidence,
                expected_return=target_return,
                max_risk=abs(vol_edge) * 0.3,
                reasoning=reasoning
            )

        except Exception as e:
            logger.error(f"Error creating vol arbitrage signal: {e}")
            return None

    def _generate_regime_signals(
        self,
        underlying: str,
        regime: VolatilityRegime,
        vol_surface: VolatilitySurface,
        spot_price: float
    ) -> List[VolatilitySignal]:
        """Generate signals based on volatility regime."""
        signals = []

        try:
            if regime == VolatilityRegime.LOW_VOL:
                # In low vol regime, look for long volatility opportunities
                signal = VolatilitySignal(
                    underlying=underlying,
                    signal_type="long_vol",
                    strategy="Long Straddle",
                    entry_price=vol_surface.atm_vol,
                    target_price=vol_surface.atm_vol * 1.5,
                    stop_loss=vol_surface.atm_vol * 0.8,
                    confidence=0.7,
                    expected_return=0.3,
                    max_risk=0.2,
                    reasoning=[
                        "Low volatility regime detected",
                        "Volatility likely to mean revert higher",
                        "Long volatility strategy recommended"
                    ]
                )
                signals.append(signal)

            elif regime == VolatilityRegime.HIGH_VOL:
                # In high vol regime, look for short volatility opportunities
                signal = VolatilitySignal(
                    underlying=underlying,
                    signal_type="short_vol",
                    strategy="Short Straddle",
                    entry_price=vol_surface.atm_vol,
                    target_price=vol_surface.atm_vol * 0.7,
                    stop_loss=vol_surface.atm_vol * 1.2,
                    confidence=0.6,
                    expected_return=0.2,
                    max_risk=0.3,
                    reasoning=[
                        "High volatility regime detected",
                        "Volatility likely to mean revert lower",
                        "Short volatility strategy recommended"
                    ]
                )
                signals.append(signal)

            return signals

        except Exception as e:
            logger.error(f"Error generating regime signals: {e}")
            return []

    def _generate_skew_signals(
        self,
        underlying: str,
        vol_surface: VolatilitySurface,
        spot_price: float
    ) -> List[VolatilitySignal]:
        """Generate signals based on volatility skew."""
        signals = []

        try:
            # Extreme skew indicates potential trading opportunities
            if abs(vol_surface.vol_skew) > 0.05:  # 5% skew threshold

                if vol_surface.vol_skew > 0.05:  # Steep put skew
                    signal = VolatilitySignal(
                        underlying=underlying,
                        signal_type="skew_trade",
                        strategy="Sell Put Spread",
                        entry_price=vol_surface.atm_vol,
                        target_price=vol_surface.atm_vol * 0.9,
                        stop_loss=vol_surface.atm_vol * 1.1,
                        confidence=0.6,
                        expected_return=0.15,
                        max_risk=0.1,
                        reasoning=[
                            f"Steep put skew detected: {vol_surface.vol_skew:.1%}",
                            "Put volatility appears overpriced",
                            "Sell put spread to capture skew premium"
                        ]
                    )
                    signals.append(signal)

                elif vol_surface.vol_skew < -0.05:  # Reverse skew
                    signal = VolatilitySignal(
                        underlying=underlying,
                        signal_type="skew_trade",
                        strategy="Buy Put Spread",
                        entry_price=vol_surface.atm_vol,
                        target_price=vol_surface.atm_vol * 1.1,
                        stop_loss=vol_surface.atm_vol * 0.9,
                        confidence=0.6,
                        expected_return=0.15,
                        max_risk=0.1,
                        reasoning=[
                            f"Reverse skew detected: {vol_surface.vol_skew:.1%}",
                            "Put volatility appears underpriced",
                            "Buy put spread to capture skew discount"
                        ]
                    )
                    signals.append(signal)

            return signals

        except Exception as e:
            logger.error(f"Error generating skew signals: {e}")
            return []

    def _generate_term_structure_signals(
        self,
        underlying: str,
        vol_surface: VolatilitySurface,
        spot_price: float
    ) -> List[VolatilitySignal]:
        """Generate signals based on volatility term structure."""
        signals = []

        try:
            if len(vol_surface.term_structure) < 2:
                return signals

            # Sort by expiration
            sorted_terms = sorted(vol_surface.term_structure.items())

            if len(sorted_terms) >= 2:
                short_term_vol = sorted_terms[0][1]
                long_term_vol = sorted_terms[-1][1]

                term_spread = short_term_vol - long_term_vol

                # Inverted term structure (short vol > long vol)
                if term_spread > 0.05:  # 5% inversion
                    signal = VolatilitySignal(
                        underlying=underlying,
                        signal_type="term_structure",
                        strategy="Calendar Spread",
                        entry_price=short_term_vol,
                        target_price=short_term_vol * 0.9,
                        stop_loss=short_term_vol * 1.1,
                        confidence=0.5,
                        expected_return=0.1,
                        max_risk=0.15,
                        reasoning=[
                            f"Inverted term structure: {term_spread:.1%}",
                            "Short-term vol elevated vs long-term",
                            "Calendar spread to capture term structure normalization"
                        ]
                    )
                    signals.append(signal)

            return signals

        except Exception as e:
            logger.error(f"Error generating term structure signals: {e}")
            return []

    def _empty_surface(self, underlying: str) -> VolatilitySurface:
        """Return empty volatility surface."""
        return VolatilitySurface(
            underlying=underlying,
            timestamp=datetime.now(),
            strikes=[],
            expirations=[],
            implied_vols=np.array([]),
            atm_vol=0.2,
            vol_skew=0.0,
            term_structure={}
        )
