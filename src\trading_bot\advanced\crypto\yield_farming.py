"""Yield farming optimization and strategy management."""

import asyncio
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from decimal import Decimal

from ...core.config import settings
from ...core.logger import get_logger
from .defi_integration import <PERSON>FiIntegrator, YieldOpportunity, DeFiProtocol

logger = get_logger(__name__)


class YieldStrategy(Enum):
    """Yield farming strategy types."""
    SINGLE_ASSET = "single_asset"
    LIQUIDITY_PROVISION = "liquidity_provision"
    LEVERAGED_FARMING = "leveraged_farming"
    YIELD_AGGREGATION = "yield_aggregation"
    DELTA_NEUTRAL = "delta_neutral"


@dataclass
class YieldPosition:
    """Yield farming position."""
    strategy: YieldStrategy
    protocol: DeFiProtocol
    tokens: Dict[str, float]
    initial_value: float
    current_value: float
    apy: float
    rewards_earned: float
    impermanent_loss: float
    entry_date: datetime
    auto_compound: bool


@dataclass
class YieldStrategy:
    """Yield farming strategy configuration."""
    name: str
    strategy_type: YieldStrategy
    target_apy: float
    max_risk: float
    min_liquidity: float
    auto_rebalance: bool
    compound_frequency: int  # days
    exit_conditions: Dict[str, Any]


@dataclass
class OptimizationResult:
    """Yield optimization result."""
    recommended_positions: List[YieldPosition]
    expected_apy: float
    risk_score: float
    capital_allocation: Dict[str, float]
    rebalancing_schedule: List[datetime]
    monitoring_alerts: List[str]


class YieldOptimizer:
    """Optimize yield farming strategies and positions."""
    
    def __init__(self):
        self.defi_integrator = DeFiIntegrator()
        self.active_positions = {}
        self.strategies = self._load_yield_strategies()
        
    async def optimize_yield_portfolio(
        self,
        capital: float,
        risk_tolerance: float,
        preferred_tokens: List[str] = None,
        strategy_types: List[YieldStrategy] = None
    ) -> OptimizationResult:
        """
        Optimize yield farming portfolio allocation.
        
        Args:
            capital: Available capital
            risk_tolerance: Risk tolerance (0-10)
            preferred_tokens: Preferred tokens to farm
            strategy_types: Preferred strategy types
            
        Returns:
            Optimization result with recommendations
        """
        try:
            if preferred_tokens is None:
                preferred_tokens = ['ETH', 'USDC', 'USDT', 'DAI', 'WBTC']
            
            if strategy_types is None:
                strategy_types = [YieldStrategy.LIQUIDITY_PROVISION, YieldStrategy.SINGLE_ASSET]
            
            # Find available opportunities
            opportunities = await self.defi_integrator.find_yield_opportunities(
                tokens=preferred_tokens,
                min_apy=0.01,
                max_risk=risk_tolerance
            )
            
            # Filter by strategy types
            filtered_opportunities = [
                opp for opp in opportunities
                if self._get_strategy_type(opp) in strategy_types
            ]
            
            # Optimize allocation
            optimal_allocation = self._optimize_allocation(
                filtered_opportunities, capital, risk_tolerance
            )
            
            # Create recommended positions
            recommended_positions = []
            for allocation in optimal_allocation:
                position = self._create_yield_position(allocation)
                recommended_positions.append(position)
            
            # Calculate portfolio metrics
            expected_apy = self._calculate_portfolio_apy(recommended_positions)
            risk_score = self._calculate_portfolio_risk(recommended_positions)
            
            # Generate rebalancing schedule
            rebalancing_schedule = self._generate_rebalancing_schedule()
            
            # Generate monitoring alerts
            monitoring_alerts = self._generate_monitoring_alerts(recommended_positions)
            
            return OptimizationResult(
                recommended_positions=recommended_positions,
                expected_apy=expected_apy,
                risk_score=risk_score,
                capital_allocation={pos.protocol.value: sum(pos.tokens.values()) 
                                  for pos in recommended_positions},
                rebalancing_schedule=rebalancing_schedule,
                monitoring_alerts=monitoring_alerts
            )
            
        except Exception as e:
            logger.error(f"Error optimizing yield portfolio: {e}")
            return self._empty_optimization_result()
    
    async def monitor_yield_positions(self) -> Dict[str, Any]:
        """Monitor active yield farming positions."""
        try:
            position_updates = {}
            alerts = []
            
            for position_id, position in self.active_positions.items():
                # Update position metrics
                current_metrics = await self._update_position_metrics(position)
                
                # Check for alerts
                position_alerts = self._check_position_alerts(position, current_metrics)
                alerts.extend(position_alerts)
                
                # Calculate performance
                pnl = current_metrics['current_value'] - position.initial_value
                pnl_percentage = pnl / position.initial_value if position.initial_value > 0 else 0
                
                position_updates[position_id] = {
                    'current_value': current_metrics['current_value'],
                    'pnl': pnl,
                    'pnl_percentage': pnl_percentage,
                    'apy_actual': current_metrics['apy_actual'],
                    'rewards_earned': current_metrics['rewards_earned'],
                    'impermanent_loss': current_metrics['impermanent_loss'],
                    'days_active': (datetime.now() - position.entry_date).days,
                    'alerts': position_alerts
                }
            
            # Portfolio summary
            total_value = sum(pos.current_value for pos in self.active_positions.values())
            total_initial = sum(pos.initial_value for pos in self.active_positions.values())
            portfolio_pnl = total_value - total_initial
            
            return {
                'timestamp': datetime.now(),
                'positions': position_updates,
                'portfolio_summary': {
                    'total_value': total_value,
                    'total_pnl': portfolio_pnl,
                    'portfolio_pnl_percentage': portfolio_pnl / total_initial if total_initial > 0 else 0,
                    'position_count': len(self.active_positions),
                    'active_protocols': list(set(pos.protocol.value for pos in self.active_positions.values()))
                },
                'alerts': alerts
            }
            
        except Exception as e:
            logger.error(f"Error monitoring yield positions: {e}")
            return {'error': str(e)}
    
    async def rebalance_portfolio(
        self,
        target_allocation: Dict[str, float],
        slippage_tolerance: float = 0.01
    ) -> Dict[str, Any]:
        """
        Rebalance yield farming portfolio.
        
        Args:
            target_allocation: Target allocation percentages
            slippage_tolerance: Maximum slippage tolerance
            
        Returns:
            Rebalancing execution report
        """
        try:
            rebalancing_actions = []
            
            # Calculate current allocation
            current_allocation = self._calculate_current_allocation()
            
            # Determine required actions
            for protocol, target_pct in target_allocation.items():
                current_pct = current_allocation.get(protocol, 0)
                difference = target_pct - current_pct
                
                if abs(difference) > 0.05:  # 5% threshold
                    if difference > 0:
                        # Need to increase allocation
                        action = {
                            'type': 'increase',
                            'protocol': protocol,
                            'amount': difference,
                            'priority': abs(difference)
                        }
                    else:
                        # Need to decrease allocation
                        action = {
                            'type': 'decrease',
                            'protocol': protocol,
                            'amount': abs(difference),
                            'priority': abs(difference)
                        }
                    
                    rebalancing_actions.append(action)
            
            # Sort by priority
            rebalancing_actions.sort(key=lambda x: x['priority'], reverse=True)
            
            # Execute rebalancing (simulation)
            execution_results = []
            for action in rebalancing_actions:
                result = await self._execute_rebalancing_action(action, slippage_tolerance)
                execution_results.append(result)
            
            return {
                'timestamp': datetime.now(),
                'actions_planned': len(rebalancing_actions),
                'actions_executed': len([r for r in execution_results if r['success']]),
                'execution_results': execution_results,
                'new_allocation': self._calculate_current_allocation(),
                'total_cost': sum(r.get('cost', 0) for r in execution_results)
            }
            
        except Exception as e:
            logger.error(f"Error rebalancing portfolio: {e}")
            return {'error': str(e)}
    
    def _optimize_allocation(
        self,
        opportunities: List[YieldOpportunity],
        capital: float,
        risk_tolerance: float
    ) -> List[Dict[str, Any]]:
        """Optimize capital allocation across opportunities."""
        try:
            if not opportunities:
                return []
            
            # Simple risk-adjusted allocation
            allocations = []
            remaining_capital = capital
            
            # Sort by risk-adjusted return
            sorted_opportunities = sorted(
                opportunities,
                key=lambda x: x.apy / (1 + x.risk_score),
                reverse=True
            )
            
            # Allocate capital (max 5 positions for diversification)
            for i, opp in enumerate(sorted_opportunities[:5]):
                if remaining_capital <= 0:
                    break
                
                # Risk-based allocation
                risk_factor = max(0.1, 1 - (opp.risk_score / 10))
                base_allocation = capital * 0.2  # 20% base allocation
                risk_adjusted_allocation = base_allocation * risk_factor
                
                allocation_amount = min(risk_adjusted_allocation, remaining_capital)
                
                if allocation_amount >= opp.min_deposit:
                    allocations.append({
                        'opportunity': opp,
                        'amount': allocation_amount,
                        'percentage': allocation_amount / capital
                    })
                    remaining_capital -= allocation_amount
            
            return allocations
            
        except Exception as e:
            logger.error(f"Error optimizing allocation: {e}")
            return []
    
    def _create_yield_position(self, allocation: Dict[str, Any]) -> YieldPosition:
        """Create yield position from allocation."""
        opp = allocation['opportunity']
        amount = allocation['amount']
        
        # Determine tokens based on strategy
        if opp.strategy == "Liquidity Provision":
            # Split amount between two tokens
            tokens = {
                opp.pool.token0: amount / 2,
                opp.pool.token1: amount / 2
            }
        else:
            # Single token
            tokens = {opp.pool.token0: amount}
        
        return YieldPosition(
            strategy=self._get_strategy_type(opp),
            protocol=opp.protocol,
            tokens=tokens,
            initial_value=amount,
            current_value=amount,
            apy=opp.apy,
            rewards_earned=0.0,
            impermanent_loss=0.0,
            entry_date=datetime.now(),
            auto_compound=opp.auto_compound
        )
    
    def _get_strategy_type(self, opportunity: YieldOpportunity) -> YieldStrategy:
        """Determine strategy type from opportunity."""
        if opportunity.strategy == "Liquidity Provision":
            return YieldStrategy.LIQUIDITY_PROVISION
        elif opportunity.strategy == "Lending":
            return YieldStrategy.SINGLE_ASSET
        else:
            return YieldStrategy.YIELD_AGGREGATION
    
    def _calculate_portfolio_apy(self, positions: List[YieldPosition]) -> float:
        """Calculate weighted portfolio APY."""
        if not positions:
            return 0.0
        
        total_value = sum(pos.initial_value for pos in positions)
        weighted_apy = sum(pos.apy * pos.initial_value for pos in positions)
        
        return weighted_apy / total_value if total_value > 0 else 0.0
    
    def _calculate_portfolio_risk(self, positions: List[YieldPosition]) -> float:
        """Calculate portfolio risk score."""
        if not positions:
            return 0.0
        
        # Simple average risk (would be more sophisticated in practice)
        protocol_risks = {
            DeFiProtocol.AAVE: 2.0,
            DeFiProtocol.COMPOUND: 2.0,
            DeFiProtocol.UNISWAP_V3: 4.0,
            DeFiProtocol.CURVE: 3.0,
            DeFiProtocol.YEARN: 3.5
        }
        
        total_value = sum(pos.initial_value for pos in positions)
        weighted_risk = sum(
            protocol_risks.get(pos.protocol, 5.0) * pos.initial_value
            for pos in positions
        )
        
        return weighted_risk / total_value if total_value > 0 else 5.0
    
    def _generate_rebalancing_schedule(self) -> List[datetime]:
        """Generate rebalancing schedule."""
        schedule = []
        current_date = datetime.now()
        
        # Weekly rebalancing for the next month
        for i in range(4):
            schedule.append(current_date + timedelta(weeks=i+1))
        
        return schedule
    
    def _generate_monitoring_alerts(self, positions: List[YieldPosition]) -> List[str]:
        """Generate monitoring alerts."""
        alerts = []
        
        for pos in positions:
            if pos.apy < 0.05:  # Less than 5% APY
                alerts.append(f"Low APY alert for {pos.protocol.value}: {pos.apy:.1%}")
            
            if pos.strategy == YieldStrategy.LIQUIDITY_PROVISION:
                alerts.append(f"Monitor IL for {pos.protocol.value} LP position")
        
        return alerts
    
    async def _update_position_metrics(self, position: YieldPosition) -> Dict[str, Any]:
        """Update position metrics."""
        # This would fetch real-time data from the blockchain
        # For now, simulate some updates
        
        days_active = (datetime.now() - position.entry_date).days
        
        # Simulate yield accrual
        daily_yield = position.apy / 365
        accrued_yield = position.initial_value * daily_yield * days_active
        
        # Simulate some impermanent loss for LP positions
        il = 0.0
        if position.strategy == YieldStrategy.LIQUIDITY_PROVISION:
            il = position.initial_value * 0.02  # 2% IL simulation
        
        current_value = position.initial_value + accrued_yield - il
        
        return {
            'current_value': current_value,
            'apy_actual': position.apy,  # Would calculate from actual performance
            'rewards_earned': accrued_yield,
            'impermanent_loss': il
        }
    
    def _check_position_alerts(
        self,
        position: YieldPosition,
        current_metrics: Dict[str, Any]
    ) -> List[str]:
        """Check for position alerts."""
        alerts = []
        
        # Check for significant IL
        if current_metrics['impermanent_loss'] > position.initial_value * 0.05:
            alerts.append(f"High impermanent loss: {current_metrics['impermanent_loss']:.2f}")
        
        # Check for low performance
        expected_value = position.initial_value * (1 + position.apy * 
                       (datetime.now() - position.entry_date).days / 365)
        
        if current_metrics['current_value'] < expected_value * 0.9:
            alerts.append("Position underperforming expectations")
        
        return alerts
    
    def _calculate_current_allocation(self) -> Dict[str, float]:
        """Calculate current portfolio allocation."""
        total_value = sum(pos.current_value for pos in self.active_positions.values())
        
        allocation = {}
        for pos in self.active_positions.values():
            protocol = pos.protocol.value
            if protocol not in allocation:
                allocation[protocol] = 0
            allocation[protocol] += pos.current_value / total_value
        
        return allocation
    
    async def _execute_rebalancing_action(
        self,
        action: Dict[str, Any],
        slippage_tolerance: float
    ) -> Dict[str, Any]:
        """Execute a rebalancing action."""
        # This would execute actual transactions
        # For now, simulate the execution
        
        return {
            'action': action,
            'success': True,
            'cost': 50.0,  # Simulated gas cost
            'slippage': 0.005,  # 0.5% slippage
            'timestamp': datetime.now()
        }
    
    def _load_yield_strategies(self) -> Dict[str, YieldStrategy]:
        """Load predefined yield strategies."""
        return {
            'conservative': YieldStrategy(
                name="Conservative Yield",
                strategy_type=YieldStrategy.SINGLE_ASSET,
                target_apy=0.05,
                max_risk=3.0,
                min_liquidity=1000000,
                auto_rebalance=True,
                compound_frequency=7,
                exit_conditions={'max_drawdown': 0.1}
            ),
            'balanced': YieldStrategy(
                name="Balanced Yield",
                strategy_type=YieldStrategy.LIQUIDITY_PROVISION,
                target_apy=0.12,
                max_risk=5.0,
                min_liquidity=500000,
                auto_rebalance=True,
                compound_frequency=3,
                exit_conditions={'max_drawdown': 0.15}
            ),
            'aggressive': YieldStrategy(
                name="Aggressive Yield",
                strategy_type=YieldStrategy.LEVERAGED_FARMING,
                target_apy=0.25,
                max_risk=8.0,
                min_liquidity=100000,
                auto_rebalance=True,
                compound_frequency=1,
                exit_conditions={'max_drawdown': 0.25}
            )
        }
    
    def _empty_optimization_result(self) -> OptimizationResult:
        """Return empty optimization result."""
        return OptimizationResult(
            recommended_positions=[],
            expected_apy=0.0,
            risk_score=0.0,
            capital_allocation={},
            rebalancing_schedule=[],
            monitoring_alerts=[]
        )
