"""
Clean up duplicate Python files in the project
"""

import os
import shutil
from datetime import datetime

def cleanup_duplicates():
    """Remove duplicate test files and organize the project"""
    
    # Files to keep (main files)
    keep_files = {
        'src/trading_bot/automation/webull_browser_bot.py': 'Main browser automation',
        'src/trading_bot/automation/webull_data_extractor.py': 'Data extraction utilities',
    }
    
    # Files to remove (test/duplicate files)
    remove_files = [
        'webull_data_extractor_fixed.py',
        'analyze_webull_pages.py',
        'webull_wait_extractor.py',
        'simple_working_extractor.py',
        'improved_webull_extractor.py',
        'simple_browser_test.py',
        'standalone_browser_test.py',
    ]
    
    # Create backup directory
    backup_dir = f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    os.makedirs(backup_dir, exist_ok=True)
    
    print("🧹 Cleaning up duplicate files...")
    print(f"📁 Backing up to: {backup_dir}/")
    
    # Move duplicates to backup
    for file in remove_files:
        if os.path.exists(file):
            shutil.move(file, os.path.join(backup_dir, file))
            print(f"  ✓ Moved {file} to backup")
    
    # Keep generated data files
    data_files = ['page_source_*.html', 'page_text_*.txt', 'analysis_*.json']
    
    print("\n📊 Keeping data files for reference")
    print("\n✅ Cleanup complete!")
    print(f"\n📁 Main files to use:")
    for file, desc in keep_files.items():
        print(f"  - {file}: {desc}")

if __name__ == "__main__":
    cleanup_duplicates()
