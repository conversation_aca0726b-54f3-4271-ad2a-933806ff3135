"""Main ML Pipeline for trading bot integration."""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import os
import json
from pathlib import Path

from .models.ensemble import EnsembleCoordinator, EnsembleConfig
from .features.feature_engineering import FeatureEngineering
from .training.trainer import Trainer, TrainingConfig
from .training.backtester import Backtester, BacktestConfig
from .training.hyperparameter_tuner import HyperparameterTuner, TuningConfig
from .training.model_evaluator import ModelEvaluator, EvaluationConfig
from .prediction.predictor import Predictor, PredictionConfig
from .prediction.signal_generator import SignalGenerator, SignalConfig
from .prediction.confidence_scorer import ConfidenceScorer, ConfidenceConfig

logger = logging.getLogger(__name__)


@dataclass
class MLPipelineConfig:
    """Configuration for ML pipeline."""
    # Model paths
    model_save_dir: str = "models"
    results_save_dir: str = "results"
    
    # Pipeline components
    enable_feature_engineering: bool = True
    enable_training: bool = True
    enable_prediction: bool = True
    enable_signal_generation: bool = True
    enable_confidence_scoring: bool = True
    
    # Real-time settings
    prediction_frequency: str = '1min'
    auto_retrain_enabled: bool = False
    auto_retrain_interval_hours: int = 24
    
    # Performance monitoring
    track_performance: bool = True
    performance_update_frequency: str = '1hour'
    
    # Component configs
    training_config: Optional[TrainingConfig] = None
    prediction_config: Optional[PredictionConfig] = None
    signal_config: Optional[SignalConfig] = None
    confidence_config: Optional[ConfidenceConfig] = None
    
    def __post_init__(self):
        if self.training_config is None:
            self.training_config = TrainingConfig()
        if self.prediction_config is None:
            self.prediction_config = PredictionConfig()
        if self.signal_config is None:
            self.signal_config = SignalConfig()
        if self.confidence_config is None:
            self.confidence_config = ConfidenceConfig()


class TradingMLPipeline:
    """Complete ML pipeline for trading bot."""
    
    def __init__(self, 
                 models: Optional[List[str]] = None,
                 ensemble_method: str = 'weighted_voting',
                 confidence_threshold: float = 0.7,
                 config: Optional[MLPipelineConfig] = None):
        
        self.config = config or MLPipelineConfig()
        self.models = models or ['lstm', 'xgboost', 'transformer', 'rl']
        
        # Initialize components
        self.feature_engineer = FeatureEngineering() if self.config.enable_feature_engineering else None
        self.trainer = None
        self.predictor = None
        self.signal_generator = None
        self.confidence_scorer = None
        
        # Ensemble configuration
        ensemble_config = EnsembleConfig(
            ensemble_method=ensemble_method,
            confidence_threshold=confidence_threshold
        )
        self.ensemble = EnsembleCoordinator(ensemble_config)
        
        # Pipeline state
        self.is_trained = False
        self.is_running = False
        self.training_results = {}
        self.performance_metrics = {}
        
        # Setup directories
        self._setup_directories()
        
        logger.info(f"Initialized TradingMLPipeline with models: {self.models}")
    
    def _setup_directories(self):
        """Setup required directories."""
        Path(self.config.model_save_dir).mkdir(parents=True, exist_ok=True)
        Path(self.config.results_save_dir).mkdir(parents=True, exist_ok=True)
    
    async def train(self, 
                   symbols: List[str],
                   start_date: str = "2020-01-01",
                   end_date: str = "2024-01-01",
                   validation_split: float = 0.2,
                   data_source: Optional[Callable] = None) -> Dict[str, Any]:
        """Train all models in the pipeline."""
        
        if not self.config.enable_training:
            logger.warning("Training disabled in configuration")
            return {}
        
        logger.info(f"Starting training for symbols: {symbols}")
        
        # Initialize trainer
        training_config = self.config.training_config
        training_config.models_to_train = self.models
        self.trainer = Trainer(training_config)
        
        training_results = {}
        
        for symbol in symbols:
            try:
                # Get training data
                if data_source:
                    data = await data_source(symbol, start_date, end_date)
                else:
                    # Use default data source (implement as needed)
                    data = await self._get_default_data(symbol, start_date, end_date)
                
                if data is None or data.empty:
                    logger.warning(f"No data available for {symbol}")
                    continue
                
                # Train models for this symbol
                symbol_results = await self.trainer.train_all_models(
                    data=data,
                    target_column='target',
                    symbol=symbol
                )
                
                training_results[symbol] = symbol_results
                
                logger.info(f"Training completed for {symbol}")
                
            except Exception as e:
                logger.error(f"Training failed for {symbol}: {e}")
                training_results[symbol] = {'error': str(e)}
        
        self.training_results = training_results
        self.is_trained = len([r for r in training_results.values() if 'error' not in r]) > 0
        
        # Save training results
        results_path = os.path.join(self.config.results_save_dir, "training_results.json")
        with open(results_path, 'w') as f:
            json.dump(training_results, f, indent=2, default=str)
        
        logger.info("Training pipeline completed")
        return training_results
    
    async def _get_default_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Get default training data (placeholder implementation)."""
        # This should be implemented to fetch actual market data
        # For now, return None to indicate no default data source
        logger.warning("No default data source implemented")
        return None
    
    def load_trained_models(self, model_paths: Dict[str, str]):
        """Load pre-trained models."""
        
        logger.info("Loading trained models...")
        
        # Initialize predictor if not exists
        if self.predictor is None:
            self.predictor = Predictor(self.config.prediction_config)
        
        # Load models
        self.predictor.load_models(model_paths)
        
        # Load ensemble if available
        if 'ensemble' in model_paths:
            self.ensemble.load_ensemble_state(model_paths['ensemble'])
        
        self.is_trained = True
        logger.info("Models loaded successfully")
    
    async def predict(self, 
                     data: pd.DataFrame,
                     symbol: str,
                     return_confidence: bool = True,
                     return_signals: bool = True) -> Dict[str, Any]:
        """Make predictions using the trained pipeline."""
        
        if not self.is_trained:
            raise ValueError("Pipeline not trained. Call train() or load_trained_models() first.")
        
        if not self.config.enable_prediction:
            logger.warning("Prediction disabled in configuration")
            return {}
        
        # Initialize components if needed
        if self.predictor is None:
            self.predictor = Predictor(self.config.prediction_config)
        
        # Make prediction
        prediction_result = await self.predictor.predict(data, symbol)
        
        result = {
            'prediction': prediction_result,
            'timestamp': datetime.now(),
            'symbol': symbol
        }
        
        # Generate confidence score
        if return_confidence and self.config.enable_confidence_scoring:
            if self.confidence_scorer is None:
                self.confidence_scorer = ConfidenceScorer(self.config.confidence_config)
            
            confidence_result = self.confidence_scorer.score_prediction(prediction_result, data)
            result['confidence'] = confidence_result
        
        # Generate trading signals
        if return_signals and self.config.enable_signal_generation:
            if self.signal_generator is None:
                self.signal_generator = SignalGenerator(self.config.signal_config)
            
            trading_signal = self.signal_generator.generate_signal(
                prediction_result, data
            )
            result['signal'] = trading_signal
        
        return result
    
    async def start_real_time_pipeline(self, 
                                      data_callback: Callable,
                                      prediction_callback: Optional[Callable] = None):
        """Start real-time prediction pipeline."""
        
        if not self.is_trained:
            raise ValueError("Pipeline not trained")
        
        if self.is_running:
            logger.warning("Pipeline already running")
            return
        
        logger.info("Starting real-time ML pipeline...")
        
        # Initialize components
        if self.predictor is None:
            self.predictor = Predictor(self.config.prediction_config)
        
        if self.signal_generator is None and self.config.enable_signal_generation:
            self.signal_generator = SignalGenerator(self.config.signal_config)
        
        if self.confidence_scorer is None and self.config.enable_confidence_scoring:
            self.confidence_scorer = ConfidenceScorer(self.config.confidence_config)
        
        # Define prediction callback
        async def internal_prediction_callback(prediction_result):
            """Internal callback to process predictions."""
            try:
                # Add confidence scoring
                if self.confidence_scorer:
                    # Note: We need market data for confidence scoring
                    # This is a simplified version
                    pass
                
                # Generate signals
                if self.signal_generator:
                    # Note: We need market data for signal generation
                    # This is a simplified version
                    pass
                
                # Call user callback
                if prediction_callback:
                    await prediction_callback(prediction_result)
                    
            except Exception as e:
                logger.error(f"Error in prediction callback: {e}")
        
        # Start real-time predictions
        self.predictor.start_real_time(internal_prediction_callback)
        self.is_running = True
        
        logger.info("Real-time pipeline started")
    
    def stop_real_time_pipeline(self):
        """Stop real-time prediction pipeline."""
        
        if not self.is_running:
            return
        
        if self.predictor:
            self.predictor.stop_real_time()
        
        self.is_running = False
        logger.info("Real-time pipeline stopped")
    
    async def backtest(self, 
                      data: pd.DataFrame,
                      initial_capital: float = 100000,
                      commission: float = 0.001,
                      symbol: str = "UNKNOWN") -> Dict[str, Any]:
        """Backtest the ML pipeline."""
        
        if not self.is_trained:
            raise ValueError("Pipeline not trained")
        
        logger.info(f"Starting backtest for {symbol}")
        
        # Generate signals for backtesting
        signals = []
        timestamps = []
        
        # Process data in chunks to simulate real-time
        chunk_size = 200  # Use last 200 periods for prediction
        
        for i in range(chunk_size, len(data)):
            try:
                # Get data chunk
                chunk = data.iloc[i-chunk_size:i+1]
                current_time = data.index[i]
                
                # Make prediction
                result = await self.predict(
                    chunk, symbol, 
                    return_confidence=True, 
                    return_signals=True
                )
                
                # Extract signal
                if 'signal' in result and result['signal']:
                    signal_data = {
                        'timestamp': current_time,
                        'signal': result['signal'].signal_type.value,
                        'confidence': result['signal'].confidence,
                        'position_size': result['signal'].position_size
                    }
                    signals.append(signal_data)
                    timestamps.append(current_time)
                
            except Exception as e:
                logger.warning(f"Error generating signal at {data.index[i]}: {e}")
                continue
        
        # Convert signals to DataFrame
        if signals:
            signals_df = pd.DataFrame(signals)
            signals_df.set_index('timestamp', inplace=True)
        else:
            logger.warning("No signals generated for backtest")
            return {'error': 'No signals generated'}
        
        # Run backtest
        backtest_config = BacktestConfig(
            initial_capital=initial_capital,
            commission=commission
        )
        
        backtester = Backtester(backtest_config)
        results = backtester.run_backtest(data, signals_df)
        
        # Generate report
        report = backtester.generate_report()
        
        backtest_results = {
            'results': results,
            'report': report,
            'n_signals': len(signals),
            'symbol': symbol
        }
        
        # Save backtest results
        results_path = os.path.join(self.config.results_save_dir, f"backtest_{symbol}.json")
        with open(results_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Backtest completed for {symbol}")
        return backtest_results
    
    async def tune_hyperparameters(self, 
                                  data: pd.DataFrame,
                                  target_column: str = 'target',
                                  n_trials: int = 50) -> Dict[str, Any]:
        """Tune hyperparameters for all models."""
        
        logger.info("Starting hyperparameter tuning...")
        
        # Split data
        split_idx = int(len(data) * 0.8)
        train_data = data.iloc[:split_idx]
        val_data = data.iloc[split_idx:]
        
        # Initialize tuner
        tuning_config = TuningConfig(n_trials=n_trials)
        tuner = HyperparameterTuner(tuning_config)
        
        # Tune models
        tuning_results = tuner.tune_all_models(
            train_data=train_data,
            val_data=val_data,
            models=self.models,
            target_column=target_column
        )
        
        # Save results
        results_path = os.path.join(self.config.results_save_dir, "hyperparameter_tuning.json")
        tuner.save_results(results_path)
        
        logger.info("Hyperparameter tuning completed")
        return tuning_results
    
    async def evaluate_models(self, 
                             test_data: pd.DataFrame,
                             target_column: str = 'target') -> Dict[str, Any]:
        """Evaluate trained models."""
        
        if not self.is_trained:
            raise ValueError("Pipeline not trained")
        
        logger.info("Starting model evaluation...")
        
        # Initialize evaluator
        eval_config = EvaluationConfig()
        evaluator = ModelEvaluator(eval_config)
        
        # Prepare test data
        feature_cols = [col for col in test_data.columns if col != target_column]
        X_test = test_data[feature_cols]
        y_test = test_data[target_column]
        
        # Evaluate models (this would need to load the actual trained models)
        # For now, return placeholder results
        evaluation_results = {
            'message': 'Model evaluation not fully implemented',
            'data_shape': test_data.shape,
            'target_column': target_column
        }
        
        # Save results
        results_path = os.path.join(self.config.results_save_dir, "model_evaluation.json")
        with open(results_path, 'w') as f:
            json.dump(evaluation_results, f, indent=2, default=str)
        
        logger.info("Model evaluation completed")
        return evaluation_results
    
    def update_performance(self, 
                          prediction_result: Dict[str, Any],
                          actual_outcome: Union[str, float]):
        """Update performance tracking."""
        
        if not self.config.track_performance:
            return
        
        # Update confidence scorer performance
        if self.confidence_scorer and 'prediction' in prediction_result:
            self.confidence_scorer.update_performance(
                prediction_result['prediction'], 
                actual_outcome
            )
        
        # Update ensemble performance
        if 'prediction' in prediction_result:
            self.ensemble.update_performance(
                prediction_result, 
                actual_outcome
            )
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status."""
        
        status = {
            'is_trained': self.is_trained,
            'is_running': self.is_running,
            'models': self.models,
            'config': {
                'feature_engineering': self.config.enable_feature_engineering,
                'training': self.config.enable_training,
                'prediction': self.config.enable_prediction,
                'signal_generation': self.config.enable_signal_generation,
                'confidence_scoring': self.config.enable_confidence_scoring
            }
        }
        
        # Add component status
        if self.predictor:
            status['predictor_health'] = self.predictor.get_health()
        
        if self.ensemble:
            status['ensemble_summary'] = self.ensemble.get_model_performance_summary().to_dict()
        
        if self.confidence_scorer:
            status['confidence_performance'] = self.confidence_scorer.get_performance_summary()
        
        return status
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        
        metrics = {}
        
        # Ensemble metrics
        if self.ensemble:
            ensemble_summary = self.ensemble.get_model_performance_summary()
            if not ensemble_summary.empty:
                metrics['ensemble'] = ensemble_summary.to_dict()
        
        # Confidence scoring metrics
        if self.confidence_scorer:
            metrics['confidence'] = self.confidence_scorer.get_performance_summary()
        
        # Predictor metrics
        if self.predictor:
            metrics['predictor'] = self.predictor.pipeline.get_performance_metrics()
        
        return metrics
    
    def save_pipeline_state(self, filepath: str):
        """Save pipeline state."""
        
        state = {
            'config': self.config.__dict__,
            'models': self.models,
            'is_trained': self.is_trained,
            'training_results': self.training_results,
            'performance_metrics': self.get_performance_metrics()
        }
        
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2, default=str)
        
        logger.info(f"Pipeline state saved to {filepath}")
    
    def load_pipeline_state(self, filepath: str):
        """Load pipeline state."""
        
        with open(filepath, 'r') as f:
            state = json.load(f)
        
        self.models = state.get('models', self.models)
        self.is_trained = state.get('is_trained', False)
        self.training_results = state.get('training_results', {})
        
        logger.info(f"Pipeline state loaded from {filepath}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        
        health = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'components': {}
        }
        
        # Check pipeline status
        health['components']['pipeline'] = {
            'status': 'pass' if self.is_trained else 'fail',
            'is_trained': self.is_trained,
            'is_running': self.is_running
        }
        
        # Check predictor
        if self.predictor:
            predictor_health = self.predictor.get_health()
            health['components']['predictor'] = predictor_health
        
        # Check ensemble
        if self.ensemble:
            model_summary = self.ensemble.get_model_performance_summary()
            health['components']['ensemble'] = {
                'status': 'pass' if not model_summary.empty else 'fail',
                'n_models': len(model_summary)
            }
        
        # Overall status
        failed_components = [
            comp for comp in health['components'].values() 
            if comp.get('status') == 'fail'
        ]
        
        if failed_components:
            health['status'] = 'unhealthy'
        elif any(comp.get('status') == 'warn' for comp in health['components'].values()):
            health['status'] = 'degraded'
        
        return health