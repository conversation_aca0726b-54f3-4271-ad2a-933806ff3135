#!/usr/bin/env python3
"""
Emergency Kill Switch Dashboard Runner

🚨 THE MOST CRITICAL COMPONENT 🚨
Run this to start the emergency control dashboard.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.trading_bot.dashboard.emergency_dashboard import run_emergency_dashboard
from src.trading_bot.emergency.kill_switch import get_emergency_kill_switch
from src.trading_bot.brokers.webull_broker import WebullBroker
from src.trading_bot.strategies.strategy_manager import StrategyManager

def main():
    """Start the emergency dashboard"""
    
    print("🚨" * 30)
    print("🚨 EMERGENCY KILL SWITCH DASHBOARD 🚨")
    print("🚨" * 30)
    print()
    print("🔴 BIG RED BUTTON SYSTEM STARTING...")
    print()
    
    try:
        # Initialize components
        print("📋 Initializing broker...")
        broker = WebullBroker(paper_trading=True)
        
        print("📋 Initializing strategy manager...")
        strategy_manager = StrategyManager()
        
        print("📋 Initializing emergency kill switch...")
        kill_switch = get_emergency_kill_switch(broker, strategy_manager)
        
        print("🌐 Starting web dashboard...")
        print()
        print("🚨 EMERGENCY DASHBOARD READY 🚨")
        print()
        print("📱 Access the dashboard at: http://localhost:5000")
        print("🔑 Emergency password: EMERGENCY_2024")
        print()
        print("🚨 EMERGENCY CONTROLS AVAILABLE:")
        print("   🛑 STOP TRADING - Stop all new trades")
        print("   🔴 CLOSE POSITIONS - Close all positions")
        print("   ⚡ FULL SHUTDOWN - Complete system shutdown")
        print("   🔒 LOCK SYSTEM - Lock system completely")
        print()
        print("📱 You can also control via:")
        print("   - Web browser (any device)")
        print("   - Mobile app (when configured)")
        print("   - SMS commands (when configured)")
        print("   - Phone hotline (when configured)")
        print()
        print("⚠️  KEEP THIS DASHBOARD ACCESSIBLE AT ALL TIMES ⚠️")
        print("⚠️  THIS IS YOUR EMERGENCY LIFELINE ⚠️")
        print()
        print("Press Ctrl+C to stop the dashboard")
        print("-" * 50)
        
        # Start the dashboard
        run_emergency_dashboard(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n🛑 Emergency dashboard stopped by user")
        
    except Exception as e:
        print(f"\n💥 Error starting emergency dashboard: {e}")
        print("🚨 CRITICAL: Emergency system not available!")
        print("🚨 Fix this issue immediately!")
        sys.exit(1)

if __name__ == "__main__":
    main()
