"""Service Manager - Microservices management and orchestration.

This module provides comprehensive microservices management including:
- Service discovery and registration
- Load balancing and circuit breakers
- Health checks and auto-scaling
- Graceful shutdown and failover
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
import json

from ..core.config import settings
from ..utils.logger import get_structured_logger
from ..utils.retry import retry_manager

logger = get_structured_logger(__name__)


class ServiceStatus(Enum):
    """Service status enumeration."""
    STARTING = "starting"
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    STOPPED = "stopped"
    FAILED = "failed"


@dataclass
class ServiceInstance:
    """Represents a service instance."""
    service_name: str
    instance_id: str
    host: str
    port: int
    status: ServiceStatus = ServiceStatus.STARTING
    health_endpoint: str = "/health"
    last_health_check: Optional[datetime] = None
    consecutive_failures: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def url(self) -> str:
        """Get the base URL for this service instance."""
        return f"http://{self.host}:{self.port}"
    
    @property
    def health_url(self) -> str:
        """Get the health check URL for this service instance."""
        return f"{self.url}{self.health_endpoint}"


@dataclass
class ServiceConfig:
    """Configuration for a service."""
    name: str
    min_instances: int = 1
    max_instances: int = 3
    health_check_interval: int = 30  # seconds
    health_check_timeout: int = 5    # seconds
    max_consecutive_failures: int = 3
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 60  # seconds
    load_balancer_strategy: str = "round_robin"  # round_robin, least_connections, weighted
    auto_scaling_enabled: bool = True
    scaling_cpu_threshold: float = 80.0  # percentage
    scaling_memory_threshold: float = 85.0  # percentage


class CircuitBreaker:
    """Circuit breaker implementation for service calls."""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open
    
    def can_execute(self) -> bool:
        """Check if the circuit breaker allows execution."""
        if self.state == "closed":
            return True
        elif self.state == "open":
            if time.time() - self.last_failure_time > self.timeout:
                self.state = "half_open"
                return True
            return False
        else:  # half_open
            return True
    
    def record_success(self):
        """Record a successful operation."""
        self.failure_count = 0
        self.state = "closed"
    
    def record_failure(self):
        """Record a failed operation."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"


class LoadBalancer:
    """Load balancer for distributing requests across service instances."""
    
    def __init__(self, strategy: str = "round_robin"):
        self.strategy = strategy
        self.current_index = 0
        self.connection_counts = {}
    
    def select_instance(self, instances: List[ServiceInstance]) -> Optional[ServiceInstance]:
        """Select an instance based on the load balancing strategy."""
        healthy_instances = [i for i in instances if i.status == ServiceStatus.HEALTHY]
        
        if not healthy_instances:
            return None
        
        if self.strategy == "round_robin":
            instance = healthy_instances[self.current_index % len(healthy_instances)]
            self.current_index += 1
            return instance
        
        elif self.strategy == "least_connections":
            return min(healthy_instances, 
                      key=lambda i: self.connection_counts.get(i.instance_id, 0))
        
        else:  # Default to round robin
            return healthy_instances[0]
    
    def record_connection(self, instance_id: str):
        """Record a new connection to an instance."""
        self.connection_counts[instance_id] = self.connection_counts.get(instance_id, 0) + 1
    
    def record_disconnection(self, instance_id: str):
        """Record a disconnection from an instance."""
        if instance_id in self.connection_counts:
            self.connection_counts[instance_id] = max(0, self.connection_counts[instance_id] - 1)


class ServiceManager:
    """
    Comprehensive microservices management system.
    
    Provides service discovery, load balancing, health monitoring,
    circuit breakers, and auto-scaling capabilities.
    """
    
    def __init__(self):
        self.services: Dict[str, ServiceConfig] = {}
        self.service_instances: Dict[str, List[ServiceInstance]] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.load_balancers: Dict[str, LoadBalancer] = {}
        
        # Monitoring and management
        self.health_check_tasks: Dict[str, asyncio.Task] = {}
        self.auto_scaling_tasks: Dict[str, asyncio.Task] = {}
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # HTTP session for health checks
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self):
        """Initialize the service manager."""
        logger.info("Initializing service manager...")
        
        # Create HTTP session
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        # Register core services
        await self._register_core_services()
        
        self.is_running = True
        logger.info("Service manager initialized")
    
    async def _register_core_services(self):
        """Register core trading bot services."""
        core_services = [
            ServiceConfig(
                name="data_service",
                min_instances=1,
                max_instances=2,
                health_check_interval=30
            ),
            ServiceConfig(
                name="ml_service", 
                min_instances=1,
                max_instances=3,
                health_check_interval=60
            ),
            ServiceConfig(
                name="strategy_service",
                min_instances=1,
                max_instances=2,
                health_check_interval=30
            ),
            ServiceConfig(
                name="execution_service",
                min_instances=1,
                max_instances=1,  # Single instance for order consistency
                health_check_interval=15
            ),
            ServiceConfig(
                name="risk_service",
                min_instances=1,
                max_instances=2,
                health_check_interval=15
            ),
            ServiceConfig(
                name="analytics_service",
                min_instances=1,
                max_instances=2,
                health_check_interval=60
            )
        ]
        
        for service_config in core_services:
            await self.register_service(service_config)
    
    async def register_service(self, config: ServiceConfig):
        """Register a new service with the manager."""
        logger.info(f"Registering service: {config.name}")
        
        self.services[config.name] = config
        self.service_instances[config.name] = []
        self.circuit_breakers[config.name] = CircuitBreaker(
            failure_threshold=config.circuit_breaker_threshold,
            timeout=config.circuit_breaker_timeout
        )
        self.load_balancers[config.name] = LoadBalancer(config.load_balancer_strategy)
        
        # Start health monitoring
        health_task = asyncio.create_task(self._health_monitor_loop(config.name))
        self.health_check_tasks[config.name] = health_task
        
        # Start auto-scaling if enabled
        if config.auto_scaling_enabled:
            scaling_task = asyncio.create_task(self._auto_scaling_loop(config.name))
            self.auto_scaling_tasks[config.name] = scaling_task
        
        logger.info(f"Service {config.name} registered successfully")
    
    async def register_instance(self, service_name: str, instance: ServiceInstance):
        """Register a service instance."""
        if service_name not in self.services:
            logger.error(f"Service {service_name} not registered")
            return False
        
        self.service_instances[service_name].append(instance)
        logger.info(f"Instance {instance.instance_id} registered for service {service_name}")
        return True
    
    async def deregister_instance(self, service_name: str, instance_id: str):
        """Deregister a service instance."""
        if service_name not in self.service_instances:
            return False
        
        instances = self.service_instances[service_name]
        self.service_instances[service_name] = [
            i for i in instances if i.instance_id != instance_id
        ]
        
        logger.info(f"Instance {instance_id} deregistered from service {service_name}")
        return True
    
    async def get_service_instance(self, service_name: str) -> Optional[ServiceInstance]:
        """Get a healthy service instance using load balancing."""
        if service_name not in self.service_instances:
            logger.error(f"Service {service_name} not found")
            return None
        
        # Check circuit breaker
        circuit_breaker = self.circuit_breakers.get(service_name)
        if circuit_breaker and not circuit_breaker.can_execute():
            logger.warning(f"Circuit breaker open for service {service_name}")
            return None
        
        # Get instance from load balancer
        load_balancer = self.load_balancers.get(service_name)
        instances = self.service_instances[service_name]
        
        if load_balancer:
            return load_balancer.select_instance(instances)
        
        # Fallback to first healthy instance
        for instance in instances:
            if instance.status == ServiceStatus.HEALTHY:
                return instance
        
        return None

    async def call_service(self, service_name: str, endpoint: str,
                          method: str = "GET", data: Optional[Dict] = None,
                          timeout: int = 30) -> Optional[Dict[str, Any]]:
        """Make a call to a service with circuit breaker and retry logic."""
        instance = await self.get_service_instance(service_name)
        if not instance:
            logger.error(f"No healthy instance available for service {service_name}")
            return None

        circuit_breaker = self.circuit_breakers.get(service_name)
        load_balancer = self.load_balancers.get(service_name)

        try:
            # Record connection
            if load_balancer:
                load_balancer.record_connection(instance.instance_id)

            url = f"{instance.url}{endpoint}"

            async with self.session.request(
                method=method,
                url=url,
                json=data,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                if response.status == 200:
                    result = await response.json()

                    # Record success
                    if circuit_breaker:
                        circuit_breaker.record_success()

                    return result
                else:
                    logger.warning(f"Service call failed: {response.status}")
                    raise aiohttp.ClientError(f"HTTP {response.status}")

        except Exception as e:
            logger.error(f"Service call to {service_name} failed: {e}")

            # Record failure
            if circuit_breaker:
                circuit_breaker.record_failure()

            # Update instance health
            instance.consecutive_failures += 1
            if instance.consecutive_failures >= self.services[service_name].max_consecutive_failures:
                instance.status = ServiceStatus.UNHEALTHY

            return None

        finally:
            # Record disconnection
            if load_balancer:
                load_balancer.record_disconnection(instance.instance_id)

    async def _health_monitor_loop(self, service_name: str):
        """Health monitoring loop for a specific service."""
        config = self.services[service_name]

        while self.is_running and not self.shutdown_event.is_set():
            try:
                instances = self.service_instances.get(service_name, [])

                for instance in instances:
                    await self._check_instance_health(instance, config)

                await asyncio.sleep(config.health_check_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring for {service_name}: {e}")
                await asyncio.sleep(30)

    async def _check_instance_health(self, instance: ServiceInstance, config: ServiceConfig):
        """Check health of a specific service instance."""
        try:
            async with self.session.get(
                instance.health_url,
                timeout=aiohttp.ClientTimeout(total=config.health_check_timeout)
            ) as response:
                if response.status == 200:
                    health_data = await response.json()

                    # Update instance status
                    if health_data.get('status') == 'healthy':
                        instance.status = ServiceStatus.HEALTHY
                        instance.consecutive_failures = 0
                    else:
                        instance.status = ServiceStatus.DEGRADED

                    instance.last_health_check = datetime.utcnow()

                else:
                    self._handle_health_check_failure(instance, config)

        except Exception as e:
            logger.warning(f"Health check failed for {instance.instance_id}: {e}")
            self._handle_health_check_failure(instance, config)

    def _handle_health_check_failure(self, instance: ServiceInstance, config: ServiceConfig):
        """Handle health check failure for an instance."""
        instance.consecutive_failures += 1

        if instance.consecutive_failures >= config.max_consecutive_failures:
            instance.status = ServiceStatus.UNHEALTHY
            logger.warning(f"Instance {instance.instance_id} marked as unhealthy")
        else:
            instance.status = ServiceStatus.DEGRADED

    async def _auto_scaling_loop(self, service_name: str):
        """Auto-scaling loop for a specific service."""
        config = self.services[service_name]

        while self.is_running and not self.shutdown_event.is_set():
            try:
                await self._evaluate_scaling_needs(service_name, config)
                await asyncio.sleep(60)  # Check every minute

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in auto-scaling for {service_name}: {e}")
                await asyncio.sleep(60)

    async def _evaluate_scaling_needs(self, service_name: str, config: ServiceConfig):
        """Evaluate if scaling is needed for a service."""
        instances = self.service_instances.get(service_name, [])
        healthy_instances = [i for i in instances if i.status == ServiceStatus.HEALTHY]

        # Scale up if needed
        if len(healthy_instances) < config.min_instances:
            logger.info(f"Scaling up {service_name}: {len(healthy_instances)} < {config.min_instances}")
            await self._scale_up_service(service_name, config.min_instances - len(healthy_instances))

        # Scale down if too many instances
        elif len(healthy_instances) > config.max_instances:
            logger.info(f"Scaling down {service_name}: {len(healthy_instances)} > {config.max_instances}")
            await self._scale_down_service(service_name, len(healthy_instances) - config.max_instances)

        # Performance-based scaling (placeholder)
        # This would integrate with actual resource monitoring
        await self._performance_based_scaling(service_name, config)

    async def _scale_up_service(self, service_name: str, count: int):
        """Scale up a service by adding instances."""
        logger.info(f"Scaling up {service_name} by {count} instances")

        # This would integrate with container orchestration (Docker, Kubernetes)
        # For now, this is a placeholder
        for i in range(count):
            # In a real implementation, this would:
            # 1. Start a new container/pod
            # 2. Wait for it to be ready
            # 3. Register the new instance
            pass

    async def _scale_down_service(self, service_name: str, count: int):
        """Scale down a service by removing instances."""
        logger.info(f"Scaling down {service_name} by {count} instances")

        instances = self.service_instances.get(service_name, [])
        instances_to_remove = instances[-count:]  # Remove last instances

        for instance in instances_to_remove:
            await self._graceful_shutdown_instance(instance)
            await self.deregister_instance(service_name, instance.instance_id)

    async def _performance_based_scaling(self, service_name: str, config: ServiceConfig):
        """Evaluate scaling based on performance metrics."""
        # This would integrate with actual performance monitoring
        # For now, this is a placeholder

        instances = self.service_instances.get(service_name, [])
        healthy_instances = [i for i in instances if i.status == ServiceStatus.HEALTHY]

        # Example: Check if we need to scale based on load
        # avg_cpu = await self._get_average_cpu_usage(service_name)
        # avg_memory = await self._get_average_memory_usage(service_name)

        # if avg_cpu > config.scaling_cpu_threshold and len(healthy_instances) < config.max_instances:
        #     await self._scale_up_service(service_name, 1)
        # elif avg_cpu < 30 and len(healthy_instances) > config.min_instances:
        #     await self._scale_down_service(service_name, 1)

    async def _graceful_shutdown_instance(self, instance: ServiceInstance):
        """Gracefully shutdown a service instance."""
        try:
            # Send shutdown signal to instance
            await self.session.post(f"{instance.url}/shutdown", timeout=aiohttp.ClientTimeout(total=30))

            # Wait for graceful shutdown
            await asyncio.sleep(10)

        except Exception as e:
            logger.warning(f"Failed to gracefully shutdown instance {instance.instance_id}: {e}")

    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """Get status information for a service."""
        if service_name not in self.services:
            return {"error": "Service not found"}

        instances = self.service_instances.get(service_name, [])
        config = self.services[service_name]
        circuit_breaker = self.circuit_breakers.get(service_name)

        status_counts = {}
        for status in ServiceStatus:
            status_counts[status.value] = len([i for i in instances if i.status == status])

        return {
            "service_name": service_name,
            "config": {
                "min_instances": config.min_instances,
                "max_instances": config.max_instances,
                "auto_scaling_enabled": config.auto_scaling_enabled
            },
            "instances": {
                "total": len(instances),
                "status_breakdown": status_counts
            },
            "circuit_breaker": {
                "state": circuit_breaker.state if circuit_breaker else "unknown",
                "failure_count": circuit_breaker.failure_count if circuit_breaker else 0
            },
            "load_balancer": {
                "strategy": config.load_balancer_strategy
            }
        }

    def get_all_services_status(self) -> Dict[str, Any]:
        """Get status for all registered services."""
        return {
            service_name: self.get_service_status(service_name)
            for service_name in self.services.keys()
        }

    async def shutdown(self):
        """Graceful shutdown of the service manager."""
        logger.info("Shutting down service manager...")

        self.is_running = False
        self.shutdown_event.set()

        # Cancel all monitoring tasks
        all_tasks = list(self.health_check_tasks.values()) + list(self.auto_scaling_tasks.values())

        for task in all_tasks:
            task.cancel()

        if all_tasks:
            await asyncio.gather(*all_tasks, return_exceptions=True)

        # Gracefully shutdown all service instances
        for service_name, instances in self.service_instances.items():
            for instance in instances:
                await self._graceful_shutdown_instance(instance)

        # Close HTTP session
        if self.session:
            await self.session.close()

        logger.info("Service manager shutdown completed")
