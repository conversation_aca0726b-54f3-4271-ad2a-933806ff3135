#!/usr/bin/env python3
"""
Simple ML Training Script
Direct implementation without complex dependencies
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import yfinance as yf
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
import pickle

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def calculate_rsi(prices, window=14):
    """Calculate RSI indicator."""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))


def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD indicator."""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    macd_hist = macd - macd_signal
    return macd, macd_signal, macd_hist


def engineer_features(df):
    """Engineer features from price data."""
    # Returns
    df['returns'] = df['Close'].pct_change()
    df['returns_1d'] = df['returns']
    df['returns_5d'] = df['Close'].pct_change(5)
    df['returns_20d'] = df['Close'].pct_change(20)
    
    # Moving averages
    df['sma_5'] = df['Close'].rolling(5).mean()
    df['sma_20'] = df['Close'].rolling(20).mean()
    df['sma_50'] = df['Close'].rolling(50).mean()
    df['ema_12'] = df['Close'].ewm(span=12).mean()
    df['ema_26'] = df['Close'].ewm(span=26).mean()
    
    # Price ratios
    df['price_to_sma20'] = df['Close'] / df['sma_20']
    df['price_to_sma50'] = df['Close'] / df['sma_50']
    df['sma_ratio'] = df['sma_5'] / df['sma_20']
    
    # Volatility
    df['volatility_20d'] = df['returns'].rolling(20).std()
    df['high_low_ratio'] = df['High'] / df['Low']
    
    # Volume features
    df['volume_sma20'] = df['Volume'].rolling(20).mean()
    df['volume_ratio'] = df['Volume'] / df['volume_sma20']
    df['volume_change'] = df['Volume'].pct_change()
    
    # Technical indicators
    df['rsi'] = calculate_rsi(df['Close'])
    macd, macd_signal, macd_hist = calculate_macd(df['Close'])
    df['macd'] = macd
    df['macd_signal'] = macd_signal
    df['macd_hist'] = macd_hist
    
    # Bollinger Bands
    bb_period = 20
    bb_std = 2
    df['bb_middle'] = df['Close'].rolling(bb_period).mean()
    bb_std_val = df['Close'].rolling(bb_period).std()
    df['bb_upper'] = df['bb_middle'] + (bb_std_val * bb_std)
    df['bb_lower'] = df['bb_middle'] - (bb_std_val * bb_std)
    df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
    df['bb_position'] = (df['Close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # Momentum indicators
    df['momentum_10'] = df['Close'] / df['Close'].shift(10)
    df['momentum_20'] = df['Close'] / df['Close'].shift(20)
    
    # Create target: next day return direction (1 if positive, 0 if negative)
    df['target'] = (df['returns'].shift(-1) > 0).astype(int)
    
    return df


def get_training_data(symbols):
    """Get and prepare training data for multiple symbols."""
    logger.info(f"Fetching training data for {len(symbols)} symbols...")
    
    all_data = []
    
    for symbol in symbols:
        logger.info(f"Processing {symbol}...")
        
        try:
            # Get 2 years of daily data
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="2y")
            
            if hist.empty:
                logger.warning(f"No data for {symbol}")
                continue
            
            # Reset index to get datetime as column
            df = hist.reset_index()
            df['symbol'] = symbol
            
            # Engineer features
            df = engineer_features(df)
            
            # Add symbol encoding
            df['symbol_encoded'] = hash(symbol) % 1000  # Simple symbol encoding
            
            all_data.append(df)
            logger.info(f"✅ {symbol}: {len(df)} data points")
            
        except Exception as e:
            logger.error(f"Error processing {symbol}: {e}")
            continue
    
    if not all_data:
        raise Exception("No training data collected")
    
    # Combine all data
    combined_df = pd.concat(all_data, ignore_index=True)
    logger.info(f"Combined dataset: {len(combined_df)} total records")
    
    return combined_df


def prepare_ml_data(df):
    """Prepare data for machine learning."""
    # Feature columns (exclude target and metadata)
    feature_cols = [
        'returns_1d', 'returns_5d', 'returns_20d',
        'price_to_sma20', 'price_to_sma50', 'sma_ratio',
        'volatility_20d', 'high_low_ratio',
        'volume_ratio', 'volume_change',
        'rsi', 'macd', 'macd_signal', 'macd_hist',
        'bb_width', 'bb_position',
        'momentum_10', 'momentum_20',
        'symbol_encoded'
    ]
    
    # Remove rows with NaN values
    df_clean = df.dropna()
    
    # Check we have enough data
    if len(df_clean) < 100:
        raise Exception(f"Not enough data after cleaning: {len(df_clean)} rows")
    
    # Prepare features and target
    X = df_clean[feature_cols]
    y = df_clean['target']
    
    logger.info(f"ML dataset: {len(X)} samples, {len(feature_cols)} features")
    logger.info(f"Target distribution: {y.value_counts().to_dict()}")
    
    return X, y, feature_cols


def train_ml_model():
    """Train a machine learning model."""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                   Simple ML Training                         ║
    ║                                                              ║
    ║  🤖 Training a Random Forest model to predict stock moves   ║
    ║  📊 Using Yahoo Finance data with technical indicators      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # 1. Get training data
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN']
    logger.info(f"Training symbols: {symbols}")
    
    training_data = get_training_data(symbols)
    
    # 2. Prepare ML data
    X, y, feature_cols = prepare_ml_data(training_data)
    
    # 3. Train model
    logger.info("Training machine learning model...")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Train Random Forest
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=10,
        min_samples_leaf=5,
        random_state=42,
        n_jobs=-1
    )
    
    model.fit(X_train, y_train)
    
    # 4. Evaluate model
    logger.info("Evaluating model...")
    
    # Predictions
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    # Accuracy
    train_accuracy = accuracy_score(y_train, y_pred_train)
    test_accuracy = accuracy_score(y_test, y_pred_test)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_train, y_train, cv=5)
    
    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': feature_cols,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    # 5. Results
    print("\n🎯 Model Training Results:")
    print("=" * 50)
    print(f"Training samples: {len(X_train):,}")
    print(f"Test samples: {len(X_test):,}")
    print(f"Features: {len(feature_cols)}")
    print(f"\nAccuracy:")
    print(f"  Training: {train_accuracy:.4f} ({train_accuracy*100:.2f}%)")
    print(f"  Test: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
    print(f"  Cross-validation: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    print(f"\nTop 10 Most Important Features:")
    for i, (feature, importance) in enumerate(feature_importance.head(10).values):
        print(f"  {i+1:2d}. {feature:<20} {importance:.4f}")
    
    print(f"\nClassification Report:")
    print(classification_report(y_test, y_pred_test))
    
    # 6. Save model
    model_path = "simple_trading_model.pkl"
    with open(model_path, 'wb') as f:
        pickle.dump({
            'model': model,
            'feature_cols': feature_cols,
            'training_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'cv_scores': cv_scores,
            'feature_importance': feature_importance
        }, f)
    
    logger.info(f"Model saved to {model_path}")
    
    # 7. Test prediction on recent data
    logger.info("Testing prediction on recent data...")
    
    try:
        # Get recent data for AAPL
        ticker = yf.Ticker("AAPL")
        recent_data = ticker.history(period="1mo")
        
        if not recent_data.empty:
            # Convert to DataFrame and engineer features
            recent_df = recent_data.reset_index()
            recent_df['symbol'] = 'AAPL'
            recent_df = engineer_features(recent_df)
            recent_df['symbol_encoded'] = hash('AAPL') % 1000
            
            # Get latest features
            latest_features = recent_df[feature_cols].iloc[-1:].dropna()
            
            if not latest_features.empty:
                prediction = model.predict(latest_features)[0]
                probability = model.predict_proba(latest_features)[0]
                
                print(f"\n🔮 Latest Prediction for AAPL:")
                print(f"  Direction: {'UP' if prediction == 1 else 'DOWN'}")
                print(f"  Confidence: {max(probability):.2%}")
                print(f"  Probabilities: UP={probability[1]:.2%}, DOWN={probability[0]:.2%}")
            
    except Exception as e:
        logger.warning(f"Could not generate recent prediction: {e}")
    
    print(f"\n🎉 Simple ML Training Complete!")
    print(f"✅ Model accuracy: {test_accuracy:.2%}")
    print(f"✅ Model saved: {model_path}")
    print(f"\nNext steps:")
    print(f"  1. The model is ready to use for predictions")
    print(f"  2. You can load it with: pickle.load(open('{model_path}', 'rb'))")
    print(f"  3. Try enhancing with more features or different models")
    
    return model, test_accuracy


def main():
    """Main function."""
    try:
        model, accuracy = train_ml_model()
        
        if accuracy > 0.5:
            print(f"\n🚀 SUCCESS! ML model trained with {accuracy:.2%} accuracy")
            print("Your trading bot machine learning system is now working!")
        else:
            print(f"\n⚠️ Model trained but accuracy is low: {accuracy:.2%}")
            print("Consider adding more features or data for better performance.")
            
        return True
        
    except Exception as e:
        print(f"\n💥 Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)