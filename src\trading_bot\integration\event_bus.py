"""Event Bus - Event-driven architecture implementation.

This module provides a comprehensive event bus system for decoupled
communication between system components using publish-subscribe patterns.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
import aioredis
from collections import defaultdict

from ..core.config import settings
from ..utils.logger import get_structured_logger

logger = get_structured_logger(__name__)


class EventPriority(Enum):
    """Event priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Event:
    """Event data structure."""
    id: str
    type: str
    source: str
    data: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.utcnow)
    priority: EventPriority = EventPriority.NORMAL
    correlation_id: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    ttl_seconds: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EventSubscription:
    """Event subscription configuration."""
    event_type: str
    handler: Callable
    filter_func: Optional[Callable] = None
    priority: EventPriority = EventPriority.NORMAL
    max_concurrent: int = 10
    retry_on_failure: bool = True
    dead_letter_queue: bool = True


class EventBus:
    """
    Comprehensive event bus for event-driven architecture.
    
    Provides publish-subscribe messaging with:
    - Event filtering and routing
    - Priority-based processing
    - Retry mechanisms and dead letter queues
    - Distributed event handling via Redis
    - Performance monitoring and metrics
    """
    
    def __init__(self):
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Local event handling
        self.subscribers: Dict[str, List[EventSubscription]] = defaultdict(list)
        self.event_queue: asyncio.Queue = asyncio.Queue()
        self.dead_letter_queue: asyncio.Queue = asyncio.Queue()
        
        # Distributed event handling
        self.redis_client: Optional[aioredis.Redis] = None
        self.redis_pubsub = None
        
        # Processing tasks
        self.processing_tasks: List[asyncio.Task] = []
        self.worker_count = 4
        
        # Metrics and monitoring
        self.events_published = 0
        self.events_processed = 0
        self.events_failed = 0
        self.processing_times: List[float] = []
        
        # Event types registry
        self.registered_event_types: Set[str] = set()
        
        # Built-in event types
        self._register_builtin_events()
    
    def _register_builtin_events(self):
        """Register built-in event types."""
        builtin_events = [
            # Trading events
            "order.placed",
            "order.filled",
            "order.cancelled",
            "position.opened",
            "position.closed",
            "trade.executed",
            
            # Market data events
            "market.data.update",
            "market.hours.changed",
            "price.alert",
            "volume.spike",
            
            # Risk events
            "risk.limit.breached",
            "drawdown.alert",
            "position.size.exceeded",
            "correlation.warning",
            
            # System events
            "system.startup",
            "system.shutdown",
            "system.error",
            "health.check.failed",
            "performance.degraded",
            
            # ML events
            "model.prediction",
            "model.retrained",
            "model.drift.detected",
            "signal.generated",
            
            # Strategy events
            "strategy.started",
            "strategy.stopped",
            "strategy.signal",
            "strategy.performance.update"
        ]
        
        self.registered_event_types.update(builtin_events)
    
    async def initialize(self):
        """Initialize the event bus."""
        logger.info("Initializing event bus...")
        
        # Initialize Redis connection for distributed events
        await self._initialize_redis()
        
        # Start processing workers
        await self._start_workers()
        
        self.is_running = True
        logger.info("Event bus initialized")
    
    async def _initialize_redis(self):
        """Initialize Redis connection for distributed event handling."""
        try:
            self.redis_client = aioredis.from_url(
                f"redis://{settings.redis.host}:{settings.redis.port}",
                password=settings.redis.password,
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            
            # Initialize pub/sub
            self.redis_pubsub = self.redis_client.pubsub()
            
            logger.info("Redis connection established for event bus")
        
        except Exception as e:
            logger.error(f"Failed to initialize Redis for event bus: {e}")
            self.redis_client = None
    
    async def _start_workers(self):
        """Start event processing workers."""
        for i in range(self.worker_count):
            worker_task = asyncio.create_task(self._event_worker(f"worker-{i}"))
            self.processing_tasks.append(worker_task)
        
        # Start Redis subscriber if available
        if self.redis_client:
            redis_task = asyncio.create_task(self._redis_subscriber())
            self.processing_tasks.append(redis_task)
        
        # Start dead letter queue processor
        dlq_task = asyncio.create_task(self._dead_letter_processor())
        self.processing_tasks.append(dlq_task)
        
        logger.info(f"Started {len(self.processing_tasks)} event processing workers")
    
    async def _event_worker(self, worker_id: str):
        """Event processing worker."""
        logger.debug(f"Event worker {worker_id} started")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Get event from queue with timeout
                event = await asyncio.wait_for(
                    self.event_queue.get(),
                    timeout=1.0
                )
                
                await self._process_event(event, worker_id)
                self.event_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in event worker {worker_id}: {e}")
                await asyncio.sleep(1)
        
        logger.debug(f"Event worker {worker_id} stopped")
    
    async def _process_event(self, event: Event, worker_id: str):
        """Process a single event."""
        start_time = time.time()
        
        try:
            # Get subscribers for this event type
            subscribers = self.subscribers.get(event.type, [])
            
            if not subscribers:
                logger.debug(f"No subscribers for event type: {event.type}")
                return
            
            # Process event for each subscriber
            for subscription in subscribers:
                try:
                    # Apply filter if present
                    if subscription.filter_func:
                        if not subscription.filter_func(event):
                            continue
                    
                    # Call event handler
                    if asyncio.iscoroutinefunction(subscription.handler):
                        await subscription.handler(event)
                    else:
                        subscription.handler(event)
                    
                    logger.debug(f"Event {event.id} processed by {subscription.handler.__name__}")
                
                except Exception as e:
                    logger.error(f"Error processing event {event.id} in handler {subscription.handler.__name__}: {e}")
                    
                    if subscription.retry_on_failure and event.retry_count < event.max_retries:
                        event.retry_count += 1
                        await self.event_queue.put(event)
                        logger.info(f"Event {event.id} queued for retry ({event.retry_count}/{event.max_retries})")
                    elif subscription.dead_letter_queue:
                        await self.dead_letter_queue.put(event)
                        logger.warning(f"Event {event.id} moved to dead letter queue")
                    
                    self.events_failed += 1
            
            self.events_processed += 1
            
        except Exception as e:
            logger.error(f"Error processing event {event.id}: {e}")
            self.events_failed += 1
        
        finally:
            # Record processing time
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            
            # Keep only recent processing times
            if len(self.processing_times) > 1000:
                self.processing_times = self.processing_times[-1000:]
    
    async def _redis_subscriber(self):
        """Redis pub/sub subscriber for distributed events."""
        if not self.redis_pubsub:
            return
        
        try:
            # Subscribe to all trading bot events
            await self.redis_pubsub.subscribe("trading_bot:events:*")
            
            logger.info("Redis event subscriber started")
            
            while self.is_running and not self.shutdown_event.is_set():
                try:
                    message = await self.redis_pubsub.get_message(timeout=1.0)
                    
                    if message and message['type'] == 'message':
                        # Parse event from Redis message
                        event_data = json.loads(message['data'])
                        event = Event(**event_data)
                        
                        # Add to local processing queue
                        await self.event_queue.put(event)
                        
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    logger.error(f"Error in Redis subscriber: {e}")
                    await asyncio.sleep(5)
        
        except Exception as e:
            logger.error(f"Redis subscriber failed: {e}")
        
        finally:
            if self.redis_pubsub:
                await self.redis_pubsub.unsubscribe()
    
    async def _dead_letter_processor(self):
        """Process events in the dead letter queue."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Get event from dead letter queue
                event = await asyncio.wait_for(
                    self.dead_letter_queue.get(),
                    timeout=5.0
                )
                
                # Log dead letter event
                logger.error(f"Dead letter event: {event.id} - {event.type} from {event.source}")
                
                # Store in persistent storage for analysis
                if self.redis_client:
                    await self.redis_client.lpush(
                        "trading_bot:dead_letter_events",
                        json.dumps({
                            'id': event.id,
                            'type': event.type,
                            'source': event.source,
                            'timestamp': event.timestamp.isoformat(),
                            'retry_count': event.retry_count,
                            'data': event.data
                        })
                    )
                
                self.dead_letter_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in dead letter processor: {e}")
                await asyncio.sleep(5)
    
    def register_event_type(self, event_type: str):
        """Register a new event type."""
        self.registered_event_types.add(event_type)
        logger.debug(f"Registered event type: {event_type}")
    
    def subscribe(self, event_type: str, handler: Callable, 
                 filter_func: Optional[Callable] = None,
                 priority: EventPriority = EventPriority.NORMAL,
                 **kwargs) -> EventSubscription:
        """Subscribe to events of a specific type."""
        subscription = EventSubscription(
            event_type=event_type,
            handler=handler,
            filter_func=filter_func,
            priority=priority,
            **kwargs
        )
        
        self.subscribers[event_type].append(subscription)
        
        logger.info(f"Subscribed {handler.__name__} to event type: {event_type}")
        return subscription
    
    def unsubscribe(self, event_type: str, handler: Callable):
        """Unsubscribe from events."""
        if event_type in self.subscribers:
            self.subscribers[event_type] = [
                sub for sub in self.subscribers[event_type]
                if sub.handler != handler
            ]
            
            logger.info(f"Unsubscribed {handler.__name__} from event type: {event_type}")
    
    async def publish(self, event_type: str, source: str, data: Dict[str, Any],
                     priority: EventPriority = EventPriority.NORMAL,
                     correlation_id: Optional[str] = None,
                     **kwargs) -> str:
        """Publish an event."""
        # Generate event ID
        event_id = f"{event_type}_{int(time.time() * 1000000)}"
        
        # Create event
        event = Event(
            id=event_id,
            type=event_type,
            source=source,
            data=data,
            priority=priority,
            correlation_id=correlation_id,
            **kwargs
        )
        
        # Add to local queue
        await self.event_queue.put(event)
        
        # Publish to Redis for distributed processing
        if self.redis_client:
            try:
                event_data = {
                    'id': event.id,
                    'type': event.type,
                    'source': event.source,
                    'data': event.data,
                    'timestamp': event.timestamp.isoformat(),
                    'priority': event.priority.value,
                    'correlation_id': event.correlation_id,
                    'metadata': event.metadata
                }
                
                await self.redis_client.publish(
                    f"trading_bot:events:{event_type}",
                    json.dumps(event_data)
                )
            
            except Exception as e:
                logger.error(f"Failed to publish event to Redis: {e}")
        
        self.events_published += 1
        logger.debug(f"Published event: {event_id} - {event_type}")
        
        return event_id

    def get_metrics(self) -> Dict[str, Any]:
        """Get event bus metrics."""
        avg_processing_time = (
            sum(self.processing_times) / len(self.processing_times)
            if self.processing_times else 0
        )

        return {
            'events_published': self.events_published,
            'events_processed': self.events_processed,
            'events_failed': self.events_failed,
            'success_rate': (
                self.events_processed / max(1, self.events_published)
                if self.events_published > 0 else 0
            ),
            'avg_processing_time_ms': avg_processing_time * 1000,
            'queue_size': self.event_queue.qsize(),
            'dead_letter_queue_size': self.dead_letter_queue.qsize(),
            'subscriber_count': sum(len(subs) for subs in self.subscribers.values()),
            'registered_event_types': len(self.registered_event_types),
            'worker_count': self.worker_count
        }

    def get_event_types(self) -> List[str]:
        """Get all registered event types."""
        return sorted(list(self.registered_event_types))

    def get_subscribers(self, event_type: Optional[str] = None) -> Dict[str, List[str]]:
        """Get subscriber information."""
        if event_type:
            return {
                event_type: [sub.handler.__name__ for sub in self.subscribers.get(event_type, [])]
            }

        return {
            et: [sub.handler.__name__ for sub in subs]
            for et, subs in self.subscribers.items()
        }

    async def shutdown(self):
        """Graceful shutdown of the event bus."""
        logger.info("Shutting down event bus...")

        self.is_running = False
        self.shutdown_event.set()

        # Wait for queues to empty
        if not self.event_queue.empty():
            logger.info("Waiting for event queue to empty...")
            await self.event_queue.join()

        # Cancel all processing tasks
        for task in self.processing_tasks:
            task.cancel()

        if self.processing_tasks:
            await asyncio.gather(*self.processing_tasks, return_exceptions=True)

        # Close Redis connections
        if self.redis_pubsub:
            await self.redis_pubsub.close()

        if self.redis_client:
            await self.redis_client.close()

        logger.info("Event bus shutdown completed")


# Global event bus instance
event_bus = EventBus()
