"""
Disaster Recovery Manager

This module provides comprehensive disaster recovery capabilities including:
- Automated backup verification
- Geographic redundancy
- Real-time data replication
- Disaster recovery orchestration
- Failover automation
- Recovery testing
"""

import asyncio
import json
import shutil
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import aiofiles
import aiofiles.os

from ..core.logger import get_logger
from ..core.config import settings

logger = get_logger(__name__)


class BackupType(Enum):
    """Types of backups."""
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"
    SNAPSHOT = "snapshot"


class BackupStatus(Enum):
    """Backup status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    VERIFIED = "verified"
    CORRUPTED = "corrupted"


class RecoveryObjective(Enum):
    """Recovery objectives."""
    RTO = "rto"  # Recovery Time Objective
    RPO = "rpo"  # Recovery Point Objective


@dataclass
class BackupRecord:
    """Backup record tracking."""
    backup_id: str
    timestamp: datetime
    backup_type: BackupType
    status: BackupStatus
    source_path: str
    backup_path: str
    size_bytes: int
    checksum: str
    compression_ratio: float
    verification_status: Optional[str] = None
    verification_timestamp: Optional[datetime] = None
    retention_until: Optional[datetime] = None


@dataclass
class ReplicationTarget:
    """Replication target configuration."""
    target_id: str
    name: str
    location: str  # geographic location
    endpoint: str
    credentials: Dict[str, str]
    priority: int  # 1 = primary, 2 = secondary, etc.
    lag_tolerance_seconds: int
    enabled: bool = True


@dataclass
class RecoveryPlan:
    """Disaster recovery plan."""
    plan_id: str
    name: str
    description: str
    trigger_conditions: List[str]
    recovery_steps: List[Dict[str, Any]]
    estimated_rto_minutes: int
    estimated_rpo_minutes: int
    last_tested: Optional[datetime] = None
    test_success_rate: float = 0.0


class DisasterRecoveryManager:
    """Comprehensive disaster recovery management."""
    
    def __init__(self, backup_root: str = "backups", replication_config: Optional[Dict] = None):
        self.backup_root = Path(backup_root)
        self.backup_root.mkdir(exist_ok=True)
        
        # Backup tracking
        self.backup_records: List[BackupRecord] = []
        self.active_backups: Dict[str, asyncio.Task] = {}
        
        # Replication
        self.replication_targets: List[ReplicationTarget] = []
        self.replication_tasks: Dict[str, asyncio.Task] = {}
        
        # Recovery plans
        self.recovery_plans: List[RecoveryPlan] = []
        
        # Configuration
        self.config = {
            'backup_retention_days': 30,
            'verification_interval_hours': 6,
            'replication_check_interval_seconds': 60,
            'max_concurrent_backups': 3,
            'compression_enabled': True,
            'encryption_enabled': True,
            'geographic_redundancy_required': True,
            'min_replication_targets': 2
        }
        
        # Recovery objectives
        self.recovery_objectives = {
            RecoveryObjective.RTO: 15,  # 15 minutes
            RecoveryObjective.RPO: 5    # 5 minutes
        }
        
        # Monitoring
        self.is_monitoring = False
        self.monitoring_tasks: List[asyncio.Task] = []
        
        # Initialize replication targets
        if replication_config:
            self._initialize_replication_targets(replication_config)
    
    def _initialize_replication_targets(self, config: Dict):
        """Initialize replication targets from configuration."""
        for target_config in config.get('targets', []):
            target = ReplicationTarget(
                target_id=target_config['id'],
                name=target_config['name'],
                location=target_config['location'],
                endpoint=target_config['endpoint'],
                credentials=target_config.get('credentials', {}),
                priority=target_config.get('priority', 2),
                lag_tolerance_seconds=target_config.get('lag_tolerance', 300),
                enabled=target_config.get('enabled', True)
            )
            self.replication_targets.append(target)
    
    async def start_monitoring(self):
        """Start disaster recovery monitoring."""
        if self.is_monitoring:
            logger.warning("DR monitoring already started")
            return
        
        self.is_monitoring = True
        logger.info("Starting disaster recovery monitoring...")
        
        # Start monitoring tasks
        self.monitoring_tasks = [
            asyncio.create_task(self._backup_scheduler()),
            asyncio.create_task(self._backup_verifier()),
            asyncio.create_task(self._replication_monitor()),
            asyncio.create_task(self._retention_manager()),
            asyncio.create_task(self._health_checker())
        ]
        
        logger.info("Disaster recovery monitoring started")
        
        try:
            await asyncio.gather(*self.monitoring_tasks)
        except Exception as e:
            logger.error(f"DR monitoring error: {e}")
        finally:
            self.is_monitoring = False
    
    async def stop_monitoring(self):
        """Stop disaster recovery monitoring."""
        self.is_monitoring = False
        
        # Cancel monitoring tasks
        for task in self.monitoring_tasks:
            if not task.done():
                task.cancel()
        
        # Cancel active backups
        for backup_id, task in self.active_backups.items():
            if not task.done():
                logger.info(f"Cancelling backup {backup_id}")
                task.cancel()
        
        # Cancel replication tasks
        for target_id, task in self.replication_tasks.items():
            if not task.done():
                logger.info(f"Cancelling replication to {target_id}")
                task.cancel()
        
        # Wait for tasks to complete
        all_tasks = self.monitoring_tasks + list(self.active_backups.values()) + list(self.replication_tasks.values())
        if all_tasks:
            await asyncio.gather(*all_tasks, return_exceptions=True)
        
        self.monitoring_tasks.clear()
        self.active_backups.clear()
        self.replication_tasks.clear()
        
        logger.info("Disaster recovery monitoring stopped")
    
    async def create_backup(self, 
                           source_path: str,
                           backup_type: BackupType = BackupType.FULL,
                           description: str = "") -> str:
        """Create a backup of specified data."""
        
        backup_id = f"backup_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{backup_type.value}"
        
        # Check if we're at max concurrent backups
        if len(self.active_backups) >= self.config['max_concurrent_backups']:
            logger.warning(f"Max concurrent backups reached, queuing backup {backup_id}")
            # In production, implement a queue
            await asyncio.sleep(60)
        
        # Create backup task
        backup_task = asyncio.create_task(
            self._execute_backup(backup_id, source_path, backup_type, description)
        )
        
        self.active_backups[backup_id] = backup_task
        
        logger.info(f"Started backup {backup_id} for {source_path}")
        return backup_id
    
    async def _execute_backup(self, 
                             backup_id: str,
                             source_path: str,
                             backup_type: BackupType,
                             description: str):
        """Execute backup operation."""
        
        try:
            source = Path(source_path)
            if not source.exists():
                raise FileNotFoundError(f"Source path not found: {source_path}")
            
            # Create backup directory
            backup_dir = self.backup_root / backup_id
            backup_dir.mkdir(exist_ok=True)
            
            # Create backup record
            backup_record = BackupRecord(
                backup_id=backup_id,
                timestamp=datetime.utcnow(),
                backup_type=backup_type,
                status=BackupStatus.IN_PROGRESS,
                source_path=source_path,
                backup_path=str(backup_dir),
                size_bytes=0,
                checksum="",
                compression_ratio=0.0,
                retention_until=datetime.utcnow() + timedelta(days=self.config['backup_retention_days'])
            )
            
            self.backup_records.append(backup_record)
            
            # Perform backup based on type
            if backup_type == BackupType.FULL:
                await self._full_backup(source, backup_dir)
            elif backup_type == BackupType.INCREMENTAL:
                await self._incremental_backup(source, backup_dir)
            elif backup_type == BackupType.SNAPSHOT:
                await self._snapshot_backup(source, backup_dir)
            
            # Calculate size and checksum
            backup_record.size_bytes = await self._calculate_directory_size(backup_dir)
            backup_record.checksum = await self._calculate_directory_checksum(backup_dir)
            backup_record.status = BackupStatus.COMPLETED
            
            # Compress if enabled
            if self.config['compression_enabled']:
                compressed_path = await self._compress_backup(backup_dir)
                original_size = backup_record.size_bytes
                compressed_size = await self._calculate_file_size(compressed_path)
                backup_record.compression_ratio = compressed_size / original_size
                backup_record.backup_path = str(compressed_path)
            
            # Encrypt if enabled
            if self.config['encryption_enabled']:
                encrypted_path = await self._encrypt_backup(backup_record.backup_path)
                backup_record.backup_path = encrypted_path
            
            # Replicate to targets
            await self._replicate_backup(backup_record)
            
            logger.info(f"Backup {backup_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Backup {backup_id} failed: {e}")
            backup_record.status = BackupStatus.FAILED
            
        finally:
            # Remove from active backups
            if backup_id in self.active_backups:
                del self.active_backups[backup_id]
    
    async def _full_backup(self, source: Path, backup_dir: Path):
        """Perform full backup."""
        if source.is_file():
            await self._copy_file(source, backup_dir / source.name)
        else:
            await self._copy_directory(source, backup_dir)
    
    async def _incremental_backup(self, source: Path, backup_dir: Path):
        """Perform incremental backup."""
        # Find last backup
        last_backup = self._find_last_backup(str(source))
        
        if not last_backup:
            # No previous backup, perform full backup
            await self._full_backup(source, backup_dir)
            return
        
        # Copy only changed files since last backup
        await self._copy_changed_files(source, backup_dir, last_backup.timestamp)
    
    async def _snapshot_backup(self, source: Path, backup_dir: Path):
        """Perform snapshot backup."""
        # Create point-in-time snapshot
        snapshot_metadata = {
            'timestamp': datetime.utcnow().isoformat(),
            'source_path': str(source),
            'snapshot_type': 'filesystem'
        }
        
        # Save metadata
        metadata_file = backup_dir / 'snapshot_metadata.json'
        async with aiofiles.open(metadata_file, 'w') as f:
            await f.write(json.dumps(snapshot_metadata, indent=2))
        
        # Perform full backup for now (in production, use filesystem snapshots)
        await self._full_backup(source, backup_dir)
    
    async def _copy_file(self, source: Path, destination: Path):
        """Copy a single file."""
        destination.parent.mkdir(parents=True, exist_ok=True)
        await aiofiles.os.link(str(source), str(destination))
    
    async def _copy_directory(self, source: Path, destination: Path):
        """Copy entire directory."""
        for item in source.rglob('*'):
            if item.is_file():
                relative_path = item.relative_to(source)
                dest_file = destination / relative_path
                await self._copy_file(item, dest_file)
    
    async def _copy_changed_files(self, source: Path, backup_dir: Path, since: datetime):
        """Copy only files changed since specified time."""
        for item in source.rglob('*'):
            if item.is_file():
                stat = item.stat()
                if datetime.fromtimestamp(stat.st_mtime) > since:
                    relative_path = item.relative_to(source)
                    dest_file = backup_dir / relative_path
                    await self._copy_file(item, dest_file)
    
    async def _calculate_directory_size(self, directory: Path) -> int:
        """Calculate total size of directory."""
        total_size = 0
        for item in directory.rglob('*'):
            if item.is_file():
                total_size += item.stat().st_size
        return total_size
    
    async def _calculate_file_size(self, file_path: str) -> int:
        """Calculate size of a single file."""
        return Path(file_path).stat().st_size
    
    async def _calculate_directory_checksum(self, directory: Path) -> str:
        """Calculate checksum for directory contents."""
        hasher = hashlib.sha256()
        
        for item in sorted(directory.rglob('*')):
            if item.is_file():
                async with aiofiles.open(item, 'rb') as f:
                    while chunk := await f.read(8192):
                        hasher.update(chunk)
        
        return hasher.hexdigest()
    
    async def _compress_backup(self, backup_dir: Path) -> str:
        """Compress backup directory."""
        import tarfile
        
        compressed_path = f"{backup_dir}.tar.gz"
        
        with tarfile.open(compressed_path, 'w:gz') as tar:
            tar.add(backup_dir, arcname=backup_dir.name)
        
        # Remove original directory
        shutil.rmtree(backup_dir)
        
        return compressed_path
    
    async def _encrypt_backup(self, backup_path: str) -> str:
        """Encrypt backup file."""
        # In production, use proper encryption like AES-256
        encrypted_path = f"{backup_path}.enc"
        
        # Placeholder encryption (use cryptography library in production)
        async with aiofiles.open(backup_path, 'rb') as source:
            async with aiofiles.open(encrypted_path, 'wb') as dest:
                content = await source.read()
                # Simple XOR encryption for demo (use proper encryption in production)
                encrypted = bytes(b ^ 0x42 for b in content)
                await dest.write(encrypted)
        
        # Remove unencrypted file
        await aiofiles.os.remove(backup_path)
        
        return encrypted_path
