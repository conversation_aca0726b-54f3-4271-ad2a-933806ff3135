"""
Production monitoring system for AI Trading Bot.

This module provides comprehensive real-time monitoring of all system
components, performance metrics, and trading operations for production deployment.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
import psutil
import aiohttp
from prometheus_client import Counter, Histogram, Gauge, start_http_server

from ...core.config import Config
from ...utils.logger import get_logger

logger = get_logger(__name__)


class MetricType(Enum):
    """Types of metrics collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class MonitoringMetrics:
    """Real-time monitoring metrics."""
    # Trading Metrics
    total_trades: int = 0
    successful_trades: int = 0
    failed_trades: int = 0
    total_pnl: float = 0.0
    daily_pnl: float = 0.0
    current_positions: int = 0
    
    # Performance Metrics
    avg_response_time: float = 0.0
    max_response_time: float = 0.0
    api_requests_per_second: float = 0.0
    error_rate: float = 0.0
    
    # System Metrics
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    network_io: Dict[str, float] = field(default_factory=dict)
    
    # Database Metrics
    db_connections: int = 0
    db_query_time: float = 0.0
    cache_hit_rate: float = 0.0
    
    # ML Metrics
    model_accuracy: float = 0.0
    prediction_latency: float = 0.0
    model_drift_score: float = 0.0
    
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class Alert:
    """System alert."""
    id: str
    severity: AlertSeverity
    title: str
    message: str
    component: str
    metric_name: str
    current_value: float
    threshold_value: float
    timestamp: datetime = field(default_factory=datetime.utcnow)
    acknowledged: bool = False
    resolved: bool = False


class ProductionMonitor:
    """
    Comprehensive production monitoring system.
    
    Monitors all aspects of the trading bot in production:
    - Real-time P&L tracking (second-by-second)
    - Position exposure by sector
    - Risk metric dashboards
    - API usage and limits
    - System resource usage
    - Model prediction accuracy
    - Order fill rates
    - Slippage analysis
    - Database performance
    - Network connectivity
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.is_monitoring = False
        self.metrics = MonitoringMetrics()
        self.alerts: List[Alert] = []
        self.metric_history: List[MonitoringMetrics] = []
        
        # Prometheus metrics
        self._setup_prometheus_metrics()
        
        # Alert thresholds
        self.alert_thresholds = {
            'daily_loss': 0.01,  # 1%
            'position_size': 0.05,  # 5%
            'api_error_rate': 0.05,  # 5%
            'model_accuracy': 0.60,  # 60%
            'cpu_usage': 0.80,  # 80%
            'memory_usage': 0.85,  # 85%
            'disk_usage': 0.90,  # 90%
            'response_time': 1000,  # 1 second
            'db_latency': 100,  # 100ms
        }
        
        # Monitoring tasks
        self.monitoring_tasks: List[asyncio.Task] = []
    
    def _setup_prometheus_metrics(self):
        """Set up Prometheus metrics for monitoring."""
        # Trading metrics
        self.trades_total = Counter('trading_trades_total', 'Total number of trades', ['status'])
        self.pnl_gauge = Gauge('trading_pnl_total', 'Total P&L')
        self.positions_gauge = Gauge('trading_positions_current', 'Current number of positions')
        
        # Performance metrics
        self.response_time_histogram = Histogram('api_response_time_seconds', 'API response time')
        self.error_rate_gauge = Gauge('api_error_rate', 'API error rate')
        
        # System metrics
        self.cpu_usage_gauge = Gauge('system_cpu_usage_percent', 'CPU usage percentage')
        self.memory_usage_gauge = Gauge('system_memory_usage_percent', 'Memory usage percentage')
        self.disk_usage_gauge = Gauge('system_disk_usage_percent', 'Disk usage percentage')
        
        # Database metrics
        self.db_connections_gauge = Gauge('database_connections_active', 'Active database connections')
        self.db_query_time_histogram = Histogram('database_query_time_seconds', 'Database query time')
        
        # ML metrics
        self.model_accuracy_gauge = Gauge('ml_model_accuracy', 'Model accuracy')
        self.prediction_latency_histogram = Histogram('ml_prediction_latency_seconds', 'ML prediction latency')
    
    async def start_monitoring(self):
        """Start comprehensive production monitoring."""
        if self.is_monitoring:
            logger.warning("Monitoring already started")
            return
        
        self.is_monitoring = True
        logger.info("Starting production monitoring...")
        
        # Start Prometheus metrics server
        start_http_server(self.config.monitoring.metrics_port)
        
        # Start monitoring tasks
        self.monitoring_tasks = [
            asyncio.create_task(self._monitor_trading_metrics()),
            asyncio.create_task(self._monitor_system_metrics()),
            asyncio.create_task(self._monitor_performance_metrics()),
            asyncio.create_task(self._monitor_database_metrics()),
            asyncio.create_task(self._monitor_ml_metrics()),
            asyncio.create_task(self._monitor_alerts()),
            asyncio.create_task(self._collect_metrics_history()),
        ]
        
        logger.info("Production monitoring started successfully")
    
    async def stop_monitoring(self):
        """Stop production monitoring."""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        logger.info("Stopping production monitoring...")
        
        # Cancel monitoring tasks
        for task in self.monitoring_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
        
        self.monitoring_tasks.clear()
        logger.info("Production monitoring stopped")
    
    async def _monitor_trading_metrics(self):
        """Monitor trading-specific metrics."""
        while self.is_monitoring:
            try:
                # This would integrate with actual trading components
                # For now, simulate trading metrics
                
                # Update trading metrics
                self.metrics.total_trades += 1
                self.metrics.successful_trades += 1
                self.metrics.total_pnl += 10.0  # Simulate profit
                self.metrics.daily_pnl += 10.0
                self.metrics.current_positions = 5
                
                # Update Prometheus metrics
                self.trades_total.labels(status='success').inc()
                self.pnl_gauge.set(self.metrics.total_pnl)
                self.positions_gauge.set(self.metrics.current_positions)
                
                # Check for trading alerts
                await self._check_trading_alerts()
                
                await asyncio.sleep(10)  # Update every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring trading metrics: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_system_metrics(self):
        """Monitor system resource metrics."""
        while self.is_monitoring:
            try:
                # Get system metrics
                self.metrics.cpu_usage = psutil.cpu_percent(interval=1)
                self.metrics.memory_usage = psutil.virtual_memory().percent
                self.metrics.disk_usage = psutil.disk_usage('/').percent
                
                # Network I/O
                net_io = psutil.net_io_counters()
                self.metrics.network_io = {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv
                }
                
                # Update Prometheus metrics
                self.cpu_usage_gauge.set(self.metrics.cpu_usage)
                self.memory_usage_gauge.set(self.metrics.memory_usage)
                self.disk_usage_gauge.set(self.metrics.disk_usage)
                
                # Check for system alerts
                await self._check_system_alerts()
                
                await asyncio.sleep(15)  # Update every 15 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring system metrics: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_performance_metrics(self):
        """Monitor API and application performance metrics."""
        while self.is_monitoring:
            try:
                # Simulate performance metrics
                self.metrics.avg_response_time = 50.0  # 50ms
                self.metrics.max_response_time = 200.0  # 200ms
                self.metrics.api_requests_per_second = 25.0
                self.metrics.error_rate = 0.02  # 2%
                
                # Update Prometheus metrics
                self.error_rate_gauge.set(self.metrics.error_rate)
                
                # Check for performance alerts
                await self._check_performance_alerts()
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring performance metrics: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_database_metrics(self):
        """Monitor database performance metrics."""
        while self.is_monitoring:
            try:
                # Simulate database metrics
                self.metrics.db_connections = 15
                self.metrics.db_query_time = 25.0  # 25ms
                self.metrics.cache_hit_rate = 95.0  # 95%
                
                # Update Prometheus metrics
                self.db_connections_gauge.set(self.metrics.db_connections)
                
                # Check for database alerts
                await self._check_database_alerts()
                
                await asyncio.sleep(60)  # Update every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring database metrics: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_ml_metrics(self):
        """Monitor ML model performance metrics."""
        while self.is_monitoring:
            try:
                # Simulate ML metrics
                self.metrics.model_accuracy = 0.75  # 75%
                self.metrics.prediction_latency = 30.0  # 30ms
                self.metrics.model_drift_score = 0.1  # Low drift
                
                # Update Prometheus metrics
                self.model_accuracy_gauge.set(self.metrics.model_accuracy)
                
                # Check for ML alerts
                await self._check_ml_alerts()
                
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring ML metrics: {e}")
                await asyncio.sleep(300)
    
    async def _monitor_alerts(self):
        """Monitor and process alerts."""
        while self.is_monitoring:
            try:
                # Process unacknowledged alerts
                unacknowledged_alerts = [a for a in self.alerts if not a.acknowledged]
                
                for alert in unacknowledged_alerts:
                    await self._process_alert(alert)
                
                # Clean up old resolved alerts
                cutoff_time = datetime.utcnow() - timedelta(hours=24)
                self.alerts = [a for a in self.alerts if not a.resolved or a.timestamp > cutoff_time]
                
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring alerts: {e}")
                await asyncio.sleep(60)
    
    async def _collect_metrics_history(self):
        """Collect metrics history for analysis."""
        while self.is_monitoring:
            try:
                # Store current metrics snapshot
                metrics_snapshot = MonitoringMetrics(
                    total_trades=self.metrics.total_trades,
                    successful_trades=self.metrics.successful_trades,
                    failed_trades=self.metrics.failed_trades,
                    total_pnl=self.metrics.total_pnl,
                    daily_pnl=self.metrics.daily_pnl,
                    current_positions=self.metrics.current_positions,
                    avg_response_time=self.metrics.avg_response_time,
                    max_response_time=self.metrics.max_response_time,
                    api_requests_per_second=self.metrics.api_requests_per_second,
                    error_rate=self.metrics.error_rate,
                    cpu_usage=self.metrics.cpu_usage,
                    memory_usage=self.metrics.memory_usage,
                    disk_usage=self.metrics.disk_usage,
                    network_io=self.metrics.network_io.copy(),
                    db_connections=self.metrics.db_connections,
                    db_query_time=self.metrics.db_query_time,
                    cache_hit_rate=self.metrics.cache_hit_rate,
                    model_accuracy=self.metrics.model_accuracy,
                    prediction_latency=self.metrics.prediction_latency,
                    model_drift_score=self.metrics.model_drift_score,
                    timestamp=datetime.utcnow()
                )
                
                self.metric_history.append(metrics_snapshot)
                
                # Keep only last 24 hours of history
                cutoff_time = datetime.utcnow() - timedelta(hours=24)
                self.metric_history = [m for m in self.metric_history if m.timestamp > cutoff_time]
                
                await asyncio.sleep(60)  # Collect every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error collecting metrics history: {e}")
                await asyncio.sleep(60)

    async def _check_trading_alerts(self):
        """Check for trading-related alerts."""
        # Daily loss alert
        if abs(self.metrics.daily_pnl) > self.alert_thresholds['daily_loss'] * 100000:  # Assuming $100k portfolio
            await self._create_alert(
                severity=AlertSeverity.CRITICAL,
                title="Daily Loss Threshold Exceeded",
                message=f"Daily P&L: ${self.metrics.daily_pnl:.2f}",
                component="trading",
                metric_name="daily_pnl",
                current_value=abs(self.metrics.daily_pnl),
                threshold_value=self.alert_thresholds['daily_loss'] * 100000
            )

        # Position size alert
        if self.metrics.current_positions > 20:  # Max positions
            await self._create_alert(
                severity=AlertSeverity.WARNING,
                title="High Number of Positions",
                message=f"Current positions: {self.metrics.current_positions}",
                component="trading",
                metric_name="current_positions",
                current_value=self.metrics.current_positions,
                threshold_value=20
            )

    async def _check_system_alerts(self):
        """Check for system resource alerts."""
        # CPU usage alert
        if self.metrics.cpu_usage > self.alert_thresholds['cpu_usage'] * 100:
            await self._create_alert(
                severity=AlertSeverity.WARNING,
                title="High CPU Usage",
                message=f"CPU usage: {self.metrics.cpu_usage:.1f}%",
                component="system",
                metric_name="cpu_usage",
                current_value=self.metrics.cpu_usage,
                threshold_value=self.alert_thresholds['cpu_usage'] * 100
            )

        # Memory usage alert
        if self.metrics.memory_usage > self.alert_thresholds['memory_usage'] * 100:
            await self._create_alert(
                severity=AlertSeverity.CRITICAL,
                title="High Memory Usage",
                message=f"Memory usage: {self.metrics.memory_usage:.1f}%",
                component="system",
                metric_name="memory_usage",
                current_value=self.metrics.memory_usage,
                threshold_value=self.alert_thresholds['memory_usage'] * 100
            )

        # Disk usage alert
        if self.metrics.disk_usage > self.alert_thresholds['disk_usage'] * 100:
            await self._create_alert(
                severity=AlertSeverity.CRITICAL,
                title="Low Disk Space",
                message=f"Disk usage: {self.metrics.disk_usage:.1f}%",
                component="system",
                metric_name="disk_usage",
                current_value=self.metrics.disk_usage,
                threshold_value=self.alert_thresholds['disk_usage'] * 100
            )

    async def _check_performance_alerts(self):
        """Check for performance-related alerts."""
        # API error rate alert
        if self.metrics.error_rate > self.alert_thresholds['api_error_rate']:
            await self._create_alert(
                severity=AlertSeverity.WARNING,
                title="High API Error Rate",
                message=f"Error rate: {self.metrics.error_rate:.1%}",
                component="api",
                metric_name="error_rate",
                current_value=self.metrics.error_rate,
                threshold_value=self.alert_thresholds['api_error_rate']
            )

        # Response time alert
        if self.metrics.avg_response_time > self.alert_thresholds['response_time']:
            await self._create_alert(
                severity=AlertSeverity.WARNING,
                title="Slow API Response Time",
                message=f"Average response time: {self.metrics.avg_response_time:.1f}ms",
                component="api",
                metric_name="avg_response_time",
                current_value=self.metrics.avg_response_time,
                threshold_value=self.alert_thresholds['response_time']
            )

    async def _check_database_alerts(self):
        """Check for database-related alerts."""
        # Database latency alert
        if self.metrics.db_query_time > self.alert_thresholds['db_latency']:
            await self._create_alert(
                severity=AlertSeverity.WARNING,
                title="High Database Latency",
                message=f"Query time: {self.metrics.db_query_time:.1f}ms",
                component="database",
                metric_name="db_query_time",
                current_value=self.metrics.db_query_time,
                threshold_value=self.alert_thresholds['db_latency']
            )

        # Cache hit rate alert
        if self.metrics.cache_hit_rate < 90.0:
            await self._create_alert(
                severity=AlertSeverity.INFO,
                title="Low Cache Hit Rate",
                message=f"Cache hit rate: {self.metrics.cache_hit_rate:.1f}%",
                component="cache",
                metric_name="cache_hit_rate",
                current_value=self.metrics.cache_hit_rate,
                threshold_value=90.0
            )

    async def _check_ml_alerts(self):
        """Check for ML model alerts."""
        # Model accuracy alert
        if self.metrics.model_accuracy < self.alert_thresholds['model_accuracy']:
            await self._create_alert(
                severity=AlertSeverity.CRITICAL,
                title="Low Model Accuracy",
                message=f"Model accuracy: {self.metrics.model_accuracy:.1%}",
                component="ml",
                metric_name="model_accuracy",
                current_value=self.metrics.model_accuracy,
                threshold_value=self.alert_thresholds['model_accuracy']
            )

        # Model drift alert
        if self.metrics.model_drift_score > 0.3:
            await self._create_alert(
                severity=AlertSeverity.WARNING,
                title="Model Drift Detected",
                message=f"Drift score: {self.metrics.model_drift_score:.2f}",
                component="ml",
                metric_name="model_drift_score",
                current_value=self.metrics.model_drift_score,
                threshold_value=0.3
            )

    async def _create_alert(self, severity: AlertSeverity, title: str, message: str,
                          component: str, metric_name: str, current_value: float,
                          threshold_value: float):
        """Create a new alert."""
        alert_id = f"{component}_{metric_name}_{int(time.time())}"

        # Check if similar alert already exists
        existing_alert = next(
            (a for a in self.alerts
             if a.component == component and a.metric_name == metric_name and not a.resolved),
            None
        )

        if existing_alert:
            # Update existing alert
            existing_alert.current_value = current_value
            existing_alert.timestamp = datetime.utcnow()
            return

        # Create new alert
        alert = Alert(
            id=alert_id,
            severity=severity,
            title=title,
            message=message,
            component=component,
            metric_name=metric_name,
            current_value=current_value,
            threshold_value=threshold_value
        )

        self.alerts.append(alert)
        logger.warning(f"Alert created: {title} - {message}")

    async def _process_alert(self, alert: Alert):
        """Process an alert (send notifications, etc.)."""
        try:
            # Log the alert
            logger.warning(f"Processing alert: {alert.title} - {alert.message}")

            # Here you would integrate with notification systems:
            # - Send email notifications
            # - Send Slack/Teams messages
            # - Send Telegram notifications
            # - Update monitoring dashboards
            # - Trigger automated responses

            # For now, just mark as acknowledged
            alert.acknowledged = True

        except Exception as e:
            logger.error(f"Failed to process alert {alert.id}: {e}")

    def get_current_metrics(self) -> MonitoringMetrics:
        """Get current monitoring metrics."""
        return self.metrics

    def get_active_alerts(self) -> List[Alert]:
        """Get all active (unresolved) alerts."""
        return [a for a in self.alerts if not a.resolved]

    def get_metrics_history(self, hours: int = 24) -> List[MonitoringMetrics]:
        """Get metrics history for specified hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return [m for m in self.metric_history if m.timestamp > cutoff_time]

    def acknowledge_alert(self, alert_id: str):
        """Acknowledge an alert."""
        alert = next((a for a in self.alerts if a.id == alert_id), None)
        if alert:
            alert.acknowledged = True
            logger.info(f"Alert acknowledged: {alert_id}")

    def resolve_alert(self, alert_id: str):
        """Resolve an alert."""
        alert = next((a for a in self.alerts if a.id == alert_id), None)
        if alert:
            alert.resolved = True
            logger.info(f"Alert resolved: {alert_id}")

    async def generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive system health report."""
        current_time = datetime.utcnow()

        # Calculate uptime (would be tracked from system start)
        uptime_hours = 24  # Placeholder

        # Get recent metrics
        recent_metrics = self.get_metrics_history(1)  # Last hour

        # Calculate averages
        if recent_metrics:
            avg_cpu = sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
            avg_response_time = sum(m.avg_response_time for m in recent_metrics) / len(recent_metrics)
        else:
            avg_cpu = self.metrics.cpu_usage
            avg_memory = self.metrics.memory_usage
            avg_response_time = self.metrics.avg_response_time

        # Count alerts by severity
        active_alerts = self.get_active_alerts()
        alert_counts = {
            'critical': len([a for a in active_alerts if a.severity == AlertSeverity.CRITICAL]),
            'warning': len([a for a in active_alerts if a.severity == AlertSeverity.WARNING]),
            'info': len([a for a in active_alerts if a.severity == AlertSeverity.INFO])
        }

        # Determine overall health status
        if alert_counts['critical'] > 0:
            health_status = "critical"
        elif alert_counts['warning'] > 3:
            health_status = "degraded"
        elif avg_cpu > 80 or avg_memory > 85:
            health_status = "warning"
        else:
            health_status = "healthy"

        return {
            'timestamp': current_time.isoformat(),
            'uptime_hours': uptime_hours,
            'health_status': health_status,
            'current_metrics': {
                'trading': {
                    'total_trades': self.metrics.total_trades,
                    'daily_pnl': self.metrics.daily_pnl,
                    'current_positions': self.metrics.current_positions
                },
                'system': {
                    'cpu_usage': self.metrics.cpu_usage,
                    'memory_usage': self.metrics.memory_usage,
                    'disk_usage': self.metrics.disk_usage
                },
                'performance': {
                    'avg_response_time': self.metrics.avg_response_time,
                    'error_rate': self.metrics.error_rate,
                    'api_requests_per_second': self.metrics.api_requests_per_second
                },
                'ml': {
                    'model_accuracy': self.metrics.model_accuracy,
                    'prediction_latency': self.metrics.prediction_latency
                }
            },
            'averages_last_hour': {
                'cpu_usage': avg_cpu,
                'memory_usage': avg_memory,
                'response_time': avg_response_time
            },
            'alerts': {
                'total_active': len(active_alerts),
                'by_severity': alert_counts,
                'recent_alerts': [
                    {
                        'id': a.id,
                        'severity': a.severity.value,
                        'title': a.title,
                        'component': a.component,
                        'timestamp': a.timestamp.isoformat()
                    }
                    for a in sorted(active_alerts, key=lambda x: x.timestamp, reverse=True)[:5]
                ]
            }
        }
