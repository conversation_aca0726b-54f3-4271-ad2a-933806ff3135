version: '3.8'

services:
  # Trading Bot Application - Primary Instance
  trading-bot-primary:
    build:
      context: ../../..
      dockerfile: src/trading_bot/deployment/docker/Dockerfile
      args:
        BUILD_DATE: ${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        VERSION: ${VERSION:-latest}
        VCS_REF: ${VCS_REF:-$(git rev-parse --short HEAD)}
    container_name: trading-bot-primary
    hostname: trading-bot-primary
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - INSTANCE_ID=primary
      - POSTGRES_HOST=postgres-primary
      - REDIS_HOST=redis-primary
      - MONGO_HOST=mongo-primary
      - PROMETHEUS_HOST=prometheus
      - GRAFANA_HOST=grafana
    env_file:
      - .env.production
    volumes:
      - trading_data:/app/data
      - trading_logs:/app/logs
      - trading_models:/app/models
      - trading_config:/app/config
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis-primary:
        condition: service_healthy
      mongo-primary:
        condition: service_healthy
    networks:
      - trading-network
    ports:
      - "8000:8000"
      - "9090:9090"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Trading Bot Application - Standby Instance
  trading-bot-standby:
    build:
      context: ../../..
      dockerfile: src/trading_bot/deployment/docker/Dockerfile
    container_name: trading-bot-standby
    hostname: trading-bot-standby
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - INSTANCE_ID=standby
      - STANDBY_MODE=true
      - POSTGRES_HOST=postgres-primary
      - REDIS_HOST=redis-primary
      - MONGO_HOST=mongo-primary
    env_file:
      - .env.production
    volumes:
      - trading_data:/app/data:ro
      - trading_logs_standby:/app/logs
      - trading_models:/app/models:ro
      - trading_config:/app/config:ro
    depends_on:
      - trading-bot-primary
      - postgres-primary
      - redis-primary
    networks:
      - trading-network
    ports:
      - "8001:8000"
      - "9091:9090"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # PostgreSQL Primary Database
  postgres-primary:
    image: postgres:15-alpine
    container_name: postgres-primary
    hostname: postgres-primary
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-trading_bot}
      POSTGRES_USER: ${POSTGRES_USER:-trading_bot}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_postgres.sql:/docker-entrypoint-initdb.d/01_init.sql
      - ./scripts/postgres_config.conf:/etc/postgresql/postgresql.conf
    networks:
      - trading-network
    ports:
      - "5432:5432"
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c log_statement=all
      -c log_min_duration_statement=1000
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-trading_bot} -d ${POSTGRES_DB:-trading_bot}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Primary Cache
  redis-primary:
    image: redis:7-alpine
    container_name: redis-primary
    hostname: redis-primary
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - trading-network
    ports:
      - "6379:6379"
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # MongoDB Primary
  mongo-primary:
    image: mongo:6
    container_name: mongo-primary
    hostname: mongo-primary
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USER:-trading_bot}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DB:-trading_bot}
    volumes:
      - mongo_data:/data/db
      - ./scripts/init_mongo.js:/docker-entrypoint-initdb.d/init.js
    networks:
      - trading-network
    ports:
      - "27017:27017"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    hostname: prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - prometheus_data:/prometheus
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/alert_rules.yml:/etc/prometheus/alert_rules.yml
    networks:
      - trading-network
    ports:
      - "9090:9090"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Grafana Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    hostname: grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-piechart-panel,grafana-worldmap-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - trading-network
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: nginx-lb
    hostname: nginx-lb
    restart: unless-stopped
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
    networks:
      - trading-network
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - trading-bot-primary
      - trading-bot-standby
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

volumes:
  trading_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/trading-bot/data
  trading_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/trading-bot/logs
  trading_logs_standby:
    driver: local
  trading_models:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/trading-bot/models
  trading_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/trading-bot/config
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mongo_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  trading-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
