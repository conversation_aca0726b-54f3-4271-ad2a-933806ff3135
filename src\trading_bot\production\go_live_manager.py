"""
Go-live manager for AI Trading Bot production deployment.

This module manages the phased rollout process from paper trading
to full live trading operations with comprehensive safety checks.
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
import yaml

from ..core.config import Config
from ..production.deployment.preflight_checks import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ..production.testing.paper_trading import PaperTradingValidator
from ..production.monitoring.production_monitor import ProductionMonitor
from ..production.deployment.rollback_manager import RollbackManager
from ..utils.logger import get_logger

logger = get_logger(__name__)


class GoLivePhase(Enum):
    """Go-live phases."""
    PREPARATION = "preparation"
    PHASE_1_LIMITED = "phase_1_limited"
    PHASE_2_EXPANDED = "phase_2_expanded"
    PHASE_3_FULL = "phase_3_full"
    COMPLETED = "completed"
    ROLLED_BACK = "rolled_back"


class PhaseStatus(Enum):
    """Phase execution status."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


@dataclass
class PhaseConfiguration:
    """Configuration for a go-live phase."""
    phase: GoLivePhase
    duration_days: int
    capital_percentage: float
    max_positions: int
    max_trade_size: float
    max_daily_trades: int
    manual_approval_threshold: float
    risk_multiplier: float = 1.0
    monitoring_frequency: int = 300  # seconds
    success_criteria: Dict[str, float] = field(default_factory=dict)


@dataclass
class PhaseResult:
    """Result of a go-live phase execution."""
    phase: GoLivePhase
    status: PhaseStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration: float = 0.0
    trades_executed: int = 0
    total_pnl: float = 0.0
    win_rate: float = 0.0
    max_drawdown: float = 0.0
    risk_violations: int = 0
    success_criteria_met: bool = False
    issues: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)


@dataclass
class GoLiveReport:
    """Comprehensive go-live execution report."""
    started_at: datetime
    current_phase: GoLivePhase
    overall_status: PhaseStatus
    phase_results: List[PhaseResult] = field(default_factory=list)
    total_duration: float = 0.0
    cumulative_pnl: float = 0.0
    overall_success_rate: float = 0.0
    ready_for_next_phase: bool = False
    ready_for_full_production: bool = False


class GoLiveManager:
    """
    Comprehensive go-live manager for phased production rollout.
    
    Manages the complete go-live process:
    - Phase 1: Limited Trading (10% capital, 5 positions, manual approval >$1k)
    - Phase 2: Expanded Trading (25% capital, 10 positions, manual approval >$5k)
    - Phase 3: Full Operations (100% capital, 20 positions, manual approval >$50k)
    
    Each phase includes:
    - Automated safety checks
    - Performance monitoring
    - Risk validation
    - Success criteria evaluation
    - Automatic progression or rollback
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.current_phase = GoLivePhase.PREPARATION
        self.phase_results: List[PhaseResult] = []
        self.is_running = False
        
        # Initialize components
        self.preflight_checker = PreflightChecker(config)
        self.paper_trading_validator = PaperTradingValidator(config)
        self.production_monitor = ProductionMonitor(config)
        self.rollback_manager = RollbackManager(config)
        
        # Load phase configurations
        self.phase_configs = self._load_phase_configurations()
        
        # Go-live criteria
        self.launch_criteria = {
            'paper_trading_days': 30,
            'min_win_rate': 0.55,
            'min_sharpe_ratio': 1.0,
            'max_drawdown': 0.15,
            'min_success_rate': 0.95,
            'max_critical_failures': 0
        }
    
    def _load_phase_configurations(self) -> Dict[GoLivePhase, PhaseConfiguration]:
        """Load phase configurations from config."""
        return {
            GoLivePhase.PHASE_1_LIMITED: PhaseConfiguration(
                phase=GoLivePhase.PHASE_1_LIMITED,
                duration_days=14,
                capital_percentage=0.10,
                max_positions=5,
                max_trade_size=1000.0,
                max_daily_trades=20,
                manual_approval_threshold=1000.0,
                risk_multiplier=0.5,
                success_criteria={
                    'min_win_rate': 0.50,
                    'max_drawdown': 0.05,
                    'max_daily_loss': 0.01,
                    'min_trades': 50
                }
            ),
            GoLivePhase.PHASE_2_EXPANDED: PhaseConfiguration(
                phase=GoLivePhase.PHASE_2_EXPANDED,
                duration_days=14,
                capital_percentage=0.25,
                max_positions=10,
                max_trade_size=5000.0,
                max_daily_trades=50,
                manual_approval_threshold=5000.0,
                risk_multiplier=0.75,
                success_criteria={
                    'min_win_rate': 0.52,
                    'max_drawdown': 0.08,
                    'max_daily_loss': 0.015,
                    'min_trades': 100
                }
            ),
            GoLivePhase.PHASE_3_FULL: PhaseConfiguration(
                phase=GoLivePhase.PHASE_3_FULL,
                duration_days=30,
                capital_percentage=1.0,
                max_positions=20,
                max_trade_size=50000.0,
                max_daily_trades=100,
                manual_approval_threshold=50000.0,
                risk_multiplier=1.0,
                success_criteria={
                    'min_win_rate': 0.55,
                    'max_drawdown': 0.10,
                    'max_daily_loss': 0.02,
                    'min_trades': 200
                }
            )
        }
    
    async def initialize(self):
        """Initialize go-live manager and all components."""
        try:
            await self.preflight_checker.initialize()
            await self.production_monitor.start_monitoring()
            await self.rollback_manager.initialize()
            
            logger.info("Go-live manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize go-live manager: {e}")
            raise
    
    async def cleanup(self):
        """Clean up go-live manager and components."""
        try:
            await self.production_monitor.stop_monitoring()
            await self.rollback_manager.cleanup()
            
            logger.info("Go-live manager cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup go-live manager: {e}")
    
    async def validate_launch_readiness(self) -> Dict[str, Any]:
        """Validate system readiness for go-live."""
        logger.info("Validating launch readiness...")
        
        validation_report = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_ready": False,
            "validations": {},
            "issues": [],
            "recommendations": []
        }
        
        # Run preflight checks
        try:
            preflight_report = await self.preflight_checker.run_all_checks()
            validation_report["validations"]["preflight"] = {
                "status": "passed" if preflight_report.is_ready_for_deployment else "failed",
                "success_rate": preflight_report.success_rate,
                "critical_failures": preflight_report.critical_failures
            }
            
            if not preflight_report.is_ready_for_deployment:
                validation_report["issues"].append("Preflight checks failed")
                
        except Exception as e:
            validation_report["validations"]["preflight"] = {"status": "error", "error": str(e)}
            validation_report["issues"].append(f"Preflight check error: {e}")
        
        # Validate paper trading results
        try:
            paper_trading_report = await self.paper_trading_validator.run_validation(
                self.launch_criteria['paper_trading_days']
            )
            
            validation_report["validations"]["paper_trading"] = {
                "status": "passed" if paper_trading_report.is_ready_for_live_trading else "failed",
                "win_rate": paper_trading_report.trading_metrics.win_rate,
                "sharpe_ratio": paper_trading_report.trading_metrics.sharpe_ratio,
                "max_drawdown": paper_trading_report.trading_metrics.max_drawdown
            }
            
            if not paper_trading_report.is_ready_for_live_trading:
                validation_report["issues"].extend(paper_trading_report.issues)
                
        except Exception as e:
            validation_report["validations"]["paper_trading"] = {"status": "error", "error": str(e)}
            validation_report["issues"].append(f"Paper trading validation error: {e}")
        
        # Check launch criteria
        criteria_met = 0
        total_criteria = len(self.launch_criteria)
        
        for criterion, threshold in self.launch_criteria.items():
            if criterion == 'paper_trading_days':
                # Already validated above
                criteria_met += 1
            elif criterion == 'min_win_rate':
                if validation_report["validations"].get("paper_trading", {}).get("win_rate", 0) >= threshold:
                    criteria_met += 1
                else:
                    validation_report["issues"].append(f"Win rate below threshold: {threshold}")
            elif criterion == 'min_sharpe_ratio':
                if validation_report["validations"].get("paper_trading", {}).get("sharpe_ratio", 0) >= threshold:
                    criteria_met += 1
                else:
                    validation_report["issues"].append(f"Sharpe ratio below threshold: {threshold}")
            elif criterion == 'max_drawdown':
                if validation_report["validations"].get("paper_trading", {}).get("max_drawdown", 1) <= threshold:
                    criteria_met += 1
                else:
                    validation_report["issues"].append(f"Drawdown above threshold: {threshold}")
            elif criterion == 'max_critical_failures':
                if validation_report["validations"].get("preflight", {}).get("critical_failures", 1) <= threshold:
                    criteria_met += 1
                else:
                    validation_report["issues"].append(f"Critical failures above threshold: {threshold}")
        
        validation_report["criteria_met"] = criteria_met
        validation_report["criteria_total"] = total_criteria
        validation_report["overall_ready"] = criteria_met == total_criteria and len(validation_report["issues"]) == 0
        
        # Generate recommendations
        if not validation_report["overall_ready"]:
            validation_report["recommendations"].extend([
                "Address all identified issues before proceeding",
                "Consider extending paper trading period if performance is marginal",
                "Review and optimize strategies based on validation results"
            ])
        else:
            validation_report["recommendations"].extend([
                "System is ready for Phase 1 go-live",
                "Ensure monitoring systems are active",
                "Prepare rollback procedures"
            ])
        
        logger.info(f"Launch readiness validation completed: {'READY' if validation_report['overall_ready'] else 'NOT READY'}")
        return validation_report
    
    async def start_go_live_process(self) -> GoLiveReport:
        """Start the complete go-live process."""
        if self.is_running:
            raise RuntimeError("Go-live process already running")
        
        logger.info("Starting go-live process...")
        
        # Validate launch readiness
        readiness_report = await self.validate_launch_readiness()
        if not readiness_report["overall_ready"]:
            raise RuntimeError(f"System not ready for go-live: {readiness_report['issues']}")
        
        self.is_running = True
        start_time = datetime.utcnow()
        
        try:
            # Create initial snapshot
            await self.rollback_manager.create_snapshot("Pre go-live snapshot")
            
            # Execute phases sequentially
            phases = [
                GoLivePhase.PHASE_1_LIMITED,
                GoLivePhase.PHASE_2_EXPANDED,
                GoLivePhase.PHASE_3_FULL
            ]
            
            for phase in phases:
                logger.info(f"Starting {phase.value}...")
                self.current_phase = phase
                
                phase_result = await self._execute_phase(phase)
                self.phase_results.append(phase_result)
                
                if phase_result.status == PhaseStatus.FAILED:
                    logger.error(f"Phase {phase.value} failed - stopping go-live process")
                    break
                
                if not phase_result.success_criteria_met:
                    logger.warning(f"Phase {phase.value} success criteria not met - manual review required")
                    # In production, this would pause for manual approval
                    break
                
                logger.info(f"Phase {phase.value} completed successfully")
            
            # Generate final report
            report = self._generate_go_live_report(start_time)
            
            if report.overall_status == PhaseStatus.COMPLETED:
                self.current_phase = GoLivePhase.COMPLETED
                logger.info("Go-live process completed successfully!")
            else:
                logger.warning("Go-live process completed with issues")
            
            return report
            
        except Exception as e:
            logger.error(f"Go-live process failed: {e}")
            # Execute emergency rollback
            await self.rollback_manager.execute_emergency_rollback(
                trigger="system_failure"
            )
            raise
        
        finally:
            self.is_running = False
    
    async def _execute_phase(self, phase: GoLivePhase) -> PhaseResult:
        """Execute a single go-live phase."""
        config = self.phase_configs[phase]
        start_time = datetime.utcnow()
        
        result = PhaseResult(
            phase=phase,
            status=PhaseStatus.IN_PROGRESS,
            started_at=start_time
        )
        
        try:
            # Apply phase configuration
            await self._apply_phase_configuration(config)
            
            # Create phase snapshot
            await self.rollback_manager.create_snapshot(f"Phase {phase.value} start")
            
            # Monitor phase execution
            end_time = start_time + timedelta(days=config.duration_days)
            
            # Simulate phase execution (in production, this would run for the full duration)
            await self._simulate_phase_execution(config, result)
            
            # Evaluate phase success
            result.success_criteria_met = self._evaluate_phase_success(config, result)
            result.status = PhaseStatus.COMPLETED if result.success_criteria_met else PhaseStatus.FAILED
            result.completed_at = datetime.utcnow()
            result.duration = (result.completed_at - start_time).total_seconds()
            
            return result
            
        except Exception as e:
            result.status = PhaseStatus.FAILED
            result.completed_at = datetime.utcnow()
            result.duration = (result.completed_at - start_time).total_seconds()
            result.issues.append(str(e))
            
            logger.error(f"Phase {phase.value} execution failed: {e}")
            return result

    async def _apply_phase_configuration(self, config: PhaseConfiguration):
        """Apply phase-specific configuration."""
        logger.info(f"Applying configuration for {config.phase.value}")

        # Update trading configuration
        trading_config = {
            'capital_percentage': config.capital_percentage,
            'max_positions': config.max_positions,
            'max_trade_size': config.max_trade_size,
            'max_daily_trades': config.max_daily_trades,
            'manual_approval_threshold': config.manual_approval_threshold,
            'risk_multiplier': config.risk_multiplier
        }

        # In production, this would update the actual trading system configuration
        # For now, log the configuration
        logger.info(f"Phase configuration applied: {trading_config}")

    async def _simulate_phase_execution(self, config: PhaseConfiguration, result: PhaseResult):
        """Simulate phase execution (for demonstration)."""
        # In production, this would monitor actual trading for the phase duration
        # For now, simulate some trading metrics

        import random

        # Simulate trading activity
        result.trades_executed = random.randint(50, 200)
        result.total_pnl = random.uniform(-1000, 5000)
        result.win_rate = random.uniform(0.45, 0.65)
        result.max_drawdown = random.uniform(0.02, 0.12)
        result.risk_violations = random.randint(0, 3)

        # Add some realistic issues/recommendations
        if result.win_rate < config.success_criteria.get('min_win_rate', 0.5):
            result.issues.append(f"Win rate {result.win_rate:.1%} below target")
            result.recommendations.append("Review and optimize trading strategies")

        if result.max_drawdown > config.success_criteria.get('max_drawdown', 0.1):
            result.issues.append(f"Drawdown {result.max_drawdown:.1%} above target")
            result.recommendations.append("Implement tighter risk controls")

        if result.risk_violations > 0:
            result.issues.append(f"{result.risk_violations} risk violations detected")
            result.recommendations.append("Review risk management parameters")

        # Simulate monitoring delay
        await asyncio.sleep(2)

    def _evaluate_phase_success(self, config: PhaseConfiguration, result: PhaseResult) -> bool:
        """Evaluate if phase met success criteria."""
        criteria_met = 0
        total_criteria = len(config.success_criteria)

        for criterion, threshold in config.success_criteria.items():
            if criterion == 'min_win_rate' and result.win_rate >= threshold:
                criteria_met += 1
            elif criterion == 'max_drawdown' and result.max_drawdown <= threshold:
                criteria_met += 1
            elif criterion == 'max_daily_loss':
                # Would check actual daily loss metrics
                criteria_met += 1  # Assume met for simulation
            elif criterion == 'min_trades' and result.trades_executed >= threshold:
                criteria_met += 1

        success_rate = criteria_met / total_criteria if total_criteria > 0 else 0
        return success_rate >= 0.8  # 80% of criteria must be met

    def _generate_go_live_report(self, start_time: datetime) -> GoLiveReport:
        """Generate comprehensive go-live report."""
        current_time = datetime.utcnow()
        total_duration = (current_time - start_time).total_seconds()

        # Calculate cumulative metrics
        cumulative_pnl = sum(r.total_pnl for r in self.phase_results)
        total_trades = sum(r.trades_executed for r in self.phase_results)

        # Calculate overall success rate
        successful_phases = sum(1 for r in self.phase_results if r.success_criteria_met)
        overall_success_rate = successful_phases / len(self.phase_results) if self.phase_results else 0

        # Determine overall status
        if all(r.status == PhaseStatus.COMPLETED and r.success_criteria_met for r in self.phase_results):
            overall_status = PhaseStatus.COMPLETED
        elif any(r.status == PhaseStatus.FAILED for r in self.phase_results):
            overall_status = PhaseStatus.FAILED
        else:
            overall_status = PhaseStatus.IN_PROGRESS

        # Check readiness for next phase/full production
        ready_for_next_phase = (
            len(self.phase_results) > 0 and
            self.phase_results[-1].success_criteria_met and
            self.current_phase != GoLivePhase.PHASE_3_FULL
        )

        ready_for_full_production = (
            overall_status == PhaseStatus.COMPLETED and
            overall_success_rate >= 0.9 and
            cumulative_pnl > 0
        )

        return GoLiveReport(
            started_at=start_time,
            current_phase=self.current_phase,
            overall_status=overall_status,
            phase_results=self.phase_results.copy(),
            total_duration=total_duration,
            cumulative_pnl=cumulative_pnl,
            overall_success_rate=overall_success_rate,
            ready_for_next_phase=ready_for_next_phase,
            ready_for_full_production=ready_for_full_production
        )

    async def pause_go_live_process(self):
        """Pause the go-live process for manual review."""
        logger.info("Pausing go-live process for manual review...")

        # Update current phase status
        if self.phase_results:
            self.phase_results[-1].status = PhaseStatus.PAUSED

        # Create snapshot at pause point
        await self.rollback_manager.create_snapshot("Go-live process paused")

        self.is_running = False
        logger.info("Go-live process paused")

    async def resume_go_live_process(self):
        """Resume paused go-live process."""
        logger.info("Resuming go-live process...")

        # Update current phase status
        if self.phase_results:
            self.phase_results[-1].status = PhaseStatus.IN_PROGRESS

        self.is_running = True
        logger.info("Go-live process resumed")

    async def abort_go_live_process(self):
        """Abort go-live process and rollback."""
        logger.warning("Aborting go-live process...")

        # Execute emergency rollback
        await self.rollback_manager.execute_emergency_rollback(
            trigger="manual_abort"
        )

        # Update status
        if self.phase_results:
            self.phase_results[-1].status = PhaseStatus.FAILED

        self.current_phase = GoLivePhase.ROLLED_BACK
        self.is_running = False

        logger.warning("Go-live process aborted and rolled back")

    def get_current_status(self) -> Dict[str, Any]:
        """Get current go-live process status."""
        return {
            "current_phase": self.current_phase.value,
            "is_running": self.is_running,
            "phases_completed": len([r for r in self.phase_results if r.status == PhaseStatus.COMPLETED]),
            "total_phases": len(self.phase_configs),
            "cumulative_pnl": sum(r.total_pnl for r in self.phase_results),
            "overall_success_rate": len([r for r in self.phase_results if r.success_criteria_met]) / len(self.phase_results) if self.phase_results else 0,
            "last_phase_result": self.phase_results[-1].__dict__ if self.phase_results else None
        }

    async def generate_maintenance_schedule(self) -> Dict[str, Any]:
        """Generate ongoing maintenance schedule for production operations."""
        return {
            "daily_tasks": [
                {
                    "task": "Performance Report Review",
                    "description": "Review daily trading performance and P&L",
                    "frequency": "daily",
                    "time": "09:00 UTC",
                    "owner": "trading_team"
                },
                {
                    "task": "Risk Limit Verification",
                    "description": "Verify all risk limits are properly configured and enforced",
                    "frequency": "daily",
                    "time": "09:30 UTC",
                    "owner": "risk_team"
                },
                {
                    "task": "Model Accuracy Check",
                    "description": "Check ML model prediction accuracy and drift",
                    "frequency": "daily",
                    "time": "10:00 UTC",
                    "owner": "ml_team"
                },
                {
                    "task": "System Health Check",
                    "description": "Review system metrics and alert status",
                    "frequency": "daily",
                    "time": "10:30 UTC",
                    "owner": "ops_team"
                }
            ],
            "weekly_tasks": [
                {
                    "task": "Strategy Performance Review",
                    "description": "Comprehensive review of strategy performance and optimization opportunities",
                    "frequency": "weekly",
                    "day": "Monday",
                    "time": "14:00 UTC",
                    "owner": "strategy_team"
                },
                {
                    "task": "Model Retraining Evaluation",
                    "description": "Evaluate need for model retraining based on performance metrics",
                    "frequency": "weekly",
                    "day": "Wednesday",
                    "time": "15:00 UTC",
                    "owner": "ml_team"
                },
                {
                    "task": "Database Optimization",
                    "description": "Review and optimize database performance",
                    "frequency": "weekly",
                    "day": "Friday",
                    "time": "16:00 UTC",
                    "owner": "ops_team"
                },
                {
                    "task": "Security Updates",
                    "description": "Apply security patches and updates",
                    "frequency": "weekly",
                    "day": "Sunday",
                    "time": "02:00 UTC",
                    "owner": "security_team"
                }
            ],
            "monthly_tasks": [
                {
                    "task": "Full System Audit",
                    "description": "Comprehensive audit of all system components",
                    "frequency": "monthly",
                    "day": "first_monday",
                    "time": "09:00 UTC",
                    "owner": "audit_team"
                },
                {
                    "task": "Disaster Recovery Test",
                    "description": "Test disaster recovery and rollback procedures",
                    "frequency": "monthly",
                    "day": "second_saturday",
                    "time": "20:00 UTC",
                    "owner": "ops_team"
                },
                {
                    "task": "Performance Optimization",
                    "description": "Review and optimize system performance",
                    "frequency": "monthly",
                    "day": "third_friday",
                    "time": "14:00 UTC",
                    "owner": "engineering_team"
                },
                {
                    "task": "Strategy Rebalancing",
                    "description": "Review and rebalance trading strategies",
                    "frequency": "monthly",
                    "day": "last_friday",
                    "time": "15:00 UTC",
                    "owner": "strategy_team"
                }
            ]
        }
