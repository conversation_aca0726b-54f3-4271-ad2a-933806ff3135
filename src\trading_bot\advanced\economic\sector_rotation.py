"""Sector rotation analysis based on economic cycles."""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from ...core.config import settings
from ...core.logger import get_logger
from .macro_analyzer import MacroAnalyzer, EconomicRegime

logger = get_logger(__name__)


class EconomicCyclePhase(Enum):
    """Economic cycle phases for sector rotation."""
    EARLY_CYCLE = "early_cycle"
    MID_CYCLE = "mid_cycle"
    LATE_CYCLE = "late_cycle"
    RECESSION = "recession"


@dataclass
class SectorPerformance:
    """Sector performance data."""
    sector: str
    symbol: str  # ETF symbol representing the sector
    current_return_1m: float
    current_return_3m: float
    current_return_6m: float
    relative_strength: float  # vs market
    momentum_score: float
    cycle_score: float  # how well suited for current cycle
    recommendation: str  # 'overweight', 'neutral', 'underweight'


@dataclass
class RotationSignal:
    """Sector rotation signal."""
    from_sector: str
    to_sector: str
    signal_strength: float  # 0-1
    reasoning: List[str]
    expected_duration: int  # days
    confidence: float


class SectorRotationAnalyzer:
    """Analyze sector rotation opportunities based on economic cycles."""
    
    def __init__(self):
        self.macro_analyzer = MacroAnalyzer()
        self.sector_mappings = self._load_sector_mappings()
        self.cycle_preferences = self._load_cycle_preferences()
        
    async def analyze_sector_rotation(self) -> Dict[str, Any]:
        """
        Analyze current sector rotation opportunities.
        
        Returns:
            Dictionary with sector analysis and rotation signals
        """
        try:
            # Get current economic regime and cycle phase
            regime_analysis = await self.macro_analyzer.analyze_economic_regime()
            cycle_phase = self._determine_cycle_phase(regime_analysis)
            
            # Analyze sector performance
            sector_performance = await self._analyze_sector_performance()
            
            # Generate rotation signals
            rotation_signals = self._generate_rotation_signals(
                cycle_phase, sector_performance
            )
            
            # Get sector recommendations
            recommendations = self._get_sector_recommendations(
                cycle_phase, sector_performance
            )
            
            return {
                'timestamp': datetime.utcnow(),
                'economic_regime': regime_analysis.current_regime.value,
                'cycle_phase': cycle_phase.value,
                'sector_performance': sector_performance,
                'rotation_signals': rotation_signals,
                'recommendations': recommendations,
                'cycle_preferences': self.cycle_preferences.get(cycle_phase, {})
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sector rotation: {e}")
            return self._empty_rotation_analysis()
    
    async def get_top_sectors(
        self,
        cycle_phase: EconomicCyclePhase = None,
        top_n: int = 5
    ) -> List[SectorPerformance]:
        """
        Get top performing sectors for current or specified cycle phase.
        
        Args:
            cycle_phase: Economic cycle phase (current if None)
            top_n: Number of top sectors to return
            
        Returns:
            List of top sector performances
        """
        try:
            if cycle_phase is None:
                regime_analysis = await self.macro_analyzer.analyze_economic_regime()
                cycle_phase = self._determine_cycle_phase(regime_analysis)
            
            # Get sector performance
            sector_performance = await self._analyze_sector_performance()
            
            # Score sectors for the cycle phase
            scored_sectors = []
            for sector_data in sector_performance.values():
                cycle_score = self._calculate_cycle_score(sector_data.sector, cycle_phase)
                sector_data.cycle_score = cycle_score
                scored_sectors.append(sector_data)
            
            # Sort by combined score (performance + cycle fit)
            scored_sectors.sort(
                key=lambda x: (x.cycle_score * 0.6 + x.momentum_score * 0.4),
                reverse=True
            )
            
            return scored_sectors[:top_n]
            
        except Exception as e:
            logger.error(f"Error getting top sectors: {e}")
            return []
    
    async def _analyze_sector_performance(self) -> Dict[str, SectorPerformance]:
        """Analyze performance of all sectors."""
        try:
            sector_performance = {}
            
            for sector, symbol in self.sector_mappings.items():
                try:
                    # Get sector ETF performance
                    performance_data = await self._get_sector_etf_performance(symbol)
                    
                    if performance_data:
                        sector_performance[sector] = SectorPerformance(
                            sector=sector,
                            symbol=symbol,
                            current_return_1m=performance_data.get('return_1m', 0),
                            current_return_3m=performance_data.get('return_3m', 0),
                            current_return_6m=performance_data.get('return_6m', 0),
                            relative_strength=performance_data.get('relative_strength', 0),
                            momentum_score=performance_data.get('momentum_score', 0),
                            cycle_score=0,  # Will be calculated later
                            recommendation='neutral'
                        )
                except Exception as e:
                    logger.warning(f"Error analyzing sector {sector}: {e}")
                    continue
            
            return sector_performance
            
        except Exception as e:
            logger.error(f"Error analyzing sector performance: {e}")
            return {}
    
    async def _get_sector_etf_performance(self, symbol: str) -> Optional[Dict[str, float]]:
        """Get performance data for a sector ETF."""
        try:
            # This would typically fetch from market data API
            # For now, return simulated data
            
            # Simulate performance data
            import random
            
            return {
                'return_1m': random.uniform(-0.1, 0.1),
                'return_3m': random.uniform(-0.2, 0.2),
                'return_6m': random.uniform(-0.3, 0.3),
                'relative_strength': random.uniform(-0.1, 0.1),
                'momentum_score': random.uniform(0, 1)
            }
            
        except Exception as e:
            logger.error(f"Error getting ETF performance for {symbol}: {e}")
            return None
    
    def _determine_cycle_phase(self, regime_analysis) -> EconomicCyclePhase:
        """Determine current economic cycle phase."""
        regime = regime_analysis.current_regime
        key_indicators = regime_analysis.key_indicators
        
        # Simple mapping from regime to cycle phase
        if regime == EconomicRegime.RECESSION:
            return EconomicCyclePhase.RECESSION
        
        elif regime == EconomicRegime.RECOVERY:
            return EconomicCyclePhase.EARLY_CYCLE
        
        elif regime == EconomicRegime.EXPANSION:
            # Distinguish between mid and late cycle based on indicators
            gdp_growth = key_indicators.get('gdp_growth', 0)
            unemployment = key_indicators.get('unemployment_rate', 5)
            inflation = key_indicators.get('inflation_rate', 2)
            
            # Late cycle indicators: high growth, low unemployment, rising inflation
            if gdp_growth > 3 and unemployment < 4 and inflation > 3:
                return EconomicCyclePhase.LATE_CYCLE
            else:
                return EconomicCyclePhase.MID_CYCLE
        
        else:
            # Default to mid-cycle for unknown regimes
            return EconomicCyclePhase.MID_CYCLE
    
    def _generate_rotation_signals(
        self,
        cycle_phase: EconomicCyclePhase,
        sector_performance: Dict[str, SectorPerformance]
    ) -> List[RotationSignal]:
        """Generate sector rotation signals."""
        signals = []
        
        try:
            # Get preferred sectors for current cycle
            preferred_sectors = self.cycle_preferences.get(cycle_phase, {}).get('preferred', [])
            avoid_sectors = self.cycle_preferences.get(cycle_phase, {}).get('avoid', [])
            
            # Find rotation opportunities
            for avoid_sector in avoid_sectors:
                if avoid_sector in sector_performance:
                    avoid_perf = sector_performance[avoid_sector]
                    
                    # Look for better alternatives in preferred sectors
                    for preferred_sector in preferred_sectors:
                        if preferred_sector in sector_performance:
                            preferred_perf = sector_performance[preferred_sector]
                            
                            # Calculate signal strength
                            performance_diff = (
                                preferred_perf.momentum_score - avoid_perf.momentum_score
                            )
                            
                            if performance_diff > 0.2:  # Significant difference
                                signal_strength = min(performance_diff, 1.0)
                                
                                reasoning = [
                                    f"{preferred_sector} outperforming {avoid_sector}",
                                    f"Cycle phase favors {preferred_sector}",
                                    f"Momentum score difference: {performance_diff:.2f}"
                                ]
                                
                                signal = RotationSignal(
                                    from_sector=avoid_sector,
                                    to_sector=preferred_sector,
                                    signal_strength=signal_strength,
                                    reasoning=reasoning,
                                    expected_duration=self._estimate_rotation_duration(cycle_phase),
                                    confidence=0.7
                                )
                                signals.append(signal)
            
            # Sort by signal strength
            signals.sort(key=lambda x: x.signal_strength, reverse=True)
            
            return signals[:5]  # Top 5 signals
            
        except Exception as e:
            logger.error(f"Error generating rotation signals: {e}")
            return []
    
    def _get_sector_recommendations(
        self,
        cycle_phase: EconomicCyclePhase,
        sector_performance: Dict[str, SectorPerformance]
    ) -> Dict[str, str]:
        """Get sector recommendations based on cycle and performance."""
        recommendations = {}
        
        try:
            cycle_prefs = self.cycle_preferences.get(cycle_phase, {})
            preferred_sectors = cycle_prefs.get('preferred', [])
            avoid_sectors = cycle_prefs.get('avoid', [])
            
            for sector, perf in sector_performance.items():
                # Base recommendation on cycle preferences
                if sector in preferred_sectors:
                    base_rec = 'overweight'
                elif sector in avoid_sectors:
                    base_rec = 'underweight'
                else:
                    base_rec = 'neutral'
                
                # Adjust based on momentum
                if perf.momentum_score > 0.7:
                    if base_rec == 'underweight':
                        base_rec = 'neutral'
                    elif base_rec == 'neutral':
                        base_rec = 'overweight'
                elif perf.momentum_score < 0.3:
                    if base_rec == 'overweight':
                        base_rec = 'neutral'
                    elif base_rec == 'neutral':
                        base_rec = 'underweight'
                
                recommendations[sector] = base_rec
                perf.recommendation = base_rec
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting sector recommendations: {e}")
            return {}
    
    def _calculate_cycle_score(self, sector: str, cycle_phase: EconomicCyclePhase) -> float:
        """Calculate how well a sector fits the current cycle phase."""
        cycle_prefs = self.cycle_preferences.get(cycle_phase, {})
        
        if sector in cycle_prefs.get('preferred', []):
            return 1.0
        elif sector in cycle_prefs.get('neutral', []):
            return 0.5
        elif sector in cycle_prefs.get('avoid', []):
            return 0.0
        else:
            return 0.5  # Default neutral
    
    def _estimate_rotation_duration(self, cycle_phase: EconomicCyclePhase) -> int:
        """Estimate how long a rotation might last."""
        durations = {
            EconomicCyclePhase.EARLY_CYCLE: 180,  # 6 months
            EconomicCyclePhase.MID_CYCLE: 270,    # 9 months
            EconomicCyclePhase.LATE_CYCLE: 120,   # 4 months
            EconomicCyclePhase.RECESSION: 90      # 3 months
        }
        
        return durations.get(cycle_phase, 180)
    
    def _load_sector_mappings(self) -> Dict[str, str]:
        """Load sector to ETF symbol mappings."""
        return {
            'Technology': 'XLK',
            'Healthcare': 'XLV',
            'Financials': 'XLF',
            'Consumer Discretionary': 'XLY',
            'Consumer Staples': 'XLP',
            'Energy': 'XLE',
            'Industrials': 'XLI',
            'Materials': 'XLB',
            'Real Estate': 'XLRE',
            'Utilities': 'XLU',
            'Communication Services': 'XLC'
        }
    
    def _load_cycle_preferences(self) -> Dict[EconomicCyclePhase, Dict[str, List[str]]]:
        """Load sector preferences for each economic cycle phase."""
        return {
            EconomicCyclePhase.EARLY_CYCLE: {
                'preferred': ['Technology', 'Consumer Discretionary', 'Industrials', 'Materials'],
                'neutral': ['Healthcare', 'Communication Services'],
                'avoid': ['Utilities', 'Consumer Staples', 'Real Estate']
            },
            EconomicCyclePhase.MID_CYCLE: {
                'preferred': ['Technology', 'Industrials', 'Materials', 'Energy'],
                'neutral': ['Healthcare', 'Financials', 'Consumer Discretionary'],
                'avoid': ['Utilities', 'Real Estate']
            },
            EconomicCyclePhase.LATE_CYCLE: {
                'preferred': ['Energy', 'Materials', 'Financials'],
                'neutral': ['Healthcare', 'Consumer Staples'],
                'avoid': ['Technology', 'Consumer Discretionary', 'Real Estate']
            },
            EconomicCyclePhase.RECESSION: {
                'preferred': ['Consumer Staples', 'Healthcare', 'Utilities'],
                'neutral': ['Communication Services'],
                'avoid': ['Technology', 'Consumer Discretionary', 'Financials', 'Energy', 'Materials']
            }
        }
    
    def _empty_rotation_analysis(self) -> Dict[str, Any]:
        """Return empty rotation analysis."""
        return {
            'timestamp': datetime.utcnow(),
            'economic_regime': 'unknown',
            'cycle_phase': 'mid_cycle',
            'sector_performance': {},
            'rotation_signals': [],
            'recommendations': {},
            'cycle_preferences': {}
        }
