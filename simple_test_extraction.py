#!/usr/bin/env python3
"""
Simple test of the updated Webull data extraction
Bypasses complex configuration system
"""

import asyncio
import sys
import os
import re
from datetime import datetime
from typing import Optional
from dataclasses import dataclass

# Simple imports
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait

@dataclass
class MarketData:
    """Market data captured from browser"""
    symbol: str
    price: float
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[int] = None
    change_percent: Optional[float] = None
    timestamp: datetime = None
    pre_market_change: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class SimpleWebullExtractor:
    """Simple Webull data extractor"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        self.base_url = "https://app.webull.com"
        
    async def initialize(self):
        """Initialize the browser driver"""
        try:
            print("Initializing browser driver...")
            
            # Simple Chrome options that work
            self.driver = uc.Chrome(version_main=None)
            self.wait = WebDriverWait(self.driver, 10)
            
            # Set page load timeout
            self.driver.set_page_load_timeout(30)
            
            print("Browser driver initialized successfully")
            return True
            
        except Exception as e:
            print(f"Failed to initialize browser: {e}")
            return False
    
    async def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """
        Get real-time market data for a symbol - UPDATED VERSION
        Based on actual Webull page structure
        """
        try:
            print(f"Getting market data for {symbol}")
            
            # Navigate to stock page
            url = f"{self.base_url}/stocks/{symbol}"
            self.driver.get(url)
            
            # Wait for page to load
            await asyncio.sleep(5)
            
            # Get page text
            page_text = self.driver.find_element(By.TAG_NAME, 'body').text
            
            # Parse the data based on actual Webull format
            # Format: SYMBOL EXCHANGE Company Name Price Pre: Change%
            # Example: AAPL NASDAQ Apple Inc 211.16 Pre: -0.74%
            
            market_data = None
            
            # Look for the symbol in the page
            lines = page_text.split('\n')

            for i, line in enumerate(lines):
                if line.strip() == symbol:
                    # Found the symbol line, data is in subsequent lines
                    # Format: SYMBOL, EXCHANGE, Company Name, Price, Change, Change%
                    try:
                        if i + 5 < len(lines):
                            exchange = lines[i + 1].strip()
                            company = lines[i + 2].strip()
                            price_str = lines[i + 3].strip()
                            change_str = lines[i + 4].strip()
                            change_percent_str = lines[i + 5].strip()

                            # Parse price
                            price = float(price_str.replace(',', ''))

                            # Parse change percentage
                            change_percent = None
                            if change_percent_str.endswith('%'):
                                change_percent = float(change_percent_str.replace('%', '').replace('+', ''))

                            market_data = MarketData(
                                symbol=symbol,
                                price=price,
                                change_percent=change_percent,
                                timestamp=datetime.now()
                            )

                            print(f"Found {symbol}: ${price} ({change_percent:+.2f}%)")
                            break
                    except (ValueError, IndexError) as e:
                        print(f"Error parsing data for {symbol}: {e}")
                        continue
            
            # Alternative method: Search for pattern
            if not market_data:
                # Pattern: Symbol followed by price
                pattern = rf'{symbol}.*?(\d+\.\d{{2}})'
                match = re.search(pattern, page_text)
                if match:
                    price = float(match.group(1))
                    market_data = MarketData(
                        symbol=symbol,
                        price=price,
                        timestamp=datetime.now()
                    )
            
            # Extract additional data if available
            if market_data:
                # Volume
                volume_match = re.search(r'Volume\s*([0-9,.]+[MKB]?)', page_text, re.IGNORECASE)
                if volume_match:
                    market_data.volume = self._parse_volume(volume_match.group(1))
            
            return market_data
            
        except Exception as e:
            print(f"Failed to get market data for {symbol}: {e}")
            return None
    
    def _parse_volume(self, volume_str: str) -> int:
        """Parse volume string with K/M/B suffixes"""
        volume_str = volume_str.replace(',', '')
        
        multipliers = {
            'K': 1_000,
            'M': 1_000_000,
            'B': 1_000_000_000,
        }
        
        for suffix, mult in multipliers.items():
            if suffix in volume_str.upper():
                num = float(re.findall(r'[\d.]+', volume_str)[0])
                return int(num * mult)
        
        try:
            return int(float(volume_str))
        except:
            return 0
    
    async def cleanup(self):
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
            print("Browser driver closed")
        except Exception as e:
            print(f"Error during cleanup: {e}")

async def test_extraction():
    """Test the updated extraction"""
    extractor = SimpleWebullExtractor()
    
    try:
        print("🚀 Initializing browser...")
        if await extractor.initialize():
            print("✅ Browser initialized successfully")
            
            # Test with stocks from your page
            symbols = ['AAPL', 'GOOG', 'MSFT', 'TSLA']
            
            for symbol in symbols:
                print(f"\n📊 Testing {symbol}...")
                data = await extractor.get_market_data(symbol)
                
                if data:
                    print(f"✅ {symbol}: ${data.price}")
                    if data.change_percent:
                        print(f"   Change: {data.change_percent:+.2f}%")
                    if data.pre_market_change:
                        print(f"   Pre-market: {data.pre_market_change:+.2f}%")
                    if data.volume:
                        print(f"   Volume: {data.volume:,}")
                else:
                    print(f"❌ {symbol}: No data found")
                
                await asyncio.sleep(3)
        else:
            print("❌ Failed to initialize browser")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await extractor.cleanup()
        print("\n🧹 Cleanup complete")

if __name__ == "__main__":
    print("🔬 Testing Updated Webull Data Extraction")
    print("=" * 50)
    asyncio.run(test_extraction())
