#!/usr/bin/env python3
"""
Enhanced Webull Browser Bot Launcher

This script launches the browser automation with all production-grade integrations:
- Enhanced security and secrets management
- Advanced monitoring and anomaly detection
- Dead man's switch safety system
- Regulatory compliance checking
- Market microstructure analysis
- Performance optimization
- Emergency kill switch integration
"""

import asyncio
import sys
import yaml
import getpass
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.trading_bot.automation.enhanced_webull_browser_bot import EnhancedWebullBrowserBot
from src.trading_bot.config.production_config import get_config_manager, Environment
from src.trading_bot.security.secrets_manager import <PERSON>hancedSecretsManager, SecurityLevel, SecretType


class BrowserBotLauncher:
    """Enhanced browser bot launcher with production-grade features."""
    
    def __init__(self):
        self.config_manager = None
        self.browser_config = {}
        self.bot = None
    
    async def launch(self):
        """Launch the enhanced browser bot."""
        print("🤖 Enhanced Webull Browser Bot with Production-Grade Integration")
        print("=" * 70)
        
        try:
            # Step 1: Load configuration
            await self._load_configuration()
            
            # Step 2: Setup credentials
            await self._setup_credentials()
            
            # Step 3: Initialize bot
            await self._initialize_bot()
            
            # Step 4: Pre-flight checks
            await self._pre_flight_checks()
            
            # Step 5: Login
            await self._login()
            
            # Step 6: Start trading
            await self._start_trading()
            
        except KeyboardInterrupt:
            print("\n⏹️  Stopping enhanced browser bot...")
        except Exception as e:
            print(f"\n❌ Error: {e}")
        finally:
            await self._cleanup()
    
    async def _load_configuration(self):
        """Load configuration from files."""
        print("\n📋 Step 1: Loading Configuration")
        print("-" * 40)
        
        # Determine environment
        environment = Environment.DEVELOPMENT  # Default to development for safety
        env_arg = input("Environment (development/staging/production) [development]: ").strip()
        
        if env_arg:
            try:
                environment = Environment(env_arg)
            except ValueError:
                print(f"Invalid environment '{env_arg}', using development")
        
        # Production safety check
        if environment == Environment.PRODUCTION:
            print("⚠️  WARNING: You are launching in PRODUCTION mode!")
            print("This will use REAL MONEY for trading!")
            confirm = input("Type 'CONFIRM PRODUCTION' to continue: ")
            
            if confirm != "CONFIRM PRODUCTION":
                print("❌ Production launch cancelled")
                sys.exit(1)
        
        # Load production config
        self.config_manager = get_config_manager(environment)
        print(f"✅ Loaded {environment.value} configuration")
        
        # Load browser-specific config
        browser_config_file = Path("config/browser/browser_config.yaml")
        if browser_config_file.exists():
            with open(browser_config_file, 'r') as f:
                self.browser_config = yaml.safe_load(f)
            print("✅ Loaded browser configuration")
        else:
            print("⚠️  Browser config not found, using defaults")
            self.browser_config = self._get_default_browser_config()
        
        # Override with production config if available
        if hasattr(self.config_manager, 'config') and 'external_apis' in self.config_manager.config:
            webull_config = self.config_manager.config['external_apis'].get('webull', {})
            browser_automation = webull_config.get('browser_automation', {})
            
            if browser_automation:
                self.browser_config.update({
                    'browser': browser_automation,
                    'webull': {
                        'paper_trading': webull_config.get('paper_trading', True),
                        'watchlist': browser_automation.get('watchlist', ['AAPL'])
                    },
                    'integration': browser_automation.get('integration', {})
                })
                print("✅ Merged production browser configuration")
    
    def _get_default_browser_config(self) -> Dict[str, Any]:
        """Get default browser configuration."""
        return {
            'browser': {
                'headless': False,
                'window_size': "1920,1080",
                'screenshot_on_error': True,
                'page_load_timeout': 30
            },
            'webull': {
                'paper_trading': True,  # ALWAYS START WITH TRUE!
                'watchlist': ['AAPL', 'MSFT', 'GOOGL'],
                'min_confidence': 0.7
            },
            'integration': {
                'use_enhanced_secrets': True,
                'enable_metrics': True,
                'enable_dead_mans_switch': True,
                'enable_compliance_checks': True,
                'enable_microstructure_analysis': True,
                'enable_performance_optimization': True
            }
        }
    
    async def _setup_credentials(self):
        """Setup and verify credentials."""
        print("\n🔐 Step 2: Credentials Setup")
        print("-" * 40)
        
        # Check if enhanced secrets are enabled
        if self.browser_config.get('integration', {}).get('use_enhanced_secrets', True):
            secrets_manager = EnhancedSecretsManager()
            
            # Check if credentials exist
            try:
                username = await secrets_manager.get_secret("webull_username")
                password = await secrets_manager.get_secret("webull_password")
                
                if username and password:
                    print("✅ Credentials found in secure storage")
                    return
            except:
                pass
            
            # Credentials not found, prompt user
            print("🔑 Webull credentials not found in secure storage")
            setup_creds = input("Set up credentials now? (y/n) [y]: ").lower() != 'n'
            
            if setup_creds:
                username = input("Webull Username/Email: ")
                password = getpass.getpass("Webull Password: ")
                
                # Store securely
                await secrets_manager.store_secret(
                    name="webull_username",
                    value=username,
                    secret_type=SecretType.USERNAME,
                    security_level=SecurityLevel.HIGH
                )
                
                await secrets_manager.store_secret(
                    name="webull_password",
                    value=password,
                    secret_type=SecretType.PASSWORD,
                    security_level=SecurityLevel.CRITICAL
                )
                
                print("✅ Credentials stored securely")
            else:
                print("⚠️  Credentials will be prompted during login")
        else:
            print("✅ Using manual credential entry")
    
    async def _initialize_bot(self):
        """Initialize the enhanced browser bot."""
        print("\n🤖 Step 3: Initializing Enhanced Bot")
        print("-" * 40)
        
        # Create enhanced bot
        self.bot = EnhancedWebullBrowserBot(self.config_manager, self.browser_config)
        
        # Initialize all systems
        await self.bot.initialize_all_systems()
        
        print("✅ Enhanced browser bot initialized")
    
    async def _pre_flight_checks(self):
        """Perform pre-flight safety checks."""
        print("\n✈️  Step 4: Pre-flight Checks")
        print("-" * 40)
        
        # Check paper trading mode
        paper_trading = self.browser_config.get('webull', {}).get('paper_trading', True)
        
        if not paper_trading:
            print("⚠️  WARNING: Paper trading is DISABLED!")
            print("This will place REAL orders with REAL money!")
            
            confirm = input("Confirm live trading (type 'LIVE TRADING'): ")
            if confirm != "LIVE TRADING":
                print("❌ Live trading cancelled - enabling paper trading")
                self.browser_config['webull']['paper_trading'] = True
        else:
            print("✅ Paper trading mode enabled")
        
        # Check system status
        status = self.bot.get_enhanced_status()
        
        print(f"✅ Systems status:")
        for system, enabled in status['systems'].items():
            status_icon = "✅" if enabled else "❌"
            print(f"   {status_icon} {system.replace('_', ' ').title()}")
        
        # Test browser functionality
        print("\n🧪 Testing browser functionality...")
        try:
            await self.bot.test_navigation()
            await self.bot.test_market_data_extraction()
            print("✅ Browser tests passed")
        except Exception as e:
            print(f"⚠️  Browser test warning: {e}")
    
    async def _login(self):
        """Handle login process."""
        print("\n🔑 Step 5: Login to Webull")
        print("-" * 40)
        
        try:
            # Attempt login with stored credentials
            await self.bot.login()
            print("✅ Login successful")
            
        except Exception as e:
            print(f"⚠️  Stored credentials failed: {e}")
            
            # Manual login
            print("Please enter credentials manually:")
            username = input("Username/Email: ")
            password = getpass.getpass("Password: ")
            
            # Check if MFA is required
            mfa_required = self.browser_config.get('integration', {}).get('mfa_required', False)
            mfa_code = None
            
            if mfa_required:
                mfa_code = input("MFA Code (if required): ")
            
            await self.bot.login(username, password, mfa_code)
            print("✅ Manual login successful")
    
    async def _start_trading(self):
        """Start the enhanced trading loop."""
        print("\n🚀 Step 6: Starting Enhanced Trading")
        print("-" * 40)
        
        # Show final status
        status = self.bot.get_enhanced_status()
        print(f"📊 Session Status:")
        print(f"   • Logged in: {status['browser_bot']['logged_in']}")
        print(f"   • Paper trading: {self.browser_config.get('webull', {}).get('paper_trading', True)}")
        print(f"   • Watchlist: {self.browser_config.get('webull', {}).get('watchlist', [])}")
        print(f"   • Min confidence: {self.browser_config.get('webull', {}).get('min_confidence', 0.7)}")
        
        print("\n🎯 Starting enhanced trading loop...")
        print("Press Ctrl+C to stop")
        
        # Start the enhanced trading loop
        await self.bot.run_enhanced_trading_loop()
    
    async def _cleanup(self):
        """Clean up all systems."""
        if self.bot:
            print("\n🧹 Cleaning up systems...")
            await self.bot.cleanup_all_systems()
            print("✅ Cleanup completed")


async def main():
    """Main launcher function."""
    launcher = BrowserBotLauncher()
    await launcher.launch()


if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    # Check if setup was run
    if not Path("screenshots").exists():
        print("⚠️  Setup not detected. Please run:")
        print("python setup_browser_automation.py")
        sys.exit(1)
    
    asyncio.run(main())
