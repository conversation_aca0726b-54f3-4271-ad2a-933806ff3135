{"dashboard": {"id": null, "title": "Trading Bot Production Dashboard", "tags": ["trading", "bot", "production"], "timezone": "", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up{job=\"trading-bot\"}", "legendFormat": "System Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Portfolio Value", "type": "stat", "targets": [{"expr": "trading_bot_portfolio_value", "legendFormat": "Portfolio Value ($)"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "value"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Daily P&L", "type": "stat", "targets": [{"expr": "trading_bot_daily_pnl", "legendFormat": "Daily P&L ($)"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": -1000}, {"color": "yellow", "value": 0}, {"color": "green", "value": 100}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Active Positions", "type": "stat", "targets": [{"expr": "trading_bot_active_positions", "legendFormat": "Active Positions"}], "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Order Execution Latency", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, trading_bot_order_execution_duration_seconds_bucket)", "legendFormat": "95th Percentile"}, {"expr": "histogram_quantile(0.50, trading_bot_order_execution_duration_seconds_bucket)", "legendFormat": "50th Percentile"}], "yAxes": [{"unit": "s", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Trading Volume", "type": "graph", "targets": [{"expr": "rate(trading_bot_trades_total[5m])", "legendFormat": "Trades per Second"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "System Resources", "type": "graph", "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "legendFormat": "CPU Usage %"}, {"expr": "process_resident_memory_bytes / 1024 / 1024", "legendFormat": "Memory Usage (MB)"}], "yAxes": [{"min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(trading_bot_errors_total[5m])", "legendFormat": "Errors per Second"}], "alert": {"conditions": [{"evaluator": {"params": [0.1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "5m", "frequency": "10s", "handler": 1, "name": "High Error Rate", "noDataState": "no_data", "notifications": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "Risk Metrics", "type": "table", "targets": [{"expr": "trading_bot_max_drawdown", "legendFormat": "Max Drawdown", "format": "table"}, {"expr": "trading_bot_sharpe_ratio", "legendFormat": "<PERSON>", "format": "table"}, {"expr": "trading_bot_var_95", "legendFormat": "VaR 95%", "format": "table"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 10, "title": "Dead Man's Switch Status", "type": "stat", "targets": [{"expr": "trading_bot_dead_mans_switch_status", "legendFormat": "Dead Man's Switch"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 1}, {"color": "green", "value": 2}]}, "mappings": [{"options": {"0": {"text": "TRIGGERED"}}, "type": "value"}, {"options": {"1": {"text": "WARNING"}}, "type": "value"}, {"options": {"2": {"text": "ACTIVE"}}, "type": "value"}]}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 32}}, {"id": 11, "title": "Circuit Breaker Status", "type": "stat", "targets": [{"expr": "trading_bot_circuit_breaker_level", "legendFormat": "Circuit Breaker Level"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "orange", "value": 2}, {"color": "red", "value": 3}]}, "mappings": [{"options": {"0": {"text": "NORMAL"}}, "type": "value"}, {"options": {"1": {"text": "LEVEL 1"}}, "type": "value"}, {"options": {"2": {"text": "LEVEL 2"}}, "type": "value"}, {"options": {"3": {"text": "LEVEL 3"}}, "type": "value"}]}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 32}}, {"id": 12, "title": "Market Regime", "type": "stat", "targets": [{"expr": "trading_bot_market_regime", "legendFormat": "Market Regime"}], "fieldConfig": {"defaults": {"mappings": [{"options": {"1": {"text": "TRENDING_UP"}}, "type": "value"}, {"options": {"2": {"text": "TRENDING_DOWN"}}, "type": "value"}, {"options": {"3": {"text": "MEAN_REVERTING"}}, "type": "value"}, {"options": {"4": {"text": "HIGH_VOLATILITY"}}, "type": "value"}, {"options": {"5": {"text": "LOW_VOLATILITY"}}, "type": "value"}]}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 32}}, {"id": 13, "title": "Compliance Status", "type": "stat", "targets": [{"expr": "trading_bot_compliance_violations", "legendFormat": "Compliance Violations"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 32}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "uid": "trading-bot-prod", "version": 1, "weekStart": ""}}