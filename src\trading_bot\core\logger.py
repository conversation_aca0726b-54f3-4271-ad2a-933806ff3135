"""Logging configuration for the trading bot."""

import sys
from pathlib import Path
from typing import Optional

from loguru import logger

from .config import settings


def setup_logger(
    name: str = "trading_bot",
    level: str = "INFO",
    log_file: Optional[str] = None,
    rotation: str = "1 day",
    retention: str = "30 days",
    format_string: Optional[str] = None,
) -> None:
    """
    Set up the logger with the specified configuration.
    
    Args:
        name: Logger name
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
        rotation: Log rotation policy
        retention: Log retention policy
        format_string: Custom format string (optional)
    """
    # Remove default logger
    logger.remove()
    
    # Default format
    if format_string is None:
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    # Console handler
    logger.add(
        sys.stdout,
        format=format_string,
        level=level,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # File handler (if specified)
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            format=format_string,
            level=level,
            rotation=rotation,
            retention=retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
        )
    
    # Add custom context
    logger.configure(extra={"app_name": name})


def get_logger(name: str) -> "logger":
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)


# Initialize the main logger
setup_logger(
    name=settings.app_name,
    level=settings.log_level,
    log_file=settings.log_file,
    rotation=settings.log_rotation,
    retention=settings.log_retention,
)

# Export the main logger
main_logger = get_logger("trading_bot")
