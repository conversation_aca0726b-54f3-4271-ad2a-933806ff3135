apiVersion: apps/v1
kind: Deployment
metadata:
  name: trading-bot
  namespace: trading-bot
  labels:
    app: ai-trading-bot
    component: application
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ai-trading-bot
      component: application
  template:
    metadata:
      labels:
        app: ai-trading-bot
        component: application
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: trading-bot
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: trading-bot
        image: trading-bot:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POSTGRES_HOST
          value: "postgres-service"
        - name: REDIS_HOST
          value: "redis-service"
        - name: MONGODB_HOST
          value: "mongodb-service"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trading-bot-secrets
              key: postgres-password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trading-bot-secrets
              key: redis-password
        - name: MONGODB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trading-bot-secrets
              key: mongodb-password
        - name: WEBULL_USERNAME
          valueFrom:
            secretKeyRef:
              name: trading-bot-secrets
              key: webull-username
        - name: WEBULL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trading-bot-secrets
              key: webull-password
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: data
          mountPath: /app/data
        - name: logs
          mountPath: /app/logs
        - name: models
          mountPath: /app/models
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: config
        configMap:
          name: trading-bot-config
      - name: data
        persistentVolumeClaim:
          claimName: trading-bot-data
      - name: logs
        persistentVolumeClaim:
          claimName: trading-bot-logs
      - name: models
        persistentVolumeClaim:
          claimName: trading-bot-models
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "trading-bot"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ai-trading-bot
              topologyKey: kubernetes.io/hostname

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: trading-bot
  labels:
    app: postgres
    component: database
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: postgres
      component: database
  template:
    metadata:
      labels:
        app: postgres
        component: database
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - name: postgres
          containerPort: 5432
          protocol: TCP
        env:
        - name: POSTGRES_DB
          value: "trading_bot"
        - name: POSTGRES_USER
          value: "trading_bot"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trading-bot-secrets
              key: postgres-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql
          readOnly: true
        - name: postgres-init
          mountPath: /docker-entrypoint-initdb.d
          readOnly: true
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - trading_bot
            - -d
            - trading_bot
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - trading_bot
            - -d
            - trading_bot
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-data
      - name: postgres-config
        configMap:
          name: postgres-config
      - name: postgres-init
        configMap:
          name: postgres-init-scripts

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: trading-bot
  labels:
    app: redis
    component: cache
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: redis
      component: cache
  template:
    metadata:
      labels:
        app: redis
        component: cache
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - name: redis
          containerPort: 6379
          protocol: TCP
        command:
        - redis-server
        - /etc/redis/redis.conf
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trading-bot-secrets
              key: redis-password
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
          readOnly: true
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-data
      - name: redis-config
        configMap:
          name: redis-config

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
  namespace: trading-bot
  labels:
    app: mongodb
    component: database
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: mongodb
      component: database
  template:
    metadata:
      labels:
        app: mongodb
        component: database
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: mongodb
        image: mongo:6
        ports:
        - name: mongodb
          containerPort: 27017
          protocol: TCP
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          value: "trading_bot"
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trading-bot-secrets
              key: mongodb-password
        - name: MONGO_INITDB_DATABASE
          value: "trading_bot"
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
        - name: mongodb-init
          mountPath: /docker-entrypoint-initdb.d
          readOnly: true
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        livenessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: mongodb-data
        persistentVolumeClaim:
          claimName: mongodb-data
      - name: mongodb-init
        configMap:
          name: mongodb-init-scripts
