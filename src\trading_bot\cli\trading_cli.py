"""Trading CLI - Command-line interface for trading operations.

This module provides a comprehensive CLI for:
- Starting and stopping trading operations
- Monitoring positions and performance
- Running backtests and strategy analysis
- Managing risk parameters
- Generating reports and analytics
"""

import asyncio
import click
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.live import Live
import aiohttp

from ..core.config import settings
from ..utils.logger import get_structured_logger

logger = get_structured_logger(__name__)
console = Console()


class TradingCLI:
    """Comprehensive trading command-line interface."""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
        self.session: Optional[aiohttp.ClientSession] = None
        self.auth_token: Optional[str] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def authenticate(self, username: str, password: str) -> bool:
        """Authenticate with the trading bot API."""
        try:
            async with self.session.post(
                f"{self.api_base_url}/login",
                json={"username": username, "password": password}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("token")
                    console.print("✅ Authentication successful", style="green")
                    return True
                else:
                    console.print("❌ Authentication failed", style="red")
                    return False
        
        except Exception as e:
            console.print(f"❌ Authentication error: {e}", style="red")
            return False
    
    def _get_headers(self) -> dict:
        """Get headers with authentication token."""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    async def _api_request(self, method: str, endpoint: str, **kwargs) -> Optional[dict]:
        """Make authenticated API request."""
        try:
            async with self.session.request(
                method,
                f"{self.api_base_url}{endpoint}",
                headers=self._get_headers(),
                **kwargs
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    console.print(f"❌ API Error ({response.status}): {error_text}", style="red")
                    return None
        
        except Exception as e:
            console.print(f"❌ Request error: {e}", style="red")
            return None


@click.group()
@click.option('--api-url', default="http://localhost:8000", help='API base URL')
@click.option('--username', prompt=True, help='Username for authentication')
@click.option('--password', prompt=True, hide_input=True, help='Password for authentication')
@click.pass_context
def cli(ctx, api_url, username, password):
    """AI Trading Bot CLI - Manage your trading operations."""
    ctx.ensure_object(dict)
    ctx.obj['api_url'] = api_url
    ctx.obj['username'] = username
    ctx.obj['password'] = password


@cli.command()
@click.option('--strategy', default='all', help='Strategy to start (default: all)')
@click.option('--paper', is_flag=True, help='Use paper trading mode')
@click.option('--risk-level', type=click.Choice(['conservative', 'moderate', 'aggressive']), 
              default='moderate', help='Risk level')
@click.pass_context
def start(ctx, strategy, paper, risk_level):
    """Start trading operations."""
    async def _start():
        async with TradingCLI(ctx.obj['api_url']) as trading_cli:
            if await trading_cli.authenticate(ctx.obj['username'], ctx.obj['password']):
                
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    task = progress.add_task("Starting trading operations...", total=None)
                    
                    # Start trading
                    result = await trading_cli._api_request(
                        "POST", "/api/trading/start",
                        json={
                            "strategy": strategy,
                            "paper_trading": paper,
                            "risk_level": risk_level
                        }
                    )
                    
                    if result:
                        console.print("🚀 Trading started successfully!", style="green")
                        console.print(f"Strategy: {strategy}")
                        console.print(f"Mode: {'Paper' if paper else 'Live'}")
                        console.print(f"Risk Level: {risk_level}")
                    else:
                        console.print("❌ Failed to start trading", style="red")
    
    asyncio.run(_start())


@cli.command()
@click.option('--reason', default='Manual stop', help='Reason for stopping')
@click.pass_context
def stop(ctx, reason):
    """Stop trading operations."""
    async def _stop():
        async with TradingCLI(ctx.obj['api_url']) as trading_cli:
            if await trading_cli.authenticate(ctx.obj['username'], ctx.obj['password']):
                
                result = await trading_cli._api_request(
                    "POST", "/api/trading/stop",
                    json={"reason": reason}
                )
                
                if result:
                    console.print("🛑 Trading stopped successfully!", style="yellow")
                    console.print(f"Reason: {reason}")
                else:
                    console.print("❌ Failed to stop trading", style="red")
    
    asyncio.run(_stop())


@cli.command()
@click.option('--watch', is_flag=True, help='Watch positions in real-time')
@click.pass_context
def positions(ctx, watch):
    """View current positions."""
    async def _positions():
        async with TradingCLI(ctx.obj['api_url']) as trading_cli:
            if await trading_cli.authenticate(ctx.obj['username'], ctx.obj['password']):
                
                if watch:
                    # Real-time monitoring
                    with Live(console=console, refresh_per_second=2) as live:
                        while True:
                            try:
                                result = await trading_cli._api_request("GET", "/api/positions")
                                
                                if result:
                                    table = Table(title="Current Positions")
                                    table.add_column("Symbol", style="cyan")
                                    table.add_column("Quantity", justify="right")
                                    table.add_column("Entry Price", justify="right")
                                    table.add_column("Current Price", justify="right")
                                    table.add_column("P&L", justify="right")
                                    table.add_column("P&L %", justify="right")
                                    
                                    for position in result.get('positions', []):
                                        pnl_color = "green" if position['pnl'] >= 0 else "red"
                                        table.add_row(
                                            position['symbol'],
                                            str(position['quantity']),
                                            f"${position['entry_price']:.2f}",
                                            f"${position['current_price']:.2f}",
                                            f"[{pnl_color}]${position['pnl']:.2f}[/{pnl_color}]",
                                            f"[{pnl_color}]{position['pnl_percent']:.2f}%[/{pnl_color}]"
                                        )
                                    
                                    live.update(table)
                                
                                await asyncio.sleep(2)
                            
                            except KeyboardInterrupt:
                                break
                else:
                    # Single snapshot
                    result = await trading_cli._api_request("GET", "/api/positions")
                    
                    if result:
                        table = Table(title="Current Positions")
                        table.add_column("Symbol", style="cyan")
                        table.add_column("Quantity", justify="right")
                        table.add_column("Entry Price", justify="right")
                        table.add_column("Current Price", justify="right")
                        table.add_column("P&L", justify="right")
                        table.add_column("P&L %", justify="right")
                        
                        for position in result.get('positions', []):
                            pnl_color = "green" if position['pnl'] >= 0 else "red"
                            table.add_row(
                                position['symbol'],
                                str(position['quantity']),
                                f"${position['entry_price']:.2f}",
                                f"${position['current_price']:.2f}",
                                f"[{pnl_color}]${position['pnl']:.2f}[/{pnl_color}]",
                                f"[{pnl_color}]{position['pnl_percent']:.2f}%[/{pnl_color}]"
                            )
                        
                        console.print(table)
    
    asyncio.run(_positions())


@cli.command()
@click.option('--strategy', required=True, help='Strategy to backtest')
@click.option('--start-date', required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', required=True, help='End date (YYYY-MM-DD)')
@click.option('--initial-capital', default=100000, help='Initial capital')
@click.pass_context
def backtest(ctx, strategy, start_date, end_date, initial_capital):
    """Run strategy backtest."""
    async def _backtest():
        async with TradingCLI(ctx.obj['api_url']) as trading_cli:
            if await trading_cli.authenticate(ctx.obj['username'], ctx.obj['password']):
                
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    task = progress.add_task("Running backtest...", total=None)
                    
                    result = await trading_cli._api_request(
                        "POST", "/api/backtest",
                        json={
                            "strategy": strategy,
                            "start_date": start_date,
                            "end_date": end_date,
                            "initial_capital": initial_capital
                        }
                    )
                    
                    if result:
                        console.print("📊 Backtest Results", style="bold blue")
                        
                        # Performance metrics
                        metrics = result.get('metrics', {})
                        
                        table = Table(title="Performance Metrics")
                        table.add_column("Metric", style="cyan")
                        table.add_column("Value", justify="right")
                        
                        table.add_row("Total Return", f"{metrics.get('total_return', 0):.2f}%")
                        table.add_row("Sharpe Ratio", f"{metrics.get('sharpe_ratio', 0):.2f}")
                        table.add_row("Max Drawdown", f"{metrics.get('max_drawdown', 0):.2f}%")
                        table.add_row("Win Rate", f"{metrics.get('win_rate', 0):.2f}%")
                        table.add_row("Total Trades", str(metrics.get('total_trades', 0)))
                        
                        console.print(table)
                    else:
                        console.print("❌ Backtest failed", style="red")
    
    asyncio.run(_backtest())


@cli.command()
@click.option('--period', type=click.Choice(['daily', 'weekly', 'monthly']), 
              default='daily', help='Report period')
@click.option('--format', 'output_format', type=click.Choice(['table', 'json']), 
              default='table', help='Output format')
@click.pass_context
def report(ctx, period, output_format):
    """Generate performance report."""
    async def _report():
        async with TradingCLI(ctx.obj['api_url']) as trading_cli:
            if await trading_cli.authenticate(ctx.obj['username'], ctx.obj['password']):
                
                result = await trading_cli._api_request(
                    "GET", f"/api/reports/{period}"
                )
                
                if result:
                    if output_format == 'json':
                        console.print(json.dumps(result, indent=2))
                    else:
                        # Display as formatted table
                        console.print(f"📈 {period.title()} Performance Report", style="bold blue")
                        
                        summary = result.get('summary', {})
                        
                        # Summary panel
                        summary_text = f"""
Total P&L: ${summary.get('total_pnl', 0):.2f}
Portfolio Value: ${summary.get('portfolio_value', 0):.2f}
Total Trades: {summary.get('total_trades', 0)}
Win Rate: {summary.get('win_rate', 0):.2f}%
Sharpe Ratio: {summary.get('sharpe_ratio', 0):.2f}
                        """
                        
                        console.print(Panel(summary_text.strip(), title="Summary"))
                else:
                    console.print("❌ Failed to generate report", style="red")
    
    asyncio.run(_report())


@cli.command()
@click.pass_context
def health(ctx):
    """Check system health."""
    async def _health():
        async with TradingCLI(ctx.obj['api_url']) as trading_cli:
            result = await trading_cli._api_request("GET", "/health")
            
            if result:
                status = result.get('status', 'unknown')
                
                if status == 'healthy':
                    console.print("✅ System is healthy", style="green")
                else:
                    console.print(f"⚠️  System status: {status}", style="yellow")
                
                console.print(f"Timestamp: {result.get('timestamp')}")
                console.print(f"Version: {result.get('version')}")
            else:
                console.print("❌ Health check failed", style="red")
    
    asyncio.run(_health())


if __name__ == '__main__':
    cli()
