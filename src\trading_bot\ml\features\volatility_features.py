"""
Feature engineering specifically for volatility trading
Optimized for gap fills, oversold bounces, and breakout patterns
"""
import pandas as pd
import numpy as np
from typing import Dict, List

class VolatilityFeatureEngineer:
    def __init__(self):
        self.feature_names = []
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create ML features from OHLCV data"""
        features = pd.DataFrame(index=df.index)
        
        # Price-based features
        features['returns_1d'] = df['Close'].pct_change()
        features['returns_5d'] = df['Close'].pct_change(5)
        features['gap'] = (df['Open'] - df['Close'].shift(1)) / df['Close'].shift(1)
        features['gap_abs'] = features['gap'].abs()
        features['intraday_range'] = (df['High'] - df['Low']) / df['Open']
        
        # Volume features
        features['volume_ratio'] = df['Volume'] / df['Volume'].rolling(20).mean()
        features['volume_spike'] = (features['volume_ratio'] > 2).astype(int)
        
        # Technical indicators
        features['rsi'] = self._calculate_rsi(df['Close'])
        features['rsi_oversold'] = (features['rsi'] < 30).astype(int)
        features['bb_position'] = self._bollinger_position(df['Close'])
        
        # Volatility features
        features['atr_ratio'] = self._calculate_atr(df) / df['Close']
        features['volatility_20d'] = df['Close'].pct_change().rolling(20).std()
        
        # Pattern detection
        features['gap_fill_setup'] = ((features['gap_abs'] > 0.02) & 
                                     (features['gap_abs'] < 0.10) & 
                                     (features['volume_ratio'] > 1.5)).astype(int)
        
        features['oversold_setup'] = ((features['rsi'] < 35) & 
                                     (features['volume_ratio'] > 2.0) & 
                                     (features['intraday_range'] > 0.05)).astype(int)
        
        # Target variable (next day return)
        features['target'] = df['Close'].shift(-1) / df['Close'] - 1
        
        return features.dropna()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _bollinger_position(self, prices: pd.Series, period: int = 20) -> pd.Series:
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper = sma + 2 * std
        lower = sma - 2 * std
        return (prices - lower) / (upper - lower)
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        high_low = df['High'] - df['Low']
        high_close = abs(df['High'] - df['Close'].shift())
        low_close = abs(df['Low'] - df['Close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(period).mean()