"""
Production Configuration System

This module provides comprehensive configuration management for all trading bot components.
Includes environment-specific settings, security configurations, and deployment parameters.
"""

import os
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import yaml

from ..core.logger import get_logger

logger = get_logger(__name__)


class Environment(Enum):
    """Deployment environments."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class BrokerType(Enum):
    """Supported brokers."""
    WEBULL = "webull"
    ALPACA = "alpaca"
    INTERACTIVE_BROKERS = "interactive_brokers"
    TD_AMERITRADE = "td_ameritrade"
    SCHWAB = "schwab"


@dataclass
class SecurityConfig:
    """Security configuration."""
    hsm_provider: str = "aws_cloudhsm"  # aws_cloudhsm, azure_keyvault, mock
    hsm_cluster_id: Optional[str] = None
    mfa_provider: str = "totp"  # totp, sms, email
    secret_rotation_days: int = 30
    audit_retention_days: int = 365
    encryption_algorithm: str = "AES-256-GCM"
    key_derivation_iterations: int = 500000


@dataclass
class BrokerConfig:
    """Broker configuration."""
    broker_type: BrokerType
    api_endpoint: str
    paper_trading: bool = True
    rate_limit_requests_per_second: int = 10
    timeout_seconds: int = 30
    retry_attempts: int = 3
    credentials_secret_name: str = ""


@dataclass
class MonitoringConfig:
    """Monitoring configuration."""
    prometheus_port: int = 9090
    grafana_port: int = 3000
    alert_webhook_url: str = ""
    slack_webhook_url: str = ""
    email_smtp_server: str = "smtp.gmail.com"
    email_smtp_port: int = 587
    sms_provider: str = "twilio"  # twilio, aws_sns
    phone_provider: str = "twilio"


@dataclass
class RiskConfig:
    """Risk management configuration."""
    max_daily_loss_percent: float = 2.0
    max_position_percent: float = 5.0
    max_sector_percent: float = 25.0
    circuit_breaker_levels: Dict[str, float] = None
    pdt_enabled: bool = True
    wash_sale_enabled: bool = True
    position_limits_enabled: bool = True
    
    def __post_init__(self):
        if self.circuit_breaker_levels is None:
            self.circuit_breaker_levels = {
                "level_1": 0.02,  # 2% daily loss
                "level_2": 0.05,  # 5% daily loss  
                "level_3": 0.10   # 10% daily loss
            }


@dataclass
class PerformanceConfig:
    """Performance optimization configuration."""
    optimization_level: str = "high"  # standard, high, ultra_low_latency, real_time
    cpu_cores_trading: List[int] = None
    cpu_cores_data: List[int] = None
    memory_pool_size_mb: int = 64
    huge_pages_enabled: bool = False
    numa_optimization: bool = True
    real_time_priority: int = 50
    
    def __post_init__(self):
        if self.cpu_cores_trading is None:
            self.cpu_cores_trading = [0, 1]
        if self.cpu_cores_data is None:
            self.cpu_cores_data = [2, 3]


@dataclass
class DatabaseConfig:
    """Database configuration."""
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_database: str = "trading_bot"
    postgres_user: str = "trading_user"
    postgres_password_secret: str = "postgres_password"
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_password_secret: str = "redis_password"
    connection_pool_size: int = 20
    query_timeout_seconds: int = 30


@dataclass
class BackupConfig:
    """Backup and disaster recovery configuration."""
    backup_schedule_cron: str = "0 2 * * *"  # Daily at 2 AM
    backup_retention_days: int = 30
    geographic_regions: List[str] = None
    s3_bucket: str = ""
    encryption_enabled: bool = True
    compression_enabled: bool = True
    verification_enabled: bool = True
    
    def __post_init__(self):
        if self.geographic_regions is None:
            self.geographic_regions = ["us-east-1", "us-west-2"]


class ProductionConfigManager:
    """Comprehensive production configuration manager."""
    
    def __init__(self, environment: Environment = Environment.DEVELOPMENT):
        self.environment = environment
        self.config_dir = Path("config")
        self.config_dir.mkdir(exist_ok=True)
        
        # Initialize configurations
        self.security = SecurityConfig()
        self.broker = BrokerConfig(
            broker_type=BrokerType.WEBULL,
            api_endpoint="https://api.webull.com",
            credentials_secret_name="webull_credentials"
        )
        self.monitoring = MonitoringConfig()
        self.risk = RiskConfig()
        self.performance = PerformanceConfig()
        self.database = DatabaseConfig()
        self.backup = BackupConfig()
        
        # Load environment-specific overrides
        self._load_environment_config()
    
    def _load_environment_config(self):
        """Load environment-specific configuration overrides."""
        config_file = self.config_dir / f"{self.environment.value}.yaml"
        
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    env_config = yaml.safe_load(f)
                
                # Apply overrides
                self._apply_config_overrides(env_config)
                logger.info(f"Loaded configuration for {self.environment.value} environment")
                
            except Exception as e:
                logger.error(f"Failed to load environment config: {e}")
        else:
            logger.info(f"No environment config found for {self.environment.value}, using defaults")
    
    def _apply_config_overrides(self, overrides: Dict[str, Any]):
        """Apply configuration overrides from environment file."""
        
        if "security" in overrides:
            self._update_dataclass(self.security, overrides["security"])
        
        if "broker" in overrides:
            self._update_dataclass(self.broker, overrides["broker"])
            # Convert broker_type string to enum
            if "broker_type" in overrides["broker"]:
                self.broker.broker_type = BrokerType(overrides["broker"]["broker_type"])
        
        if "monitoring" in overrides:
            self._update_dataclass(self.monitoring, overrides["monitoring"])
        
        if "risk" in overrides:
            self._update_dataclass(self.risk, overrides["risk"])
        
        if "performance" in overrides:
            self._update_dataclass(self.performance, overrides["performance"])
        
        if "database" in overrides:
            self._update_dataclass(self.database, overrides["database"])
        
        if "backup" in overrides:
            self._update_dataclass(self.backup, overrides["backup"])
    
    def _update_dataclass(self, obj, updates: Dict[str, Any]):
        """Update dataclass fields from dictionary."""
        for key, value in updates.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def save_config_template(self, filename: str = "config_template.yaml"):
        """Save configuration template for customization."""
        template = {
            "security": asdict(self.security),
            "broker": asdict(self.broker),
            "monitoring": asdict(self.monitoring),
            "risk": asdict(self.risk),
            "performance": asdict(self.performance),
            "database": asdict(self.database),
            "backup": asdict(self.backup)
        }
        
        # Convert enums to strings for YAML serialization
        template["broker"]["broker_type"] = self.broker.broker_type.value
        
        template_file = self.config_dir / filename
        
        try:
            with open(template_file, 'w') as f:
                yaml.dump(template, f, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration template saved to {template_file}")
            return str(template_file)
            
        except Exception as e:
            logger.error(f"Failed to save configuration template: {e}")
            return None
    
    def validate_config(self) -> Dict[str, List[str]]:
        """Validate configuration for production readiness."""
        issues = {
            "errors": [],
            "warnings": [],
            "recommendations": []
        }
        
        # Security validation
        if self.environment == Environment.PRODUCTION:
            if self.security.hsm_provider == "mock":
                issues["errors"].append("Production environment cannot use mock HSM provider")
            
            if self.security.secret_rotation_days > 90:
                issues["warnings"].append("Secret rotation interval > 90 days is not recommended")
            
            if not self.broker.credentials_secret_name:
                issues["errors"].append("Broker credentials secret name must be specified")
        
        # Broker validation
        if self.broker.paper_trading and self.environment == Environment.PRODUCTION:
            issues["warnings"].append("Paper trading is enabled in production environment")
        
        if self.broker.rate_limit_requests_per_second > 30:
            issues["warnings"].append("High rate limit may trigger broker API limits")
        
        # Risk validation
        if self.risk.max_daily_loss_percent > 5.0:
            issues["warnings"].append("Daily loss limit > 5% is aggressive for production")
        
        if self.risk.max_position_percent > 10.0:
            issues["warnings"].append("Position limit > 10% increases concentration risk")
        
        # Performance validation
        if self.performance.optimization_level == "real_time" and os.geteuid() != 0:
            issues["warnings"].append("Real-time optimization requires root privileges")
        
        # Database validation
        if self.database.postgres_host == "localhost" and self.environment == Environment.PRODUCTION:
            issues["recommendations"].append("Consider using managed database service for production")
        
        # Backup validation
        if not self.backup.s3_bucket and self.environment == Environment.PRODUCTION:
            issues["errors"].append("S3 bucket must be specified for production backups")
        
        if len(self.backup.geographic_regions) < 2:
            issues["recommendations"].append("Multiple geographic regions recommended for disaster recovery")
        
        return issues
    
    def get_environment_variables(self) -> Dict[str, str]:
        """Get environment variables for deployment."""
        env_vars = {
            # Application
            "TRADING_BOT_ENVIRONMENT": self.environment.value,
            "TRADING_BOT_LOG_LEVEL": "INFO" if self.environment == Environment.PRODUCTION else "DEBUG",
            
            # Database
            "POSTGRES_HOST": self.database.postgres_host,
            "POSTGRES_PORT": str(self.database.postgres_port),
            "POSTGRES_DATABASE": self.database.postgres_database,
            "POSTGRES_USER": self.database.postgres_user,
            "REDIS_HOST": self.database.redis_host,
            "REDIS_PORT": str(self.database.redis_port),
            
            # Monitoring
            "PROMETHEUS_PORT": str(self.monitoring.prometheus_port),
            "GRAFANA_PORT": str(self.monitoring.grafana_port),
            
            # Performance
            "OPTIMIZATION_LEVEL": self.performance.optimization_level,
            "MEMORY_POOL_SIZE_MB": str(self.performance.memory_pool_size_mb),
            
            # Security
            "HSM_PROVIDER": self.security.hsm_provider,
            "MFA_PROVIDER": self.security.mfa_provider,
            
            # Broker
            "BROKER_TYPE": self.broker.broker_type.value,
            "BROKER_API_ENDPOINT": self.broker.api_endpoint,
            "PAPER_TRADING": str(self.broker.paper_trading).lower(),
        }
        
        return env_vars
    
    def generate_docker_compose(self) -> str:
        """Generate Docker Compose configuration."""
        compose_config = {
            "version": "3.8",
            "services": {
                "trading-bot": {
                    "build": ".",
                    "environment": self.get_environment_variables(),
                    "depends_on": ["postgres", "redis", "prometheus"],
                    "volumes": [
                        "./data:/app/data",
                        "./logs:/app/logs",
                        "./config:/app/config"
                    ],
                    "ports": ["8000:8000"],
                    "restart": "unless-stopped"
                },
                "postgres": {
                    "image": "postgres:15",
                    "environment": {
                        "POSTGRES_DB": self.database.postgres_database,
                        "POSTGRES_USER": self.database.postgres_user,
                        "POSTGRES_PASSWORD": "${POSTGRES_PASSWORD}"
                    },
                    "volumes": ["postgres_data:/var/lib/postgresql/data"],
                    "ports": [f"{self.database.postgres_port}:5432"],
                    "restart": "unless-stopped"
                },
                "redis": {
                    "image": "redis:7-alpine",
                    "command": "redis-server --requirepass ${REDIS_PASSWORD}",
                    "volumes": ["redis_data:/data"],
                    "ports": [f"{self.database.redis_port}:6379"],
                    "restart": "unless-stopped"
                },
                "prometheus": {
                    "image": "prom/prometheus:latest",
                    "ports": [f"{self.monitoring.prometheus_port}:9090"],
                    "volumes": [
                        "./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml",
                        "prometheus_data:/prometheus"
                    ],
                    "restart": "unless-stopped"
                },
                "grafana": {
                    "image": "grafana/grafana:latest",
                    "ports": [f"{self.monitoring.grafana_port}:3000"],
                    "environment": {
                        "GF_SECURITY_ADMIN_PASSWORD": "${GRAFANA_PASSWORD}"
                    },
                    "volumes": [
                        "grafana_data:/var/lib/grafana",
                        "./monitoring/grafana:/etc/grafana/provisioning"
                    ],
                    "restart": "unless-stopped"
                }
            },
            "volumes": {
                "postgres_data": {},
                "redis_data": {},
                "prometheus_data": {},
                "grafana_data": {}
            }
        }
        
        return yaml.dump(compose_config, default_flow_style=False, indent=2)
    
    def get_secrets_list(self) -> List[str]:
        """Get list of secrets that need to be configured."""
        secrets = [
            self.broker.credentials_secret_name,
            self.database.postgres_password_secret,
            self.database.redis_password_secret,
            "grafana_password",
            "email_password",
            "twilio_auth_token",
            "slack_webhook_token"
        ]
        
        if self.security.hsm_provider != "mock":
            secrets.extend([
                "hsm_cluster_certificate",
                "hsm_client_certificate",
                "hsm_client_private_key"
            ])
        
        return secrets


# Global configuration instance
_config_manager = None

def get_config_manager(environment: Environment = None) -> ProductionConfigManager:
    """Get global configuration manager instance."""
    global _config_manager
    
    if _config_manager is None:
        env = environment or Environment(os.getenv("TRADING_BOT_ENVIRONMENT", "development"))
        _config_manager = ProductionConfigManager(env)
    
    return _config_manager
