"""Real-time risk monitoring and alerting system."""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

import aiohttp
from sqlalchemy import and_, func, select

from ..core.config import settings
from ..core.exceptions import RiskError
from ..core.logger import get_logger
from ..data.database import get_postgres_session
from ..data.models import RiskMetrics, RiskLimitBreach

logger = get_logger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class AlertChannel(Enum):
    """Alert delivery channels."""
    TELEGRAM = "telegram"
    EMAIL = "email"
    WEBHOOK = "webhook"
    LOG = "log"


class AlertManager:
    """Comprehensive alert management system."""
    
    def __init__(self):
        self.config = settings
        self.telegram_bot_token = getattr(settings, 'telegram_bot_token', None)
        self.telegram_chat_id = getattr(settings, 'telegram_chat_id', None)
        self.webhook_url = getattr(settings, 'alert_webhook_url', None)
        self.alert_history = []
        self.alert_cooldowns = {}  # Prevent spam
        self.cooldown_duration = timedelta(minutes=5)
        
    async def send_alert(
        self,
        message: str,
        severity: AlertSeverity = AlertSeverity.MEDIUM,
        channels: List[AlertChannel] = None,
        metadata: Dict[str, Any] = None
    ):
        """
        Send alert through specified channels.
        
        Args:
            message: Alert message
            severity: Alert severity level
            channels: List of channels to send alert to
            metadata: Additional metadata for the alert
        """
        try:
            if channels is None:
                channels = [AlertChannel.LOG, AlertChannel.TELEGRAM]
            
            # Check cooldown to prevent spam
            alert_key = f"{message[:50]}_{severity.value}"
            if self._is_in_cooldown(alert_key):
                logger.debug(f"Alert in cooldown, skipping: {alert_key}")
                return
            
            alert_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "message": message,
                "severity": severity.value,
                "metadata": metadata or {}
            }
            
            # Send to each channel
            for channel in channels:
                try:
                    if channel == AlertChannel.TELEGRAM:
                        await self._send_telegram_alert(alert_data)
                    elif channel == AlertChannel.EMAIL:
                        await self._send_email_alert(alert_data)
                    elif channel == AlertChannel.WEBHOOK:
                        await self._send_webhook_alert(alert_data)
                    elif channel == AlertChannel.LOG:
                        self._send_log_alert(alert_data)
                        
                except Exception as e:
                    logger.error(f"Failed to send alert via {channel.value}: {e}")
            
            # Record alert
            self.alert_history.append(alert_data)
            self.alert_cooldowns[alert_key] = datetime.utcnow()
            
            # Keep only last 1000 alerts
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-1000:]
                
        except Exception as e:
            logger.error(f"Error sending alert: {e}")
    
    async def _send_telegram_alert(self, alert_data: Dict[str, Any]):
        """Send alert via Telegram."""
        if not self.telegram_bot_token or not self.telegram_chat_id:
            return
        
        try:
            # Format message for Telegram
            severity_emoji = {
                "LOW": "🟡",
                "MEDIUM": "🟠", 
                "HIGH": "🔴",
                "CRITICAL": "🚨"
            }
            
            emoji = severity_emoji.get(alert_data["severity"], "ℹ️")
            timestamp = datetime.fromisoformat(alert_data["timestamp"]).strftime("%H:%M:%S")
            
            message = f"{emoji} *{alert_data['severity']}* Alert - {timestamp}\n\n"
            message += f"{alert_data['message']}\n"
            
            if alert_data.get("metadata"):
                message += "\n📊 *Details:*\n"
                for key, value in alert_data["metadata"].items():
                    if isinstance(value, float):
                        message += f"• {key}: {value:.4f}\n"
                    else:
                        message += f"• {key}: {value}\n"
            
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                "chat_id": self.telegram_chat_id,
                "text": message,
                "parse_mode": "Markdown"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status != 200:
                        logger.error(f"Telegram API error: {response.status}")
                        
        except Exception as e:
            logger.error(f"Error sending Telegram alert: {e}")
    
    async def _send_email_alert(self, alert_data: Dict[str, Any]):
        """Send alert via email."""
        # TODO: Implement email alerting
        logger.info(f"Email alert (not implemented): {alert_data['message']}")
    
    async def _send_webhook_alert(self, alert_data: Dict[str, Any]):
        """Send alert via webhook."""
        if not self.webhook_url:
            return
            
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.webhook_url, json=alert_data) as response:
                    if response.status not in [200, 201, 202]:
                        logger.error(f"Webhook alert failed: {response.status}")
                        
        except Exception as e:
            logger.error(f"Error sending webhook alert: {e}")
    
    def _send_log_alert(self, alert_data: Dict[str, Any]):
        """Send alert to logs."""
        severity = alert_data["severity"]
        message = alert_data["message"]
        
        if severity == "CRITICAL":
            logger.critical(f"RISK ALERT: {message}")
        elif severity == "HIGH":
            logger.error(f"RISK ALERT: {message}")
        elif severity == "MEDIUM":
            logger.warning(f"RISK ALERT: {message}")
        else:
            logger.info(f"RISK ALERT: {message}")
    
    def _is_in_cooldown(self, alert_key: str) -> bool:
        """Check if alert is in cooldown period."""
        if alert_key not in self.alert_cooldowns:
            return False
        
        last_sent = self.alert_cooldowns[alert_key]
        return datetime.utcnow() - last_sent < self.cooldown_duration
    
    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get recent alerts within specified hours."""
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        
        return [
            alert for alert in self.alert_history
            if datetime.fromisoformat(alert["timestamp"]) > cutoff
        ]


class RiskMonitor:
    """Real-time risk monitoring dashboard and metrics."""
    
    def __init__(self):
        self.alert_manager = AlertManager()
        self.monitoring_active = False
        self.monitor_task = None
        self.update_frequency = timedelta(seconds=30)  # Monitor every 30 seconds
        self.last_metrics = {}
        
    async def start_monitoring(self):
        """Start real-time risk monitoring."""
        if self.monitoring_active:
            logger.warning("Risk monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Risk monitoring started")
    
    async def stop_monitoring(self):
        """Stop real-time risk monitoring."""
        self.monitoring_active = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Risk monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                await self._check_risk_metrics()
                await self._check_system_health()
                await asyncio.sleep(self.update_frequency.total_seconds())
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retry
    
    async def _check_risk_metrics(self):
        """Check current risk metrics for alerts."""
        try:
            async with get_postgres_session() as session:
                # Get latest risk metrics
                stmt = (
                    select(RiskMetrics)
                    .order_by(RiskMetrics.timestamp.desc())
                    .limit(1)
                )
                result = await session.execute(stmt)
                latest_metrics = result.scalar_one_or_none()
                
                if not latest_metrics:
                    return
                
                # Check for metric-based alerts
                await self._check_var_alerts(latest_metrics)
                await self._check_drawdown_alerts(latest_metrics)
                await self._check_correlation_alerts(latest_metrics)
                
                self.last_metrics = {
                    "timestamp": latest_metrics.timestamp,
                    "portfolio_value": float(latest_metrics.portfolio_value),
                    "var_95": float(latest_metrics.var_95 or 0),
                    "current_drawdown": float(latest_metrics.current_drawdown or 0),
                    "max_correlation": float(latest_metrics.max_correlation or 0)
                }
                
        except Exception as e:
            logger.error(f"Error checking risk metrics: {e}")
    
    async def _check_var_alerts(self, metrics: RiskMetrics):
        """Check VaR-based alerts."""
        if not metrics.var_95:
            return
        
        var_95 = float(metrics.var_95)
        portfolio_value = float(metrics.portfolio_value)
        var_percentage = var_95 / portfolio_value
        
        # Alert if VaR exceeds 5% of portfolio
        if var_percentage > 0.05:
            await self.alert_manager.send_alert(
                f"High VaR Alert: 95% VaR is ${var_95:,.2f} ({var_percentage:.2%} of portfolio)",
                AlertSeverity.HIGH,
                metadata={
                    "var_95": var_95,
                    "portfolio_value": portfolio_value,
                    "var_percentage": var_percentage
                }
            )
    
    async def _check_drawdown_alerts(self, metrics: RiskMetrics):
        """Check drawdown-based alerts."""
        if not metrics.current_drawdown:
            return
        
        drawdown = float(metrics.current_drawdown)
        
        if drawdown >= 0.10:  # 10% drawdown
            await self.alert_manager.send_alert(
                f"Critical Drawdown Alert: {drawdown:.2%} drawdown detected",
                AlertSeverity.CRITICAL,
                metadata={"current_drawdown": drawdown}
            )
        elif drawdown >= 0.05:  # 5% drawdown
            await self.alert_manager.send_alert(
                f"High Drawdown Alert: {drawdown:.2%} drawdown detected",
                AlertSeverity.HIGH,
                metadata={"current_drawdown": drawdown}
            )
    
    async def _check_correlation_alerts(self, metrics: RiskMetrics):
        """Check correlation-based alerts."""
        if not metrics.max_correlation:
            return
        
        max_corr = float(metrics.max_correlation)
        
        if max_corr > 0.8:  # High correlation
            await self.alert_manager.send_alert(
                f"High Correlation Alert: Maximum correlation is {max_corr:.3f}",
                AlertSeverity.MEDIUM,
                metadata={"max_correlation": max_corr}
            )
    
    async def _check_system_health(self):
        """Check system health metrics."""
        try:
            # Check if risk metrics are being updated
            if self.last_metrics:
                last_update = self.last_metrics.get("timestamp")
                if last_update:
                    time_since_update = datetime.utcnow() - last_update
                    
                    if time_since_update > timedelta(minutes=10):
                        await self.alert_manager.send_alert(
                            f"Risk Metrics Stale: Last update {time_since_update} ago",
                            AlertSeverity.HIGH,
                            metadata={"minutes_since_update": time_since_update.total_seconds() / 60}
                        )
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get current dashboard data."""
        return {
            "monitoring_active": self.monitoring_active,
            "last_update": datetime.utcnow().isoformat(),
            "last_metrics": self.last_metrics,
            "recent_alerts": self.alert_manager.get_recent_alerts(hours=24),
            "system_status": "healthy" if self.monitoring_active else "stopped"
        }


# Global instances
alert_manager = AlertManager()
risk_monitor = RiskMonitor()
