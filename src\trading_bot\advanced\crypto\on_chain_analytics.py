"""On-chain analytics and whale tracking."""

import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import json

from ...core.config import settings
from ...core.logger import get_logger
from ...utils.cache import cache_manager
from ...utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


class TransactionType(Enum):
    """Transaction types for analysis."""
    TRANSFER = "transfer"
    SWAP = "swap"
    LIQUIDITY_ADD = "liquidity_add"
    LIQUIDITY_REMOVE = "liquidity_remove"
    LENDING = "lending"
    BORROWING = "borrowing"


@dataclass
class WhaleTransaction:
    """Large transaction data structure."""
    tx_hash: str
    from_address: str
    to_address: str
    token: str
    amount: float
    value_usd: float
    timestamp: datetime
    transaction_type: TransactionType
    exchange: Optional[str] = None


@dataclass
class WhaleWallet:
    """Whale wallet information."""
    address: str
    balance_usd: float
    tokens: Dict[str, float]
    transaction_count: int
    first_seen: datetime
    last_active: datetime
    labels: List[str]  # Exchange, DeFi protocol, etc.


@dataclass
class OnChainMetrics:
    """On-chain metrics for a token."""
    token: str
    active_addresses: int
    transaction_count: int
    volume_24h: float
    whale_activity: float
    exchange_inflows: float
    exchange_outflows: float
    defi_tvl: float
    holder_distribution: Dict[str, float]


@dataclass
class MarketSignal:
    """On-chain derived market signal."""
    token: str
    signal_type: str  # 'bullish', 'bearish', 'neutral'
    strength: float  # 0-1
    confidence: float
    reasoning: List[str]
    timeframe: str  # 'short', 'medium', 'long'


class OnChainAnalyzer:
    """On-chain data analysis and whale tracking."""
    
    def __init__(self):
        self.rate_limiter = RateLimiter(max_requests=100, time_window=60)
        self.whale_threshold = 1000000  # $1M threshold for whale transactions
        self.tracked_wallets = {}  # Cache for whale wallets
        
    async def track_whale_activity(
        self,
        tokens: List[str],
        hours_back: int = 24,
        min_value: float = None
    ) -> List[WhaleTransaction]:
        """
        Track large whale transactions.
        
        Args:
            tokens: List of tokens to monitor
            hours_back: Hours to look back
            min_value: Minimum transaction value in USD
            
        Returns:
            List of whale transactions
        """
        try:
            if min_value is None:
                min_value = self.whale_threshold
            
            whale_transactions = []
            
            for token in tokens:
                # Fetch large transactions for each token
                token_transactions = await self._fetch_large_transactions(
                    token, hours_back, min_value
                )
                whale_transactions.extend(token_transactions)
            
            # Sort by value (largest first)
            whale_transactions.sort(key=lambda x: x.value_usd, reverse=True)
            
            return whale_transactions[:50]  # Top 50 transactions
            
        except Exception as e:
            logger.error(f"Error tracking whale activity: {e}")
            return []
    
    async def analyze_exchange_flows(
        self,
        token: str,
        hours_back: int = 24
    ) -> Dict[str, float]:
        """
        Analyze token flows to/from exchanges.
        
        Args:
            token: Token to analyze
            hours_back: Hours to look back
            
        Returns:
            Dictionary with inflow/outflow data
        """
        try:
            # Get exchange addresses
            exchange_addresses = await self._get_exchange_addresses()
            
            inflows = 0.0
            outflows = 0.0
            
            # Analyze transactions to/from exchanges
            transactions = await self._fetch_token_transactions(token, hours_back)
            
            for tx in transactions:
                if tx.to_address in exchange_addresses:
                    inflows += tx.value_usd
                elif tx.from_address in exchange_addresses:
                    outflows += tx.value_usd
            
            net_flow = inflows - outflows
            
            return {
                'inflows': inflows,
                'outflows': outflows,
                'net_flow': net_flow,
                'flow_ratio': inflows / outflows if outflows > 0 else float('inf'),
                'signal': 'bearish' if net_flow > 0 else 'bullish' if net_flow < 0 else 'neutral'
            }
            
        except Exception as e:
            logger.error(f"Error analyzing exchange flows: {e}")
            return {}
    
    async def get_on_chain_metrics(
        self,
        token: str,
        timeframe: str = "24h"
    ) -> OnChainMetrics:
        """
        Get comprehensive on-chain metrics for a token.
        
        Args:
            token: Token symbol
            timeframe: Analysis timeframe
            
        Returns:
            On-chain metrics
        """
        try:
            # Fetch various on-chain data
            tasks = [
                self._get_active_addresses(token, timeframe),
                self._get_transaction_count(token, timeframe),
                self._get_volume_data(token, timeframe),
                self._get_whale_activity_score(token, timeframe),
                self._get_exchange_flows(token, timeframe),
                self._get_defi_tvl(token),
                self._get_holder_distribution(token)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            active_addresses = results[0] if not isinstance(results[0], Exception) else 0
            transaction_count = results[1] if not isinstance(results[1], Exception) else 0
            volume_24h = results[2] if not isinstance(results[2], Exception) else 0
            whale_activity = results[3] if not isinstance(results[3], Exception) else 0
            exchange_flows = results[4] if not isinstance(results[4], Exception) else {}
            defi_tvl = results[5] if not isinstance(results[5], Exception) else 0
            holder_dist = results[6] if not isinstance(results[6], Exception) else {}
            
            return OnChainMetrics(
                token=token,
                active_addresses=active_addresses,
                transaction_count=transaction_count,
                volume_24h=volume_24h,
                whale_activity=whale_activity,
                exchange_inflows=exchange_flows.get('inflows', 0),
                exchange_outflows=exchange_flows.get('outflows', 0),
                defi_tvl=defi_tvl,
                holder_distribution=holder_dist
            )
            
        except Exception as e:
            logger.error(f"Error getting on-chain metrics: {e}")
            return self._empty_metrics(token)
    
    async def generate_on_chain_signals(
        self,
        tokens: List[str]
    ) -> List[MarketSignal]:
        """
        Generate trading signals based on on-chain data.
        
        Args:
            tokens: List of tokens to analyze
            
        Returns:
            List of market signals
        """
        try:
            signals = []
            
            for token in tokens:
                # Get on-chain metrics
                metrics = await self.get_on_chain_metrics(token)
                
                # Analyze exchange flows
                exchange_signal = self._analyze_exchange_flow_signal(metrics)
                if exchange_signal:
                    signals.append(exchange_signal)
                
                # Analyze whale activity
                whale_signal = self._analyze_whale_activity_signal(token, metrics)
                if whale_signal:
                    signals.append(whale_signal)
                
                # Analyze network activity
                activity_signal = self._analyze_network_activity_signal(token, metrics)
                if activity_signal:
                    signals.append(activity_signal)
            
            # Sort by confidence and strength
            signals.sort(key=lambda x: x.confidence * x.strength, reverse=True)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating on-chain signals: {e}")
            return []
    
    async def _fetch_large_transactions(
        self,
        token: str,
        hours_back: int,
        min_value: float
    ) -> List[WhaleTransaction]:
        """Fetch large transactions for a token."""
        try:
            # This would typically use Etherscan, Moralis, or similar API
            # For now, return simulated data
            
            transactions = []
            
            # Simulate some whale transactions
            import random
            
            for i in range(random.randint(1, 5)):
                tx = WhaleTransaction(
                    tx_hash=f"0x{''.join(random.choices('0123456789abcdef', k=64))}",
                    from_address=f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    to_address=f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    token=token,
                    amount=random.uniform(1000, 100000),
                    value_usd=random.uniform(min_value, min_value * 10),
                    timestamp=datetime.now() - timedelta(hours=random.randint(1, hours_back)),
                    transaction_type=random.choice(list(TransactionType))
                )
                transactions.append(tx)
            
            return transactions
            
        except Exception as e:
            logger.error(f"Error fetching large transactions: {e}")
            return []
    
    async def _get_exchange_addresses(self) -> List[str]:
        """Get known exchange wallet addresses."""
        # This would typically be loaded from a database or API
        return [
            "******************************************",  # Binance
            "******************************************",  # Binance 2
            "******************************************",  # Binance 3
            "******************************************",  # Binance 4
            "******************************************",  # Binance 5
            "******************************************",  # Binance 6
            "******************************************",  # Binance 7
            "******************************************",  # Binance 8
            "******************************************",  # Binance 9
            "******************************************",  # Binance 10
            "******************************************",  # Binance 11
            "******************************************",  # Binance 12
            "******************************************",  # Binance 13
            "******************************************",  # Binance Hot Wallet
        ]
    
    async def _fetch_token_transactions(
        self,
        token: str,
        hours_back: int
    ) -> List[WhaleTransaction]:
        """Fetch all transactions for a token."""
        # This would fetch from blockchain APIs
        return []
    
    async def _get_active_addresses(self, token: str, timeframe: str) -> int:
        """Get number of active addresses."""
        # Simulate active addresses
        import random
        return random.randint(1000, 50000)
    
    async def _get_transaction_count(self, token: str, timeframe: str) -> int:
        """Get transaction count."""
        import random
        return random.randint(5000, 100000)
    
    async def _get_volume_data(self, token: str, timeframe: str) -> float:
        """Get volume data."""
        import random
        return random.uniform(1000000, 100000000)
    
    async def _get_whale_activity_score(self, token: str, timeframe: str) -> float:
        """Get whale activity score."""
        import random
        return random.uniform(0, 1)
    
    async def _get_exchange_flows(self, token: str, timeframe: str) -> Dict[str, float]:
        """Get exchange flow data."""
        import random
        inflows = random.uniform(100000, 10000000)
        outflows = random.uniform(100000, 10000000)
        return {
            'inflows': inflows,
            'outflows': outflows,
            'net_flow': inflows - outflows
        }
    
    async def _get_defi_tvl(self, token: str) -> float:
        """Get DeFi TVL for token."""
        import random
        return random.uniform(1000000, 1000000000)
    
    async def _get_holder_distribution(self, token: str) -> Dict[str, float]:
        """Get holder distribution."""
        return {
            'top_10': 0.4,
            'top_100': 0.7,
            'top_1000': 0.9,
            'others': 0.1
        }
    
    def _analyze_exchange_flow_signal(self, metrics: OnChainMetrics) -> Optional[MarketSignal]:
        """Analyze exchange flows for signals."""
        try:
            net_flow = metrics.exchange_inflows - metrics.exchange_outflows
            total_flow = metrics.exchange_inflows + metrics.exchange_outflows
            
            if total_flow == 0:
                return None
            
            flow_ratio = abs(net_flow) / total_flow
            
            if flow_ratio > 0.3:  # Significant flow imbalance
                if net_flow > 0:  # More inflows (bearish)
                    signal_type = 'bearish'
                    reasoning = ['Large exchange inflows detected', 'Potential selling pressure']
                else:  # More outflows (bullish)
                    signal_type = 'bullish'
                    reasoning = ['Large exchange outflows detected', 'Potential accumulation']
                
                return MarketSignal(
                    token=metrics.token,
                    signal_type=signal_type,
                    strength=min(flow_ratio, 1.0),
                    confidence=0.7,
                    reasoning=reasoning,
                    timeframe='short'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error analyzing exchange flow signal: {e}")
            return None
    
    def _analyze_whale_activity_signal(
        self,
        token: str,
        metrics: OnChainMetrics
    ) -> Optional[MarketSignal]:
        """Analyze whale activity for signals."""
        try:
            if metrics.whale_activity > 0.7:  # High whale activity
                return MarketSignal(
                    token=token,
                    signal_type='neutral',  # Could go either way
                    strength=metrics.whale_activity,
                    confidence=0.6,
                    reasoning=['High whale activity detected', 'Increased volatility expected'],
                    timeframe='short'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error analyzing whale activity signal: {e}")
            return None
    
    def _analyze_network_activity_signal(
        self,
        token: str,
        metrics: OnChainMetrics
    ) -> Optional[MarketSignal]:
        """Analyze network activity for signals."""
        try:
            # Simple activity score based on addresses and transactions
            activity_score = (metrics.active_addresses * metrics.transaction_count) / 1000000
            
            if activity_score > 100:  # High activity
                return MarketSignal(
                    token=token,
                    signal_type='bullish',
                    strength=min(activity_score / 1000, 1.0),
                    confidence=0.5,
                    reasoning=['High network activity', 'Increased user engagement'],
                    timeframe='medium'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error analyzing network activity signal: {e}")
            return None
    
    def _empty_metrics(self, token: str) -> OnChainMetrics:
        """Return empty metrics."""
        return OnChainMetrics(
            token=token,
            active_addresses=0,
            transaction_count=0,
            volume_24h=0,
            whale_activity=0,
            exchange_inflows=0,
            exchange_outflows=0,
            defi_tvl=0,
            holder_distribution={}
        )
