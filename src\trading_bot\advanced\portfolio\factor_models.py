"""Multi-factor models for portfolio analysis and risk attribution."""

import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from sklearn.decomposition import PCA
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
import statsmodels.api as sm

from ...core.config import settings
from ...core.logger import get_logger

logger = get_logger(__name__)


class FactorType(Enum):
    """Factor model types."""
    FAMA_FRENCH_3 = "fama_french_3"
    FAMA_FRENCH_5 = "fama_french_5"
    CARHART_4 = "carhart_4"
    CUSTOM = "custom"
    PCA = "pca"


@dataclass
class Factor:
    """Factor data structure."""
    name: str
    description: str
    returns: pd.Series
    category: str  # 'market', 'size', 'value', 'momentum', etc.


@dataclass
class FactorExposure:
    """Factor exposure for an asset."""
    asset: str
    factor_loadings: Dict[str, float]
    alpha: float
    r_squared: float
    tracking_error: float
    specific_risk: float


@dataclass
class FactorAttribution:
    """Factor attribution analysis."""
    asset: str
    total_return: float
    factor_contributions: Dict[str, float]
    specific_return: float
    attribution_r_squared: float


@dataclass
class RiskAttribution:
    """Risk attribution analysis."""
    portfolio_risk: float
    factor_risk_contributions: Dict[str, float]
    specific_risk: float
    diversification_ratio: float


class FactorModelAnalyzer:
    """Multi-factor model analysis and risk attribution."""
    
    def __init__(self):
        self.factor_models = {}
        self.factor_data = {}
        
    async def build_factor_model(
        self,
        returns_data: pd.DataFrame,
        factor_type: FactorType = FactorType.FAMA_FRENCH_3,
        custom_factors: List[Factor] = None
    ) -> Dict[str, FactorExposure]:
        """
        Build factor model for assets.
        
        Args:
            returns_data: Asset returns data
            factor_type: Type of factor model
            custom_factors: Custom factors for analysis
            
        Returns:
            Dictionary of factor exposures by asset
        """
        try:
            # Get factor data
            if factor_type == FactorType.CUSTOM and custom_factors:
                factors_df = self._build_custom_factors_df(custom_factors)
            elif factor_type == FactorType.PCA:
                factors_df = self._build_pca_factors(returns_data)
            else:
                factors_df = await self._get_standard_factors(factor_type, returns_data.index)
            
            if factors_df.empty:
                logger.warning("No factor data available")
                return {}
            
            # Align data
            aligned_data = self._align_data(returns_data, factors_df)
            asset_returns = aligned_data['assets']
            factor_returns = aligned_data['factors']
            
            # Calculate factor exposures for each asset
            factor_exposures = {}
            
            for asset in asset_returns.columns:
                exposure = self._calculate_factor_exposure(
                    asset_returns[asset], factor_returns, asset
                )
                factor_exposures[asset] = exposure
            
            return factor_exposures
            
        except Exception as e:
            logger.error(f"Error building factor model: {e}")
            return {}
    
    async def analyze_factor_attribution(
        self,
        portfolio_weights: Dict[str, float],
        returns_data: pd.DataFrame,
        factor_exposures: Dict[str, FactorExposure],
        period_returns: pd.Series
    ) -> FactorAttribution:
        """
        Analyze factor attribution for portfolio returns.
        
        Args:
            portfolio_weights: Portfolio weights
            returns_data: Asset returns data
            factor_exposures: Factor exposures for assets
            period_returns: Period returns for attribution
            
        Returns:
            Factor attribution analysis
        """
        try:
            # Calculate portfolio factor loadings
            portfolio_loadings = {}
            
            for factor_name in next(iter(factor_exposures.values())).factor_loadings.keys():
                portfolio_loading = sum(
                    weight * factor_exposures[asset].factor_loadings.get(factor_name, 0)
                    for asset, weight in portfolio_weights.items()
                    if asset in factor_exposures
                )
                portfolio_loadings[factor_name] = portfolio_loading
            
            # Get factor returns for the period
            factor_returns = await self._get_factor_returns_for_period(period_returns.index)
            
            # Calculate factor contributions
            factor_contributions = {}
            total_factor_contribution = 0
            
            for factor_name, loading in portfolio_loadings.items():
                if factor_name in factor_returns.columns:
                    factor_return = factor_returns[factor_name].iloc[-1]  # Last period
                    contribution = loading * factor_return
                    factor_contributions[factor_name] = contribution
                    total_factor_contribution += contribution
            
            # Calculate portfolio return
            portfolio_return = sum(
                weight * period_returns.get(asset, 0)
                for asset, weight in portfolio_weights.items()
            )
            
            # Specific return (alpha)
            specific_return = portfolio_return - total_factor_contribution
            
            # Calculate R-squared
            explained_variance = sum(factor_contributions.values()) ** 2
            total_variance = portfolio_return ** 2
            attribution_r_squared = explained_variance / total_variance if total_variance > 0 else 0
            
            return FactorAttribution(
                asset="Portfolio",
                total_return=portfolio_return,
                factor_contributions=factor_contributions,
                specific_return=specific_return,
                attribution_r_squared=attribution_r_squared
            )
            
        except Exception as e:
            logger.error(f"Error analyzing factor attribution: {e}")
            return self._empty_attribution()
    
    async def analyze_risk_attribution(
        self,
        portfolio_weights: Dict[str, float],
        factor_exposures: Dict[str, FactorExposure],
        factor_covariance: pd.DataFrame
    ) -> RiskAttribution:
        """
        Analyze risk attribution using factor model.
        
        Args:
            portfolio_weights: Portfolio weights
            factor_exposures: Factor exposures for assets
            factor_covariance: Factor covariance matrix
            
        Returns:
            Risk attribution analysis
        """
        try:
            # Calculate portfolio factor loadings
            portfolio_loadings = {}
            
            for factor_name in factor_covariance.columns:
                portfolio_loading = sum(
                    weight * factor_exposures[asset].factor_loadings.get(factor_name, 0)
                    for asset, weight in portfolio_weights.items()
                    if asset in factor_exposures
                )
                portfolio_loadings[factor_name] = portfolio_loading
            
            # Convert to array for matrix operations
            loadings_array = np.array([portfolio_loadings.get(factor, 0) 
                                     for factor in factor_covariance.columns])
            
            # Calculate factor risk
            factor_variance = np.dot(loadings_array, np.dot(factor_covariance.values, loadings_array))
            
            # Calculate specific risk
            specific_variance = sum(
                (weight ** 2) * factor_exposures[asset].specific_risk ** 2
                for asset, weight in portfolio_weights.items()
                if asset in factor_exposures
            )
            
            # Total portfolio risk
            portfolio_variance = factor_variance + specific_variance
            portfolio_risk = np.sqrt(portfolio_variance)
            
            # Factor risk contributions
            factor_risk_contributions = {}
            
            for i, factor in enumerate(factor_covariance.columns):
                # Marginal contribution to risk
                marginal_contrib = 2 * loadings_array[i] * np.dot(
                    factor_covariance.iloc[i].values, loadings_array
                )
                contribution = marginal_contrib / (2 * portfolio_risk) if portfolio_risk > 0 else 0
                factor_risk_contributions[factor] = contribution
            
            # Diversification ratio
            individual_risks = [
                weight * np.sqrt(factor_exposures[asset].tracking_error ** 2 + 
                               factor_exposures[asset].specific_risk ** 2)
                for asset, weight in portfolio_weights.items()
                if asset in factor_exposures
            ]
            
            weighted_avg_risk = sum(individual_risks)
            diversification_ratio = weighted_avg_risk / portfolio_risk if portfolio_risk > 0 else 1
            
            return RiskAttribution(
                portfolio_risk=portfolio_risk,
                factor_risk_contributions=factor_risk_contributions,
                specific_risk=np.sqrt(specific_variance),
                diversification_ratio=diversification_ratio
            )
            
        except Exception as e:
            logger.error(f"Error analyzing risk attribution: {e}")
            return self._empty_risk_attribution()
    
    def _calculate_factor_exposure(
        self,
        asset_returns: pd.Series,
        factor_returns: pd.DataFrame,
        asset_name: str
    ) -> FactorExposure:
        """Calculate factor exposure for a single asset."""
        try:
            # Prepare data
            y = asset_returns.dropna()
            X = factor_returns.loc[y.index].dropna()
            
            # Align data
            common_index = y.index.intersection(X.index)
            y = y.loc[common_index]
            X = X.loc[common_index]
            
            if len(y) < 20:  # Minimum observations
                return self._empty_exposure(asset_name)
            
            # Add constant for alpha
            X_with_const = sm.add_constant(X)
            
            # Run regression
            model = sm.OLS(y, X_with_const).fit()
            
            # Extract results
            alpha = model.params['const']
            factor_loadings = {factor: model.params[factor] for factor in X.columns}
            r_squared = model.rsquared
            
            # Calculate tracking error and specific risk
            residuals = model.resid
            tracking_error = residuals.std() * np.sqrt(252)  # Annualized
            specific_risk = tracking_error  # Simplified
            
            return FactorExposure(
                asset=asset_name,
                factor_loadings=factor_loadings,
                alpha=alpha,
                r_squared=r_squared,
                tracking_error=tracking_error,
                specific_risk=specific_risk
            )
            
        except Exception as e:
            logger.error(f"Error calculating factor exposure for {asset_name}: {e}")
            return self._empty_exposure(asset_name)
    
    async def _get_standard_factors(
        self,
        factor_type: FactorType,
        date_range: pd.DatetimeIndex
    ) -> pd.DataFrame:
        """Get standard factor data (Fama-French, etc.)."""
        try:
            # This would typically fetch from data providers
            # For now, simulate factor data
            
            factors = {}
            
            if factor_type in [FactorType.FAMA_FRENCH_3, FactorType.FAMA_FRENCH_5, FactorType.CARHART_4]:
                # Market factor (Rm - Rf)
                factors['MKT'] = np.random.normal(0.08/252, 0.16/np.sqrt(252), len(date_range))
                
                # Size factor (SMB - Small Minus Big)
                factors['SMB'] = np.random.normal(0.02/252, 0.12/np.sqrt(252), len(date_range))
                
                # Value factor (HML - High Minus Low)
                factors['HML'] = np.random.normal(0.03/252, 0.14/np.sqrt(252), len(date_range))
                
                if factor_type == FactorType.CARHART_4:
                    # Momentum factor (UMD - Up Minus Down)
                    factors['UMD'] = np.random.normal(0.04/252, 0.18/np.sqrt(252), len(date_range))
                
                if factor_type == FactorType.FAMA_FRENCH_5:
                    # Profitability factor (RMW - Robust Minus Weak)
                    factors['RMW'] = np.random.normal(0.02/252, 0.10/np.sqrt(252), len(date_range))
                    
                    # Investment factor (CMA - Conservative Minus Aggressive)
                    factors['CMA'] = np.random.normal(0.01/252, 0.08/np.sqrt(252), len(date_range))
            
            return pd.DataFrame(factors, index=date_range)
            
        except Exception as e:
            logger.error(f"Error getting standard factors: {e}")
            return pd.DataFrame()
    
    def _build_custom_factors_df(self, custom_factors: List[Factor]) -> pd.DataFrame:
        """Build DataFrame from custom factors."""
        try:
            factor_data = {}
            for factor in custom_factors:
                factor_data[factor.name] = factor.returns
            
            return pd.DataFrame(factor_data)
            
        except Exception as e:
            logger.error(f"Error building custom factors DataFrame: {e}")
            return pd.DataFrame()
    
    def _build_pca_factors(
        self,
        returns_data: pd.DataFrame,
        n_components: int = 5
    ) -> pd.DataFrame:
        """Build PCA factors from returns data."""
        try:
            # Standardize returns
            scaler = StandardScaler()
            scaled_returns = scaler.fit_transform(returns_data.fillna(0))
            
            # Apply PCA
            pca = PCA(n_components=n_components)
            factor_scores = pca.fit_transform(scaled_returns)
            
            # Create factor DataFrame
            factor_names = [f'PC{i+1}' for i in range(n_components)]
            factors_df = pd.DataFrame(
                factor_scores,
                index=returns_data.index,
                columns=factor_names
            )
            
            return factors_df
            
        except Exception as e:
            logger.error(f"Error building PCA factors: {e}")
            return pd.DataFrame()
    
    def _align_data(
        self,
        returns_data: pd.DataFrame,
        factors_df: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """Align asset returns and factor data."""
        try:
            # Find common date range
            common_index = returns_data.index.intersection(factors_df.index)
            
            if len(common_index) == 0:
                logger.warning("No common dates between returns and factors")
                return {'assets': pd.DataFrame(), 'factors': pd.DataFrame()}
            
            aligned_returns = returns_data.loc[common_index]
            aligned_factors = factors_df.loc[common_index]
            
            return {
                'assets': aligned_returns,
                'factors': aligned_factors
            }
            
        except Exception as e:
            logger.error(f"Error aligning data: {e}")
            return {'assets': pd.DataFrame(), 'factors': pd.DataFrame()}
    
    async def _get_factor_returns_for_period(
        self,
        date_range: pd.DatetimeIndex
    ) -> pd.DataFrame:
        """Get factor returns for specific period."""
        # This would fetch actual factor returns
        # For now, return simulated data
        
        factors = {
            'MKT': np.random.normal(0.001, 0.02, len(date_range)),
            'SMB': np.random.normal(0.0005, 0.015, len(date_range)),
            'HML': np.random.normal(0.0003, 0.012, len(date_range))
        }
        
        return pd.DataFrame(factors, index=date_range)
    
    def calculate_factor_tilts(
        self,
        portfolio_weights: Dict[str, float],
        factor_exposures: Dict[str, FactorExposure],
        benchmark_weights: Dict[str, float] = None
    ) -> Dict[str, float]:
        """Calculate portfolio factor tilts vs benchmark."""
        try:
            if benchmark_weights is None:
                # Equal weight benchmark
                benchmark_weights = {asset: 1/len(portfolio_weights) 
                                   for asset in portfolio_weights.keys()}
            
            # Calculate portfolio factor loadings
            portfolio_loadings = {}
            benchmark_loadings = {}
            
            # Get all factor names
            all_factors = set()
            for exposure in factor_exposures.values():
                all_factors.update(exposure.factor_loadings.keys())
            
            for factor in all_factors:
                # Portfolio loading
                port_loading = sum(
                    weight * factor_exposures[asset].factor_loadings.get(factor, 0)
                    for asset, weight in portfolio_weights.items()
                    if asset in factor_exposures
                )
                portfolio_loadings[factor] = port_loading
                
                # Benchmark loading
                bench_loading = sum(
                    weight * factor_exposures[asset].factor_loadings.get(factor, 0)
                    for asset, weight in benchmark_weights.items()
                    if asset in factor_exposures
                )
                benchmark_loadings[factor] = bench_loading
            
            # Calculate tilts (portfolio - benchmark)
            factor_tilts = {
                factor: portfolio_loadings[factor] - benchmark_loadings.get(factor, 0)
                for factor in portfolio_loadings
            }
            
            return factor_tilts
            
        except Exception as e:
            logger.error(f"Error calculating factor tilts: {e}")
            return {}
    
    def _empty_exposure(self, asset_name: str) -> FactorExposure:
        """Return empty factor exposure."""
        return FactorExposure(
            asset=asset_name,
            factor_loadings={},
            alpha=0.0,
            r_squared=0.0,
            tracking_error=0.0,
            specific_risk=0.0
        )
    
    def _empty_attribution(self) -> FactorAttribution:
        """Return empty factor attribution."""
        return FactorAttribution(
            asset="",
            total_return=0.0,
            factor_contributions={},
            specific_return=0.0,
            attribution_r_squared=0.0
        )
    
    def _empty_risk_attribution(self) -> RiskAttribution:
        """Return empty risk attribution."""
        return RiskAttribution(
            portfolio_risk=0.0,
            factor_risk_contributions={},
            specific_risk=0.0,
            diversification_ratio=1.0
        )
