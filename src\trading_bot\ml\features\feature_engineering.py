"""Feature Engineering Pipeline - combines all feature types."""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
import logging
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.decomposition import PCA
import warnings

from .technical_indicators import TechnicalIndicators
from .market_microstructure import MarketMicrostructure
from .sentiment_features import SentimentFeatures

logger = logging.getLogger(__name__)


class FeatureEngineering:
    """Complete feature engineering pipeline."""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Feature calculators
        self.technical_indicators = TechnicalIndicators()
        self.microstructure = MarketMicrostructure()
        self.sentiment = SentimentFeatures()
        
        # Preprocessing components
        self.scaler = None
        self.feature_selector = None
        self.pca = None
        
        # Feature metadata
        self.feature_names = []
        self.feature_importance = {}
        self.feature_correlations = None
        
        logger.info("Initialized Feature Engineering pipeline")
    
    def engineer_features(self, 
                         price_data: pd.DataFrame,
                         news_data: Optional[pd.DataFrame] = None,
                         social_data: Optional[pd.DataFrame] = None,
                         tick_data: Optional[pd.DataFrame] = None,
                         order_book_data: Optional[pd.DataFrame] = None,
                         symbol: Optional[str] = None) -> pd.DataFrame:
        """Engineer all features from raw data."""
        logger.info("Starting feature engineering...")
        
        # Start with price data
        features = price_data.copy()
        
        # Add technical indicators
        logger.info("Calculating technical indicators...")
        features = self.technical_indicators.calculate_all_indicators(features)
        
        # Add microstructure features
        logger.info("Calculating microstructure features...")
        features = self.microstructure.calculate_microstructure_features(
            features, tick_data, order_book_data
        )
        
        # Add sentiment features
        logger.info("Calculating sentiment features...")
        features = self.sentiment.calculate_sentiment_features(
            features, news_data, social_data, symbol
        )
        
        # Add derived features
        features = self._add_derived_features(features)
        
        # Add interaction features
        features = self._add_interaction_features(features)
        
        # Add time-based features
        features = self._add_time_features(features)
        
        # Clean features
        features = self._clean_features(features)
        
        self.feature_names = list(features.columns)
        
        logger.info(f"Feature engineering completed. Generated {len(self.feature_names)} features.")
        
        return features
    
    def _add_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add derived features from existing ones."""
        # Price-based derived features
        if 'close' in df.columns:
            close = df['close']
            
            # Returns at multiple horizons
            for period in [1, 5, 10, 20, 60]:
                df[f'return_{period}d'] = close.pct_change(period)
                df[f'log_return_{period}d'] = np.log(close / close.shift(period))
            
            # Moving average ratios
            if 'sma_20' in df.columns and 'sma_50' in df.columns:
                df['ma_ratio_20_50'] = df['sma_20'] / df['sma_50']
            
            if 'ema_12' in df.columns and 'ema_26' in df.columns:
                df['ma_ratio_12_26'] = df['ema_12'] / df['ema_26']
            
            # Price relative to moving averages
            for ma_period in [10, 20, 50, 200]:
                ma_col = f'sma_{ma_period}'
                if ma_col in df.columns:
                    df[f'price_to_{ma_col}'] = close / df[ma_col]
        
        # Volume-based derived features
        if 'volume' in df.columns:
            volume = df['volume']
            
            # Volume ratios
            for period in [5, 10, 20]:
                vol_ma_col = f'vol_sma_{period}'
                if vol_ma_col in df.columns:
                    df[f'volume_ratio_{period}'] = volume / df[vol_ma_col]
            
            # Volume momentum
            df['volume_momentum_5'] = volume.pct_change(5)
            df['volume_momentum_10'] = volume.pct_change(10)
        
        # Volatility-based derived features
        if 'atr' in df.columns and 'close' in df.columns:
            df['atr_percent'] = df['atr'] / df['close']
        
        # RSI derived features
        if 'rsi_14' in df.columns:
            rsi = df['rsi_14']
            df['rsi_overbought'] = (rsi > 70).astype(int)
            df['rsi_oversold'] = (rsi < 30).astype(int)
            df['rsi_momentum'] = rsi.diff()
        
        # MACD derived features
        if 'macd' in df.columns and 'macd_signal' in df.columns:
            df['macd_above_signal'] = (df['macd'] > df['macd_signal']).astype(int)
            df['macd_momentum'] = df['macd'].diff()
        
        # Bollinger Bands derived features
        if all(col in df.columns for col in ['bb_upper_20', 'bb_lower_20', 'close']):
            bb_width = df['bb_upper_20'] - df['bb_lower_20']
            bb_middle = (df['bb_upper_20'] + df['bb_lower_20']) / 2
            df['bb_squeeze'] = (bb_width < bb_width.rolling(20).mean()).astype(int)
            df['bb_breakout_up'] = (df['close'] > df['bb_upper_20']).astype(int)
            df['bb_breakout_down'] = (df['close'] < df['bb_lower_20']).astype(int)
        
        return df
    
    def _add_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add interaction features between different indicators."""
        # RSI x Volume interaction
        if 'rsi_14' in df.columns and 'volume' in df.columns:
            vol_ma = df['volume'].rolling(20).mean()
            df['rsi_volume_interaction'] = df['rsi_14'] * (df['volume'] / vol_ma)
        
        # MACD x Price momentum interaction
        if 'macd' in df.columns and 'return_5d' in df.columns:
            df['macd_momentum_interaction'] = df['macd'] * df['return_5d']
        
        # Volatility x Volume interaction
        if 'atr' in df.columns and 'volume' in df.columns:
            df['volatility_volume_interaction'] = df['atr'] * df['volume']
        
        # Sentiment x Technical interaction
        if 'momentum_sentiment' in df.columns and 'rsi_14' in df.columns:
            df['sentiment_rsi_interaction'] = df['momentum_sentiment'] * df['rsi_14']
        
        # Support/Resistance x Volume
        if all(col in df.columns for col in ['dist_to_support', 'dist_to_resistance', 'volume']):
            vol_ratio = df['volume'] / df['volume'].rolling(20).mean()
            df['support_volume_interaction'] = df['dist_to_support'] * vol_ratio
            df['resistance_volume_interaction'] = df['dist_to_resistance'] * vol_ratio
        
        return df
    
    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features."""
        if df.index.dtype != 'datetime64[ns]':
            try:
                df.index = pd.to_datetime(df.index)
            except:
                logger.warning("Could not convert index to datetime")
                return df
        
        # Basic time features
        df['hour'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['day_of_month'] = df.index.day
        df['month'] = df.index.month
        df['quarter'] = df.index.quarter
        
        # Market session features
        df['is_market_open'] = ((df['hour'] >= 9) & (df['hour'] < 16)).astype(int)
        df['is_pre_market'] = ((df['hour'] >= 4) & (df['hour'] < 9)).astype(int)
        df['is_after_market'] = ((df['hour'] >= 16) & (df['hour'] < 20)).astype(int)
        
        # Cyclical encoding for time features
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Special days
        df['is_monday'] = (df['day_of_week'] == 0).astype(int)
        df['is_friday'] = (df['day_of_week'] == 4).astype(int)
        df['is_month_end'] = (df.index.day > 25).astype(int)
        df['is_quarter_end'] = ((df['month'].isin([3, 6, 9, 12])) & (df['is_month_end'])).astype(int)
        
        return df
    
    def _clean_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess features."""
        # Remove infinite values
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # Handle missing values
        # Forward fill first, then backward fill, then fill with 0
        df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        # Remove constant features
        constant_features = [col for col in df.columns if df[col].nunique() <= 1]
        if constant_features:
            logger.info(f"Removing {len(constant_features)} constant features")
            df = df.drop(columns=constant_features)
        
        # Remove highly correlated features
        df = self._remove_highly_correlated_features(df)
        
        return df
    
    def _remove_highly_correlated_features(self, df: pd.DataFrame, threshold: float = 0.95) -> pd.DataFrame:
        """Remove highly correlated features."""
        # Calculate correlation matrix
        corr_matrix = df.corr().abs()
        
        # Find highly correlated pairs
        upper_triangle = corr_matrix.where(
            np.triu(np.ones_like(corr_matrix, dtype=bool), k=1)
        )
        
        # Find features to drop
        features_to_drop = [
            column for column in upper_triangle.columns 
            if any(upper_triangle[column] > threshold)
        ]
        
        if features_to_drop:
            logger.info(f"Removing {len(features_to_drop)} highly correlated features")
            df = df.drop(columns=features_to_drop)
        
        # Store correlation matrix for analysis
        self.feature_correlations = df.corr()
        
        return df
    
    def scale_features(self, 
                      df: pd.DataFrame, 
                      method: str = 'standard',
                      fit: bool = True) -> pd.DataFrame:
        """Scale features using specified method."""
        if method == 'standard':
            scaler = StandardScaler()
        elif method == 'minmax':
            scaler = MinMaxScaler()
        elif method == 'robust':
            scaler = RobustScaler()
        else:
            raise ValueError(f"Unknown scaling method: {method}")
        
        if fit:
            self.scaler = scaler
            scaled_data = scaler.fit_transform(df)
        else:
            if self.scaler is None:
                raise ValueError("Scaler not fitted. Set fit=True first.")
            scaled_data = self.scaler.transform(df)
        
        return pd.DataFrame(scaled_data, columns=df.columns, index=df.index)
    
    def select_features(self, 
                       X: pd.DataFrame, 
                       y: pd.Series,
                       method: str = 'mutual_info',
                       k: int = 50,
                       fit: bool = True) -> pd.DataFrame:
        """Select top k features using specified method."""
        if method == 'f_regression':
            selector = SelectKBest(score_func=f_regression, k=k)
        elif method == 'mutual_info':
            selector = SelectKBest(score_func=mutual_info_regression, k=k)
        else:
            raise ValueError(f"Unknown feature selection method: {method}")
        
        if fit:
            self.feature_selector = selector
            selected_data = selector.fit_transform(X, y)
            
            # Get selected feature names
            selected_features = X.columns[selector.get_support()].tolist()
            
            # Store feature importance scores
            self.feature_importance = dict(zip(
                X.columns, 
                selector.scores_
            ))
            
        else:
            if self.feature_selector is None:
                raise ValueError("Feature selector not fitted. Set fit=True first.")
            selected_data = self.feature_selector.transform(X)
            selected_features = X.columns[self.feature_selector.get_support()].tolist()
        
        return pd.DataFrame(selected_data, columns=selected_features, index=X.index)
    
    def apply_pca(self, 
                  df: pd.DataFrame,
                  n_components: Union[int, float] = 0.95,
                  fit: bool = True) -> pd.DataFrame:
        """Apply PCA for dimensionality reduction."""
        if fit:
            self.pca = PCA(n_components=n_components)
            transformed_data = self.pca.fit_transform(df)
            
            logger.info(f"PCA reduced features from {df.shape[1]} to {transformed_data.shape[1]}")
            logger.info(f"Explained variance ratio: {self.pca.explained_variance_ratio_.sum():.3f}")
            
        else:
            if self.pca is None:
                raise ValueError("PCA not fitted. Set fit=True first.")
            transformed_data = self.pca.transform(df)
        
        # Create column names for PCA components
        columns = [f'PC_{i+1}' for i in range(transformed_data.shape[1])]
        
        return pd.DataFrame(transformed_data, columns=columns, index=df.index)
    
    def create_lag_features(self, 
                           df: pd.DataFrame, 
                           columns: List[str], 
                           lags: List[int]) -> pd.DataFrame:
        """Create lagged features."""
        lagged_df = df.copy()
        
        for col in columns:
            if col in df.columns:
                for lag in lags:
                    lagged_df[f'{col}_lag_{lag}'] = df[col].shift(lag)
        
        return lagged_df
    
    def create_rolling_features(self, 
                               df: pd.DataFrame,
                               columns: List[str],
                               windows: List[int],
                               operations: List[str] = ['mean', 'std', 'min', 'max']) -> pd.DataFrame:
        """Create rolling window features."""
        rolling_df = df.copy()
        
        for col in columns:
            if col in df.columns:
                for window in windows:
                    for op in operations:
                        if op == 'mean':
                            rolling_df[f'{col}_rolling_{window}_mean'] = df[col].rolling(window).mean()
                        elif op == 'std':
                            rolling_df[f'{col}_rolling_{window}_std'] = df[col].rolling(window).std()
                        elif op == 'min':
                            rolling_df[f'{col}_rolling_{window}_min'] = df[col].rolling(window).min()
                        elif op == 'max':
                            rolling_df[f'{col}_rolling_{window}_max'] = df[col].rolling(window).max()
                        elif op == 'skew':
                            rolling_df[f'{col}_rolling_{window}_skew'] = df[col].rolling(window).skew()
                        elif op == 'kurt':
                            rolling_df[f'{col}_rolling_{window}_kurt'] = df[col].rolling(window).kurt()
        
        return rolling_df
    
    def get_feature_importance(self, top_n: int = 20) -> pd.DataFrame:
        """Get top n most important features."""
        if not self.feature_importance:
            logger.warning("Feature importance not available. Run feature selection first.")
            return pd.DataFrame()
        
        importance_df = pd.DataFrame(
            list(self.feature_importance.items()),
            columns=['feature', 'importance']
        ).sort_values('importance', ascending=False)
        
        return importance_df.head(top_n)
    
    def get_feature_correlations(self, feature: str, top_n: int = 10) -> pd.DataFrame:
        """Get features most correlated with specified feature."""
        if self.feature_correlations is None:
            logger.warning("Feature correlations not available.")
            return pd.DataFrame()
        
        if feature not in self.feature_correlations.columns:
            logger.warning(f"Feature '{feature}' not found.")
            return pd.DataFrame()
        
        correlations = self.feature_correlations[feature].abs().sort_values(ascending=False)
        
        return correlations.head(top_n + 1)[1:]  # Exclude self-correlation
    
    def get_all_feature_names(self) -> List[str]:
        """Get all available feature names."""
        all_features = []
        
        # Technical indicators
        all_features.extend(self.technical_indicators.get_indicator_list())
        
        # Microstructure features
        all_features.extend(self.microstructure.get_microstructure_feature_list())
        
        # Sentiment features
        all_features.extend(self.sentiment.get_sentiment_feature_list())
        
        # Derived features (examples)
        derived_features = [
            'return_1d', 'return_5d', 'return_10d', 'return_20d', 'return_60d',
            'log_return_1d', 'log_return_5d', 'log_return_10d', 'log_return_20d', 'log_return_60d',
            'ma_ratio_20_50', 'ma_ratio_12_26',
            'price_to_sma_10', 'price_to_sma_20', 'price_to_sma_50', 'price_to_sma_200',
            'volume_ratio_5', 'volume_ratio_10', 'volume_ratio_20',
            'volume_momentum_5', 'volume_momentum_10',
            'atr_percent', 'rsi_overbought', 'rsi_oversold', 'rsi_momentum',
            'macd_above_signal', 'macd_momentum',
            'bb_squeeze', 'bb_breakout_up', 'bb_breakout_down'
        ]
        all_features.extend(derived_features)
        
        # Time features
        time_features = [
            'hour', 'day_of_week', 'day_of_month', 'month', 'quarter',
            'is_market_open', 'is_pre_market', 'is_after_market',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos',
            'is_monday', 'is_friday', 'is_month_end', 'is_quarter_end'
        ]
        all_features.extend(time_features)
        
        return all_features
    
    def save_preprocessing_components(self, filepath: str):
        """Save preprocessing components."""
        import joblib
        
        components = {
            'scaler': self.scaler,
            'feature_selector': self.feature_selector,
            'pca': self.pca,
            'feature_names': self.feature_names,
            'feature_importance': self.feature_importance,
            'feature_correlations': self.feature_correlations
        }
        
        joblib.dump(components, filepath)
        logger.info(f"Preprocessing components saved to {filepath}")
    
    def load_preprocessing_components(self, filepath: str):
        """Load preprocessing components."""
        import joblib
        
        components = joblib.load(filepath)
        
        self.scaler = components.get('scaler')
        self.feature_selector = components.get('feature_selector')
        self.pca = components.get('pca')
        self.feature_names = components.get('feature_names', [])
        self.feature_importance = components.get('feature_importance', {})
        self.feature_correlations = components.get('feature_correlations')
        
        logger.info(f"Preprocessing components loaded from {filepath}")