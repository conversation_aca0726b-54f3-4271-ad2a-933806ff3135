## ✅ Environment Setup (WSL Virtualenv)

- Created a Python virtual environment at `/mnt/c/Trading-bot/trading_bot_env`
- Installed all required packages: `yfinance`, `pandas`, `numpy`, `scikit-learn`, `xgboost`, etc.
- Aliases added to `~/.bashrc`:
  - `venvpy` → Python inside the venv
  - `venvpip` → Pip inside the venv
- Verified working with:
  - `venvpy --version` → Python 3.12.3
  - `venvpip list | grep yfinance` → yfinance 0.2.65
- Created `run.sh` script to activate and run `integrated_volatility_trader.py`

<PERSON> can now safely use `venvpy` or `./run.sh` to execute scripts in the correct environment.


# 🔧 Project Environment Setup (For Claude)

### 1. Location
This project is located in:

```bash
~/projects/trading-bot
Always use this path when operating within WSL.

2. Git Sync
Before running anything, always pull the latest files:

bash
Copy
Edit
git pull origin main
3. Python Virtual Environment
This project already has a virtual environment created. Do NOT create a new one.

To activate it in WSL:

bash
Copy
Edit
cd ~/projects/trading-bot
source trading_bot_env/bin/activate
Once activated, you can run Python scripts like this:

bash
Copy
Edit
python run_emergency_dashboard.py
Or if aliases are needed:

bash
Copy
Edit
venvpy run_emergency_dashboard.py
4. VS Code Access
To open the project in VS Code under WSL:

bash
Copy
Edit
code ~/projects/trading-bot
✅ What Claude Should Do:
✅ Assume virtual environment already exists

✅ Use source trading_bot_env/bin/activate before running any scripts

✅ Skip creating or reinitializing venv

✅ Use CLAUDE.md for context and pick up where it left off

Volatility Hunter Strategy - Simplified Version
Scans for stocks that move 5-10% daily for quick day trading profits
"""


import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import yfinance as yf

# Simple logger setup
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class VolatilityOpportunity:
    """Represents a high-volatility trading opportunity"""
    symbol: str
    strategy_type: str  # 'gap_fill', 'oversold_bounce', 'breakout_pullback'
    entry_price: float
    target_price: float
    stop_loss: float
    daily_volatility: float
    volume_ratio: float
    confidence: float
    kelly_fraction: float
    position_size: int
    expected_return: float
    risk_reward_ratio: float
    signals: Dict[str, float]
    timestamp: datetime


class VolatilityHunter:
    """
    High-Probability Volatility Trading Strategy
    Focus: Stocks that move 5-10% daily with predictable patterns
    """
    
    def __init__(self):
        # Target stocks for high volatility
        self.volatility_watchlist = [
            # Leveraged ETFs
            'TQQQ', 'SQQQ', 'UVXY', 'SOXL', 'SOXS',
            'LABU', 'LABD', 'TNA', 'TZA', 'UPRO', 'SPXU',
            # Meme stocks
            'GME', 'AMC', 'BB', 'NOK',
            # Penny stocks / High volatility
            'MULN', 'FFIE', 'NKLA', 'RIDE', 'WKHS',
            # Crypto-related
            'RIOT', 'MARA', 'COIN', 'MSTR',
            # Biotech volatility
            'MRNA', 'BNTX', 'NVAX', 'INO'
        ]
        
        # Strategy parameters
        self.min_daily_range = 0.05  # 5% minimum daily movement
        self.max_daily_range = 0.15  # 15% maximum (avoid pump and dumps)
        self.min_volume = 1_000_000  # Minimum daily volume
        self.gap_fill_probability = 0.80  # Historical 80% win rate
        
    def scan_for_opportunities(self, 
                             additional_symbols: List[str] = None,
                             portfolio_value: float = 10000) -> List[VolatilityOpportunity]:
        """
        Scan for high-volatility trading opportunities across all strategies
        """
        opportunities = []
        
        # Combine watchlist with any additional symbols
        symbols = list(set(self.volatility_watchlist + (additional_symbols or [])))
        
        logger.info(f"🔍 Scanning {len(symbols)} symbols for volatility opportunities...")
        
        for symbol in symbols:
            try:
                # Get data from Yahoo Finance
                ticker = yf.Ticker(symbol)
                
                # Get historical data
                hist = ticker.history(period="1mo", interval="1d")
                
                if hist.empty or len(hist) < 20:
                    continue
                
                # Get current price
                info = ticker.info
                current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
                
                if current_price == 0:
                    # Try to get from history
                    current_price = hist['Close'].iloc[-1]
                
                # Calculate technical indicators
                df = self._calculate_indicators(hist)
                
                # Check each strategy
                # 1. Gap Fill Strategy
                gap_opp = self._check_gap_fill(symbol, current_price, df, portfolio_value)
                if gap_opp:
                    opportunities.append(gap_opp)
                
                # 2. Oversold Bounce Strategy
                oversold_opp = self._check_oversold_bounce(symbol, current_price, df, portfolio_value)
                if oversold_opp:
                    opportunities.append(oversold_opp)
                
                # 3. Breakout Pullback Strategy
                breakout_opp = self._check_breakout_pullback(symbol, current_price, df, portfolio_value)
                if breakout_opp:
                    opportunities.append(breakout_opp)
                    
            except Exception as e:
                logger.error(f"Error scanning {symbol}: {e}")
                continue
        
        # Sort by expected return
        opportunities.sort(key=lambda x: x.expected_return, reverse=True)
        
        logger.info(f"✅ Found {len(opportunities)} volatility opportunities")
        return opportunities[:10]  # Return top 10 opportunities
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for analysis"""
        # Price movement
        df['daily_range'] = (df['High'] - df['Low']) / df['Open']
        df['daily_return'] = df['Close'].pct_change()
        df['gap'] = df['Open'] / df['Close'].shift(1) - 1
        
        # Volume
        df['volume_sma'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma']
        
        # RSI
        df['rsi'] = self._calculate_rsi(df['Close'], 14)
        
        # Bollinger Bands
        df['bb_middle'] = df['Close'].rolling(20).mean()
        df['bb_std'] = df['Close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['Close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # ATR for volatility
        df['atr'] = self._calculate_atr(df, 14)
        df['atr_ratio'] = df['atr'] / df['Close']
        
        # Support/Resistance levels
        df['resistance'] = df['High'].rolling(20).max()
        df['support'] = df['Low'].rolling(20).min()
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        high_low = df['High'] - df['Low']
        high_close = abs(df['High'] - df['Close'].shift())
        low_close = abs(df['Low'] - df['Close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(period).mean()
    
    def _calculate_kelly_position_size(self, symbol: str, entry_price: float, 
                                     stop_loss: float, win_rate: float,
                                     avg_win: float, avg_loss: float,
                                     portfolio_value: float,
                                     safety_factor: float = 0.25) -> int:
        """Calculate position size using Kelly Criterion"""
        if avg_loss <= 0 or win_rate <= 0 or win_rate >= 1:
            return 100  # Default position size
        
        # Kelly formula
        kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
        kelly_fraction = max(0, min(kelly_fraction * safety_factor, 0.25))
        
        # Position size
        kelly_amount = portfolio_value * kelly_fraction
        position_size = int(kelly_amount / entry_price)
        
        return max(position_size, 1)
    
    def _check_gap_fill(self, symbol: str, current_price: float, df: pd.DataFrame, 
                       portfolio_value: float) -> Optional[VolatilityOpportunity]:
        """Gap Fill Strategy - 80% win rate"""
        try:
            latest = df.iloc[-1]
            
            # Check for significant gap
            gap_size = abs(latest['gap'])
            if gap_size < 0.02 or gap_size > 0.10:  # 2-10% gaps
                return None
            
            # Ensure good volume
            if latest['volume_ratio'] < 1.5:
                return None
            
            # Determine direction
            is_gap_up = latest['gap'] > 0
            prev_close = df.iloc[-2]['Close']
            
            # Entry at current price, target at gap fill
            entry_price = current_price
            target_price = prev_close
            
            # Stop loss beyond the gap extreme
            if is_gap_up:
                stop_loss = latest['High'] * 1.01
                if entry_price >= target_price:  # Gap already filled
                    return None
            else:
                stop_loss = latest['Low'] * 0.99
                if entry_price <= target_price:  # Gap already filled
                    return None
            
            # Calculate position size
            win_rate = self.gap_fill_probability
            avg_win = abs(target_price - entry_price) / entry_price
            avg_loss = abs(stop_loss - entry_price) / entry_price
            
            position_size = self._calculate_kelly_position_size(
                symbol, entry_price, stop_loss, win_rate, 
                avg_win, avg_loss, portfolio_value
            )
            
            # Calculate expected return
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss if avg_loss > 0 else 0
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='gap_fill',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=win_rate,
                kelly_fraction=position_size * entry_price / portfolio_value,
                position_size=position_size,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals={
                    'gap_size': gap_size,
                    'volume_surge': latest['volume_ratio'],
                    'atr_ratio': latest['atr_ratio']
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error in gap fill check for {symbol}: {e}")
            return None
    
    def _check_oversold_bounce(self, symbol: str, current_price: float, df: pd.DataFrame,
                              portfolio_value: float) -> Optional[VolatilityOpportunity]:
        """Oversold Bounce Strategy"""
        try:
            latest = df.iloc[-1]
            
            # Check if oversold
            if latest['rsi'] > 35:  # Not oversold enough
                return None
            
            # Check for high volatility
            if latest['daily_range'] < self.min_daily_range:
                return None
            
            # Ensure good volume
            if latest['volume_ratio'] < 2.0:
                return None
            
            # Entry at current price
            entry_price = current_price
            
            # Target at 20-period SMA
            target_price = latest['bb_middle']
            
            # Stop loss below recent low
            stop_loss = latest['support'] * 0.98
            
            # Win rate based on RSI
            win_rate = 0.65 + (30 - latest['rsi']) * 0.01
            win_rate = min(win_rate, 0.75)
            
            avg_win = (target_price - entry_price) / entry_price
            avg_loss = (entry_price - stop_loss) / entry_price
            
            if avg_win < avg_loss * 1.5:
                return None
            
            position_size = self._calculate_kelly_position_size(
                symbol, entry_price, stop_loss, win_rate,
                avg_win, avg_loss, portfolio_value, 0.20
            )
            
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss if avg_loss > 0 else 0
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='oversold_bounce',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=win_rate,
                kelly_fraction=position_size * entry_price / portfolio_value,
                position_size=position_size,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals={
                    'rsi': latest['rsi'],
                    'bb_position': latest['bb_position'],
                    'volume_surge': latest['volume_ratio']
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error in oversold bounce check for {symbol}: {e}")
            return None
    
    def _check_breakout_pullback(self, symbol: str, current_price: float, df: pd.DataFrame,
                                portfolio_value: float) -> Optional[VolatilityOpportunity]:
        """Breakout Pullback Strategy"""
        try:
            latest = df.iloc[-1]
            
            # Check for recent breakout
            recent_high = df['High'][-5:-2].max()
            breakout_level = df['resistance'][-10:-3].mean()
            
            if recent_high < breakout_level * 1.02:
                return None
            
            # Check for pullback
            pullback_distance = abs(current_price - breakout_level) / breakout_level
            if pullback_distance > 0.03:
                return None
            
            if current_price < breakout_level:
                return None
            
            # Volume confirmation
            if latest['volume_ratio'] < 1.2:
                return None
            
            # Entry at current price
            entry_price = current_price
            
            # Target at recent high
            target_price = recent_high * 1.05
            
            # Stop loss below breakout
            stop_loss = breakout_level * 0.98
            
            # Win rate calculation
            momentum_score = (latest['rsi'] - 50) / 50
            win_rate = 0.60 + momentum_score * 0.15
            win_rate = min(max(win_rate, 0.55), 0.75)
            
            avg_win = (target_price - entry_price) / entry_price
            avg_loss = (entry_price - stop_loss) / entry_price
            
            if avg_win < avg_loss * 2:
                return None
            
            position_size = self._calculate_kelly_position_size(
                symbol, entry_price, stop_loss, win_rate,
                avg_win, avg_loss, portfolio_value
            )
            
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss if avg_loss > 0 else 0
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='breakout_pullback',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=win_rate,
                kelly_fraction=position_size * entry_price / portfolio_value,
                position_size=position_size,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals={
                    'rsi': latest['rsi'],
                    'breakout_distance': pullback_distance,
                    'momentum_score': momentum_score
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error in breakout pullback check for {symbol}: {e}")
            return None
    
    def get_daily_summary(self, opportunities: List[VolatilityOpportunity]) -> str:
        """Generate a summary of daily opportunities"""
        if not opportunities:
            return "No volatility opportunities found today."
        
        summary = f"\n📊 VOLATILITY HUNTER DAILY SUMMARY - {datetime.now().strftime('%Y-%m-%d')}\n"
        summary += "=" * 60 + "\n"
        
        # Group by strategy
        gap_fills = [o for o in opportunities if o.strategy_type == 'gap_fill']
        oversold = [o for o in opportunities if o.strategy_type == 'oversold_bounce']
        breakouts = [o for o in opportunities if o.strategy_type == 'breakout_pullback']
        
        summary += f"\n🎯 Total Opportunities: {len(opportunities)}\n"
        summary += f"   - Gap Fills: {len(gap_fills)}\n"
        summary += f"   - Oversold Bounces: {len(oversold)}\n"
        summary += f"   - Breakout Pullbacks: {len(breakouts)}\n"
        
        summary += "\n📈 TOP 5 OPPORTUNITIES:\n"
        for i, opp in enumerate(opportunities[:5], 1):
            summary += f"\n{i}. {opp.symbol} - {opp.strategy_type.upper()}\n"
            summary += f"   Expected Return: {opp.expected_return*100:.1f}%\n"
            summary += f"   Risk/Reward: {opp.risk_reward_ratio:.2f}\n"
            summary += f"   Confidence: {opp.confidence*100:.0f}%\n"
            summary += f"   Entry: ${opp.entry_price:.2f} | Target: ${opp.target_price:.2f}\n"
        
        return summary

Engineering Instructions for ML Paper Trading Implementation
Environment Context

Working Directory: ~/projects/trading-bot
Virtual Environment: trading_bot_env (already activated)
Python Command: python (within venv)
Dependencies: Already installed (yfinance, pandas, numpy, scikit-learn, xgboost confirmed)

Phase 1: Validate Existing Infrastructure (15 mins)
bash# 1. Test existing volatility hunter
cd ~/projects/trading-bot
python -m src.trading_bot.strategies.volatility_hunter

# 2. Verify Yahoo data source is working
python -c "from src.trading_bot.data.yahoo_data_source import YahooDataSource; print('✓ Yahoo data source imports successfully')"

# 3. Check project structure
ls -la src/trading_bot/{strategies,ml,data,risk}/
Phase 2: Create ML Training Pipeline (1 hour)
2.1 Create Feature Engineering Module
bash# Create if doesn't exist
mkdir -p src/trading_bot/ml/features
touch src/trading_bot/ml/features/__init__.py
Create src/trading_bot/ml/features/volatility_features.py:
python"""
Feature engineering specifically for volatility trading
Optimized for gap fills, oversold bounces, and breakout patterns
"""
import pandas as pd
import numpy as np
from typing import Dict, List

class VolatilityFeatureEngineer:
    def __init__(self):
        self.feature_names = []
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create ML features from OHLCV data"""
        features = pd.DataFrame(index=df.index)
        
        # Price-based features
        features['returns_1d'] = df['Close'].pct_change()
        features['returns_5d'] = df['Close'].pct_change(5)
        features['gap'] = (df['Open'] - df['Close'].shift(1)) / df['Close'].shift(1)
        features['gap_abs'] = features['gap'].abs()
        features['intraday_range'] = (df['High'] - df['Low']) / df['Open']
        
        # Volume features
        features['volume_ratio'] = df['Volume'] / df['Volume'].rolling(20).mean()
        features['volume_spike'] = (features['volume_ratio'] > 2).astype(int)
        
        # Technical indicators
        features['rsi'] = self._calculate_rsi(df['Close'])
        features['rsi_oversold'] = (features['rsi'] < 30).astype(int)
        features['bb_position'] = self._bollinger_position(df['Close'])
        
        # Volatility features
        features['atr_ratio'] = self._calculate_atr(df) / df['Close']
        features['volatility_20d'] = df['Close'].pct_change().rolling(20).std()
        
        # Pattern detection
        features['gap_fill_setup'] = ((features['gap_abs'] > 0.02) & 
                                     (features['gap_abs'] < 0.10) & 
                                     (features['volume_ratio'] > 1.5)).astype(int)
        
        features['oversold_setup'] = ((features['rsi'] < 35) & 
                                     (features['volume_ratio'] > 2.0) & 
                                     (features['intraday_range'] > 0.05)).astype(int)
        
        # Target variable (next day return)
        features['target'] = df['Close'].shift(-1) / df['Close'] - 1
        
        return features.dropna()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _bollinger_position(self, prices: pd.Series, period: int = 20) -> pd.Series:
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper = sma + 2 * std
        lower = sma - 2 * std
        return (prices - lower) / (upper - lower)
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        high_low = df['High'] - df['Low']
        high_close = abs(df['High'] - df['Close'].shift())
        low_close = abs(df['Low'] - df['Close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(period).mean()
2.2 Create ML Training Script
Create src/trading_bot/ml/train_volatility_model.py:
python"""
ML Training Pipeline for Volatility Trading
Trains models to predict profitable volatility setups
"""
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix
import xgboost as xgb
import joblib
import os

from ..data.yahoo_data_source import YahooDataSource
from .features.volatility_features import VolatilityFeatureEngineer

class VolatilityModelTrainer:
    def __init__(self):
        self.data_source = YahooDataSource()
        self.feature_engineer = VolatilityFeatureEngineer()
        self.models = {}
        self.model_dir = "models/volatility"
        os.makedirs(self.model_dir, exist_ok=True)
        
    async def train_models(self, symbols: List[str], lookback_days: int = 500):
        """Train ML models on volatility patterns"""
        print(f"🚀 Training volatility models on {len(symbols)} symbols...")
        
        # Collect training data
        all_features = []
        
        for symbol in symbols:
            try:
                # Get historical data
                ticker = yf.Ticker(symbol)
                df = ticker.history(period=f"{lookback_days}d", interval="1d")
                
                if len(df) < 100:
                    continue
                
                # Engineer features
                features = self.feature_engineer.create_features(df)
                features['symbol'] = symbol
                all_features.append(features)
                
            except Exception as e:
                print(f"Error processing {symbol}: {e}")
                continue
        
        # Combine all data
        combined_data = pd.concat(all_features, ignore_index=True)
        print(f"📊 Total training samples: {len(combined_data)}")
        
        # Prepare features and targets
        feature_cols = [col for col in combined_data.columns 
                       if col not in ['target', 'symbol']]
        
        X = combined_data[feature_cols]
        # Create classification target (1 if profitable, 0 otherwise)
        y = (combined_data['target'] > 0.02).astype(int)  # 2% profit threshold
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train models
        self._train_random_forest(X_train, y_train, X_test, y_test)
        self._train_xgboost(X_train, y_train, X_test, y_test)
        self._train_ensemble(X_train, y_train, X_test, y_test)
        
        # Save feature names
        joblib.dump(feature_cols, f"{self.model_dir}/feature_names.pkl")
        
        print("✅ Model training complete!")
        return self.models
    
    def _train_random_forest(self, X_train, y_train, X_test, y_test):
        """Train Random Forest model"""
        print("\n🌲 Training Random Forest...")
        
        rf = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=20,
            random_state=42,
            n_jobs=-1
        )
        
        rf.fit(X_train, y_train)
        
        # Evaluate
        train_score = rf.score(X_train, y_train)
        test_score = rf.score(X_test, y_test)
        
        print(f"Train accuracy: {train_score:.3f}")
        print(f"Test accuracy: {test_score:.3f}")
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': X_train.columns,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\nTop 10 features:")
        print(feature_importance.head(10))
        
        # Save model
        joblib.dump(rf, f"{self.model_dir}/random_forest.pkl")
        self.models['random_forest'] = rf
    
    def _train_xgboost(self, X_train, y_train, X_test, y_test):
        """Train XGBoost model"""
        print("\n🚀 Training XGBoost...")
        
        xgb_model = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            objective='binary:logistic',
            random_state=42
        )
        
        xgb_model.fit(
            X_train, y_train,
            eval_set=[(X_test, y_test)],
            early_stopping_rounds=10,
            verbose=False
        )
        
        # Evaluate
        train_score = xgb_model.score(X_train, y_train)
        test_score = xgb_model.score(X_test, y_test)
        
        print(f"Train accuracy: {train_score:.3f}")
        print(f"Test accuracy: {test_score:.3f}")
        
        # Save model
        joblib.dump(xgb_model, f"{self.model_dir}/xgboost.pkl")
        self.models['xgboost'] = xgb_model
    
    def _train_ensemble(self, X_train, y_train, X_test, y_test):
        """Create ensemble predictions"""
        print("\n🎯 Creating ensemble model...")
        
        # Get predictions from both models
        rf_pred = self.models['random_forest'].predict_proba(X_test)[:, 1]
        xgb_pred = self.models['xgboost'].predict_proba(X_test)[:, 1]
        
        # Weighted average
        ensemble_pred = 0.6 * rf_pred + 0.4 * xgb_pred
        ensemble_binary = (ensemble_pred > 0.5).astype(int)
        
        # Evaluate ensemble
        from sklearn.metrics import accuracy_score, precision_score, recall_score
        
        accuracy = accuracy_score(y_test, ensemble_binary)
        precision = precision_score(y_test, ensemble_binary)
        recall = recall_score(y_test, ensemble_binary)
        
        print(f"Ensemble accuracy: {accuracy:.3f}")
        print(f"Ensemble precision: {precision:.3f}")
        print(f"Ensemble recall: {recall:.3f}")
        
        # Save ensemble weights
        ensemble_config = {
            'weights': {'random_forest': 0.6, 'xgboost': 0.4},
            'threshold': 0.5,
            'performance': {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall
            }
        }
        
        joblib.dump(ensemble_config, f"{self.model_dir}/ensemble_config.pkl")

# Async wrapper for training
async def train_volatility_models():
    trainer = VolatilityModelTrainer()
    
    # High volatility symbols for training
    symbols = [
        'TQQQ', 'SQQQ', 'UVXY', 'SOXL', 'SOXS',
        'GME', 'AMC', 'TSLA', 'NVDA', 'RIOT',
        'MARA', 'COIN', 'MRNA', 'BNTX'
    ]
    
    await trainer.train_models(symbols)
Phase 3: Create Paper Trading System (1 hour)
Create src/trading_bot/paper_trading/paper_trader.py:
python"""
Paper Trading System for Testing Strategies
Simulates real trading with virtual money
"""
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional
import json
import os
from dataclasses import dataclass, asdict
import asyncio

from ..strategies.volatility_hunter import VolatilityHunter, VolatilityOpportunity
from ..data.yahoo_data_source import YahooDataSource

@dataclass
class PaperTrade:
    """Represents a paper trade"""
    trade_id: str
    symbol: str
    strategy: str
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: int
    entry_time: datetime
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    status: str = "OPEN"  # OPEN, CLOSED, STOPPED
    pnl: float = 0.0
    pnl_percent: float = 0.0

class PaperTradingEngine:
    def __init__(self, starting_capital: float = 10000):
        self.capital = starting_capital
        self.starting_capital = starting_capital
        self.positions: Dict[str, PaperTrade] = {}
        self.closed_trades: List[PaperTrade] = []
        self.trade_log_file = "paper_trades.json"
        self.volatility_hunter = VolatilityHunter()
        self.data_source = YahooDataSource()
        
        # Load existing trades if any
        self._load_trades()
    
    async def run_paper_trading(self):
        """Main paper trading loop"""
        print(f"🚀 Starting Paper Trading with ${self.capital:,.2f}")
        
        while True:
            try:
                # 1. Update existing positions
                await self._update_positions()
                
                # 2. Find new opportunities
                opportunities = self.volatility_hunter.scan_for_opportunities(
                    portfolio_value=self.capital
                )
                
                # 3. Execute new trades
                for opp in opportunities[:3]:  # Take top 3 opportunities
                    if self._can_trade(opp):
                        await self._execute_trade(opp)
                
                # 4. Display status
                self._display_status()
                
                # 5. Save state
                self._save_trades()
                
                # Wait 5 minutes before next scan
                await asyncio.sleep(300)
                
            except KeyboardInterrupt:
                print("\n📊 Paper trading stopped by user")
                break
            except Exception as e:
                print(f"Error in paper trading loop: {e}")
                await asyncio.sleep(60)
    
    async def _update_positions(self):
        """Update all open positions with current prices"""
        for symbol, trade in list(self.positions.items()):
            try:
                # Get current price
                quote = await self.data_source.get_realtime_quote(symbol)
                current_price = quote.price
                
                # Check stop loss
                if current_price <= trade.stop_loss:
                    await self._close_position(trade, current_price, "STOPPED")
                
                # Check target
                elif current_price >= trade.target_price:
                    await self._close_position(trade, current_price, "TARGET")
                
                # Update unrealized P&L
                else:
                    trade.pnl = (current_price - trade.entry_price) * trade.position_size
                    trade.pnl_percent = ((current_price / trade.entry_price) - 1) * 100
                    
            except Exception as e:
                print(f"Error updating position {symbol}: {e}")
    
    def _can_trade(self, opportunity: VolatilityOpportunity) -> bool:
        """Check if we can take this trade"""
        # Check if already in position
        if opportunity.symbol in self.positions:
            return False
        
        # Check capital requirements
        required_capital = opportunity.entry_price * opportunity.position_size
        if required_capital > self.capital * 0.25:  # Max 25% per position
            return False
        
        # Check max positions
        if len(self.positions) >= 5:  # Max 5 concurrent positions
            return False
        
        return True
    
    async def _execute_trade(self, opportunity: VolatilityOpportunity):
        """Execute a paper trade"""
        trade = PaperTrade(
            trade_id=f"{opportunity.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            symbol=opportunity.symbol,
            strategy=opportunity.strategy_type,
            entry_price=opportunity.entry_price,
            target_price=opportunity.target_price,
            stop_loss=opportunity.stop_loss,
            position_size=opportunity.position_size,
            entry_time=datetime.now()
        )
        
        # Add to positions
        self.positions[opportunity.symbol] = trade
        
        # Update capital
        self.capital -= trade.entry_price * trade.position_size
        
        print(f"\n✅ PAPER TRADE EXECUTED:")
        print(f"   Symbol: {trade.symbol}")
        print(f"   Strategy: {trade.strategy}")
        print(f"   Entry: ${trade.entry_price:.2f}")
        print(f"   Target: ${trade.target_price:.2f}")
        print(f"   Stop: ${trade.stop_loss:.2f}")
        print(f"   Size: {trade.position_size} shares")
    
    async def _close_position(self, trade: PaperTrade, exit_price: float, reason: str):
        """Close a paper position"""
        trade.exit_time = datetime.now()
        trade.exit_price = exit_price
        trade.status = reason
        trade.pnl = (exit_price - trade.entry_price) * trade.position_size
        trade.pnl_percent = ((exit_price / trade.entry_price) - 1) * 100
        
        # Update capital
        self.capital += exit_price * trade.position_size
        
        # Move to closed trades
        self.closed_trades.append(trade)
        del self.positions[trade.symbol]
        
        print(f"\n💰 POSITION CLOSED:")
        print(f"   Symbol: {trade.symbol}")
        print(f"   Reason: {reason}")
        print(f"   P&L: ${trade.pnl:.2f} ({trade.pnl_percent:.1f}%)")
    
    def _display_status(self):
        """Display current trading status"""
        total_value = self.capital + sum(
            t.entry_price * t.position_size + t.pnl 
            for t in self.positions.values()
        )
        
        print(f"\n📊 PAPER TRADING STATUS")
        print(f"{'='*50}")
        print(f"Capital: ${self.capital:,.2f}")
        print(f"Positions: {len(self.positions)}")
        print(f"Total Value: ${total_value:,.2f}")
        print(f"Total Return: {((total_value/self.starting_capital)-1)*100:.1f}%")
        
        if self.closed_trades:
            wins = sum(1 for t in self.closed_trades if t.pnl > 0)
            total_trades = len(self.closed_trades)
            win_rate = (wins / total_trades) * 100 if total_trades > 0 else 0
            
            print(f"\nClosed Trades: {total_trades}")
            print(f"Win Rate: {win_rate:.1f}%")
            print(f"Total P&L: ${sum(t.pnl for t in self.closed_trades):.2f}")
    
    def _save_trades(self):
        """Save trades to file"""
        data = {
            'capital': self.capital,
            'starting_capital': self.starting_capital,
            'positions': [asdict(t) for t in self.positions.values()],
            'closed_trades': [asdict(t) for t in self.closed_trades]
        }
        
        with open(self.trade_log_file, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    
    def _load_trades(self):
        """Load trades from file"""
        if os.path.exists(self.trade_log_file):
            try:
                with open(self.trade_log_file, 'r') as f:
                    data = json.load(f)
                    self.capital = data.get('capital', self.starting_capital)
                    # Load closed trades history
                    # (Skip loading open positions as they need fresh prices)
            except Exception as e:
                print(f"Error loading trades: {e}")

# Main execution
async def run_paper_trading():
    """Run the paper trading system"""
    engine = PaperTradingEngine(starting_capital=10000)
    await engine.run_paper_trading()
Phase 4: Integration Script (30 mins)
Create run_ml_paper_trading.py in the project root:
python"""
Main script to run ML-enhanced paper trading
Combines volatility hunting with ML predictions
"""
import asyncio
import sys
import os

# Add project to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.trading_bot.ml.train_volatility_model import train_volatility_models
from src.trading_bot.paper_trading.paper_trader import run_paper_trading

async def main():
    print("🚀 ML-Enhanced Paper Trading System")
    print("="*50)
    
    # Step 1: Train ML models (if needed)
    if not os.path.exists("models/volatility/random_forest.pkl"):
        print("\n📚 Training ML models...")
        await train_volatility_models()
    else:
        print("\n✅ ML models already trained")
    
    # Step 2: Start paper trading
    print("\n💰 Starting paper trading...")
    await run_paper_trading()

if __name__ == "__main__":
    asyncio.run(main())
Phase 5: Execution Commands (5 mins)
bash# 1. Ensure you're in the right directory with venv activated
cd ~/projects/trading-bot
source trading_bot_env/bin/activate

# 2. Create necessary directories
mkdir -p models/volatility
mkdir -p logs
mkdir -p src/trading_bot/{ml/features,paper_trading}

# 3. Run the integrated system
python run_ml_paper_trading.py

# 4. Monitor paper trades (in another terminal)
tail -f paper_trades.json | jq '.'

# 5. View trading performance
python -c "import json; data=json.load(open('paper_trades.json')); print(f\"Win Rate: {sum(1 for t in data['closed_trades'] if t['pnl']>0)/len(data['closed_trades'])*100:.1f}%\" if data['closed_trades'] else 'No closed trades yet')"
Key Performance Indicators to Track

Win Rate: Target 60%+
Risk/Reward: Minimum 1.5:1
Daily Return: Target 2-3%
Max Drawdown: Keep under 10%
Sharpe Ratio: Target > 1.5

Next Steps After Initial Success

Add ML predictions to trade selection
Implement portfolio-level risk management
Create real-time dashboard
Add more sophisticated ML features
Backtest over longer timeframes

Critical Success Factors

Start with small position sizes
Focus on high-probability setups only
Let winners run, cut losses quickly
Track and analyze every trade
Continuously improve based on results

Execute these phases in order. The system will start paper trading immediately while collecting data for ML improvements. Report any errors or successful trades.




# Main execution
def main():
    """Run the volatility hunter"""
    hunter = VolatilityHunter()
    
    # Scan for opportunities
    opportunities = hunter.scan_for_opportunities(portfolio_value=10000)
    
    # Print summary
    print(hunter.get_daily_summary(opportunities))
    
    # Show first opportunity in detail
    if opportunities:
        opp = opportunities[0]
        print(f"\n🎯 TOP OPPORTUNITY DETAILS:")
        print(f"Symbol: {opp.symbol}")
        print(f"Strategy: {opp.strategy_type}")
        print(f"Entry: ${opp.entry_price:.2f}")
        print(f"Target: ${opp.target_price:.2f} ({(opp.target_price/opp.entry_price-1)*100:.1f}%)")
        print(f"Stop Loss: ${opp.stop_loss:.2f} ({(opp.stop_loss/opp.entry_price-1)*100:.1f}%)")
        print(f"Position Size: {opp.position_size} shares")
        print(f"Risk/Reward: {opp.risk_reward_ratio:.2f}")


if __name__ == "__main__":
    main()
Save the file with Ctrl+X, Y, Enter.
3. Install Required Dependencies
bashpip3 install yfinance pandas numpy
4. Run the Volatility Hunter
bashpython3 -m src.trading_bot.strategies.volatility_hunter
Expected Output
The script will:

Scan 20+ high-volatility symbols (TQQQ, SQQQ, GME, AMC, etc.)
Check for three trading strategies:

Gap fills (80% win rate)
Oversold bounces (RSI < 35)
Breakout pullbacks


Calculate position sizes using Kelly Criterion
Display a summary of top opportunities

Integration Tasks (After Basic Implementation Works)

Create Integration with ML Models

Connect volatility signals to existing ML predictions
Use ensemble voting for higher confidence trades


Add Real-time Monitoring

Create a dashboard to monitor opportunities throughout the day
Set up alerts for high-probability setups


Connect to Risk Management

Integrate with existing risk limits
Add portfolio-level position sizing



Troubleshooting

If "python3" not found: Install with sudo apt update && sudo apt install python3
If yfinance fails: Check internet connection, Yahoo Finance may be temporarily down
If import errors persist: The simplified version has no dependencies on your framework

Success Criteria

Script runs without errors
Finds at least 1-5 volatility opportunities
Displays expected returns and risk/reward ratios
Kelly Criterion calculates reasonable position sizes

Execute these steps in order and report back with the output from running the volatility hunter.