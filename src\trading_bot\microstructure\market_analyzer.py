"""
Market Microstructure Analysis

This module provides sophisticated market microstructure analysis including:
- Order flow imbalance detection
- Hidden liquidity discovery
- Optimal execution timing
- Market regime detection
- Bid-ask spread analysis
- Volume profile analysis
- Market impact estimation
"""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json
from collections import deque
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

from ..core.logger import get_logger
from ..core.config import settings
from ..data.models import Order, Trade, Quote

logger = get_logger(__name__)


class MarketRegime(Enum):
    """Market regime types."""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    MEAN_REVERTING = "mean_reverting"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    ILLIQUID = "illiquid"
    LIQUID = "liquid"


class OrderFlowDirection(Enum):
    """Order flow direction."""
    BUY_PRESSURE = "buy_pressure"
    SELL_PRESSURE = "sell_pressure"
    BALANCED = "balanced"
    UNCERTAIN = "uncertain"


class LiquidityType(Enum):
    """Types of liquidity."""
    VISIBLE = "visible"
    HIDDEN = "hidden"
    ICEBERG = "iceberg"
    DARK_POOL = "dark_pool"


@dataclass
class OrderFlowMetrics:
    """Order flow analysis metrics."""
    symbol: str
    timestamp: datetime
    buy_volume: float
    sell_volume: float
    buy_count: int
    sell_count: int
    volume_imbalance: float  # (buy_vol - sell_vol) / (buy_vol + sell_vol)
    order_imbalance: float   # (buy_count - sell_count) / (buy_count + sell_count)
    average_trade_size: float
    flow_direction: OrderFlowDirection
    confidence: float


@dataclass
class LiquidityMetrics:
    """Liquidity analysis metrics."""
    symbol: str
    timestamp: datetime
    bid_ask_spread: float
    bid_ask_spread_bps: float
    effective_spread: float
    market_depth: float
    visible_liquidity: float
    estimated_hidden_liquidity: float
    liquidity_ratio: float  # hidden / visible
    market_impact_coefficient: float


@dataclass
class MarketRegimeMetrics:
    """Market regime analysis metrics."""
    symbol: str
    timestamp: datetime
    regime: MarketRegime
    confidence: float
    volatility: float
    trend_strength: float
    mean_reversion_strength: float
    liquidity_score: float
    regime_duration: timedelta
    expected_regime_change_probability: float


@dataclass
class ExecutionTiming:
    """Optimal execution timing analysis."""
    symbol: str
    timestamp: datetime
    optimal_execution_time: datetime
    expected_market_impact: float
    liquidity_forecast: float
    volatility_forecast: float
    recommended_order_size: float
    recommended_execution_strategy: str
    confidence: float


class MarketMicrostructureAnalyzer:
    """Advanced market microstructure analysis system."""
    
    def __init__(self):
        # Data storage
        self.quotes_buffer: Dict[str, deque] = {}
        self.trades_buffer: Dict[str, deque] = {}
        self.order_flow_history: Dict[str, List[OrderFlowMetrics]] = {}
        self.liquidity_history: Dict[str, List[LiquidityMetrics]] = {}
        self.regime_history: Dict[str, List[MarketRegimeMetrics]] = {}
        
        # Analysis models
        self.regime_models: Dict[str, KMeans] = {}
        self.scalers: Dict[str, StandardScaler] = {}
        
        # Configuration
        self.config = {
            'buffer_size': 1000,
            'analysis_window_minutes': 15,
            'regime_detection_window_hours': 4,
            'liquidity_estimation_window_minutes': 5,
            'order_flow_window_seconds': 60,
            'min_trades_for_analysis': 10,
            'regime_confidence_threshold': 0.7,
            'hidden_liquidity_detection_threshold': 0.15
        }
        
        # Monitoring
        self.is_analyzing = False
        self.analysis_tasks: List[asyncio.Task] = []
    
    async def start_analysis(self):
        """Start market microstructure analysis."""
        if self.is_analyzing:
            logger.warning("Market analysis already running")
            return
        
        self.is_analyzing = True
        logger.info("Starting market microstructure analysis...")
        
        # Start analysis tasks
        self.analysis_tasks = [
            asyncio.create_task(self._order_flow_analyzer()),
            asyncio.create_task(self._liquidity_analyzer()),
            asyncio.create_task(self._regime_detector()),
            asyncio.create_task(self._execution_timer()),
            asyncio.create_task(self._data_cleanup())
        ]
        
        logger.info("Market microstructure analysis started")
        
        try:
            await asyncio.gather(*self.analysis_tasks)
        except Exception as e:
            logger.error(f"Market analysis error: {e}")
        finally:
            self.is_analyzing = False
    
    async def stop_analysis(self):
        """Stop market microstructure analysis."""
        self.is_analyzing = False
        
        for task in self.analysis_tasks:
            if not task.done():
                task.cancel()
        
        if self.analysis_tasks:
            await asyncio.gather(*self.analysis_tasks, return_exceptions=True)
        
        self.analysis_tasks.clear()
        logger.info("Market microstructure analysis stopped")
    
    async def process_quote(self, symbol: str, quote: Quote):
        """Process incoming quote data."""
        if symbol not in self.quotes_buffer:
            self.quotes_buffer[symbol] = deque(maxlen=self.config['buffer_size'])
        
        self.quotes_buffer[symbol].append({
            'timestamp': quote.timestamp,
            'bid': quote.bid,
            'ask': quote.ask,
            'bid_size': quote.bid_size,
            'ask_size': quote.ask_size,
            'spread': quote.ask - quote.bid,
            'mid_price': (quote.bid + quote.ask) / 2
        })
    
    async def process_trade(self, symbol: str, trade: Trade):
        """Process incoming trade data."""
        if symbol not in self.trades_buffer:
            self.trades_buffer[symbol] = deque(maxlen=self.config['buffer_size'])
        
        # Determine trade direction (buy/sell)
        direction = self._classify_trade_direction(symbol, trade)
        
        self.trades_buffer[symbol].append({
            'timestamp': trade.timestamp,
            'price': trade.price,
            'size': trade.size,
            'direction': direction,
            'trade_id': trade.trade_id
        })
    
    def _classify_trade_direction(self, symbol: str, trade: Trade) -> str:
        """Classify trade as buy or sell using tick rule and quote rule."""
        
        # Get recent quotes
        if symbol not in self.quotes_buffer or len(self.quotes_buffer[symbol]) == 0:
            return 'unknown'
        
        recent_quote = self.quotes_buffer[symbol][-1]
        
        # Quote rule: compare trade price to bid/ask
        mid_price = recent_quote['mid_price']
        
        if trade.price > mid_price:
            return 'buy'
        elif trade.price < mid_price:
            return 'sell'
        else:
            # Use tick rule for trades at mid-price
            if len(self.trades_buffer[symbol]) > 0:
                last_trade = self.trades_buffer[symbol][-1]
                if trade.price > last_trade['price']:
                    return 'buy'
                elif trade.price < last_trade['price']:
                    return 'sell'
        
        return 'unknown'
    
    async def _order_flow_analyzer(self):
        """Analyze order flow patterns."""
        while self.is_analyzing:
            try:
                for symbol in self.trades_buffer.keys():
                    await self._analyze_order_flow(symbol)
                
                await asyncio.sleep(self.config['order_flow_window_seconds'])
                
            except Exception as e:
                logger.error(f"Error in order flow analysis: {e}")
                await asyncio.sleep(60)
    
    async def _analyze_order_flow(self, symbol: str):
        """Analyze order flow for a specific symbol."""
        if symbol not in self.trades_buffer:
            return
        
        # Get recent trades
        cutoff_time = datetime.utcnow() - timedelta(seconds=self.config['order_flow_window_seconds'])
        recent_trades = [
            trade for trade in self.trades_buffer[symbol]
            if trade['timestamp'] > cutoff_time
        ]
        
        if len(recent_trades) < self.config['min_trades_for_analysis']:
            return
        
        # Calculate order flow metrics
        buy_trades = [t for t in recent_trades if t['direction'] == 'buy']
        sell_trades = [t for t in recent_trades if t['direction'] == 'sell']
        
        buy_volume = sum(t['size'] for t in buy_trades)
        sell_volume = sum(t['size'] for t in sell_trades)
        buy_count = len(buy_trades)
        sell_count = len(sell_trades)
        
        total_volume = buy_volume + sell_volume
        total_count = buy_count + sell_count
        
        if total_volume == 0 or total_count == 0:
            return
        
        # Calculate imbalances
        volume_imbalance = (buy_volume - sell_volume) / total_volume
        order_imbalance = (buy_count - sell_count) / total_count
        
        # Determine flow direction
        flow_direction = self._determine_flow_direction(volume_imbalance, order_imbalance)
        
        # Calculate confidence
        confidence = min(abs(volume_imbalance), abs(order_imbalance))
        
        # Create metrics
        metrics = OrderFlowMetrics(
            symbol=symbol,
            timestamp=datetime.utcnow(),
            buy_volume=buy_volume,
            sell_volume=sell_volume,
            buy_count=buy_count,
            sell_count=sell_count,
            volume_imbalance=volume_imbalance,
            order_imbalance=order_imbalance,
            average_trade_size=total_volume / total_count,
            flow_direction=flow_direction,
            confidence=confidence
        )
        
        # Store metrics
        if symbol not in self.order_flow_history:
            self.order_flow_history[symbol] = []
        
        self.order_flow_history[symbol].append(metrics)
        
        # Log significant imbalances
        if abs(volume_imbalance) > 0.3 and confidence > 0.5:
            logger.info(f"Significant order flow imbalance in {symbol}: {volume_imbalance:.2%} volume, {flow_direction.value}")
    
    def _determine_flow_direction(self, volume_imbalance: float, order_imbalance: float) -> OrderFlowDirection:
        """Determine order flow direction from imbalances."""
        
        # Strong buy pressure
        if volume_imbalance > 0.2 and order_imbalance > 0.1:
            return OrderFlowDirection.BUY_PRESSURE
        
        # Strong sell pressure
        if volume_imbalance < -0.2 and order_imbalance < -0.1:
            return OrderFlowDirection.SELL_PRESSURE
        
        # Balanced flow
        if abs(volume_imbalance) < 0.1 and abs(order_imbalance) < 0.1:
            return OrderFlowDirection.BALANCED
        
        # Uncertain
        return OrderFlowDirection.UNCERTAIN
    
    async def _liquidity_analyzer(self):
        """Analyze market liquidity patterns."""
        while self.is_analyzing:
            try:
                for symbol in self.quotes_buffer.keys():
                    await self._analyze_liquidity(symbol)
                
                await asyncio.sleep(self.config['liquidity_estimation_window_minutes'] * 60)
                
            except Exception as e:
                logger.error(f"Error in liquidity analysis: {e}")
                await asyncio.sleep(60)
    
    async def _analyze_liquidity(self, symbol: str):
        """Analyze liquidity for a specific symbol."""
        if symbol not in self.quotes_buffer or len(self.quotes_buffer[symbol]) < 10:
            return
        
        # Get recent quotes
        cutoff_time = datetime.utcnow() - timedelta(minutes=self.config['liquidity_estimation_window_minutes'])
        recent_quotes = [
            quote for quote in self.quotes_buffer[symbol]
            if quote['timestamp'] > cutoff_time
        ]
        
        if len(recent_quotes) < 10:
            return
        
        # Calculate liquidity metrics
        spreads = [q['spread'] for q in recent_quotes]
        mid_prices = [q['mid_price'] for q in recent_quotes]
        bid_sizes = [q['bid_size'] for q in recent_quotes]
        ask_sizes = [q['ask_size'] for q in recent_quotes]
        
        avg_spread = np.mean(spreads)
        avg_mid_price = np.mean(mid_prices)
        avg_spread_bps = (avg_spread / avg_mid_price) * 10000
        
        # Effective spread (includes market impact)
        effective_spread = avg_spread * 1.2  # Simplified calculation
        
        # Market depth
        market_depth = np.mean(bid_sizes) + np.mean(ask_sizes)
        
        # Visible liquidity
        visible_liquidity = market_depth
        
        # Estimate hidden liquidity using spread analysis
        estimated_hidden_liquidity = await self._estimate_hidden_liquidity(symbol, recent_quotes)
        
        # Liquidity ratio
        liquidity_ratio = estimated_hidden_liquidity / visible_liquidity if visible_liquidity > 0 else 0
        
        # Market impact coefficient (simplified)
        market_impact_coefficient = avg_spread_bps / market_depth if market_depth > 0 else float('inf')
        
        # Create metrics
        metrics = LiquidityMetrics(
            symbol=symbol,
            timestamp=datetime.utcnow(),
            bid_ask_spread=avg_spread,
            bid_ask_spread_bps=avg_spread_bps,
            effective_spread=effective_spread,
            market_depth=market_depth,
            visible_liquidity=visible_liquidity,
            estimated_hidden_liquidity=estimated_hidden_liquidity,
            liquidity_ratio=liquidity_ratio,
            market_impact_coefficient=market_impact_coefficient
        )
        
        # Store metrics
        if symbol not in self.liquidity_history:
            self.liquidity_history[symbol] = []
        
        self.liquidity_history[symbol].append(metrics)
        
        # Log liquidity insights
        if liquidity_ratio > self.config['hidden_liquidity_detection_threshold']:
            logger.info(f"Significant hidden liquidity detected in {symbol}: {liquidity_ratio:.2%} ratio")
    
    async def _estimate_hidden_liquidity(self, symbol: str, quotes: List[Dict]) -> float:
        """Estimate hidden liquidity using spread and volume analysis."""
        
        # Get corresponding trades
        if symbol not in self.trades_buffer:
            return 0.0
        
        # Look for signs of hidden liquidity:
        # 1. Trades larger than visible depth
        # 2. Minimal price impact despite large trades
        # 3. Rapid spread recovery after trades
        
        cutoff_time = quotes[0]['timestamp'] if quotes else datetime.utcnow() - timedelta(minutes=5)
        recent_trades = [
            trade for trade in self.trades_buffer[symbol]
            if trade['timestamp'] > cutoff_time
        ]
        
        if not recent_trades:
            return 0.0
        
        # Calculate average visible depth
        avg_visible_depth = np.mean([q['bid_size'] + q['ask_size'] for q in quotes])
        
        # Find trades larger than visible depth
        large_trades = [t for t in recent_trades if t['size'] > avg_visible_depth * 0.5]
        
        # Estimate hidden liquidity as multiple of large trade sizes
        if large_trades:
            avg_large_trade_size = np.mean([t['size'] for t in large_trades])
            estimated_hidden = avg_large_trade_size * len(large_trades) * 0.3  # Conservative estimate
            return estimated_hidden
        
        return 0.0

    async def _data_cleanup(self):
        """Clean up old data to prevent memory issues."""
        while self.is_analyzing:
            try:
                cutoff_time = datetime.utcnow() - timedelta(hours=24)

                # Clean up history data
                for symbol in list(self.order_flow_history.keys()):
                    self.order_flow_history[symbol] = [
                        metrics for metrics in self.order_flow_history[symbol]
                        if metrics.timestamp > cutoff_time
                    ]

                for symbol in list(self.liquidity_history.keys()):
                    self.liquidity_history[symbol] = [
                        metrics for metrics in self.liquidity_history[symbol]
                        if metrics.timestamp > cutoff_time
                    ]

                for symbol in list(self.regime_history.keys()):
                    self.regime_history[symbol] = [
                        metrics for metrics in self.regime_history[symbol]
                        if metrics.timestamp > cutoff_time
                    ]

                await asyncio.sleep(3600)  # Clean up every hour

            except Exception as e:
                logger.error(f"Error in data cleanup: {e}")
                await asyncio.sleep(3600)

    # Public API methods

    async def get_order_flow_analysis(self, symbol: str) -> Optional[OrderFlowMetrics]:
        """Get latest order flow analysis for symbol."""
        if symbol in self.order_flow_history and self.order_flow_history[symbol]:
            return self.order_flow_history[symbol][-1]
        return None

    async def get_liquidity_analysis(self, symbol: str) -> Optional[LiquidityMetrics]:
        """Get latest liquidity analysis for symbol."""
        if symbol in self.liquidity_history and self.liquidity_history[symbol]:
            return self.liquidity_history[symbol][-1]
        return None

    async def get_regime_analysis(self, symbol: str) -> Optional[MarketRegimeMetrics]:
        """Get latest regime analysis for symbol."""
        if symbol in self.regime_history and self.regime_history[symbol]:
            return self.regime_history[symbol][-1]
        return None

    async def get_execution_recommendation(self, symbol: str, order_size: float) -> Dict[str, Any]:
        """Get execution recommendation for a specific order."""

        # Get current market analysis
        order_flow = await self.get_order_flow_analysis(symbol)
        liquidity = await self.get_liquidity_analysis(symbol)
        regime = await self.get_regime_analysis(symbol)

        if not all([order_flow, liquidity, regime]):
            return {
                'recommendation': 'insufficient_data',
                'confidence': 0.0,
                'reason': 'Not enough market data for analysis'
            }

        # Calculate market impact
        estimated_impact = self._estimate_order_impact(liquidity, order_size)

        # Determine execution strategy
        strategy = self._recommend_execution_strategy(order_flow, liquidity, regime, order_size)

        # Calculate timing recommendation
        timing = self._recommend_execution_timing(order_flow, liquidity, regime)

        # Overall confidence
        confidence = min(order_flow.confidence, regime.confidence, 1.0)

        return {
            'recommendation': strategy,
            'estimated_impact_bps': estimated_impact * 10000,
            'timing': timing,
            'confidence': confidence,
            'order_flow_direction': order_flow.flow_direction.value,
            'market_regime': regime.regime.value,
            'liquidity_score': liquidity.liquidity_ratio,
            'hidden_liquidity_available': liquidity.estimated_hidden_liquidity > order_size * 0.5
        }

    def _estimate_order_impact(self, liquidity: LiquidityMetrics, order_size: float) -> float:
        """Estimate market impact of an order."""

        # Simple linear impact model
        # Impact = (Order Size / Market Depth) * Impact Coefficient

        if liquidity.market_depth <= 0:
            return 0.1  # 10% impact if no liquidity data

        size_ratio = order_size / liquidity.market_depth
        base_impact = size_ratio * (liquidity.bid_ask_spread_bps / 10000)

        # Adjust for hidden liquidity
        if liquidity.estimated_hidden_liquidity > 0:
            total_liquidity = liquidity.visible_liquidity + liquidity.estimated_hidden_liquidity
            adjusted_ratio = order_size / total_liquidity
            base_impact *= adjusted_ratio / size_ratio

        # Cap impact at reasonable levels
        return min(base_impact, 0.05)  # Max 5% impact

    def _recommend_execution_strategy(self,
                                    order_flow: OrderFlowMetrics,
                                    liquidity: LiquidityMetrics,
                                    regime: MarketRegimeMetrics,
                                    order_size: float) -> str:
        """Recommend execution strategy based on market conditions."""

        # Large order relative to liquidity
        if order_size > liquidity.visible_liquidity:
            if liquidity.estimated_hidden_liquidity > order_size:
                return "iceberg_with_hidden_liquidity"
            else:
                return "twap_aggressive"

        # High volatility regime
        if regime.regime in [MarketRegime.HIGH_VOLATILITY, MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN]:
            if order_flow.flow_direction == OrderFlowDirection.BUY_PRESSURE:
                return "immediate_aggressive"
            else:
                return "patient_passive"

        # Mean reverting regime
        if regime.regime == MarketRegime.MEAN_REVERTING:
            return "contrarian_passive"

        # Low volatility, good liquidity
        if regime.regime == MarketRegime.LOW_VOLATILITY and liquidity.liquidity_ratio > 0.5:
            return "optimal_passive"

        # Default strategy
        return "balanced_twap"

    def _recommend_execution_timing(self,
                                  order_flow: OrderFlowMetrics,
                                  liquidity: LiquidityMetrics,
                                  regime: MarketRegimeMetrics) -> str:
        """Recommend execution timing."""

        # Strong order flow against our direction
        if order_flow.flow_direction == OrderFlowDirection.SELL_PRESSURE and order_flow.confidence > 0.7:
            return "wait_for_flow_reversal"

        # Good liquidity and stable regime
        if liquidity.liquidity_ratio > 0.3 and regime.confidence > 0.7:
            return "execute_immediately"

        # Regime change expected
        if regime.expected_regime_change_probability > 0.8:
            return "execute_before_regime_change"

        # Low liquidity
        if liquidity.market_depth < 1000:  # Arbitrary threshold
            return "wait_for_liquidity"

        return "execute_when_ready"

    async def get_market_summary(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive market summary for symbol."""

        order_flow = await self.get_order_flow_analysis(symbol)
        liquidity = await self.get_liquidity_analysis(symbol)
        regime = await self.get_regime_analysis(symbol)

        summary = {
            'symbol': symbol,
            'timestamp': datetime.utcnow().isoformat(),
            'data_available': {
                'order_flow': order_flow is not None,
                'liquidity': liquidity is not None,
                'regime': regime is not None
            }
        }

        if order_flow:
            summary['order_flow'] = {
                'direction': order_flow.flow_direction.value,
                'volume_imbalance': order_flow.volume_imbalance,
                'confidence': order_flow.confidence
            }

        if liquidity:
            summary['liquidity'] = {
                'spread_bps': liquidity.bid_ask_spread_bps,
                'market_depth': liquidity.market_depth,
                'hidden_liquidity_ratio': liquidity.liquidity_ratio,
                'market_impact_coefficient': liquidity.market_impact_coefficient
            }

        if regime:
            summary['regime'] = {
                'current_regime': regime.regime.value,
                'confidence': regime.confidence,
                'volatility': regime.volatility,
                'trend_strength': regime.trend_strength,
                'change_probability': regime.expected_regime_change_probability
            }

        return summary


# Global instance
_market_analyzer = None

def get_market_analyzer() -> MarketMicrostructureAnalyzer:
    """Get global market analyzer instance."""
    global _market_analyzer

    if _market_analyzer is None:
        _market_analyzer = MarketMicrostructureAnalyzer()

    return _market_analyzer
