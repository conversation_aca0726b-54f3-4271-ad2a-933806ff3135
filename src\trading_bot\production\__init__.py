"""
Production deployment and go-live system for AI Trading Bot.

This module provides comprehensive production deployment capabilities including:
- Pre-deployment validation and preflight checks
- Comprehensive testing suite (integration, stress, paper trading)
- Performance optimization and profiling tools
- Real-time production monitoring and alerting
- Deployment management with rollback procedures
- Go-live procedures with phased rollout
- Production documentation and operational runbooks

The production system ensures bulletproof deployment with:
- Thorough testing and validation
- Performance optimization to meet strict latency requirements
- Safe deployment with rollback procedures
- Constant monitoring with comprehensive alerting
- Production-ready documentation and procedures
"""

from .deployment.preflight_checks import Preflight<PERSON>he<PERSON>, ValidationReport
from .deployment.deployment_config import ProductionConfig
from .deployment.migration_scripts import MigrationManager
from .deployment.rollback_manager import RollbackManager

from .testing.integration_tests import IntegrationTestSuite
from .testing.stress_tests import StressTestSuite
from .testing.paper_trading import PaperTradingValidator
from .testing.performance_tests import PerformanceTestSuite

from .optimization.code_profiler import CodeProfiler
from .optimization.query_optimizer import QueryOptimizer
from .optimization.cache_optimizer import CacheOptimizer
from .optimization.resource_tuner import ResourceTuner

from .monitoring.production_monitor import ProductionMonitor
from .monitoring.alert_manager import AlertManager
from .monitoring.log_aggregator import LogAggregator
from .monitoring.audit_trail import AuditTrail

__all__ = [
    # Deployment
    'PreflightChecker',
    'ValidationReport',
    'ProductionConfig',
    'MigrationManager',
    'RollbackManager',
    
    # Testing
    'IntegrationTestSuite',
    'StressTestSuite',
    'PaperTradingValidator',
    'PerformanceTestSuite',
    
    # Optimization
    'CodeProfiler',
    'QueryOptimizer',
    'CacheOptimizer',
    'ResourceTuner',
    
    # Monitoring
    'ProductionMonitor',
    'AlertManager',
    'LogAggregator',
    'AuditTrail',
]

__version__ = "1.0.0"
