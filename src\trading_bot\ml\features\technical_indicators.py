"""Technical Indicators for feature engineering - 50+ indicators."""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Tuple
import talib
from scipy import stats
from scipy.signal import argrelextrema
import logging

logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """Comprehensive technical indicators calculator."""
    
    def __init__(self):
        self.indicators = {}
        
    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate all 50+ technical indicators."""
        df = data.copy()
        
        # Ensure required columns exist
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                logger.warning(f"Missing column: {col}")
                df[col] = df.get('close', 0)
        
        # Price-based indicators
        df = self._add_trend_indicators(df)
        df = self._add_momentum_indicators(df)
        df = self._add_volatility_indicators(df)
        df = self._add_volume_indicators(df)
        df = self._add_cycle_indicators(df)
        df = self._add_pattern_indicators(df)
        df = self._add_statistical_indicators(df)
        df = self._add_custom_indicators(df)
        
        return df
    
    def _add_trend_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add trend-following indicators."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'sma_{period}'] = talib.SMA(close, timeperiod=period)
            df[f'ema_{period}'] = talib.EMA(close, timeperiod=period)
            df[f'wma_{period}'] = talib.WMA(close, timeperiod=period)
        
        # MACD
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(close)
        
        # ADX (Average Directional Index)
        df['adx'] = talib.ADX(high, low, close, timeperiod=14)
        df['plus_di'] = talib.PLUS_DI(high, low, close, timeperiod=14)
        df['minus_di'] = talib.MINUS_DI(high, low, close, timeperiod=14)
        
        # Parabolic SAR
        df['sar'] = talib.SAR(high, low)
        
        # Aroon
        df['aroon_up'], df['aroon_down'] = talib.AROON(high, low, timeperiod=14)
        df['aroon_osc'] = talib.AROONOSC(high, low, timeperiod=14)
        
        # Ichimoku Cloud components
        df = self._calculate_ichimoku(df)
        
        # Trend strength
        df['trend_strength'] = abs(df['adx'])
        
        return df
    
    def _add_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum oscillators."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # RSI
        for period in [14, 21, 30]:
            df[f'rsi_{period}'] = talib.RSI(close, timeperiod=period)
        
        # Stochastic
        df['stoch_k'], df['stoch_d'] = talib.STOCH(high, low, close)
        df['stochf_k'], df['stochf_d'] = talib.STOCHF(high, low, close)
        
        # Williams %R
        df['willr'] = talib.WILLR(high, low, close, timeperiod=14)
        
        # CCI (Commodity Channel Index)
        df['cci'] = talib.CCI(high, low, close, timeperiod=14)
        
        # ROC (Rate of Change)
        for period in [10, 20, 30]:
            df[f'roc_{period}'] = talib.ROC(close, timeperiod=period)
        
        # CMO (Chande Momentum Oscillator)
        df['cmo'] = talib.CMO(close, timeperiod=14)
        
        # Ultimate Oscillator
        df['ultosc'] = talib.ULTOSC(high, low, close)
        
        # TRIX
        df['trix'] = talib.TRIX(close, timeperiod=14)
        
        # Money Flow Index
        df['mfi'] = talib.MFI(high, low, close, volume, timeperiod=14)
        
        return df
    
    def _add_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility indicators."""
        high, low, close = df['high'], df['low'], df['close']
        
        # Bollinger Bands
        for period in [20, 50]:
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=period)
            df[f'bb_upper_{period}'] = bb_upper
            df[f'bb_middle_{period}'] = bb_middle
            df[f'bb_lower_{period}'] = bb_lower
            df[f'bb_width_{period}'] = (bb_upper - bb_lower) / bb_middle
            df[f'bb_position_{period}'] = (close - bb_lower) / (bb_upper - bb_lower)
        
        # ATR (Average True Range)
        df['atr'] = talib.ATR(high, low, close, timeperiod=14)
        df['atr_ratio'] = df['atr'] / close
        
        # True Range
        df['tr'] = talib.TRANGE(high, low, close)
        
        # Keltner Channels
        df = self._calculate_keltner_channels(df)
        
        # Donchian Channels
        df = self._calculate_donchian_channels(df)
        
        # Historical Volatility
        df['hist_vol_10'] = close.pct_change().rolling(10).std() * np.sqrt(252)
        df['hist_vol_30'] = close.pct_change().rolling(30).std() * np.sqrt(252)
        
        return df
    
    def _add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based indicators."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # On Balance Volume
        df['obv'] = talib.OBV(close, volume)
        
        # Accumulation/Distribution Line
        df['ad'] = talib.AD(high, low, close, volume)
        
        # Chaikin A/D Oscillator
        df['adosc'] = talib.ADOSC(high, low, close, volume)
        
        # Volume Weighted Average Price
        df['vwap'] = self._calculate_vwap(df)
        
        # Volume Moving Averages
        df['vol_sma_10'] = talib.SMA(volume, timeperiod=10)
        df['vol_sma_30'] = talib.SMA(volume, timeperiod=30)
        df['vol_ratio'] = volume / df['vol_sma_30']
        
        # Price Volume Trend
        df['pvt'] = self._calculate_pvt(df)
        
        # Ease of Movement
        df['eom'] = self._calculate_eom(df)
        
        # Volume Rate of Change
        df['vol_roc'] = talib.ROC(volume, timeperiod=10)
        
        # Negative Volume Index
        df['nvi'] = self._calculate_nvi(df)
        
        # Positive Volume Index
        df['pvi'] = self._calculate_pvi(df)
        
        return df
    
    def _add_cycle_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add cycle analysis indicators."""
        close = df['close']
        
        # Hilbert Transform indicators
        df['ht_dcperiod'] = talib.HT_DCPERIOD(close)
        df['ht_dcphase'] = talib.HT_DCPHASE(close)
        df['ht_trendmode'] = talib.HT_TRENDMODE(close)
        
        # Hilbert Transform Sine Wave
        df['ht_sine'], df['ht_leadsine'] = talib.HT_SINE(close)
        
        # MESA Adaptive Moving Average
        df['mama'], df['fama'] = talib.MAMA(close)
        
        return df
    
    def _add_pattern_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add pattern recognition indicators."""
        # Support and Resistance levels
        df = self._calculate_support_resistance(df)
        
        # Fibonacci levels
        df = self._calculate_fibonacci_levels(df)
        
        # Pivot points
        df = self._calculate_pivot_points(df)
        
        # Gap detection
        df = self._detect_gaps(df)
        
        return df
    
    def _add_statistical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add statistical indicators."""
        close = df['close']
        
        # Linear regression
        for period in [14, 30]:
            df[f'linreg_{period}'] = talib.LINEARREG(close, timeperiod=period)
            df[f'linreg_angle_{period}'] = talib.LINEARREG_ANGLE(close, timeperiod=period)
            df[f'linreg_slope_{period}'] = talib.LINEARREG_SLOPE(close, timeperiod=period)
        
        # Standard deviation
        df['stddev'] = talib.STDDEV(close, timeperiod=20)
        
        # Variance
        df['var'] = talib.VAR(close, timeperiod=20)
        
        # Beta (requires market data - simplified version)
        df['beta'] = self._calculate_beta(df)
        
        # Correlation with volume
        df['price_volume_corr'] = close.rolling(20).corr(df['volume'])
        
        # Z-Score
        df['z_score'] = (close - close.rolling(20).mean()) / close.rolling(20).std()
        
        return df
    
    def _add_custom_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add custom indicators."""
        high, low, close, volume = df['high'], df['low'], df['close'], df['volume']
        
        # Price momentum
        df['price_momentum'] = close.pct_change(10)
        
        # Volume momentum
        df['volume_momentum'] = volume.pct_change(10)
        
        # High-Low ratio
        df['hl_ratio'] = high / low
        
        # Close position in range
        df['close_position'] = (close - low) / (high - low)
        
        # Efficiency ratio
        df['efficiency_ratio'] = self._calculate_efficiency_ratio(df)
        
        # Fractal dimension
        df['fractal_dimension'] = self._calculate_fractal_dimension(df)
        
        # Hurst exponent
        df['hurst_exponent'] = self._calculate_hurst_exponent(df)
        
        # Market regime indicator
        df['market_regime'] = self._calculate_market_regime(df)
        
        return df
    
    def _calculate_ichimoku(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate Ichimoku Cloud components."""
        high, low, close = df['high'], df['low'], df['close']
        
        # Tenkan-sen (Conversion Line)
        tenkan_period = 9
        df['tenkan_sen'] = (high.rolling(tenkan_period).max() + low.rolling(tenkan_period).min()) / 2
        
        # Kijun-sen (Base Line)
        kijun_period = 26
        df['kijun_sen'] = (high.rolling(kijun_period).max() + low.rolling(kijun_period).min()) / 2
        
        # Senkou Span A (Leading Span A)
        df['senkou_span_a'] = ((df['tenkan_sen'] + df['kijun_sen']) / 2).shift(26)
        
        # Senkou Span B (Leading Span B)
        senkou_period = 52
        df['senkou_span_b'] = ((high.rolling(senkou_period).max() + low.rolling(senkou_period).min()) / 2).shift(26)
        
        # Chikou Span (Lagging Span)
        df['chikou_span'] = close.shift(-26)
        
        return df
    
    def _calculate_keltner_channels(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate Keltner Channels."""
        close, atr = df['close'], df['atr']
        
        # Middle line (EMA)
        middle = talib.EMA(close, timeperiod=20)
        
        # Upper and lower bands
        df['keltner_upper'] = middle + (2 * atr)
        df['keltner_middle'] = middle
        df['keltner_lower'] = middle - (2 * atr)
        
        return df
    
    def _calculate_donchian_channels(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate Donchian Channels."""
        high, low = df['high'], df['low']
        
        period = 20
        df['donchian_upper'] = high.rolling(period).max()
        df['donchian_lower'] = low.rolling(period).min()
        df['donchian_middle'] = (df['donchian_upper'] + df['donchian_lower']) / 2
        
        return df
    
    def _calculate_vwap(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Volume Weighted Average Price."""
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        return vwap
    
    def _calculate_pvt(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Price Volume Trend."""
        close_change = df['close'].pct_change()
        pvt = (close_change * df['volume']).cumsum()
        return pvt
    
    def _calculate_eom(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Ease of Movement."""
        high, low, volume = df['high'], df['low'], df['volume']
        
        distance = (high + low) / 2 - (high.shift(1) + low.shift(1)) / 2
        box_height = volume / (high - low)
        eom = distance / box_height
        
        return eom.rolling(14).mean()
    
    def _calculate_nvi(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Negative Volume Index."""
        close, volume = df['close'], df['volume']
        
        nvi = pd.Series(index=df.index, dtype=float)
        nvi.iloc[0] = 1000
        
        for i in range(1, len(df)):
            if volume.iloc[i] < volume.iloc[i-1]:
                nvi.iloc[i] = nvi.iloc[i-1] * (close.iloc[i] / close.iloc[i-1])
            else:
                nvi.iloc[i] = nvi.iloc[i-1]
        
        return nvi
    
    def _calculate_pvi(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Positive Volume Index."""
        close, volume = df['close'], df['volume']
        
        pvi = pd.Series(index=df.index, dtype=float)
        pvi.iloc[0] = 1000
        
        for i in range(1, len(df)):
            if volume.iloc[i] > volume.iloc[i-1]:
                pvi.iloc[i] = pvi.iloc[i-1] * (close.iloc[i] / close.iloc[i-1])
            else:
                pvi.iloc[i] = pvi.iloc[i-1]
        
        return pvi
    
    def _calculate_support_resistance(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate support and resistance levels."""
        high, low, close = df['high'], df['low'], df['close']
        
        # Find local maxima and minima
        window = 5
        df['resistance'] = high.rolling(window*2+1, center=True).max()
        df['support'] = low.rolling(window*2+1, center=True).min()
        
        # Distance to support/resistance
        df['dist_to_resistance'] = (df['resistance'] - close) / close
        df['dist_to_support'] = (close - df['support']) / close
        
        return df
    
    def _calculate_fibonacci_levels(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate Fibonacci retracement levels."""
        high, low = df['high'], df['low']
        
        # Calculate over rolling window
        window = 50
        max_high = high.rolling(window).max()
        min_low = low.rolling(window).min()
        
        diff = max_high - min_low
        
        # Fibonacci levels
        df['fib_23.6'] = max_high - 0.236 * diff
        df['fib_38.2'] = max_high - 0.382 * diff
        df['fib_50.0'] = max_high - 0.500 * diff
        df['fib_61.8'] = max_high - 0.618 * diff
        df['fib_78.6'] = max_high - 0.786 * diff
        
        return df
    
    def _calculate_pivot_points(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate pivot points."""
        high, low, close = df['high'], df['low'], df['close']
        
        # Previous day's HLC
        prev_high = high.shift(1)
        prev_low = low.shift(1)
        prev_close = close.shift(1)
        
        # Pivot point
        df['pivot'] = (prev_high + prev_low + prev_close) / 3
        
        # Resistance levels
        df['r1'] = 2 * df['pivot'] - prev_low
        df['r2'] = df['pivot'] + (prev_high - prev_low)
        df['r3'] = prev_high + 2 * (df['pivot'] - prev_low)
        
        # Support levels
        df['s1'] = 2 * df['pivot'] - prev_high
        df['s2'] = df['pivot'] - (prev_high - prev_low)
        df['s3'] = prev_low - 2 * (prev_high - df['pivot'])
        
        return df
    
    def _detect_gaps(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect price gaps."""
        high, low, close = df['high'], df['low'], df['close']
        
        # Gap up: current low > previous high
        df['gap_up'] = (low > high.shift(1)).astype(int)
        
        # Gap down: current high < previous low
        df['gap_down'] = (high < low.shift(1)).astype(int)
        
        # Gap size
        df['gap_size'] = np.where(df['gap_up'], 
                                 (low - high.shift(1)) / close.shift(1),
                                 np.where(df['gap_down'],
                                         (low.shift(1) - high) / close.shift(1),
                                         0))
        
        return df
    
    def _calculate_beta(self, df: pd.DataFrame) -> pd.Series:
        """Calculate simplified beta (correlation with own price)."""
        close = df['close']
        returns = close.pct_change()
        market_returns = returns.rolling(20).mean()  # Simplified market proxy
        
        beta = returns.rolling(20).cov(market_returns) / market_returns.rolling(20).var()
        return beta.fillna(1.0)
    
    def _calculate_efficiency_ratio(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Kaufman's Efficiency Ratio."""
        close = df['close']
        
        period = 10
        direction = abs(close - close.shift(period))
        volatility = abs(close.diff()).rolling(period).sum()
        
        efficiency_ratio = direction / volatility
        return efficiency_ratio.fillna(0)
    
    def _calculate_fractal_dimension(self, df: pd.DataFrame) -> pd.Series:
        """Calculate fractal dimension."""
        close = df['close']
        
        def fractal_dimension_series(series, window=10):
            fd_values = []
            for i in range(window, len(series)):
                data = series.iloc[i-window:i].values
                
                # Calculate fractal dimension using Higuchi method
                def higuchi_fd(data, kmax=10):
                    L = []
                    x = np.array(data)
                    N = len(x)
                    
                    for k in range(1, kmax + 1):
                        Lk = []
                        for m in range(k):
                            Lmk = 0
                            for i in range(1, int((N - m) / k)):
                                Lmk += abs(x[m + i * k] - x[m + (i - 1) * k])
                            
                            if int((N - m) / k) > 0:
                                Lmk = Lmk * (N - 1) / (int((N - m) / k) * k)
                                Lk.append(Lmk)
                        
                        if Lk:
                            L.append(np.log(np.mean(Lk)))
                    
                    if len(L) >= 2:
                        return -np.polyfit(np.log(range(1, len(L) + 1)), L, 1)[0]
                    else:
                        return 1.5  # Default fractal dimension
                
                fd_values.append(higuchi_fd(data))
            
            # Pad with default values
            fd_series = pd.Series([1.5] * window + fd_values, index=series.index)
            return fd_series
        
        return fractal_dimension_series(close)
    
    def _calculate_hurst_exponent(self, df: pd.DataFrame) -> pd.Series:
        """Calculate Hurst exponent."""
        close = df['close']
        
        def hurst_exponent_series(series, window=20):
            hurst_values = []
            
            for i in range(window, len(series)):
                data = series.iloc[i-window:i].values
                
                # Calculate Hurst exponent using R/S analysis
                def hurst_exponent(ts):
                    lags = range(2, min(len(ts) // 2, 20))
                    tau = [np.sqrt(np.std(np.subtract(ts[lag:], ts[:-lag]))) for lag in lags]
                    
                    # Linear fit to log-log plot
                    if len(tau) >= 2:
                        poly = np.polyfit(np.log(lags), np.log(tau), 1)
                        return poly[0]
                    else:
                        return 0.5  # Random walk default
                
                hurst_values.append(hurst_exponent(data))
            
            # Pad with default values
            hurst_series = pd.Series([0.5] * window + hurst_values, index=series.index)
            return hurst_series
        
        return hurst_exponent_series(close)
    
    def _calculate_market_regime(self, df: pd.DataFrame) -> pd.Series:
        """Calculate market regime indicator."""
        close = df['close']
        
        # Use multiple indicators to determine regime
        sma_20 = close.rolling(20).mean()
        sma_50 = close.rolling(50).mean()
        volatility = close.pct_change().rolling(20).std()
        
        # Regime classification
        regime = pd.Series(index=df.index, dtype=int)
        
        # Bull market: price above MAs, low volatility
        bull_condition = (close > sma_20) & (sma_20 > sma_50) & (volatility < volatility.rolling(50).mean())
        
        # Bear market: price below MAs, high volatility
        bear_condition = (close < sma_20) & (sma_20 < sma_50) & (volatility > volatility.rolling(50).mean())
        
        regime = np.where(bull_condition, 1,  # Bull
                         np.where(bear_condition, -1,  # Bear
                                 0))  # Neutral
        
        return pd.Series(regime, index=df.index)
    
    def get_indicator_list(self) -> List[str]:
        """Get list of all available indicators."""
        return [
            # Trend indicators
            'sma_5', 'sma_10', 'sma_20', 'sma_50', 'sma_100', 'sma_200',
            'ema_5', 'ema_10', 'ema_20', 'ema_50', 'ema_100', 'ema_200',
            'wma_5', 'wma_10', 'wma_20', 'wma_50', 'wma_100', 'wma_200',
            'macd', 'macd_signal', 'macd_hist',
            'adx', 'plus_di', 'minus_di',
            'sar', 'aroon_up', 'aroon_down', 'aroon_osc',
            'tenkan_sen', 'kijun_sen', 'senkou_span_a', 'senkou_span_b', 'chikou_span',
            'trend_strength',
            
            # Momentum indicators
            'rsi_14', 'rsi_21', 'rsi_30',
            'stoch_k', 'stoch_d', 'stochf_k', 'stochf_d',
            'willr', 'cci', 'roc_10', 'roc_20', 'roc_30',
            'cmo', 'ultosc', 'trix', 'mfi',
            
            # Volatility indicators
            'bb_upper_20', 'bb_middle_20', 'bb_lower_20', 'bb_width_20', 'bb_position_20',
            'bb_upper_50', 'bb_middle_50', 'bb_lower_50', 'bb_width_50', 'bb_position_50',
            'atr', 'atr_ratio', 'tr',
            'keltner_upper', 'keltner_middle', 'keltner_lower',
            'donchian_upper', 'donchian_lower', 'donchian_middle',
            'hist_vol_10', 'hist_vol_30',
            
            # Volume indicators
            'obv', 'ad', 'adosc', 'vwap',
            'vol_sma_10', 'vol_sma_30', 'vol_ratio',
            'pvt', 'eom', 'vol_roc', 'nvi', 'pvi',
            
            # Cycle indicators
            'ht_dcperiod', 'ht_dcphase', 'ht_trendmode',
            'ht_sine', 'ht_leadsine', 'mama', 'fama',
            
            # Pattern indicators
            'resistance', 'support', 'dist_to_resistance', 'dist_to_support',
            'fib_23.6', 'fib_38.2', 'fib_50.0', 'fib_61.8', 'fib_78.6',
            'pivot', 'r1', 'r2', 'r3', 's1', 's2', 's3',
            'gap_up', 'gap_down', 'gap_size',
            
            # Statistical indicators
            'linreg_14', 'linreg_30', 'linreg_angle_14', 'linreg_angle_30',
            'linreg_slope_14', 'linreg_slope_30',
            'stddev', 'var', 'beta', 'price_volume_corr', 'z_score',
            
            # Custom indicators
            'price_momentum', 'volume_momentum', 'hl_ratio', 'close_position',
            'efficiency_ratio', 'fractal_dimension', 'hurst_exponent', 'market_regime'
        ]