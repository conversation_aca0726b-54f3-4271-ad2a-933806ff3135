"""
Production testing module for AI Trading Bot.

This module provides comprehensive testing capabilities including:
- Full system integration tests
- Load and stress testing
- Paper trading validation
- Performance and latency testing
- End-to-end trading flow validation

Components:
- IntegrationTestSuite: End-to-end system integration tests
- StressTestSuite: Load and stress testing for production readiness
- PaperTradingValidator: 30-day paper trading validation
- PerformanceTestSuite: Latency and throughput performance tests
"""

from .integration_tests import IntegrationTestSuite, IntegrationTestResult
from .stress_tests import StressTestSuite, StressTestResult
from .paper_trading import PaperTradingValidator, PaperTradingReport
from .performance_tests import PerformanceTestSuite, PerformanceMetrics

__all__ = [
    'IntegrationTestSuite',
    'IntegrationTestResult',
    'StressTestSuite',
    'StressTestResult',
    'PaperTradingValidator',
    'PaperTradingReport',
    'PerformanceTestSuite',
    'PerformanceMetrics',
]
