"""Confidence scoring system for predictions and signals."""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from scipy import stats
import warnings

from .predictor import PredictionResult
from .signal_generator import TradingSignal

logger = logging.getLogger(__name__)


@dataclass
class ConfidenceConfig:
    """Configuration for confidence scoring."""
    # Base confidence factors
    model_weight: float = 0.3
    ensemble_weight: float = 0.2
    historical_weight: float = 0.2
    market_condition_weight: float = 0.15
    volatility_weight: float = 0.15
    
    # Historical performance tracking
    performance_window: int = 100  # Number of recent predictions to consider
    min_historical_samples: int = 10
    
    # Market condition factors
    volume_importance: float = 0.3
    volatility_importance: float = 0.4
    trend_importance: float = 0.3
    
    # Confidence adjustments
    max_confidence_boost: float = 0.2
    max_confidence_penalty: float = 0.3
    
    # Decay factors
    time_decay_hours: float = 24.0
    performance_decay_factor: float = 0.95


@dataclass
class ConfidenceFactors:
    """Individual confidence factors."""
    model_confidence: float = 0.5
    ensemble_agreement: float = 0.5
    historical_performance: float = 0.5
    market_conditions: float = 0.5
    volatility_factor: float = 0.5
    volume_factor: float = 0.5
    trend_alignment: float = 0.5
    
    # Meta factors
    prediction_consistency: float = 0.5
    signal_strength: float = 0.5
    risk_assessment: float = 0.5


@dataclass
class ConfidenceResult:
    """Result of confidence scoring."""
    final_confidence: float
    base_confidence: float
    confidence_factors: ConfidenceFactors
    adjustments: Dict[str, float]
    warnings: List[str] = field(default_factory=list)
    
    # Breakdown
    factor_contributions: Dict[str, float] = field(default_factory=dict)
    confidence_level: str = "UNKNOWN"  # LOW, MEDIUM, HIGH, VERY_HIGH


class HistoricalPerformanceTracker:
    """Tracks historical performance of predictions and models."""
    
    def __init__(self, config: ConfidenceConfig):
        self.config = config
        self.prediction_history = []
        self.model_performance = {}
        self.ensemble_performance = []
        
    def add_prediction_outcome(self, 
                              prediction: PredictionResult,
                              actual_outcome: Union[str, float],
                              timestamp: Optional[datetime] = None):
        """Add prediction outcome for tracking."""
        
        timestamp = timestamp or datetime.now()
        
        # Calculate accuracy based on prediction type
        if isinstance(actual_outcome, str):
            # Categorical prediction
            accuracy = 1.0 if prediction.signal == actual_outcome else 0.0
        else:
            # Numerical prediction - check directional accuracy
            pred_direction = 1 if prediction.signal == 'BUY' else -1 if prediction.signal == 'SELL' else 0
            actual_direction = np.sign(actual_outcome)
            accuracy = 1.0 if pred_direction == actual_direction else 0.0
        
        # Store prediction result
        result = {
            'timestamp': timestamp,
            'prediction': prediction,
            'actual_outcome': actual_outcome,
            'accuracy': accuracy,
            'confidence': prediction.confidence
        }
        
        self.prediction_history.append(result)
        
        # Keep only recent predictions
        cutoff_time = timestamp - timedelta(hours=self.config.time_decay_hours * 10)
        self.prediction_history = [
            p for p in self.prediction_history 
            if p['timestamp'] > cutoff_time
        ]
        
        # Update model-specific performance
        if prediction.model_predictions:
            for model_name, model_pred in prediction.model_predictions.items():
                if model_name not in self.model_performance:
                    self.model_performance[model_name] = []
                
                # Calculate model-specific accuracy
                if isinstance(model_pred, dict):
                    model_signal = model_pred.get('signal', 'HOLD')
                else:
                    model_signal = str(model_pred)
                
                if isinstance(actual_outcome, str):
                    model_accuracy = 1.0 if model_signal == actual_outcome else 0.0
                else:
                    model_dir = 1 if model_signal == 'BUY' else -1 if model_signal == 'SELL' else 0
                    model_accuracy = 1.0 if model_dir == np.sign(actual_outcome) else 0.0
                
                self.model_performance[model_name].append({
                    'timestamp': timestamp,
                    'accuracy': model_accuracy,
                    'confidence': model_pred.get('confidence', 0.5) if isinstance(model_pred, dict) else 0.5
                })
                
                # Keep recent performance only
                self.model_performance[model_name] = [
                    p for p in self.model_performance[model_name][-self.config.performance_window:]
                ]
    
    def get_historical_confidence(self, 
                                 prediction: PredictionResult,
                                 current_time: Optional[datetime] = None) -> float:
        """Get confidence based on historical performance."""
        
        current_time = current_time or datetime.now()
        
        if len(self.prediction_history) < self.config.min_historical_samples:
            return 0.5  # Neutral confidence if insufficient history
        
        # Get recent performance with time decay
        recent_performance = []
        
        for pred_result in self.prediction_history[-self.config.performance_window:]:
            # Apply time decay
            hours_ago = (current_time - pred_result['timestamp']).total_seconds() / 3600
            decay_factor = self.config.performance_decay_factor ** (hours_ago / self.config.time_decay_hours)
            
            weighted_accuracy = pred_result['accuracy'] * decay_factor
            recent_performance.append(weighted_accuracy)
        
        # Calculate confidence-weighted performance
        if recent_performance:
            historical_confidence = np.mean(recent_performance)
        else:
            historical_confidence = 0.5
        
        return min(1.0, max(0.0, historical_confidence))
    
    def get_model_performance(self, model_name: str) -> float:
        """Get performance score for a specific model."""
        
        if model_name not in self.model_performance or not self.model_performance[model_name]:
            return 0.5
        
        recent_performance = self.model_performance[model_name][-20:]  # Last 20 predictions
        accuracies = [p['accuracy'] for p in recent_performance]
        
        return np.mean(accuracies) if accuracies else 0.5
    
    def get_ensemble_performance(self) -> float:
        """Get ensemble performance score."""
        
        if len(self.prediction_history) < self.config.min_historical_samples:
            return 0.5
        
        recent_predictions = self.prediction_history[-self.config.performance_window:]
        
        # Filter for ensemble predictions (those with agreement scores)
        ensemble_predictions = [
            p for p in recent_predictions 
            if p['prediction'].ensemble_agreement is not None
        ]
        
        if not ensemble_predictions:
            return 0.5
        
        # Weight by agreement score
        weighted_accuracy = 0
        total_weight = 0
        
        for pred in ensemble_predictions:
            agreement = pred['prediction'].ensemble_agreement
            accuracy = pred['accuracy']
            weight = agreement  # Higher agreement = higher weight
            
            weighted_accuracy += accuracy * weight
            total_weight += weight
        
        return weighted_accuracy / total_weight if total_weight > 0 else 0.5


class MarketConditionAnalyzer:
    """Analyzes market conditions for confidence scoring."""
    
    def __init__(self, config: ConfidenceConfig):
        self.config = config
    
    def analyze_conditions(self, 
                          market_data: pd.DataFrame,
                          prediction: PredictionResult) -> ConfidenceFactors:
        """Analyze market conditions and return confidence factors."""
        
        factors = ConfidenceFactors()
        
        try:
            # Volume analysis
            factors.volume_factor = self._analyze_volume(market_data)
            
            # Volatility analysis
            factors.volatility_factor = self._analyze_volatility(market_data)
            
            # Trend analysis
            factors.trend_alignment = self._analyze_trend_alignment(market_data, prediction)
            
            # Market structure
            factors.market_conditions = self._analyze_market_structure(market_data)
            
        except Exception as e:
            logger.warning(f"Error analyzing market conditions: {e}")
            # Return neutral factors on error
            
        return factors
    
    def _analyze_volume(self, market_data: pd.DataFrame) -> float:
        """Analyze volume conditions."""
        
        if 'volume' not in market_data.columns or len(market_data) < 20:
            return 0.5
        
        current_volume = market_data['volume'].iloc[-1]
        avg_volume = market_data['volume'].tail(20).mean()
        volume_std = market_data['volume'].tail(20).std()
        
        # Normalize volume ratio
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # Optimal volume is slightly above average
        if 1.2 <= volume_ratio <= 3.0:
            volume_confidence = 0.8  # Good volume
        elif 0.8 <= volume_ratio < 1.2:
            volume_confidence = 0.6  # Decent volume
        elif volume_ratio > 3.0:
            volume_confidence = 0.4  # Too high volume (might be chaotic)
        else:
            volume_confidence = 0.3  # Low volume
        
        return volume_confidence
    
    def _analyze_volatility(self, market_data: pd.DataFrame) -> float:
        """Analyze volatility conditions."""
        
        if 'close' not in market_data.columns or len(market_data) < 30:
            return 0.5
        
        # Calculate recent volatility
        returns = market_data['close'].pct_change().tail(20)
        current_vol = returns.std()
        
        # Calculate longer-term volatility for comparison
        long_returns = market_data['close'].pct_change().tail(100)
        historical_vol = long_returns.std()
        
        if historical_vol == 0:
            return 0.5
        
        vol_ratio = current_vol / historical_vol
        
        # Moderate volatility is optimal for predictions
        if 0.7 <= vol_ratio <= 1.3:
            vol_confidence = 0.8  # Normal volatility
        elif 0.5 <= vol_ratio < 0.7:
            vol_confidence = 0.6  # Low volatility
        elif 1.3 < vol_ratio <= 2.0:
            vol_confidence = 0.5  # High volatility
        else:
            vol_confidence = 0.3  # Extreme volatility
        
        return vol_confidence
    
    def _analyze_trend_alignment(self, 
                                market_data: pd.DataFrame,
                                prediction: PredictionResult) -> float:
        """Analyze trend alignment with prediction."""
        
        if 'close' not in market_data.columns or len(market_data) < 50:
            return 0.5
        
        close = market_data['close']
        
        # Calculate multiple timeframe trends
        sma_10 = close.tail(10).mean()
        sma_20 = close.tail(20).mean()
        sma_50 = close.tail(50).mean()
        current_price = close.iloc[-1]
        
        # Determine trend direction
        short_trend = 1 if current_price > sma_10 > sma_20 else -1 if current_price < sma_10 < sma_20 else 0
        medium_trend = 1 if sma_10 > sma_20 > sma_50 else -1 if sma_10 < sma_20 < sma_50 else 0
        
        # Get prediction direction
        pred_direction = 1 if prediction.signal == 'BUY' else -1 if prediction.signal == 'SELL' else 0
        
        # Calculate alignment
        if pred_direction == 0:  # HOLD signal
            return 0.6  # Neutral alignment
        
        alignment_score = 0.5  # Base score
        
        # Short-term alignment
        if short_trend == pred_direction:
            alignment_score += 0.2
        elif short_trend == -pred_direction:
            alignment_score -= 0.2
        
        # Medium-term alignment
        if medium_trend == pred_direction:
            alignment_score += 0.3
        elif medium_trend == -pred_direction:
            alignment_score -= 0.3
        
        return max(0.0, min(1.0, alignment_score))
    
    def _analyze_market_structure(self, market_data: pd.DataFrame) -> float:
        """Analyze overall market structure."""
        
        if len(market_data) < 50:
            return 0.5
        
        structure_score = 0.5
        
        try:
            # Price action quality
            if 'high' in market_data.columns and 'low' in market_data.columns:
                ranges = market_data['high'] - market_data['low']
                avg_range = ranges.tail(20).mean()
                recent_range = ranges.iloc[-1]
                
                # Prefer moderate ranges
                range_ratio = recent_range / avg_range if avg_range > 0 else 1.0
                if 0.8 <= range_ratio <= 1.5:
                    structure_score += 0.1
                elif range_ratio > 2.0:
                    structure_score -= 0.2  # Too volatile
            
            # Momentum consistency
            if 'close' in market_data.columns:
                close = market_data['close']
                momentum_5 = close.iloc[-1] / close.iloc[-6] - 1 if len(close) >= 6 else 0
                momentum_10 = close.iloc[-1] / close.iloc[-11] - 1 if len(close) >= 11 else 0
                
                # Consistent momentum is good
                if np.sign(momentum_5) == np.sign(momentum_10) and abs(momentum_5) > 0.001:
                    structure_score += 0.1
        
        except Exception as e:
            logger.warning(f"Error in market structure analysis: {e}")
        
        return max(0.0, min(1.0, structure_score))


class ConfidenceScorer:
    """Main confidence scoring system."""
    
    def __init__(self, config: Optional[ConfidenceConfig] = None):
        self.config = config or ConfidenceConfig()
        self.performance_tracker = HistoricalPerformanceTracker(self.config)
        self.market_analyzer = MarketConditionAnalyzer(self.config)
        
        logger.info("Initialized ConfidenceScorer")
    
    def score_prediction(self, 
                        prediction: PredictionResult,
                        market_data: pd.DataFrame,
                        current_time: Optional[datetime] = None) -> ConfidenceResult:
        """Score prediction confidence comprehensively."""
        
        current_time = current_time or datetime.now()
        
        # Get base confidence from prediction
        base_confidence = prediction.confidence
        
        # Analyze individual factors
        factors = self._analyze_confidence_factors(prediction, market_data, current_time)
        
        # Calculate weighted confidence
        weighted_confidence = self._calculate_weighted_confidence(factors, base_confidence)
        
        # Apply adjustments
        adjusted_confidence, adjustments = self._apply_adjustments(
            weighted_confidence, factors, prediction, market_data
        )
        
        # Generate warnings
        warnings = self._generate_warnings(factors, prediction, adjusted_confidence)
        
        # Determine confidence level
        confidence_level = self._determine_confidence_level(adjusted_confidence)
        
        # Calculate factor contributions
        factor_contributions = self._calculate_factor_contributions(factors, base_confidence)
        
        result = ConfidenceResult(
            final_confidence=adjusted_confidence,
            base_confidence=base_confidence,
            confidence_factors=factors,
            adjustments=adjustments,
            warnings=warnings,
            factor_contributions=factor_contributions,
            confidence_level=confidence_level
        )
        
        return result
    
    def _analyze_confidence_factors(self, 
                                   prediction: PredictionResult,
                                   market_data: pd.DataFrame,
                                   current_time: datetime) -> ConfidenceFactors:
        """Analyze all confidence factors."""
        
        factors = ConfidenceFactors()
        
        # Model confidence (from prediction)
        factors.model_confidence = prediction.confidence
        
        # Ensemble agreement
        if prediction.ensemble_agreement is not None:
            factors.ensemble_agreement = prediction.ensemble_agreement
        
        # Historical performance
        factors.historical_performance = self.performance_tracker.get_historical_confidence(
            prediction, current_time
        )
        
        # Market conditions
        market_factors = self.market_analyzer.analyze_conditions(market_data, prediction)
        factors.volume_factor = market_factors.volume_factor
        factors.volatility_factor = market_factors.volatility_factor
        factors.trend_alignment = market_factors.trend_alignment
        factors.market_conditions = market_factors.market_conditions
        
        # Prediction consistency
        factors.prediction_consistency = self._assess_prediction_consistency(prediction)
        
        # Signal strength
        factors.signal_strength = self._assess_signal_strength(prediction)
        
        # Risk assessment
        factors.risk_assessment = self._assess_risk_factors(prediction, market_data)
        
        return factors
    
    def _calculate_weighted_confidence(self, 
                                     factors: ConfidenceFactors,
                                     base_confidence: float) -> float:
        """Calculate weighted confidence score."""
        
        config = self.config
        
        # Model confidence component
        model_component = factors.model_confidence * config.model_weight
        
        # Ensemble component
        ensemble_component = factors.ensemble_agreement * config.ensemble_weight
        
        # Historical component
        historical_component = factors.historical_performance * config.historical_weight
        
        # Market condition component
        market_score = (
            factors.volume_factor * config.volume_importance +
            factors.volatility_factor * config.volatility_importance +
            factors.trend_alignment * config.trend_importance
        )
        market_component = market_score * config.market_condition_weight
        
        # Volatility component
        volatility_component = factors.volatility_factor * config.volatility_weight
        
        # Combine components
        weighted_confidence = (
            model_component +
            ensemble_component +
            historical_component +
            market_component +
            volatility_component
        )
        
        return max(0.0, min(1.0, weighted_confidence))
    
    def _apply_adjustments(self, 
                          base_score: float,
                          factors: ConfidenceFactors,
                          prediction: PredictionResult,
                          market_data: pd.DataFrame) -> Tuple[float, Dict[str, float]]:
        """Apply additional adjustments to confidence score."""
        
        adjustments = {}
        adjusted_score = base_score
        
        # Risk adjustment
        if prediction.risk_score is not None:
            if prediction.risk_score > 0.7:
                risk_penalty = -self.config.max_confidence_penalty * 0.5
                adjustments['high_risk_penalty'] = risk_penalty
                adjusted_score += risk_penalty
            elif prediction.risk_score < 0.3:
                risk_boost = self.config.max_confidence_boost * 0.3
                adjustments['low_risk_boost'] = risk_boost
                adjusted_score += risk_boost
        
        # Consistency adjustment
        if factors.prediction_consistency > 0.8:
            consistency_boost = self.config.max_confidence_boost * 0.2
            adjustments['consistency_boost'] = consistency_boost
            adjusted_score += consistency_boost
        elif factors.prediction_consistency < 0.4:
            consistency_penalty = -self.config.max_confidence_penalty * 0.2
            adjustments['consistency_penalty'] = consistency_penalty
            adjusted_score += consistency_penalty
        
        # Market condition adjustment
        if factors.market_conditions > 0.8:
            market_boost = self.config.max_confidence_boost * 0.1
            adjustments['market_boost'] = market_boost
            adjusted_score += market_boost
        elif factors.market_conditions < 0.3:
            market_penalty = -self.config.max_confidence_penalty * 0.2
            adjustments['market_penalty'] = market_penalty
            adjusted_score += market_penalty
        
        # Signal strength adjustment
        if factors.signal_strength > 0.9:
            signal_boost = self.config.max_confidence_boost * 0.15
            adjustments['signal_strength_boost'] = signal_boost
            adjusted_score += signal_boost
        
        return max(0.0, min(1.0, adjusted_score)), adjustments
    
    def _assess_prediction_consistency(self, prediction: PredictionResult) -> float:
        """Assess prediction consistency across models."""
        
        if not prediction.model_predictions or len(prediction.model_predictions) < 2:
            return 0.5
        
        signals = []
        confidences = []
        
        for model_pred in prediction.model_predictions.values():
            if isinstance(model_pred, dict):
                signals.append(model_pred.get('signal', 'HOLD'))
                confidences.append(model_pred.get('confidence', 0.5))
            else:
                signals.append(str(model_pred))
                confidences.append(0.5)
        
        # Signal consistency
        from collections import Counter
        signal_counts = Counter(signals)
        max_agreement = max(signal_counts.values()) / len(signals)
        
        # Confidence consistency
        conf_std = np.std(confidences) if len(confidences) > 1 else 0
        conf_consistency = 1 - min(conf_std * 2, 1)  # Lower std = higher consistency
        
        # Combined consistency
        consistency = (max_agreement + conf_consistency) / 2
        
        return consistency
    
    def _assess_signal_strength(self, prediction: PredictionResult) -> float:
        """Assess overall signal strength."""
        
        strength = prediction.confidence
        
        # Boost for non-HOLD signals
        if prediction.signal != 'HOLD':
            strength += 0.1
        
        # Consider ensemble agreement
        if prediction.ensemble_agreement is not None:
            strength = (strength + prediction.ensemble_agreement) / 2
        
        # Consider alerts
        if prediction.alerts:
            for alert in prediction.alerts:
                if "STRONG_SIGNAL" in alert:
                    strength += 0.1
                elif "LOW_CONFIDENCE" in alert:
                    strength -= 0.1
        
        return max(0.0, min(1.0, strength))
    
    def _assess_risk_factors(self, 
                           prediction: PredictionResult,
                           market_data: pd.DataFrame) -> float:
        """Assess risk factors affecting confidence."""
        
        risk_score = 0.5  # Neutral risk
        
        # Use prediction risk score if available
        if prediction.risk_score is not None:
            risk_score = 1 - prediction.risk_score  # Invert risk to get confidence
        
        # Additional risk factors from market data
        try:
            if 'close' in market_data.columns and len(market_data) >= 20:
                # Volatility risk
                returns = market_data['close'].pct_change().tail(20)
                volatility = returns.std()
                
                if volatility > 0.05:  # High volatility
                    risk_score -= 0.2
                elif volatility < 0.01:  # Very low volatility
                    risk_score -= 0.1  # Also risky (might be illiquid)
        
        except Exception as e:
            logger.warning(f"Error assessing risk factors: {e}")
        
        return max(0.0, min(1.0, risk_score))
    
    def _generate_warnings(self, 
                          factors: ConfidenceFactors,
                          prediction: PredictionResult,
                          final_confidence: float) -> List[str]:
        """Generate warnings based on confidence factors."""
        
        warnings = []
        
        # Low confidence warning
        if final_confidence < 0.4:
            warnings.append("Overall confidence is low")
        
        # Model disagreement
        if factors.ensemble_agreement < 0.5:
            warnings.append("Models show significant disagreement")
        
        # Poor historical performance
        if factors.historical_performance < 0.4:
            warnings.append("Historical performance is poor")
        
        # Poor market conditions
        if factors.market_conditions < 0.4:
            warnings.append("Unfavorable market conditions")
        
        # High volatility
        if factors.volatility_factor < 0.4:
            warnings.append("High volatility environment")
        
        # Low volume
        if factors.volume_factor < 0.4:
            warnings.append("Low volume conditions")
        
        # Trend misalignment
        if factors.trend_alignment < 0.4:
            warnings.append("Signal against prevailing trend")
        
        # Risk warnings
        if prediction.risk_score and prediction.risk_score > 0.7:
            warnings.append("High risk score detected")
        
        return warnings
    
    def _determine_confidence_level(self, confidence: float) -> str:
        """Determine confidence level category."""
        
        if confidence >= 0.8:
            return "VERY_HIGH"
        elif confidence >= 0.65:
            return "HIGH"
        elif confidence >= 0.45:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _calculate_factor_contributions(self, 
                                      factors: ConfidenceFactors,
                                      base_confidence: float) -> Dict[str, float]:
        """Calculate how much each factor contributes to final confidence."""
        
        contributions = {
            'model_confidence': factors.model_confidence * self.config.model_weight,
            'ensemble_agreement': factors.ensemble_agreement * self.config.ensemble_weight,
            'historical_performance': factors.historical_performance * self.config.historical_weight,
            'market_conditions': factors.market_conditions * self.config.market_condition_weight,
            'volatility_factor': factors.volatility_factor * self.config.volatility_weight,
            'volume_factor': factors.volume_factor * (self.config.market_condition_weight * self.config.volume_importance),
            'trend_alignment': factors.trend_alignment * (self.config.market_condition_weight * self.config.trend_importance)
        }
        
        return contributions
    
    def update_performance(self, 
                          prediction: PredictionResult,
                          actual_outcome: Union[str, float],
                          timestamp: Optional[datetime] = None):
        """Update performance tracking with actual outcome."""
        
        self.performance_tracker.add_prediction_outcome(prediction, actual_outcome, timestamp)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        
        summary = {
            'total_predictions': len(self.performance_tracker.prediction_history),
            'recent_accuracy': self.performance_tracker.get_historical_confidence(None),
            'ensemble_performance': self.performance_tracker.get_ensemble_performance()
        }
        
        # Model-specific performance
        model_performance = {}
        for model_name in self.performance_tracker.model_performance:
            model_performance[model_name] = self.performance_tracker.get_model_performance(model_name)
        
        summary['model_performance'] = model_performance
        
        return summary