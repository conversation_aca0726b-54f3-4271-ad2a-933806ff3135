"""
Emergency Kill Switch Web Dashboard

Web interface for emergency control with big red buttons.
Accessible from any browser for instant system control.
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import asyncio
import logging
from datetime import datetime
import json
import os
from functools import wraps

from ..emergency.kill_switch import get_emergency_kill_switch, EmergencyLevel, EmergencyTrigger
from ..core.logger import get_logger

logger = get_logger(__name__)

app = Flask(__name__)
app.secret_key = os.urandom(24)  # Random secret key for sessions

# Simple authentication (in production, use proper auth)
EMERGENCY_PASSWORD = "EMERGENCY_2024"  # Change this!

def require_auth(f):
    """Require authentication for emergency functions"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth = request.authorization
        if not auth or auth.password != EMERGENCY_PASSWORD:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def dashboard():
    """Main emergency dashboard"""
    
    kill_switch = get_emergency_kill_switch()
    
    if kill_switch:
        status = kill_switch.get_emergency_status()
    else:
        status = {
            'emergency_active': False,
            'system_locked': False,
            'trading_halted': False
        }
    
    return render_template('emergency_dashboard.html', status=status)

@app.route('/api/status')
def api_status():
    """Get current system status"""
    
    kill_switch = get_emergency_kill_switch()
    
    if kill_switch:
        status = kill_switch.get_emergency_status()
    else:
        status = {
            'emergency_active': False,
            'system_locked': False,
            'trading_halted': False,
            'error': 'Kill switch not initialized'
        }
    
    return jsonify(status)

@app.route('/api/emergency/stop_trading', methods=['POST'])
@require_auth
def api_stop_trading():
    """Stop all trading immediately"""
    
    try:
        kill_switch = get_emergency_kill_switch()
        
        if not kill_switch:
            return jsonify({'error': 'Kill switch not available'}), 500
        
        reason = request.json.get('reason', 'Web dashboard stop trading')
        user_id = request.json.get('user_id', 'web_user')
        
        # Run async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        success = loop.run_until_complete(
            kill_switch.activate_emergency(
                EmergencyLevel.STOP_TRADING,
                EmergencyTrigger.WEB_DASHBOARD,
                reason,
                user_id
            )
        )
        
        loop.close()
        
        if success:
            return jsonify({'success': True, 'message': 'Trading stopped successfully'})
        else:
            return jsonify({'error': 'Failed to stop trading'}), 500
            
    except Exception as e:
        logger.error(f"Error stopping trading: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emergency/close_positions', methods=['POST'])
@require_auth
def api_close_positions():
    """Close all positions immediately"""
    
    try:
        kill_switch = get_emergency_kill_switch()
        
        if not kill_switch:
            return jsonify({'error': 'Kill switch not available'}), 500
        
        reason = request.json.get('reason', 'Web dashboard close positions')
        user_id = request.json.get('user_id', 'web_user')
        
        # Run async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        success = loop.run_until_complete(
            kill_switch.activate_emergency(
                EmergencyLevel.CLOSE_POSITIONS,
                EmergencyTrigger.WEB_DASHBOARD,
                reason,
                user_id
            )
        )
        
        loop.close()
        
        if success:
            return jsonify({'success': True, 'message': 'All positions closed successfully'})
        else:
            return jsonify({'error': 'Failed to close positions'}), 500
            
    except Exception as e:
        logger.error(f"Error closing positions: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emergency/full_shutdown', methods=['POST'])
@require_auth
def api_full_shutdown():
    """Full system shutdown"""
    
    try:
        kill_switch = get_emergency_kill_switch()
        
        if not kill_switch:
            return jsonify({'error': 'Kill switch not available'}), 500
        
        reason = request.json.get('reason', 'Web dashboard full shutdown')
        user_id = request.json.get('user_id', 'web_user')
        
        # Run async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        success = loop.run_until_complete(
            kill_switch.activate_emergency(
                EmergencyLevel.FULL_SHUTDOWN,
                EmergencyTrigger.WEB_DASHBOARD,
                reason,
                user_id
            )
        )
        
        loop.close()
        
        if success:
            return jsonify({'success': True, 'message': 'System shutdown initiated'})
        else:
            return jsonify({'error': 'Failed to shutdown system'}), 500
            
    except Exception as e:
        logger.error(f"Error shutting down system: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emergency/system_lock', methods=['POST'])
@require_auth
def api_system_lock():
    """Lock the entire system"""
    
    try:
        kill_switch = get_emergency_kill_switch()
        
        if not kill_switch:
            return jsonify({'error': 'Kill switch not available'}), 500
        
        reason = request.json.get('reason', 'Web dashboard system lock')
        user_id = request.json.get('user_id', 'web_user')
        
        # Run async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        success = loop.run_until_complete(
            kill_switch.activate_emergency(
                EmergencyLevel.SYSTEM_LOCK,
                EmergencyTrigger.WEB_DASHBOARD,
                reason,
                user_id
            )
        )
        
        loop.close()
        
        if success:
            return jsonify({'success': True, 'message': 'System locked successfully'})
        else:
            return jsonify({'error': 'Failed to lock system'}), 500
            
    except Exception as e:
        logger.error(f"Error locking system: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emergency/deactivate', methods=['POST'])
@require_auth
def api_deactivate_emergency():
    """Deactivate emergency mode"""
    
    try:
        kill_switch = get_emergency_kill_switch()
        
        if not kill_switch:
            return jsonify({'error': 'Kill switch not available'}), 500
        
        user_id = request.json.get('user_id', 'web_user')
        
        # Run async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        success = loop.run_until_complete(
            kill_switch.deactivate_emergency(user_id)
        )
        
        loop.close()
        
        if success:
            return jsonify({'success': True, 'message': 'Emergency mode deactivated'})
        else:
            return jsonify({'error': 'Failed to deactivate emergency mode'}), 500
            
    except Exception as e:
        logger.error(f"Error deactivating emergency: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emergency/log')
def api_emergency_log():
    """Get emergency log"""
    
    try:
        kill_switch = get_emergency_kill_switch()
        
        if not kill_switch:
            return jsonify({'error': 'Kill switch not available'}), 500
        
        return jsonify({
            'log': kill_switch.emergency_log[-10:],  # Last 10 events
            'total_events': len(kill_switch.emergency_log)
        })
        
    except Exception as e:
        logger.error(f"Error getting emergency log: {e}")
        return jsonify({'error': str(e)}), 500

def create_emergency_dashboard_app(kill_switch=None):
    """Create Flask app with kill switch instance"""
    
    if kill_switch:
        # Store kill switch in app config
        app.config['KILL_SWITCH'] = kill_switch
    
    return app

def run_emergency_dashboard(host='0.0.0.0', port=5000, debug=False):
    """Run the emergency dashboard server"""
    
    logger.info(f"🚨 Starting Emergency Dashboard on http://{host}:{port}")
    logger.info("🚨 Access the dashboard to control the trading bot")
    
    app.run(host=host, port=port, debug=debug)

if __name__ == "__main__":
    # Run the dashboard
    run_emergency_dashboard(debug=True)
