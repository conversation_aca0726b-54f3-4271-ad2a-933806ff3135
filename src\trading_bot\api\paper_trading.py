"""Paper trading API client for Webull."""

from decimal import Decimal
from typing import Dict, List, Optional

from ..models.enums import APIEndpointType
from ..models.orders import Order, OrderRequest, Position
from ..utils.logger import get_structured_logger
from .trading import TradingClient

logger = get_structured_logger(__name__)


class PaperTradingClient(TradingClient):
    """Paper trading client with additional simulation features."""
    
    def __init__(self):
        super().__init__()
        # Force paper trading mode
        self.is_paper_trading = True
        
    async def reset_account(self, initial_balance: Optional[float] = None) -> bool:
        """
        Reset paper trading account to initial state.
        
        Args:
            initial_balance: Starting balance (uses default if None)
            
        Returns:
            True if reset successful
        """
        await self.ensure_authenticated()
        
        try:
            data = {}
            if initial_balance is not None:
                data["initialBalance"] = initial_balance
            
            response = await self._make_request(
                "POST",
                APIEndpointType.PAPER,
                "reset_account",
                data=data,
                auth_required=True,
                use_cache=False,
            )
            
            success = response.get("success", False)
            
            if success:
                logger.info(
                    "Paper trading account reset",
                    extra={
                        "initial_balance": initial_balance,
                        "account_id": self.account_id,
                    }
                )
            else:
                logger.error(
                    f"Failed to reset paper trading account: {response.get('msg', 'Unknown error')}"
                )
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to reset paper trading account: {e}")
            raise
    
    async def simulate_market_order_fill(
        self,
        order: Order,
        fill_price: Optional[float] = None,
    ) -> Order:
        """
        Simulate immediate fill of a market order.
        
        Args:
            order: Order to simulate fill for
            fill_price: Price to fill at (uses current market price if None)
            
        Returns:
            Updated order with fill information
        """
        if not fill_price:
            # Get current market price
            quote = await self.get_quote(order.symbol)
            fill_price = float(quote.price)
        
        # Update order with fill information
        order.filled_quantity = order.quantity
        order.avg_fill_price = Decimal(str(fill_price))
        order.status = order.status.FILLED
        
        logger.order_event(
            "simulated_fill",
            order_id=order.id,
            symbol=order.symbol,
            side=order.side.value,
            quantity=order.filled_quantity,
            price=fill_price,
            status=order.status.value,
        )
        
        return order
    
    async def get_paper_performance(self) -> Dict[str, float]:
        """
        Get paper trading performance metrics.
        
        Returns:
            Dictionary with performance metrics
        """
        try:
            portfolio = await self.get_portfolio()
            positions = portfolio.positions
            
            # Calculate performance metrics
            total_pnl = sum(pos.total_pnl for pos in positions)
            total_cost = sum(pos.cost_basis or Decimal("0") for pos in positions)
            
            performance = {
                "total_pnl": float(total_pnl),
                "total_return_pct": float((total_pnl / total_cost) * 100) if total_cost > 0 else 0.0,
                "day_pnl": float(portfolio.balance.day_pnl),
                "day_return_pct": portfolio.balance.day_pnl_percentage,
                "total_value": float(portfolio.balance.total_value),
                "cash": float(portfolio.balance.cash),
                "positions_value": float(portfolio.balance.positions_value),
                "position_count": len(positions),
                "long_positions": len([p for p in positions if p.is_long]),
                "short_positions": len([p for p in positions if p.is_short]),
            }
            
            # Calculate win/loss statistics
            winning_positions = [p for p in positions if p.total_pnl > 0]
            losing_positions = [p for p in positions if p.total_pnl < 0]
            
            performance.update({
                "winning_positions": len(winning_positions),
                "losing_positions": len(losing_positions),
                "win_rate": len(winning_positions) / len(positions) if positions else 0.0,
                "avg_win": float(sum(p.total_pnl for p in winning_positions) / len(winning_positions)) if winning_positions else 0.0,
                "avg_loss": float(sum(p.total_pnl for p in losing_positions) / len(losing_positions)) if losing_positions else 0.0,
            })
            
            # Calculate profit factor
            total_wins = sum(p.total_pnl for p in winning_positions)
            total_losses = abs(sum(p.total_pnl for p in losing_positions))
            performance["profit_factor"] = float(total_wins / total_losses) if total_losses > 0 else float('inf')
            
            logger.performance_metric(
                "paper_trading_performance",
                performance["total_return_pct"],
                unit="percent",
                tags={
                    "account_type": "paper",
                    "position_count": str(len(positions)),
                    "win_rate": str(performance["win_rate"]),
                }
            )
            
            return performance
            
        except Exception as e:
            logger.error(f"Failed to get paper performance: {e}")
            raise
    
    async def simulate_order_scenarios(
        self,
        order_request: OrderRequest,
        scenarios: List[Dict[str, float]],
    ) -> List[Dict[str, any]]:
        """
        Simulate order execution under different market scenarios.
        
        Args:
            order_request: Order to simulate
            scenarios: List of scenarios with price and volume data
            
        Returns:
            List of simulation results
        """
        results = []
        
        for i, scenario in enumerate(scenarios):
            scenario_name = scenario.get("name", f"Scenario {i+1}")
            market_price = scenario.get("price", 0.0)
            volume = scenario.get("volume", 1000000)
            volatility = scenario.get("volatility", 0.01)
            
            # Simulate order execution
            simulation_result = {
                "scenario": scenario_name,
                "market_price": market_price,
                "volume": volume,
                "volatility": volatility,
                "order_type": order_request.order_type.value,
                "order_side": order_request.side.value,
                "order_quantity": order_request.quantity,
            }
            
            # Simulate fill based on order type
            if order_request.order_type.value == "MKT":
                # Market order - immediate fill with slippage
                slippage = market_price * volatility * 0.1  # 10% of volatility
                if order_request.side.value == "BUY":
                    fill_price = market_price + slippage
                else:
                    fill_price = market_price - slippage
                
                simulation_result.update({
                    "filled": True,
                    "fill_price": fill_price,
                    "fill_quantity": order_request.quantity,
                    "slippage": slippage,
                })
                
            elif order_request.order_type.value == "LMT":
                # Limit order - fill only if price is favorable
                limit_price = float(order_request.price)
                
                if order_request.side.value == "BUY":
                    filled = market_price <= limit_price
                    fill_price = min(market_price, limit_price) if filled else None
                else:
                    filled = market_price >= limit_price
                    fill_price = max(market_price, limit_price) if filled else None
                
                simulation_result.update({
                    "filled": filled,
                    "fill_price": fill_price,
                    "fill_quantity": order_request.quantity if filled else 0,
                    "limit_price": limit_price,
                })
            
            # Calculate P&L impact
            if simulation_result.get("filled"):
                fill_price = simulation_result["fill_price"]
                quantity = simulation_result["fill_quantity"]
                
                if order_request.side.value == "BUY":
                    cost = fill_price * quantity
                    simulation_result["cost"] = cost
                    simulation_result["market_value"] = market_price * quantity
                    simulation_result["unrealized_pnl"] = (market_price - fill_price) * quantity
                else:
                    proceeds = fill_price * quantity
                    simulation_result["proceeds"] = proceeds
                    simulation_result["market_value"] = market_price * quantity
                    simulation_result["unrealized_pnl"] = (fill_price - market_price) * quantity
            
            results.append(simulation_result)
        
        logger.info(
            f"Simulated order execution for {len(scenarios)} scenarios",
            extra={
                "symbol": order_request.symbol,
                "order_type": order_request.order_type.value,
                "scenarios_count": len(scenarios),
            }
        )
        
        return results
    
    async def get_paper_trading_limits(self) -> Dict[str, any]:
        """
        Get paper trading account limits and restrictions.
        
        Returns:
            Dictionary with account limits
        """
        try:
            account_summary = await self.get_account_summary()
            
            limits = {
                "max_position_value": 50000.0,  # $50k max per position
                "max_daily_trades": 100,        # 100 trades per day
                "max_open_orders": 50,          # 50 open orders
                "min_order_value": 1.0,         # $1 minimum order
                "max_order_value": 100000.0,    # $100k maximum order
                "available_buying_power": float(account_summary.balance.buying_power),
                "day_trading_enabled": True,
                "short_selling_enabled": True,
                "options_trading_enabled": False,
                "crypto_trading_enabled": False,
            }
            
            logger.info("Retrieved paper trading limits", extra=limits)
            
            return limits
            
        except Exception as e:
            logger.error(f"Failed to get paper trading limits: {e}")
            raise
