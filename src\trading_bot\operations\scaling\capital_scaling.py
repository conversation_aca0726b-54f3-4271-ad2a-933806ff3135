"""
Capital Scaling System

Progressive capital deployment based on performance metrics and risk management.
Implements staged scaling with strict criteria for each level.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
import numpy as np
from enum import Enum

from ...core.logger import get_logger
from ...risk.manager import RiskManager
from ..continuous_improvement.performance_analyzer import PerformanceAnalyzer, PerformanceMetrics

logger = get_logger(__name__)

class ScalingStage(Enum):
    """Capital scaling stages"""
    PAPER_TRADING = "paper_trading"
    STAGE_1 = "stage_1"  # 10% capital
    STAGE_2 = "stage_2"  # 25% capital
    STAGE_3 = "stage_3"  # 50% capital
    STAGE_4 = "stage_4"  # 100% capital

@dataclass
class ScalingCriteria:
    """Criteria for advancing to next scaling stage"""
    min_profitable_months: int
    min_sharpe_ratio: float
    max_drawdown: float
    min_win_rate: float
    min_system_uptime: float
    min_total_trades: int
    min_profit_factor: float
    additional_requirements: Dict[str, Any] = None

@dataclass
class ScalingStageConfig:
    """Configuration for a scaling stage"""
    stage: ScalingStage
    capital_percentage: float
    max_positions: int
    max_position_size: float
    max_sector_exposure: float
    criteria: ScalingCriteria
    duration_days: int

class CapitalScaler:
    """Progressive capital deployment based on performance"""
    
    def __init__(self, 
                 performance_analyzer: PerformanceAnalyzer,
                 risk_manager: RiskManager,
                 total_capital: float):
        self.performance_analyzer = performance_analyzer
        self.risk_manager = risk_manager
        self.total_capital = total_capital
        self.current_stage = ScalingStage.PAPER_TRADING
        self.stage_start_date = datetime.now()
        
        # Define scaling stages
        self.scaling_stages = {
            ScalingStage.PAPER_TRADING: ScalingStageConfig(
                stage=ScalingStage.PAPER_TRADING,
                capital_percentage=0.0,
                max_positions=3,
                max_position_size=0.01,  # 1% of capital
                max_sector_exposure=0.2,  # 20%
                criteria=ScalingCriteria(
                    min_profitable_months=1,
                    min_sharpe_ratio=1.0,
                    max_drawdown=0.1,
                    min_win_rate=0.55,
                    min_system_uptime=0.95,
                    min_total_trades=50,
                    min_profit_factor=1.2
                ),
                duration_days=30
            ),
            ScalingStage.STAGE_1: ScalingStageConfig(
                stage=ScalingStage.STAGE_1,
                capital_percentage=0.1,  # 10%
                max_positions=5,
                max_position_size=0.02,  # 2% of capital
                max_sector_exposure=0.25,
                criteria=ScalingCriteria(
                    min_profitable_months=2,
                    min_sharpe_ratio=1.2,
                    max_drawdown=0.08,
                    min_win_rate=0.58,
                    min_system_uptime=0.98,
                    min_total_trades=100,
                    min_profit_factor=1.3
                ),
                duration_days=60
            ),
            ScalingStage.STAGE_2: ScalingStageConfig(
                stage=ScalingStage.STAGE_2,
                capital_percentage=0.25,  # 25%
                max_positions=10,
                max_position_size=0.03,  # 3% of capital
                max_sector_exposure=0.3,
                criteria=ScalingCriteria(
                    min_profitable_months=3,
                    min_sharpe_ratio=1.5,
                    max_drawdown=0.1,
                    min_win_rate=0.6,
                    min_system_uptime=0.99,
                    min_total_trades=200,
                    min_profit_factor=1.4
                ),
                duration_days=90
            ),
            ScalingStage.STAGE_3: ScalingStageConfig(
                stage=ScalingStage.STAGE_3,
                capital_percentage=0.5,  # 50%
                max_positions=15,
                max_position_size=0.04,  # 4% of capital
                max_sector_exposure=0.35,
                criteria=ScalingCriteria(
                    min_profitable_months=6,
                    min_sharpe_ratio=1.8,
                    max_drawdown=0.12,
                    min_win_rate=0.62,
                    min_system_uptime=0.995,
                    min_total_trades=500,
                    min_profit_factor=1.5
                ),
                duration_days=180
            ),
            ScalingStage.STAGE_4: ScalingStageConfig(
                stage=ScalingStage.STAGE_4,
                capital_percentage=1.0,  # 100%
                max_positions=20,
                max_position_size=0.05,  # 5% of capital
                max_sector_exposure=0.4,
                criteria=ScalingCriteria(
                    min_profitable_months=12,
                    min_sharpe_ratio=2.0,
                    max_drawdown=0.15,
                    min_win_rate=0.65,
                    min_system_uptime=0.999,
                    min_total_trades=1000,
                    min_profit_factor=1.6
                ),
                duration_days=365
            )
        }
        
    async def evaluate_scaling_readiness(self) -> Dict[str, Any]:
        """Check if ready to scale to next level"""
        logger.info(f"Evaluating scaling readiness from {self.current_stage.value}")
        
        current_config = self.scaling_stages[self.current_stage]
        
        # Check if minimum duration has passed
        days_in_stage = (datetime.now() - self.stage_start_date).days
        if days_in_stage < current_config.duration_days:
            return {
                'ready_to_scale': False,
                'reason': f'Minimum duration not met ({days_in_stage}/{current_config.duration_days} days)',
                'current_stage': self.current_stage.value,
                'next_stage': None,
                'criteria_met': {}
            }
        
        # Get next stage
        next_stage = self._get_next_stage()
        if not next_stage:
            return {
                'ready_to_scale': False,
                'reason': 'Already at maximum scaling stage',
                'current_stage': self.current_stage.value,
                'next_stage': None,
                'criteria_met': {}
            }
        
        next_config = self.scaling_stages[next_stage]
        
        # Evaluate criteria
        criteria_met = await self._evaluate_criteria(next_config.criteria)
        
        all_criteria_met = all(criteria_met.values())
        
        return {
            'ready_to_scale': all_criteria_met,
            'reason': 'All criteria met' if all_criteria_met else 'Some criteria not met',
            'current_stage': self.current_stage.value,
            'next_stage': next_stage.value,
            'criteria_met': criteria_met,
            'next_config': {
                'capital_percentage': next_config.capital_percentage,
                'max_positions': next_config.max_positions,
                'max_position_size': next_config.max_position_size
            }
        }
    
    async def scale_up(self, force: bool = False) -> bool:
        """Scale up to the next level"""
        evaluation = await self.evaluate_scaling_readiness()
        
        if not evaluation['ready_to_scale'] and not force:
            logger.warning(f"Cannot scale up: {evaluation['reason']}")
            return False
        
        next_stage = self._get_next_stage()
        if not next_stage:
            logger.warning("Already at maximum scaling stage")
            return False
        
        # Perform scaling
        old_stage = self.current_stage
        self.current_stage = next_stage
        self.stage_start_date = datetime.now()
        
        # Update risk limits
        await self._update_risk_limits()
        
        logger.info(f"Successfully scaled from {old_stage.value} to {next_stage.value}")
        
        # Log scaling event
        await self._log_scaling_event(old_stage, next_stage, evaluation)
        
        return True
    
    async def scale_down(self, reason: str = "Performance degradation") -> bool:
        """Scale down to previous level due to poor performance"""
        previous_stage = self._get_previous_stage()
        
        if not previous_stage:
            logger.warning("Already at minimum scaling stage")
            return False
        
        old_stage = self.current_stage
        self.current_stage = previous_stage
        self.stage_start_date = datetime.now()
        
        # Update risk limits
        await self._update_risk_limits()
        
        logger.warning(f"Scaled down from {old_stage.value} to {previous_stage.value}. Reason: {reason}")
        
        # Log scaling event
        await self._log_scaling_event(old_stage, previous_stage, {'reason': reason, 'type': 'scale_down'})
        
        return True
    
    async def get_current_limits(self) -> Dict[str, Any]:
        """Get current scaling limits"""
        config = self.scaling_stages[self.current_stage]
        
        return {
            'stage': self.current_stage.value,
            'available_capital': self.total_capital * config.capital_percentage,
            'max_positions': config.max_positions,
            'max_position_size': self.total_capital * config.max_position_size,
            'max_sector_exposure': config.max_sector_exposure,
            'days_in_stage': (datetime.now() - self.stage_start_date).days
        }
    
    async def monitor_scaling_triggers(self):
        """Monitor for automatic scaling triggers"""
        while True:
            try:
                # Check for scale up opportunity
                evaluation = await self.evaluate_scaling_readiness()
                if evaluation['ready_to_scale']:
                    await self.scale_up()
                
                # Check for scale down triggers
                should_scale_down = await self._check_scale_down_triggers()
                if should_scale_down['should_scale_down']:
                    await self.scale_down(should_scale_down['reason'])
                
                # Sleep before next check
                await asyncio.sleep(3600)  # Check every hour
                
            except Exception as e:
                logger.error(f"Error in scaling monitor: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    def _get_next_stage(self) -> Optional[ScalingStage]:
        """Get the next scaling stage"""
        stages = list(ScalingStage)
        current_index = stages.index(self.current_stage)
        
        if current_index < len(stages) - 1:
            return stages[current_index + 1]
        return None
    
    def _get_previous_stage(self) -> Optional[ScalingStage]:
        """Get the previous scaling stage"""
        stages = list(ScalingStage)
        current_index = stages.index(self.current_stage)
        
        if current_index > 0:
            return stages[current_index - 1]
        return None
    
    async def _evaluate_criteria(self, criteria: ScalingCriteria) -> Dict[str, bool]:
        """Evaluate scaling criteria"""
        # Get performance metrics
        metrics = await self.performance_analyzer.get_performance_metrics(90)  # 90 days
        
        criteria_met = {
            'profitable_months': await self._check_profitable_months(criteria.min_profitable_months),
            'sharpe_ratio': metrics.sharpe_ratio >= criteria.min_sharpe_ratio,
            'max_drawdown': metrics.max_drawdown <= criteria.max_drawdown,
            'win_rate': metrics.win_rate >= criteria.min_win_rate,
            'system_uptime': metrics.system_uptime >= criteria.min_system_uptime,
            'total_trades': metrics.total_trades >= criteria.min_total_trades,
            'profit_factor': metrics.profit_factor >= criteria.min_profit_factor
        }
        
        # Check additional requirements
        if criteria.additional_requirements:
            for req_name, req_value in criteria.additional_requirements.items():
                criteria_met[req_name] = await self._check_additional_requirement(req_name, req_value)
        
        return criteria_met
    
    async def _check_profitable_months(self, required_months: int) -> bool:
        """Check if we have enough profitable months"""
        # This would check actual monthly P&L
        # For now, simplified implementation
        months_data = await self._get_monthly_performance(12)  # Last 12 months
        profitable_months = sum(1 for month_pnl in months_data if month_pnl > 0)
        
        return profitable_months >= required_months
    
    async def _get_monthly_performance(self, months: int) -> List[float]:
        """Get monthly performance data"""
        # This would query actual monthly P&L data
        # For now, return simulated data
        return [np.random.normal(1000, 500) for _ in range(months)]
    
    async def _check_additional_requirement(self, req_name: str, req_value: Any) -> bool:
        """Check additional scaling requirements"""
        # This would implement custom requirements
        return True  # Simplified
    
    async def _update_risk_limits(self):
        """Update risk limits based on current scaling stage"""
        config = self.scaling_stages[self.current_stage]
        
        # Update risk manager limits
        await self.risk_manager.update_limits({
            'max_positions': config.max_positions,
            'max_position_size': config.max_position_size,
            'max_sector_exposure': config.max_sector_exposure,
            'available_capital': self.total_capital * config.capital_percentage
        })
        
        logger.info(f"Updated risk limits for {self.current_stage.value}")
    
    async def _check_scale_down_triggers(self) -> Dict[str, Any]:
        """Check if we should scale down"""
        metrics = await self.performance_analyzer.get_performance_metrics(30)  # Last 30 days
        
        # Check for performance degradation
        if metrics.sharpe_ratio < 0.5:
            return {
                'should_scale_down': True,
                'reason': f'Poor Sharpe ratio: {metrics.sharpe_ratio:.2f}'
            }
        
        if metrics.max_drawdown > 0.2:  # 20% drawdown
            return {
                'should_scale_down': True,
                'reason': f'High drawdown: {metrics.max_drawdown:.1%}'
            }
        
        if metrics.win_rate < 0.4:  # Very low win rate
            return {
                'should_scale_down': True,
                'reason': f'Low win rate: {metrics.win_rate:.1%}'
            }
        
        return {'should_scale_down': False, 'reason': None}
    
    async def _log_scaling_event(self, old_stage: ScalingStage, new_stage: ScalingStage, context: Dict):
        """Log scaling events for audit trail"""
        event = {
            'timestamp': datetime.now(),
            'old_stage': old_stage.value,
            'new_stage': new_stage.value,
            'context': context,
            'total_capital': self.total_capital
        }
        
        # This would log to database or file
        logger.info(f"Scaling event logged: {event}")
    
    def get_scaling_history(self) -> List[Dict]:
        """Get history of scaling events"""
        # This would return actual scaling history from database
        return []  # Placeholder
