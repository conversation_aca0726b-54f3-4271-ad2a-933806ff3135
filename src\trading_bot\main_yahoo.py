"""
Main Trading Bot Entry Point - Updated to use Yahoo Finance
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from .core.config import settings
from .core.logger import get_logger
from .data.yahoo_data_source import YahooDataSource
from .ml.pipeline import TradingMLPipeline
from .strategies.strategy_manager import StrategyManager
from .risk.manager import RiskManager
from .orchestration.master_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>, MasterControllerConfig
from .monitoring.advanced_monitoring import SystemMonitor
from .emergency.kill_switch import EmergencyKillSwitch
from .integration.event_bus import event_bus

logger = get_logger(__name__)


class TradingBot:
    """Main trading bot that uses Yahoo Finance data."""
    
    def __init__(self, config_path: Optional[str] = None):
        # Use existing settings or load from config
        self.config = settings
        
        # Initialize components
        self.data_source = YahooDataSource(self.config)
        self.ml_pipeline = None
        self.strategy_manager = None
        self.risk_manager = None
        self.system_monitor = None
        self.kill_switch = None
        self.master_controller = None
        
        # Shutdown event
        self.shutdown_event = asyncio.Event()
        
    async def initialize_components(self):
        """Initialize all trading bot components."""
        logger.info("Initializing trading bot components...")
        
        try:
            # Initialize data source first
            if not await self.data_source.initialize():
                raise Exception("Failed to initialize Yahoo Finance data source")
            
            # Initialize ML pipeline
            logger.info("Initializing ML pipeline...")
            self.ml_pipeline = TradingMLPipeline(config=self.config)
            
            # Initialize strategy manager
            logger.info("Initializing strategy manager...")
            self.strategy_manager = StrategyManager(self.config)
            
            # Initialize risk manager
            logger.info("Initializing risk manager...")
            self.risk_manager = RiskManager(self.config)
            
            # Initialize system monitor
            logger.info("Initializing system monitor...")
            self.system_monitor = SystemMonitor(self.config)
            
            # Initialize emergency kill switch
            logger.info("Initializing emergency kill switch...")
            self.kill_switch = EmergencyKillSwitch(self.config)
            
            # Initialize master controller with all components
            logger.info("Initializing master controller...")
            controller_config = MasterControllerConfig(
                max_concurrent_orders=getattr(self.config.trading, 'max_daily_trades', 100),
                max_daily_trades=getattr(self.config.trading, 'max_daily_trades', 100),
                emergency_stop_loss=getattr(self.config.risk, 'emergency_stop_loss', 0.05),
                max_drawdown_threshold=getattr(self.config.risk, 'max_drawdown_threshold', 0.10)
            )
            
            self.master_controller = MasterController(controller_config)
            
            # Set data source for master controller
            if hasattr(self.master_controller, 'set_data_source'):
                self.master_controller.set_data_source(self.data_source)
            
            # Initialize master controller
            if not await self.master_controller.initialize_system():
                raise Exception("Failed to initialize master controller")
            
            logger.info("✅ All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    async def load_ml_models(self):
        """Load ML models."""
        try:
            logger.info("Loading ML models...")
            
            if self.ml_pipeline:
                # Load existing models or train new ones
                await self.ml_pipeline.load_models()
                logger.info("✅ ML models loaded successfully")
            else:
                logger.warning("ML pipeline not initialized")
                
        except Exception as e:
            logger.error(f"Failed to load ML models: {e}")
            # Continue without ML models for now
    
    async def start_monitoring(self):
        """Start system monitoring."""
        try:
            if self.system_monitor:
                await self.system_monitor.start()
                logger.info("✅ System monitoring started")
            
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
    
    async def setup_watchlist_streaming(self):
        """Setup real-time data streaming for watchlist."""
        try:
            # Get watchlist from config
            watchlist = getattr(self.config.external_apis.webull.browser_automation, 'watchlist', [
                "AAPL", "MSFT", "GOOGL", "TSLA", "NVDA", "AMZN", "META", "NFLX"
            ])
            
            logger.info(f"Setting up streaming for watchlist: {watchlist}")
            
            async def handle_quote_update(quote):
                """Handle real-time quote updates."""
                try:
                    # Send to master controller for processing
                    if self.master_controller:
                        await self.master_controller.process_market_data(quote)
                    
                    # Publish to event bus
                    await event_bus.publish(
                        "market_data.quote_update",
                        "yahoo_data_source",
                        {
                            "symbol": quote.symbol,
                            "price": float(quote.price),
                            "volume": quote.volume,
                            "timestamp": quote.timestamp.isoformat()
                        }
                    )
                    
                except Exception as e:
                    logger.error(f"Error handling quote update for {quote.symbol}: {e}")
            
            # Start streaming (this will run in background)
            asyncio.create_task(
                self.data_source.stream_realtime_data(watchlist, handle_quote_update)
            )
            
            logger.info("✅ Real-time data streaming started")
            
        except Exception as e:
            logger.error(f"Failed to setup watchlist streaming: {e}")
    
    async def start(self):
        """Start the trading bot."""
        try:
            logger.info("🚀 Starting AI Trading Bot with Yahoo Finance...")
            
            # Initialize event bus
            await event_bus.initialize()
            
            # Initialize all components
            await self.initialize_components()
            
            # Load ML models
            await self.load_ml_models()
            
            # Start system monitoring
            await self.start_monitoring()
            
            # Setup real-time data streaming
            await self.setup_watchlist_streaming()
            
            # Start trading if configured
            if getattr(self.config.trading, 'auto_start', False):
                logger.info("Auto-starting trading operations...")
                if self.master_controller:
                    await self.master_controller.start_trading()
            
            logger.info("✅ Trading bot started successfully!")
            logger.info("Press Ctrl+C to stop the bot gracefully")
            
            # Keep running until interrupted
            await self.shutdown_event.wait()
                
        except KeyboardInterrupt:
            logger.info("Shutdown signal received")
        except Exception as e:
            logger.error(f"Fatal error: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Gracefully shutdown the bot."""
        if self.shutdown_event.is_set():
            return  # Already shutting down
            
        logger.info("🛑 Shutting down trading bot...")
        self.shutdown_event.set()
        
        try:
            # Activate kill switch to stop trading
            if self.kill_switch:
                await self.kill_switch.activate_emergency()
            
            # Stop all components
            if self.master_controller:
                await self.master_controller.stop_trading("System shutdown")
                await self.master_controller.shutdown()
            
            if self.system_monitor:
                await self.system_monitor.stop()
            
            if self.data_source:
                await self.data_source.cleanup()
            
            # Stop event bus
            await event_bus.shutdown()
            
            logger.info("✅ Trading bot shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


async def main():
    """Main entry point."""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                AI Trading Bot with Yahoo Finance             ║
    ║                                                              ║
    ║  🤖 Intelligent • 📊 Yahoo Data • ⚡ Real-Time • 🛡️ Secure   ║
    ║                                                              ║
    ║  Reliable market data without browser automation             ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    bot = TradingBot()
    
    try:
        await bot.start()
    except Exception as e:
        logger.error(f"Fatal system error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
