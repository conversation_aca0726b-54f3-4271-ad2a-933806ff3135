"""
Production deployment configuration for AI Trading Bot.

This module provides comprehensive production configuration management
including environment-specific settings, deployment parameters, and
production-ready configurations for 24/7 operations.
"""

import os
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
import yaml

from ...core.config import Config
from ...utils.logger import get_logger

logger = get_logger(__name__)


class DeploymentEnvironment(Enum):
    """Deployment environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class DeploymentMode(Enum):
    """Deployment mode types."""
    PAPER_TRADING = "paper"
    LIVE_TRADING = "live"
    SIMULATION = "simulation"


@dataclass
class ResourceLimits:
    """Resource limits for deployment."""
    cpu_cores: float = 2.0
    memory_gb: float = 4.0
    disk_gb: float = 100.0
    max_connections: int = 100
    max_concurrent_orders: int = 10


@dataclass
class ScalingConfig:
    """Auto-scaling configuration."""
    min_replicas: int = 2
    max_replicas: int = 5
    target_cpu_utilization: int = 70
    target_memory_utilization: int = 80
    scale_up_cooldown: int = 300  # seconds
    scale_down_cooldown: int = 600  # seconds


@dataclass
class HealthCheckConfig:
    """Health check configuration."""
    enabled: bool = True
    interval: int = 30  # seconds
    timeout: int = 10  # seconds
    retries: int = 3
    startup_delay: int = 60  # seconds
    endpoints: List[str] = field(default_factory=lambda: ["/health", "/ready"])


@dataclass
class MonitoringConfig:
    """Monitoring configuration."""
    metrics_enabled: bool = True
    metrics_port: int = 9090
    metrics_path: str = "/metrics"
    logging_level: str = "INFO"
    log_format: str = "json"
    alert_cooldown: int = 300  # seconds


@dataclass
class SecurityConfig:
    """Security configuration."""
    enable_tls: bool = True
    require_auth: bool = True
    api_key_rotation_days: int = 30
    session_timeout: int = 3600  # seconds
    max_login_attempts: int = 3
    lockout_duration: int = 900  # seconds


@dataclass
class BackupConfig:
    """Backup configuration."""
    enabled: bool = True
    frequency: str = "daily"  # daily, hourly
    retention_days: int = 30
    backup_location: str = "/opt/trading-bot/backups"
    compress: bool = True
    encrypt: bool = True


@dataclass
class DeploymentSettings:
    """Comprehensive deployment settings."""
    environment: DeploymentEnvironment
    mode: DeploymentMode
    version: str
    build_date: str
    git_commit: str
    
    # Resource configuration
    resources: ResourceLimits = field(default_factory=ResourceLimits)
    scaling: ScalingConfig = field(default_factory=ScalingConfig)
    
    # Operational configuration
    health_check: HealthCheckConfig = field(default_factory=HealthCheckConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    backup: BackupConfig = field(default_factory=BackupConfig)
    
    # Trading configuration
    trading_config: Dict[str, Any] = field(default_factory=dict)
    
    # Feature flags
    feature_flags: Dict[str, bool] = field(default_factory=dict)


class ProductionConfig:
    """
    Production configuration manager.
    
    Manages environment-specific configurations, deployment settings,
    and production-ready parameters for the trading bot.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/production.yaml"
        self.settings: Optional[DeploymentSettings] = None
        self.base_config: Optional[Config] = None
        
        # Load configuration
        self._load_configuration()
    
    def _load_configuration(self):
        """Load production configuration from file and environment."""
        try:
            # Load base configuration
            self.base_config = Config()
            
            # Load deployment-specific configuration
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    config_data = yaml.safe_load(f)
                self.settings = self._parse_deployment_config(config_data)
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                self.settings = self._create_default_settings()
            
            # Override with environment variables
            self._apply_environment_overrides()
            
            logger.info(f"Loaded production configuration for {self.settings.environment.value}")
            
        except Exception as e:
            logger.error(f"Failed to load production configuration: {e}")
            self.settings = self._create_default_settings()
    
    def _parse_deployment_config(self, config_data: Dict[str, Any]) -> DeploymentSettings:
        """Parse deployment configuration from YAML data."""
        deployment_data = config_data.get('deployment', {})
        
        # Parse environment and mode
        environment = DeploymentEnvironment(
            deployment_data.get('environment', 'production')
        )
        mode = DeploymentMode(
            deployment_data.get('mode', 'paper')
        )
        
        # Parse resource limits
        resources_data = deployment_data.get('resources', {})
        resources = ResourceLimits(
            cpu_cores=resources_data.get('cpu_cores', 2.0),
            memory_gb=resources_data.get('memory_gb', 4.0),
            disk_gb=resources_data.get('disk_gb', 100.0),
            max_connections=resources_data.get('max_connections', 100),
            max_concurrent_orders=resources_data.get('max_concurrent_orders', 10)
        )
        
        # Parse scaling configuration
        scaling_data = deployment_data.get('scaling', {})
        scaling = ScalingConfig(
            min_replicas=scaling_data.get('min_replicas', 2),
            max_replicas=scaling_data.get('max_replicas', 5),
            target_cpu_utilization=scaling_data.get('target_cpu_utilization', 70),
            target_memory_utilization=scaling_data.get('target_memory_utilization', 80)
        )
        
        # Parse health check configuration
        health_data = deployment_data.get('health_check', {})
        health_check = HealthCheckConfig(
            enabled=health_data.get('enabled', True),
            interval=health_data.get('interval', 30),
            timeout=health_data.get('timeout', 10),
            retries=health_data.get('retries', 3),
            startup_delay=health_data.get('startup_delay', 60)
        )
        
        # Parse monitoring configuration
        monitoring_data = deployment_data.get('monitoring', {})
        monitoring = MonitoringConfig(
            metrics_enabled=monitoring_data.get('metrics_enabled', True),
            metrics_port=monitoring_data.get('metrics_port', 9090),
            logging_level=monitoring_data.get('logging_level', 'INFO'),
            log_format=monitoring_data.get('log_format', 'json')
        )
        
        # Parse security configuration
        security_data = deployment_data.get('security', {})
        security = SecurityConfig(
            enable_tls=security_data.get('enable_tls', True),
            require_auth=security_data.get('require_auth', True),
            api_key_rotation_days=security_data.get('api_key_rotation_days', 30)
        )
        
        # Parse backup configuration
        backup_data = deployment_data.get('backup', {})
        backup = BackupConfig(
            enabled=backup_data.get('enabled', True),
            frequency=backup_data.get('frequency', 'daily'),
            retention_days=backup_data.get('retention_days', 30)
        )
        
        return DeploymentSettings(
            environment=environment,
            mode=mode,
            version=deployment_data.get('version', '1.0.0'),
            build_date=deployment_data.get('build_date', ''),
            git_commit=deployment_data.get('git_commit', ''),
            resources=resources,
            scaling=scaling,
            health_check=health_check,
            monitoring=monitoring,
            security=security,
            backup=backup,
            trading_config=config_data.get('trading', {}),
            feature_flags=config_data.get('feature_flags', {})
        )
    
    def _create_default_settings(self) -> DeploymentSettings:
        """Create default deployment settings."""
        return DeploymentSettings(
            environment=DeploymentEnvironment.PRODUCTION,
            mode=DeploymentMode.PAPER_TRADING,
            version="1.0.0",
            build_date="",
            git_commit=""
        )
    
    def _apply_environment_overrides(self):
        """Apply environment variable overrides."""
        if not self.settings:
            return
        
        # Override environment
        env_environment = os.getenv('DEPLOYMENT_ENVIRONMENT')
        if env_environment:
            try:
                self.settings.environment = DeploymentEnvironment(env_environment)
            except ValueError:
                logger.warning(f"Invalid deployment environment: {env_environment}")
        
        # Override mode
        env_mode = os.getenv('DEPLOYMENT_MODE')
        if env_mode:
            try:
                self.settings.mode = DeploymentMode(env_mode)
            except ValueError:
                logger.warning(f"Invalid deployment mode: {env_mode}")
        
        # Override version
        env_version = os.getenv('VERSION')
        if env_version:
            self.settings.version = env_version
        
        # Override build information
        env_build_date = os.getenv('BUILD_DATE')
        if env_build_date:
            self.settings.build_date = env_build_date
        
        env_git_commit = os.getenv('GIT_COMMIT')
        if env_git_commit:
            self.settings.git_commit = env_git_commit
    
    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading-specific configuration."""
        if not self.settings:
            return {}
        
        config = self.settings.trading_config.copy()
        
        # Add deployment-specific overrides
        config['mode'] = self.settings.mode.value
        config['environment'] = self.settings.environment.value
        
        return config
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return (
            self.settings and 
            self.settings.environment == DeploymentEnvironment.PRODUCTION
        )
    
    def is_live_trading(self) -> bool:
        """Check if live trading is enabled."""
        return (
            self.settings and 
            self.settings.mode == DeploymentMode.LIVE_TRADING
        )
    
    def get_feature_flag(self, flag_name: str, default: bool = False) -> bool:
        """Get feature flag value."""
        if not self.settings:
            return default
        
        return self.settings.feature_flags.get(flag_name, default)
    
    def validate_configuration(self) -> List[str]:
        """Validate production configuration and return any issues."""
        issues = []
        
        if not self.settings:
            issues.append("No deployment settings loaded")
            return issues
        
        # Validate resource limits
        if self.settings.resources.cpu_cores < 1.0:
            issues.append("CPU cores must be at least 1.0")
        
        if self.settings.resources.memory_gb < 2.0:
            issues.append("Memory must be at least 2GB")
        
        # Validate scaling configuration
        if self.settings.scaling.min_replicas < 1:
            issues.append("Minimum replicas must be at least 1")
        
        if self.settings.scaling.max_replicas < self.settings.scaling.min_replicas:
            issues.append("Maximum replicas must be >= minimum replicas")
        
        # Validate production-specific requirements
        if self.is_production():
            if self.settings.scaling.min_replicas < 2:
                issues.append("Production requires at least 2 replicas")
            
            if not self.settings.security.enable_tls:
                issues.append("Production requires TLS enabled")
            
            if not self.settings.backup.enabled:
                issues.append("Production requires backups enabled")
        
        return issues
