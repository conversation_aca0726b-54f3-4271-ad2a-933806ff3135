version: '3.8'

services:
  # Trading Bot Application
  trading-bot:
    build: .
    container_name: ai-trading-bot
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
      - MONGO_HOST=mongo
    env_file:
      - .env
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
      - mongo
    networks:
      - trading-network
    ports:
      - "8000:8000"

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trading-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: trading_bot
      POSTGRES_USER: trading_bot
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-trading_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_postgres.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - trading-network
    ports:
      - "5432:5432"

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trading-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    networks:
      - trading-network
    ports:
      - "6379:6379"

  # MongoDB
  mongo:
    image: mongo:6
    container_name: trading-mongo
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USER:-trading_bot}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-mongo_password}
      MONGO_INITDB_DATABASE: trading_bot
    volumes:
      - mongo_data:/data/db
      - ./scripts/init_mongo.js:/docker-entrypoint-initdb.d/init.js
    networks:
      - trading-network
    ports:
      - "27017:27017"

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: trading-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - trading-network
    ports:
      - "9090:9090"

  # Grafana (Dashboards)
  grafana:
    image: grafana/grafana:latest
    container_name: trading-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - trading-network
    ports:
      - "3000:3000"

  # Jupyter Notebook (Development)
  jupyter:
    image: jupyter/scipy-notebook:latest
    container_name: trading-jupyter
    restart: unless-stopped
    environment:
      JUPYTER_ENABLE_LAB: yes
      JUPYTER_TOKEN: ${JUPYTER_TOKEN:-trading_jupyter}
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./data:/home/<USER>/work/data
      - ./src:/home/<USER>/work/src
    networks:
      - trading-network
    ports:
      - "8888:8888"
    profiles:
      - dev

volumes:
  postgres_data:
  redis_data:
  mongo_data:
  prometheus_data:
  grafana_data:

networks:
  trading-network:
    driver: bridge
