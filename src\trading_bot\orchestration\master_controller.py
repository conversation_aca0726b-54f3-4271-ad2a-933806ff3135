"""Master Controller - Central orchestrator for the trading bot system.

This module provides the main coordination layer that manages all trading bot components
including API clients, risk management, ML pipeline, strategies, and advanced features.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field

from ..core.config import settings
from ..core.exceptions import TradingBotError
from ..utils.logger import get_structured_logger
from ..api import WebullAPI
from ..risk.manager import RiskManager
from ..ml.pipeline import TradingMLPipeline, MLPipelineConfig
from ..strategies.base_strategy import BaseStrategy
from ..advanced import AdvancedTradingSystem
from .service_manager import ServiceManager
from .health_monitor import HealthMonitor
from .performance_tracker import PerformanceTracker
from .failover_manager import FailoverManager

logger = get_structured_logger(__name__)


@dataclass
class SystemState:
    """Current state of the trading system."""
    is_initialized: bool = False
    is_running: bool = False
    is_trading_enabled: bool = False
    market_hours_active: bool = False
    emergency_stop: bool = False
    last_heartbeat: Optional[datetime] = None
    active_strategies: List[str] = field(default_factory=list)
    system_health: str = "unknown"  # healthy, degraded, critical, offline


@dataclass
class MasterControllerConfig:
    """Configuration for the master controller."""
    # Trading settings
    max_concurrent_orders: int = 10
    max_daily_trades: int = 100
    position_check_interval: int = 30  # seconds
    
    # System monitoring
    health_check_interval: int = 60  # seconds
    performance_update_interval: int = 300  # seconds
    heartbeat_interval: int = 30  # seconds
    
    # Risk management
    emergency_stop_loss: float = 0.05  # 5% daily loss
    max_drawdown_threshold: float = 0.10  # 10% max drawdown
    
    # ML pipeline
    prediction_frequency: int = 60  # seconds
    model_update_frequency: int = 3600  # seconds
    
    # Advanced features
    enable_sentiment_analysis: bool = True
    enable_options_trading: bool = False
    enable_crypto_trading: bool = False


class MasterController:
    """
    Central brain that coordinates all trading bot components.
    
    This class serves as the main orchestrator for the entire trading system,
    managing initialization, coordination, monitoring, and shutdown of all components.
    """
    
    def __init__(self, config: Optional[MasterControllerConfig] = None):
        """Initialize the master controller."""
        self.config = config or MasterControllerConfig()
        self.state = SystemState()
        
        # Core components (will be initialized in initialize_system)
        self.api_client: Optional[WebullAPI] = None
        self.risk_manager: Optional[RiskManager] = None
        self.ml_pipeline: Optional[TradingMLPipeline] = None
        self.strategies: Dict[str, BaseStrategy] = {}
        self.advanced_features: Optional[AdvancedTradingSystem] = None
        
        # Orchestration components
        self.service_manager: Optional[ServiceManager] = None
        self.health_monitor: Optional[HealthMonitor] = None
        self.performance_tracker: Optional[PerformanceTracker] = None
        self.failover_manager: Optional[FailoverManager] = None
        
        # Task management
        self.running_tasks: List[asyncio.Task] = []
        self.shutdown_event = asyncio.Event()
        
        logger.info("Master controller initialized")
    
    async def initialize_system(self) -> bool:
        """
        Initialize all system components in the correct order.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            logger.info("Starting system initialization...")
            
            # 1. Initialize orchestration components first
            await self._initialize_orchestration_components()
            
            # 2. Initialize core trading components
            await self._initialize_core_components()
            
            # 3. Initialize advanced features
            await self._initialize_advanced_features()
            
            # 4. Validate system health
            if not await self._validate_system_health():
                raise TradingBotError("System health validation failed")
            
            # 5. Start monitoring and background tasks
            await self._start_background_tasks()
            
            self.state.is_initialized = True
            self.state.system_health = "healthy"
            self.state.last_heartbeat = datetime.utcnow()
            
            logger.info("System initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            await self._emergency_shutdown(f"Initialization failure: {e}")
            return False
    
    async def _initialize_orchestration_components(self):
        """Initialize orchestration and monitoring components."""
        logger.info("Initializing orchestration components...")
        
        # Service manager for microservices coordination
        self.service_manager = ServiceManager()
        await self.service_manager.initialize()
        
        # Health monitoring system
        self.health_monitor = HealthMonitor()
        await self.health_monitor.initialize()
        
        # Performance tracking system
        self.performance_tracker = PerformanceTracker()
        await self.performance_tracker.initialize()
        
        # Failover and disaster recovery
        self.failover_manager = FailoverManager()
        await self.failover_manager.initialize()
        
        logger.info("Orchestration components initialized")
    
    async def _initialize_core_components(self):
        """Initialize core trading components."""
        logger.info("Initializing core trading components...")
        
        # 1. API Client
        self.api_client = WebullAPI(paper_trading=settings.webull.paper_trading)
        await self.api_client.initialize()
        
        # 2. Risk Manager
        self.risk_manager = RiskManager()
        await self.risk_manager.initialize()
        
        # 3. ML Pipeline
        ml_config = MLPipelineConfig(
            prediction_frequency='1min',
            auto_retrain_enabled=True,
            auto_retrain_interval_hours=24
        )
        self.ml_pipeline = TradingMLPipeline(
            models=['lstm', 'xgboost', 'transformer'],
            ensemble_method='weighted_voting',
            confidence_threshold=0.7,
            config=ml_config
        )
        
        # 4. Load and initialize strategies
        await self._initialize_strategies()
        
        logger.info("Core trading components initialized")
    
    async def _initialize_advanced_features(self):
        """Initialize advanced trading features."""
        if not self.config.enable_sentiment_analysis and \
           not self.config.enable_options_trading and \
           not self.config.enable_crypto_trading:
            logger.info("Advanced features disabled")
            return
        
        logger.info("Initializing advanced features...")
        
        # Initialize advanced trading system
        # Note: This will be implemented when AdvancedTradingSystem is available
        # self.advanced_features = AdvancedTradingSystem()
        # await self.advanced_features.initialize()
        
        logger.info("Advanced features initialized")
    
    async def _initialize_strategies(self):
        """Initialize and load trading strategies."""
        logger.info("Initializing trading strategies...")
        
        # For now, we'll create a placeholder strategy system
        # This will be expanded when StrategyManager is implemented
        
        # Example strategy configuration
        strategy_configs = {
            "momentum": {
                "max_position_size": 0.05,
                "max_positions": 3,
                "max_drawdown": 0.03
            },
            "mean_reversion": {
                "max_position_size": 0.03,
                "max_positions": 5,
                "max_drawdown": 0.02
            }
        }
        
        # Initialize strategies (placeholder implementation)
        for strategy_name, config in strategy_configs.items():
            # This will be replaced with actual strategy implementations
            strategy = BaseStrategy(
                name=strategy_name,
                config=config,
                risk_manager=self.risk_manager,
                ml_system=self.ml_pipeline
            )
            self.strategies[strategy_name] = strategy
            self.state.active_strategies.append(strategy_name)
        
        logger.info(f"Initialized {len(self.strategies)} strategies")
    
    async def _validate_system_health(self) -> bool:
        """Validate that all critical components are healthy."""
        logger.info("Validating system health...")
        
        try:
            # Check API client
            if not self.api_client or not self.api_client.is_initialized:
                logger.error("API client not properly initialized")
                return False
            
            # Check risk manager
            if not self.risk_manager:
                logger.error("Risk manager not initialized")
                return False
            
            # Check database connections
            # This would include PostgreSQL, Redis, MongoDB health checks
            
            # Check external API connectivity
            health_check = await self.api_client.health_check()
            if not health_check.get('status') == 'healthy':
                logger.error("API health check failed")
                return False
            
            logger.info("System health validation passed")
            return True
            
        except Exception as e:
            logger.error(f"System health validation failed: {e}")
            return False
    
    async def _start_background_tasks(self):
        """Start all background monitoring and processing tasks."""
        logger.info("Starting background tasks...")
        
        # Health monitoring task
        health_task = asyncio.create_task(self._health_monitoring_loop())
        self.running_tasks.append(health_task)
        
        # Performance tracking task
        performance_task = asyncio.create_task(self._performance_tracking_loop())
        self.running_tasks.append(performance_task)
        
        # Heartbeat task
        heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self.running_tasks.append(heartbeat_task)
        
        logger.info(f"Started {len(self.running_tasks)} background tasks")

    async def start_trading(self) -> bool:
        """
        Start the main trading operations.

        Returns:
            True if trading started successfully, False otherwise
        """
        try:
            if not self.state.is_initialized:
                logger.error("Cannot start trading - system not initialized")
                return False

            if self.state.is_running:
                logger.warning("Trading already running")
                return True

            logger.info("Starting trading operations...")

            # Pre-trading checks
            if not await self._pre_trading_checks():
                logger.error("Pre-trading checks failed")
                return False

            # Start the main trading loop
            trading_task = asyncio.create_task(self._main_trading_loop())
            self.running_tasks.append(trading_task)

            self.state.is_running = True
            self.state.is_trading_enabled = True

            logger.info("Trading operations started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start trading: {e}")
            return False

    async def stop_trading(self, reason: str = "Manual stop"):
        """Stop trading operations gracefully."""
        logger.info(f"Stopping trading operations: {reason}")

        self.state.is_trading_enabled = False

        # Cancel all pending orders
        if self.api_client:
            await self._cancel_all_pending_orders()

        # Close all positions if emergency stop
        if "emergency" in reason.lower():
            await self._close_all_positions()

        self.state.is_running = False
        logger.info("Trading operations stopped")

    async def _pre_trading_checks(self) -> bool:
        """Perform pre-trading validation checks."""
        logger.info("Performing pre-trading checks...")

        try:
            # Check market hours
            market_hours = await self.api_client.get_market_hours()
            if not market_hours.get('is_open', False):
                logger.warning("Market is closed")
                self.state.market_hours_active = False
                # Continue anyway for pre-market/after-hours trading
            else:
                self.state.market_hours_active = True

            # Check account status
            account_summary = await self.api_client.get_account_summary()
            if not account_summary:
                logger.error("Cannot retrieve account information")
                return False

            # Check risk limits
            risk_check = await self.risk_manager.validate_trading_conditions()
            if not risk_check.get('can_trade', False):
                logger.error(f"Risk check failed: {risk_check.get('reason')}")
                return False

            # Check system resources
            if self.health_monitor:
                health_status = await self.health_monitor.get_system_health()
                if health_status.get('status') != 'healthy':
                    logger.warning(f"System health degraded: {health_status}")

            logger.info("Pre-trading checks passed")
            return True

        except Exception as e:
            logger.error(f"Pre-trading checks failed: {e}")
            return False

    async def _main_trading_loop(self):
        """Main trading loop that coordinates all trading activities."""
        logger.info("Starting main trading loop...")

        while self.state.is_running and not self.shutdown_event.is_set():
            try:
                loop_start_time = datetime.utcnow()

                # 1. Market data ingestion
                market_data = await self._collect_market_data()

                # 2. Risk monitoring
                await self._monitor_risk_conditions()

                # 3. ML predictions and feature calculation
                predictions = await self._generate_ml_predictions(market_data)

                # 4. Strategy signal generation
                signals = await self._generate_strategy_signals(market_data, predictions)

                # 5. Risk validation and position sizing
                validated_signals = await self._validate_and_size_signals(signals)

                # 6. Order execution
                if validated_signals and self.state.is_trading_enabled:
                    await self._execute_orders(validated_signals)

                # 7. Position monitoring and management
                await self._monitor_positions()

                # 8. Performance tracking
                await self._update_performance_metrics()

                # Calculate loop execution time
                loop_duration = (datetime.utcnow() - loop_start_time).total_seconds()

                # Sleep for remaining time to maintain frequency
                sleep_time = max(0, 60 - loop_duration)  # 1-minute loop
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)

            except Exception as e:
                logger.error(f"Error in main trading loop: {e}")

                # Check if this is a critical error requiring emergency stop
                if await self._is_critical_error(e):
                    await self._emergency_shutdown(f"Critical error in trading loop: {e}")
                    break

                # For non-critical errors, wait and continue
                await asyncio.sleep(5)

        logger.info("Main trading loop stopped")

    async def _collect_market_data(self) -> Dict[str, Any]:
        """Collect current market data for all tracked symbols."""
        try:
            # Get list of symbols from active strategies
            symbols = set()
            for strategy in self.strategies.values():
                if hasattr(strategy, 'symbols'):
                    symbols.update(strategy.symbols)

            if not symbols:
                return {}

            # Collect market data
            market_data = {}
            for symbol in symbols:
                try:
                    quote = await self.api_client.get_quote(symbol)
                    bars = await self.api_client.get_bars(symbol, count=100)

                    market_data[symbol] = {
                        'quote': quote,
                        'bars': bars,
                        'timestamp': datetime.utcnow()
                    }
                except Exception as e:
                    logger.warning(f"Failed to get market data for {symbol}: {e}")

            return market_data

        except Exception as e:
            logger.error(f"Error collecting market data: {e}")
            return {}

    async def _generate_ml_predictions(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate ML predictions for all symbols."""
        if not self.ml_pipeline or not market_data:
            return {}

        try:
            predictions = {}

            for symbol, data in market_data.items():
                if 'bars' in data and data['bars']:
                    # Convert bars to DataFrame format expected by ML pipeline
                    # This would need proper data formatting
                    prediction_result = await self.ml_pipeline.predict(
                        data=data['bars'],  # This needs proper formatting
                        symbol=symbol,
                        return_confidence=True,
                        return_signals=True
                    )
                    predictions[symbol] = prediction_result

            return predictions

        except Exception as e:
            logger.error(f"Error generating ML predictions: {e}")
            return {}

    async def _generate_strategy_signals(self, market_data: Dict[str, Any],
                                       predictions: Dict[str, Any]) -> List[Any]:
        """Generate trading signals from all active strategies."""
        all_signals = []

        try:
            for strategy_name, strategy in self.strategies.items():
                if not strategy.is_active:
                    continue

                try:
                    # Pass market data to strategy
                    for symbol, data in market_data.items():
                        signals, _ = await strategy.on_market_data(data)
                        all_signals.extend(signals)

                except Exception as e:
                    logger.error(f"Error generating signals for {strategy_name}: {e}")

            return all_signals

        except Exception as e:
            logger.error(f"Error in strategy signal generation: {e}")
            return []

    async def _validate_and_size_signals(self, signals: List[Any]) -> List[Any]:
        """Validate signals through risk management and calculate position sizes."""
        if not signals or not self.risk_manager:
            return []

        validated_signals = []

        try:
            for signal in signals:
                # Risk validation
                risk_check = await self.risk_manager.validate_order_request(signal)

                if risk_check.get('approved', False):
                    # Calculate position size
                    position_size = await self.risk_manager.calculate_position_size(
                        symbol=signal.symbol,
                        entry_price=signal.entry_price,
                        stop_loss=getattr(signal, 'stop_loss', None),
                        sizing_method="dynamic",
                        signal_strength=signal.confidence
                    )

                    if position_size > 0:
                        signal.quantity = position_size
                        validated_signals.append(signal)
                else:
                    logger.warning(f"Signal rejected by risk management: {risk_check.get('reason')}")

            return validated_signals

        except Exception as e:
            logger.error(f"Error validating signals: {e}")
            return []

    async def _execute_orders(self, signals: List[Any]):
        """Execute validated trading signals."""
        if not signals or not self.api_client:
            return

        try:
            for signal in signals:
                try:
                    # Create order request
                    order_request = {
                        'symbol': signal.symbol,
                        'quantity': signal.quantity,
                        'side': signal.direction.value,
                        'order_type': signal.order_type.value,
                        'price': signal.entry_price if hasattr(signal, 'entry_price') else None
                    }

                    # Execute order
                    order_result = await self.api_client.place_order(order_request)

                    if order_result:
                        logger.info(f"Order placed successfully: {order_result}")

                        # Update performance tracking
                        if self.performance_tracker:
                            await self.performance_tracker.record_order(order_result)

                except Exception as e:
                    logger.error(f"Failed to execute order for {signal.symbol}: {e}")

        except Exception as e:
            logger.error(f"Error in order execution: {e}")

    async def _monitor_positions(self):
        """Monitor existing positions and manage exits."""
        if not self.api_client:
            return

        try:
            # Get current positions
            positions = await self.api_client.get_positions()

            for position in positions:
                # Check stop losses and take profits
                if self.risk_manager:
                    exit_signals = await self.risk_manager.check_position_exits(position)

                    for exit_signal in exit_signals:
                        await self._execute_exit_order(position, exit_signal)

        except Exception as e:
            logger.error(f"Error monitoring positions: {e}")

    async def _execute_exit_order(self, position: Any, exit_signal: Any):
        """Execute position exit order."""
        try:
            exit_order = {
                'symbol': position.symbol,
                'quantity': position.quantity,
                'side': 'sell' if position.side == 'buy' else 'buy',
                'order_type': 'market'
            }

            result = await self.api_client.place_order(exit_order)

            if result:
                logger.info(f"Exit order executed: {result}")

        except Exception as e:
            logger.error(f"Failed to execute exit order: {e}")

    async def _monitor_risk_conditions(self):
        """Monitor real-time risk conditions."""
        if not self.risk_manager:
            return

        try:
            # Check for risk limit breaches
            risk_status = await self.risk_manager.check_all_risk_limits()

            if risk_status.get('requires_action', False):
                logger.warning(f"Risk limits breached: {risk_status}")

                # Handle emergency conditions
                if risk_status.get('circuit_breaker_level', 0) >= 3:
                    await self._emergency_shutdown("Circuit breaker triggered")

        except Exception as e:
            logger.error(f"Error monitoring risk conditions: {e}")

    async def _update_performance_metrics(self):
        """Update performance tracking metrics."""
        if not self.performance_tracker:
            return

        try:
            await self.performance_tracker.update_metrics()
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    async def _cancel_all_pending_orders(self):
        """Cancel all pending orders."""
        if not self.api_client:
            return

        try:
            orders = await self.api_client.get_orders(status='pending')

            for order in orders:
                await self.api_client.cancel_order(order.id)
                logger.info(f"Cancelled order: {order.id}")

        except Exception as e:
            logger.error(f"Error cancelling orders: {e}")

    async def _close_all_positions(self):
        """Close all open positions (emergency procedure)."""
        if not self.api_client:
            return

        try:
            positions = await self.api_client.get_positions()

            for position in positions:
                exit_order = {
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'side': 'sell' if position.side == 'buy' else 'buy',
                    'order_type': 'market'
                }

                await self.api_client.place_order(exit_order)
                logger.info(f"Emergency close position: {position.symbol}")

        except Exception as e:
            logger.error(f"Error closing positions: {e}")

    async def _is_critical_error(self, error: Exception) -> bool:
        """Determine if an error is critical and requires emergency shutdown."""
        critical_errors = [
            "ConnectionError",
            "AuthenticationError",
            "RiskLimitBreach",
            "SystemResourceError"
        ]

        return any(critical in str(type(error)) for critical in critical_errors)

    async def _emergency_shutdown(self, reason: str):
        """Perform emergency shutdown of the system."""
        logger.critical(f"EMERGENCY SHUTDOWN: {reason}")

        self.state.emergency_stop = True
        self.state.is_trading_enabled = False

        # Stop trading immediately
        await self.stop_trading(f"Emergency: {reason}")

        # Trigger failover procedures
        if self.failover_manager:
            await self.failover_manager.trigger_emergency_procedures()

        # Set shutdown event
        self.shutdown_event.set()

    async def _health_monitoring_loop(self):
        """Background task for health monitoring."""
        while not self.shutdown_event.is_set():
            try:
                if self.health_monitor:
                    await self.health_monitor.perform_health_checks()

                await asyncio.sleep(self.config.health_check_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
                await asyncio.sleep(30)

    async def _performance_tracking_loop(self):
        """Background task for performance tracking."""
        while not self.shutdown_event.is_set():
            try:
                if self.performance_tracker:
                    await self.performance_tracker.update_metrics()

                await asyncio.sleep(self.config.performance_update_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in performance tracking: {e}")
                await asyncio.sleep(60)

    async def _heartbeat_loop(self):
        """Background task for system heartbeat."""
        while not self.shutdown_event.is_set():
            try:
                self.state.last_heartbeat = datetime.utcnow()

                # Log system status periodically
                if self.state.is_running:
                    logger.debug(f"System heartbeat - Status: {self.state.system_health}")

                await asyncio.sleep(self.config.heartbeat_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in heartbeat: {e}")
                await asyncio.sleep(10)

    async def shutdown(self):
        """Graceful shutdown of the entire system."""
        logger.info("Starting graceful system shutdown...")

        # Stop trading operations
        await self.stop_trading("System shutdown")

        # Cancel all background tasks
        self.shutdown_event.set()

        for task in self.running_tasks:
            task.cancel()

        # Wait for tasks to complete
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks, return_exceptions=True)

        # Shutdown components
        if self.api_client:
            await self.api_client.close()

        if self.service_manager:
            await self.service_manager.shutdown()

        if self.health_monitor:
            await self.health_monitor.shutdown()

        if self.performance_tracker:
            await self.performance_tracker.shutdown()

        if self.failover_manager:
            await self.failover_manager.shutdown()

        self.state.is_initialized = False
        self.state.is_running = False

        logger.info("System shutdown completed")

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status and metrics."""
        return {
            'state': {
                'is_initialized': self.state.is_initialized,
                'is_running': self.state.is_running,
                'is_trading_enabled': self.state.is_trading_enabled,
                'market_hours_active': self.state.market_hours_active,
                'emergency_stop': self.state.emergency_stop,
                'last_heartbeat': self.state.last_heartbeat.isoformat() if self.state.last_heartbeat else None,
                'system_health': self.state.system_health
            },
            'strategies': {
                'active_count': len([s for s in self.strategies.values() if s.is_active]),
                'total_count': len(self.strategies),
                'active_strategies': self.state.active_strategies
            },
            'tasks': {
                'running_tasks': len(self.running_tasks),
                'background_tasks_active': not self.shutdown_event.is_set()
            }
        }
