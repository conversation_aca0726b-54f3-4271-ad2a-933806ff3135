# Trading Bot Production Setup Guide

This guide will walk you through configuring and deploying your trading bot for production use with all enterprise-grade features enabled.

## 🚀 Quick Start

### 1. Run the Interactive Setup Wizard

```bash
python setup_production.py
```

This wizard will guide you through all configuration steps interactively.

### 2. Deploy to Your Environment

```bash
# For staging
python deploy.py staging

# For production (requires confirmation)
python deploy.py production
```

## 📋 Manual Configuration Steps

If you prefer manual configuration, follow these detailed steps:

### Step 1: Environment Configuration

1. **Copy the configuration template:**
   ```bash
   cp config/production.yaml config/my_production.yaml
   ```

2. **Edit the configuration file:**
   ```yaml
   # config/my_production.yaml
   security:
     hsm_provider: "aws_cloudhsm"  # or "azure_keyvault"
     hsm_cluster_id: "cluster-your-id-here"
     mfa_provider: "totp"
   
   broker:
     broker_type: "webull"  # or "alpaca", "interactive_brokers"
     paper_trading: false   # SET TO TRUE FOR TESTING!
     credentials_secret_name: "your_broker_credentials"
   
   risk:
     max_daily_loss_percent: 2.0    # 2% max daily loss
     max_position_percent: 5.0      # 5% max single position
     max_sector_percent: 25.0       # 25% max sector exposure
   ```

### Step 2: Secrets Configuration

1. **Set up your broker credentials:**
   ```python
   from src.trading_bot.security.secrets_manager import EnhancedSecretsManager
   
   secrets = EnhancedSecretsManager()
   
   # Store broker credentials
   await secrets.store_secret(
       name="broker_username",
       value="your_username",
       secret_type=SecretType.API_KEY,
       security_level=SecurityLevel.CRITICAL
   )
   ```

2. **Required secrets checklist:**
   - [ ] `broker_username`
   - [ ] `broker_password`
   - [ ] `broker_api_key` (if applicable)
   - [ ] `postgres_password`
   - [ ] `redis_password`
   - [ ] `grafana_password`
   - [ ] `email_password`
   - [ ] `twilio_auth_token`
   - [ ] `slack_webhook_token`

### Step 3: Database Setup

1. **PostgreSQL setup:**
   ```sql
   CREATE DATABASE trading_bot_prod;
   CREATE USER trading_user WITH PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE trading_bot_prod TO trading_user;
   ```

2. **Redis setup:**
   ```bash
   # Set Redis password
   redis-cli CONFIG SET requirepass "your_secure_password"
   ```

### Step 4: Monitoring Setup

1. **Configure Slack alerts:**
   ```bash
   # Get your Slack webhook URL from:
   # https://api.slack.com/messaging/webhooks
   export SLACK_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
   ```

2. **Configure email alerts:**
   ```bash
   export EMAIL_SMTP_SERVER="smtp.gmail.com"
   export EMAIL_SMTP_PORT="587"
   export EMAIL_USERNAME="<EMAIL>"
   export EMAIL_PASSWORD="your_app_password"
   ```

### Step 5: Performance Optimization

1. **For ultra-low latency (requires root):**
   ```bash
   # Enable huge pages
   echo 1024 > /proc/sys/vm/nr_hugepages
   
   # Set CPU governor to performance
   echo performance > /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
   
   # Disable CPU frequency scaling
   systemctl disable ondemand
   ```

2. **Set CPU affinity:**
   ```yaml
   performance:
     optimization_level: "ultra_low_latency"
     cpu_cores_trading: [0, 1]    # Dedicated cores for trading
     cpu_cores_data: [2, 3]       # Dedicated cores for data
     memory_pool_size_mb: 128
     huge_pages_enabled: true
   ```

## 🔧 Component Configuration

### Enhanced Security

```python
# Configure HSM (AWS CloudHSM example)
security:
  hsm_provider: "aws_cloudhsm"
  hsm_cluster_id: "cluster-xxxxxxxxx"
  mfa_provider: "totp"
  secret_rotation_days: 30
```

### Dead Man's Switch

```python
# Configure heartbeat intervals
dead_mans_switch:
  heartbeat_intervals:
    human_operator: 1800      # 30 minutes
    automated_system: 300     # 5 minutes
    health_monitor: 120       # 2 minutes
  
  emergency_contacts:
    - name: "Primary Trader"
      email: "<EMAIL>"
      phone: "+**********"
      priority: 1
```

### Market Microstructure Analysis

```python
# Configure market analysis
microstructure:
  order_flow_window_seconds: 60
  liquidity_estimation_window_minutes: 5
  regime_detection_window_hours: 4
  anomaly_threshold: 0.1
```

### Risk Management

```python
# Configure risk limits
risk:
  max_daily_loss_percent: 2.0
  max_position_percent: 5.0
  circuit_breaker_levels:
    level_1: 0.02  # 2% - Warning
    level_2: 0.05  # 5% - Reduce positions
    level_3: 0.10  # 10% - Stop trading
```

### Compliance

```python
# Configure compliance rules
compliance:
  pdt_rules:
    enabled: true
    day_trade_threshold: 4
    minimum_equity: 25000
  
  wash_sale_rules:
    enabled: true
    period_days: 30
```

## 🚀 Deployment

### Environment Variables

Create a `.env` file:

```bash
# Application
TRADING_BOT_ENVIRONMENT=production
TRADING_BOT_LOG_LEVEL=INFO

# Database
POSTGRES_HOST=your-postgres-host
POSTGRES_PASSWORD=your-postgres-password
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password

# Monitoring
GRAFANA_PASSWORD=your-grafana-password
SLACK_WEBHOOK_URL=your-slack-webhook

# Security
HSM_CLUSTER_ID=your-hsm-cluster-id
```

### Docker Deployment

1. **Build and start services:**
   ```bash
   docker-compose up -d
   ```

2. **Check service status:**
   ```bash
   docker-compose ps
   ```

3. **View logs:**
   ```bash
   docker-compose logs -f trading-bot
   ```

### Manual Deployment

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Initialize database:**
   ```bash
   python -m src.trading_bot.database.init_db
   ```

3. **Start the application:**
   ```bash
   python -m src.trading_bot.main
   ```

## 📊 Monitoring & Alerts

### Grafana Dashboards

Access Grafana at `http://localhost:3000` (admin/admin)

**Key Dashboards:**
- Trading Bot Production Dashboard
- System Performance Dashboard
- Risk Management Dashboard
- Compliance Dashboard

### Alert Channels

1. **Slack Integration:**
   - Real-time trading alerts
   - System health notifications
   - Risk limit breaches

2. **Email Alerts:**
   - Daily performance reports
   - Compliance violations
   - System errors

3. **SMS/Phone Alerts:**
   - Critical system failures
   - Dead man's switch triggers
   - Emergency situations

## 🧪 Testing

### Pre-Production Testing

1. **Run extended test suite:**
   ```python
   from src.trading_bot.testing.extended_test_framework import get_test_framework
   
   test_framework = get_test_framework()
   await test_framework.start_testing()
   ```

2. **Test scenarios:**
   - Latency spike handling
   - Data corruption recovery
   - Partial fill management
   - Clock drift tolerance
   - API version compatibility

### Paper Trading Validation

1. **Enable paper trading:**
   ```yaml
   broker:
     paper_trading: true
   ```

2. **Run for 90+ days minimum**

3. **Monitor all systems:**
   - Risk management
   - Compliance checking
   - Performance optimization
   - Monitoring & alerting

## 🔒 Security Checklist

- [ ] HSM configured and tested
- [ ] MFA enabled for critical operations
- [ ] All secrets properly encrypted
- [ ] Audit logging enabled
- [ ] Network security configured
- [ ] Database encryption enabled
- [ ] Backup encryption verified
- [ ] Access controls implemented

## ⚠️ Production Readiness Checklist

### Before Going Live:

- [ ] 90+ days successful paper trading
- [ ] All tests passing (>95% success rate)
- [ ] Monitoring dashboards configured
- [ ] Alert channels tested
- [ ] Emergency procedures documented
- [ ] Dead man's switch tested
- [ ] Backup/restore procedures verified
- [ ] Compliance rules validated
- [ ] Risk limits properly set
- [ ] Performance optimization applied

### Capital Scaling Plan:

1. **Week 1-2:** $1,000 (validation)
2. **Week 3-4:** $5,000 (confidence building)
3. **Month 2:** $10,000 (operational validation)
4. **Month 3:** $25,000 (performance validation)
5. **Month 4+:** Scale based on performance

## 🆘 Emergency Procedures

### Emergency Contacts

1. **Primary Trader:** +**********
2. **Technical Lead:** +1234567891
3. **Risk Manager:** +1234567892

### Emergency Actions

1. **Immediate Stop:**
   ```bash
   # Emergency kill switch
   curl -X POST http://localhost:8000/emergency/kill
   ```

2. **Graceful Shutdown:**
   ```bash
   docker-compose down
   ```

3. **System Recovery:**
   ```bash
   # Restore from backup
   python -m src.trading_bot.disaster_recovery.restore
   ```

## 📞 Support

For technical support or questions:

- **Documentation:** See `/docs` directory
- **Logs:** Check `docker-compose logs trading-bot`
- **Monitoring:** Grafana dashboard at `http://localhost:3000`
- **Health Check:** `http://localhost:8000/health`

## 🎯 Next Steps

1. **Complete the setup wizard:** `python setup_production.py`
2. **Deploy to staging:** `python deploy.py staging`
3. **Run comprehensive tests**
4. **Start paper trading validation**
5. **Deploy to production when ready**

Remember: **Start with paper trading and scale gradually!**
