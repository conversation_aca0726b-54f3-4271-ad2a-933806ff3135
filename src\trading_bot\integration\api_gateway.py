"""API Gateway - Unified API access and routing.

This module provides a comprehensive API gateway for:
- Request routing and load balancing
- Authentication and authorization
- Rate limiting and throttling
- Request/response transformation
- API versioning and documentation
- Monitoring and analytics
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
from aiohttp import web, WSMsgType
import aiohttp_cors
import jwt
from collections import defaultdict, deque

from ..core.config import settings
from ..utils.logger import get_structured_logger
from ..utils.rate_limiter import rate_limiter
from .event_bus import event_bus

logger = get_structured_logger(__name__)


class HTTPMethod(Enum):
    """HTTP methods."""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    OPTIONS = "OPTIONS"


@dataclass
class Route:
    """API route configuration."""
    path: str
    method: HTTPMethod
    handler: Callable
    auth_required: bool = True
    rate_limit: Optional[int] = None  # requests per minute
    version: str = "v1"
    description: str = ""
    tags: List[str] = field(default_factory=list)


@dataclass
class APIMetrics:
    """API metrics tracking."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    response_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    requests_by_endpoint: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    errors_by_endpoint: Dict[str, int] = field(default_factory=lambda: defaultdict(int))


class APIGateway:
    """
    Comprehensive API Gateway for unified service access.
    
    Provides centralized API management with authentication,
    rate limiting, monitoring, and service routing.
    """
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8000):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.runner: Optional[web.AppRunner] = None
        self.site: Optional[web.TCPSite] = None
        
        # Route management
        self.routes: Dict[str, Route] = {}
        self.websocket_handlers: Dict[str, Callable] = {}
        self.middleware_stack: List[Callable] = []
        
        # Authentication
        self.jwt_secret = settings.api.jwt_secret if hasattr(settings.api, 'jwt_secret') else "default_secret"
        self.jwt_algorithm = "HS256"
        self.token_expiry = timedelta(hours=24)
        
        # Metrics and monitoring
        self.metrics = APIMetrics()
        self.active_connections: Dict[str, aiohttp.web.WebSocketResponse] = {}
        
        # Service registry for routing
        self.services: Dict[str, Dict[str, Any]] = {}
        
        # Setup middleware and routes
        self._setup_middleware()
        self._setup_cors()
        self._setup_builtin_routes()
    
    def _setup_middleware(self):
        """Setup middleware stack."""
        # Request logging middleware
        @web.middleware
        async def logging_middleware(request, handler):
            start_time = time.time()
            
            try:
                response = await handler(request)
                
                # Record metrics
                processing_time = time.time() - start_time
                self.metrics.total_requests += 1
                self.metrics.successful_requests += 1
                self.metrics.response_times.append(processing_time)
                self.metrics.requests_by_endpoint[request.path] += 1
                
                # Update average response time
                if self.metrics.response_times:
                    self.metrics.avg_response_time = sum(self.metrics.response_times) / len(self.metrics.response_times)
                
                # Log request
                logger.info(
                    f"{request.method} {request.path} - {response.status} - {processing_time:.3f}s",
                    extra={
                        'method': request.method,
                        'path': request.path,
                        'status': response.status,
                        'response_time': processing_time,
                        'user_agent': request.headers.get('User-Agent', ''),
                        'remote_addr': request.remote
                    }
                )
                
                return response
            
            except Exception as e:
                processing_time = time.time() - start_time
                self.metrics.total_requests += 1
                self.metrics.failed_requests += 1
                self.metrics.errors_by_endpoint[request.path] += 1
                
                logger.error(
                    f"{request.method} {request.path} - ERROR - {processing_time:.3f}s: {e}",
                    extra={
                        'method': request.method,
                        'path': request.path,
                        'error': str(e),
                        'response_time': processing_time
                    }
                )
                
                raise
        
        # Authentication middleware
        @web.middleware
        async def auth_middleware(request, handler):
            # Skip auth for certain endpoints
            skip_auth_paths = ['/health', '/metrics', '/docs', '/login', '/register']
            
            if any(request.path.startswith(path) for path in skip_auth_paths):
                return await handler(request)
            
            # Check for JWT token
            auth_header = request.headers.get('Authorization', '')
            
            if not auth_header.startswith('Bearer '):
                return web.json_response(
                    {'error': 'Missing or invalid authorization header'},
                    status=401
                )
            
            token = auth_header[7:]  # Remove 'Bearer ' prefix
            
            try:
                payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
                request['user'] = payload
                return await handler(request)
            
            except jwt.ExpiredSignatureError:
                return web.json_response({'error': 'Token expired'}, status=401)
            except jwt.InvalidTokenError:
                return web.json_response({'error': 'Invalid token'}, status=401)
        
        # Rate limiting middleware
        @web.middleware
        async def rate_limit_middleware(request, handler):
            client_ip = request.remote
            endpoint = f"{request.method}:{request.path}"
            
            # Check rate limit
            if not rate_limiter.check_limit(f"api:{client_ip}:{endpoint}"):
                return web.json_response(
                    {'error': 'Rate limit exceeded'},
                    status=429
                )
            
            return await handler(request)
        
        # Add middleware to app
        self.app.middlewares.extend([
            logging_middleware,
            auth_middleware,
            rate_limit_middleware
        ])
    
    def _setup_cors(self):
        """Setup CORS configuration."""
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Add CORS to all routes
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    def _setup_builtin_routes(self):
        """Setup built-in API routes."""
        # Health check
        async def health_check(request):
            return web.json_response({
                'status': 'healthy',
                'timestamp': datetime.utcnow().isoformat(),
                'version': '1.0.0'
            })
        
        # Readiness check
        async def readiness_check(request):
            # Check if all services are ready
            ready = True
            services_status = {}
            
            for service_name, service_info in self.services.items():
                try:
                    # Perform health check on service
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            f"{service_info['url']}/health",
                            timeout=aiohttp.ClientTimeout(total=5)
                        ) as response:
                            if response.status == 200:
                                services_status[service_name] = 'ready'
                            else:
                                services_status[service_name] = 'not_ready'
                                ready = False
                except Exception:
                    services_status[service_name] = 'not_ready'
                    ready = False
            
            status_code = 200 if ready else 503
            
            return web.json_response({
                'ready': ready,
                'services': services_status,
                'timestamp': datetime.utcnow().isoformat()
            }, status=status_code)
        
        # Metrics endpoint
        async def metrics_endpoint(request):
            return web.json_response({
                'metrics': {
                    'total_requests': self.metrics.total_requests,
                    'successful_requests': self.metrics.successful_requests,
                    'failed_requests': self.metrics.failed_requests,
                    'success_rate': (
                        self.metrics.successful_requests / max(1, self.metrics.total_requests)
                    ),
                    'avg_response_time': self.metrics.avg_response_time,
                    'active_connections': len(self.active_connections),
                    'requests_by_endpoint': dict(self.metrics.requests_by_endpoint),
                    'errors_by_endpoint': dict(self.metrics.errors_by_endpoint)
                },
                'timestamp': datetime.utcnow().isoformat()
            })
        
        # Authentication endpoints
        async def login(request):
            data = await request.json()
            username = data.get('username')
            password = data.get('password')
            
            # Validate credentials (integrate with actual auth system)
            if self._validate_credentials(username, password):
                # Generate JWT token
                payload = {
                    'username': username,
                    'exp': datetime.utcnow() + self.token_expiry,
                    'iat': datetime.utcnow()
                }
                
                token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
                
                return web.json_response({
                    'token': token,
                    'expires_in': int(self.token_expiry.total_seconds())
                })
            else:
                return web.json_response(
                    {'error': 'Invalid credentials'},
                    status=401
                )
        
        # WebSocket endpoint
        async def websocket_handler(request):
            ws = web.WebSocketResponse()
            await ws.prepare(request)
            
            connection_id = f"ws_{int(time.time() * 1000000)}"
            self.active_connections[connection_id] = ws
            
            try:
                async for msg in ws:
                    if msg.type == WSMsgType.TEXT:
                        try:
                            data = msg.json()
                            await self._handle_websocket_message(connection_id, data)
                        except Exception as e:
                            await ws.send_str(json.dumps({
                                'error': f'Invalid message format: {e}'
                            }))
                    elif msg.type == WSMsgType.ERROR:
                        logger.error(f'WebSocket error: {ws.exception()}')
            
            except Exception as e:
                logger.error(f'WebSocket connection error: {e}')
            
            finally:
                if connection_id in self.active_connections:
                    del self.active_connections[connection_id]
            
            return ws
        
        # Register built-in routes
        self.app.router.add_get('/health', health_check)
        self.app.router.add_get('/ready', readiness_check)
        self.app.router.add_get('/metrics', metrics_endpoint)
        self.app.router.add_post('/login', login)
        self.app.router.add_get('/ws', websocket_handler)
    
    def _validate_credentials(self, username: str, password: str) -> bool:
        """Validate user credentials."""
        # This would integrate with actual authentication system
        # For now, use simple validation
        return username == "admin" and password == "admin123"
    
    async def _handle_websocket_message(self, connection_id: str, data: Dict[str, Any]):
        """Handle WebSocket message."""
        message_type = data.get('type')
        
        if message_type == 'subscribe':
            # Subscribe to real-time updates
            channel = data.get('channel')
            if channel:
                # Store subscription info
                logger.info(f"WebSocket {connection_id} subscribed to {channel}")
        
        elif message_type == 'ping':
            # Respond to ping
            ws = self.active_connections.get(connection_id)
            if ws:
                await ws.send_str(json.dumps({'type': 'pong'}))
    
    def register_service(self, name: str, url: str, health_endpoint: str = "/health"):
        """Register a service for routing."""
        self.services[name] = {
            'url': url,
            'health_endpoint': health_endpoint,
            'registered_at': datetime.utcnow()
        }
        
        logger.info(f"Registered service: {name} at {url}")
    
    def add_route(self, route: Route):
        """Add a new API route."""
        route_key = f"{route.method.value}:{route.path}"
        self.routes[route_key] = route
        
        # Add route to aiohttp app
        if route.method == HTTPMethod.GET:
            self.app.router.add_get(route.path, route.handler)
        elif route.method == HTTPMethod.POST:
            self.app.router.add_post(route.path, route.handler)
        elif route.method == HTTPMethod.PUT:
            self.app.router.add_put(route.path, route.handler)
        elif route.method == HTTPMethod.DELETE:
            self.app.router.add_delete(route.path, route.handler)
        elif route.method == HTTPMethod.PATCH:
            self.app.router.add_patch(route.path, route.handler)
        
        logger.info(f"Added route: {route.method.value} {route.path}")
    
    async def broadcast_websocket(self, message: Dict[str, Any]):
        """Broadcast message to all WebSocket connections."""
        if not self.active_connections:
            return
        
        message_str = json.dumps(message)
        
        # Send to all active connections
        disconnected = []
        
        for connection_id, ws in self.active_connections.items():
            try:
                await ws.send_str(message_str)
            except Exception as e:
                logger.warning(f"Failed to send to WebSocket {connection_id}: {e}")
                disconnected.append(connection_id)
        
        # Clean up disconnected connections
        for connection_id in disconnected:
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
    
    async def start(self):
        """Start the API gateway server."""
        logger.info(f"Starting API gateway on {self.host}:{self.port}")
        
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        self.site = web.TCPSite(self.runner, self.host, self.port)
        await self.site.start()
        
        logger.info(f"API gateway started on http://{self.host}:{self.port}")
    
    async def stop(self):
        """Stop the API gateway server."""
        logger.info("Stopping API gateway...")
        
        # Close all WebSocket connections
        for ws in self.active_connections.values():
            await ws.close()
        
        self.active_connections.clear()
        
        if self.site:
            await self.site.stop()
        
        if self.runner:
            await self.runner.cleanup()
        
        logger.info("API gateway stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get API gateway status."""
        return {
            'running': self.runner is not None,
            'host': self.host,
            'port': self.port,
            'routes_count': len(self.routes),
            'services_count': len(self.services),
            'active_connections': len(self.active_connections),
            'metrics': {
                'total_requests': self.metrics.total_requests,
                'success_rate': (
                    self.metrics.successful_requests / max(1, self.metrics.total_requests)
                ),
                'avg_response_time': self.metrics.avg_response_time
            }
        }
