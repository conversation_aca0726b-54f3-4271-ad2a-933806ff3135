"""Custom exceptions for the trading bot."""

from typing import Any, Dict, Optional


class TradingBotError(Exception):
    """Base exception for all trading bot errors."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
        }


class APIError(TradingBotError):
    """Exception raised for API-related errors."""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
    ):
        self.status_code = status_code
        self.response_data = response_data or {}
        
        details = {"status_code": status_code, "response_data": response_data}
        super().__init__(message, error_code, details)


class DataError(TradingBotError):
    """Exception raised for data-related errors."""
    
    def __init__(
        self,
        message: str,
        data_source: Optional[str] = None,
        symbol: Optional[str] = None,
        error_code: Optional[str] = None,
    ):
        self.data_source = data_source
        self.symbol = symbol
        
        details = {"data_source": data_source, "symbol": symbol}
        super().__init__(message, error_code, details)


class RiskError(TradingBotError):
    """Exception raised for risk management violations."""
    
    def __init__(
        self,
        message: str,
        risk_type: Optional[str] = None,
        current_value: Optional[float] = None,
        limit_value: Optional[float] = None,
        error_code: Optional[str] = None,
    ):
        self.risk_type = risk_type
        self.current_value = current_value
        self.limit_value = limit_value
        
        details = {
            "risk_type": risk_type,
            "current_value": current_value,
            "limit_value": limit_value,
        }
        super().__init__(message, error_code, details)


class StrategyError(TradingBotError):
    """Exception raised for strategy-related errors."""
    
    def __init__(
        self,
        message: str,
        strategy_name: Optional[str] = None,
        symbol: Optional[str] = None,
        error_code: Optional[str] = None,
    ):
        self.strategy_name = strategy_name
        self.symbol = symbol
        
        details = {"strategy_name": strategy_name, "symbol": symbol}
        super().__init__(message, error_code, details)


class OrderError(TradingBotError):
    """Exception raised for order-related errors."""
    
    def __init__(
        self,
        message: str,
        order_id: Optional[str] = None,
        symbol: Optional[str] = None,
        order_type: Optional[str] = None,
        error_code: Optional[str] = None,
    ):
        self.order_id = order_id
        self.symbol = symbol
        self.order_type = order_type
        
        details = {
            "order_id": order_id,
            "symbol": symbol,
            "order_type": order_type,
        }
        super().__init__(message, error_code, details)


class ModelError(TradingBotError):
    """Exception raised for machine learning model errors."""
    
    def __init__(
        self,
        message: str,
        model_name: Optional[str] = None,
        model_version: Optional[str] = None,
        error_code: Optional[str] = None,
    ):
        self.model_name = model_name
        self.model_version = model_version
        
        details = {"model_name": model_name, "model_version": model_version}
        super().__init__(message, error_code, details)


class ConfigurationError(TradingBotError):
    """Exception raised for configuration-related errors."""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        error_code: Optional[str] = None,
    ):
        self.config_key = config_key
        self.config_value = config_value
        
        details = {"config_key": config_key, "config_value": config_value}
        super().__init__(message, error_code, details)


class DatabaseError(TradingBotError):
    """Exception raised for database-related errors."""
    
    def __init__(
        self,
        message: str,
        database_type: Optional[str] = None,
        operation: Optional[str] = None,
        error_code: Optional[str] = None,
    ):
        self.database_type = database_type
        self.operation = operation
        
        details = {"database_type": database_type, "operation": operation}
        super().__init__(message, error_code, details)
