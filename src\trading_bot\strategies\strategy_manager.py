"""
Strategy Manager

Manages all trading strategies, their lifecycle, and coordination.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum

from .base_strategy import BaseStrategy
from ..core.logger import get_logger

logger = get_logger(__name__)

class StrategyState(Enum):
    """Strategy states"""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"

class StrategyManager:
    """Manages all trading strategies"""
    
    def __init__(self):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_states: Dict[str, StrategyState] = {}
        self.strategy_tasks: Dict[str, asyncio.Task] = {}
        self.running = False
    
    def register_strategy(self, name: str, strategy: BaseStrategy):
        """Register a strategy"""
        self.strategies[name] = strategy
        self.strategy_states[name] = StrategyState.STOPPED
        logger.info(f"Registered strategy: {name}")
    
    async def start_strategy(self, name: str) -> bool:
        """Start a specific strategy"""
        if name not in self.strategies:
            logger.error(f"Strategy {name} not found")
            return False
        
        if self.strategy_states[name] == StrategyState.RUNNING:
            logger.warning(f"Strategy {name} is already running")
            return True
        
        try:
            strategy = self.strategies[name]
            task = asyncio.create_task(strategy.run())
            self.strategy_tasks[name] = task
            self.strategy_states[name] = StrategyState.RUNNING
            
            logger.info(f"Started strategy: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start strategy {name}: {e}")
            self.strategy_states[name] = StrategyState.ERROR
            return False
    
    async def stop_strategy(self, name: str) -> bool:
        """Stop a specific strategy"""
        if name not in self.strategies:
            logger.error(f"Strategy {name} not found")
            return False
        
        if self.strategy_states[name] == StrategyState.STOPPED:
            logger.warning(f"Strategy {name} is already stopped")
            return True
        
        try:
            if name in self.strategy_tasks:
                task = self.strategy_tasks[name]
                task.cancel()
                
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                
                del self.strategy_tasks[name]
            
            self.strategy_states[name] = StrategyState.STOPPED
            logger.info(f"Stopped strategy: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop strategy {name}: {e}")
            return False
    
    async def disable_all_strategies(self):
        """Disable all strategies immediately"""
        logger.critical("Disabling all strategies")
        
        for name in list(self.strategies.keys()):
            await self.stop_strategy(name)
        
        self.running = False
        logger.critical("All strategies disabled")
    
    async def stop_all_tasks(self):
        """Stop all background tasks"""
        logger.info("Stopping all strategy tasks")
        
        for name in list(self.strategy_tasks.keys()):
            await self.stop_strategy(name)
        
        logger.info("All strategy tasks stopped")
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get status of all strategies"""
        return {
            name: {
                'state': state.value,
                'running': name in self.strategy_tasks
            }
            for name, state in self.strategy_states.items()
        }
