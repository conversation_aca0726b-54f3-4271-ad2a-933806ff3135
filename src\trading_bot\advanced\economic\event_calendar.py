"""Economic event calendar and impact analysis."""

import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd

from ...core.config import settings
from ...core.logger import get_logger
from ...utils.cache import cache_manager
from ...utils.rate_limiter import RateLimiter

logger = get_logger(__name__)


class EventImportance(Enum):
    """Economic event importance levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class EconomicEvent:
    """Economic event data structure."""
    id: str
    name: str
    country: str
    date: datetime
    importance: EventImportance
    actual: Optional[float]
    forecast: Optional[float]
    previous: Optional[float]
    unit: str
    currency: str
    source: str
    description: str
    impact_score: Optional[float] = None
    market_reaction: Optional[Dict[str, float]] = None


@dataclass
class EventImpact:
    """Event impact analysis."""
    event: EconomicEvent
    surprise_factor: float  # How much actual differed from forecast
    historical_volatility: float  # Historical market reaction
    expected_impact: Dict[str, float]  # Expected impact on different assets
    confidence: float


class EventCalendar:
    """Economic event calendar with impact analysis."""
    
    def __init__(self):
        self.rate_limiter = RateLimiter(max_requests=100, time_window=60)
        self.event_weights = self._load_event_weights()
        self.market_impact_history = {}  # Cache for historical impact data
        
    async def get_upcoming_events(
        self,
        days_ahead: int = 7,
        min_importance: EventImportance = EventImportance.MEDIUM,
        countries: List[str] = None
    ) -> List[EconomicEvent]:
        """
        Get upcoming economic events.
        
        Args:
            days_ahead: Number of days to look ahead
            min_importance: Minimum importance level
            countries: List of countries to filter by
            
        Returns:
            List of upcoming economic events
        """
        if countries is None:
            countries = ['US', 'EU', 'GB', 'JP', 'CN']
        
        try:
            # Fetch events from multiple sources
            all_events = []
            
            # Try different event sources
            sources = [
                self._fetch_trading_economics_events,
                self._fetch_forex_factory_events,
                self._fetch_investing_com_events
            ]
            
            for source_func in sources:
                try:
                    events = await source_func(days_ahead, countries)
                    all_events.extend(events)
                except Exception as e:
                    logger.warning(f"Failed to fetch from event source: {e}")
                    continue
            
            # Filter by importance and deduplicate
            filtered_events = []
            seen_events = set()
            
            for event in all_events:
                # Check importance
                if event.importance.value < min_importance.value:
                    continue
                
                # Deduplicate by name and date
                event_key = f"{event.name}_{event.date.date()}"
                if event_key in seen_events:
                    continue
                
                seen_events.add(event_key)
                filtered_events.append(event)
            
            # Sort by date and importance
            filtered_events.sort(key=lambda x: (x.date, -self._importance_score(x.importance)))
            
            return filtered_events
            
        except Exception as e:
            logger.error(f"Error getting upcoming events: {e}")
            return []
    
    async def analyze_event_impact(
        self,
        event: EconomicEvent
    ) -> EventImpact:
        """
        Analyze potential market impact of an economic event.
        
        Args:
            event: Economic event to analyze
            
        Returns:
            Event impact analysis
        """
        try:
            # Calculate surprise factor
            surprise_factor = self._calculate_surprise_factor(event)
            
            # Get historical volatility for this event type
            historical_volatility = await self._get_historical_volatility(event.name)
            
            # Calculate expected impact on different assets
            expected_impact = self._calculate_expected_impact(event, surprise_factor)
            
            # Calculate confidence based on data quality
            confidence = self._calculate_confidence(event, historical_volatility)
            
            return EventImpact(
                event=event,
                surprise_factor=surprise_factor,
                historical_volatility=historical_volatility,
                expected_impact=expected_impact,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error analyzing event impact: {e}")
            return self._empty_impact(event)
    
    async def get_high_impact_events(
        self,
        days_ahead: int = 3
    ) -> List[EventImpact]:
        """
        Get high-impact events with analysis.
        
        Args:
            days_ahead: Number of days to look ahead
            
        Returns:
            List of high-impact event analyses
        """
        try:
            # Get upcoming events
            events = await self.get_upcoming_events(
                days_ahead=days_ahead,
                min_importance=EventImportance.HIGH
            )
            
            # Analyze impact for each event
            impact_analyses = []
            for event in events:
                impact = await self.analyze_event_impact(event)
                if impact.confidence > 0.6:  # Only include high-confidence analyses
                    impact_analyses.append(impact)
            
            # Sort by expected impact magnitude
            impact_analyses.sort(
                key=lambda x: max(abs(v) for v in x.expected_impact.values()),
                reverse=True
            )
            
            return impact_analyses
            
        except Exception as e:
            logger.error(f"Error getting high-impact events: {e}")
            return []
    
    async def _fetch_trading_economics_events(
        self,
        days_ahead: int,
        countries: List[str]
    ) -> List[EconomicEvent]:
        """Fetch events from Trading Economics API."""
        if not hasattr(settings, 'trading_economics_api_key'):
            return []
        
        await self.rate_limiter.acquire()
        
        try:
            end_date = datetime.utcnow() + timedelta(days=days_ahead)
            
            url = "https://api.tradingeconomics.com/calendar"
            params = {
                'c': ','.join(countries),
                'd1': datetime.utcnow().strftime('%Y-%m-%d'),
                'd2': end_date.strftime('%Y-%m-%d'),
                'f': 'json'
            }
            
            headers = {
                'Authorization': f'Client {settings.trading_economics_api_key}'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    data = await response.json()
            
            events = []
            for item in data:
                try:
                    event = EconomicEvent(
                        id=str(item.get('CalendarId', '')),
                        name=item.get('Event', ''),
                        country=item.get('Country', ''),
                        date=datetime.fromisoformat(item.get('Date', '').replace('T', ' ')),
                        importance=self._parse_importance(item.get('Importance', '')),
                        actual=self._safe_float(item.get('Actual')),
                        forecast=self._safe_float(item.get('Forecast')),
                        previous=self._safe_float(item.get('Previous')),
                        unit=item.get('Unit', ''),
                        currency=item.get('Currency', ''),
                        source='Trading Economics',
                        description=item.get('Event', '')
                    )
                    events.append(event)
                except Exception as e:
                    logger.warning(f"Error parsing Trading Economics event: {e}")
                    continue
            
            return events
            
        except Exception as e:
            logger.error(f"Error fetching Trading Economics events: {e}")
            return []
    
    async def _fetch_forex_factory_events(
        self,
        days_ahead: int,
        countries: List[str]
    ) -> List[EconomicEvent]:
        """Fetch events from Forex Factory (web scraping)."""
        # This would require web scraping
        # Placeholder implementation
        return []
    
    async def _fetch_investing_com_events(
        self,
        days_ahead: int,
        countries: List[str]
    ) -> List[EconomicEvent]:
        """Fetch events from Investing.com API."""
        # This would require Investing.com API access
        # Placeholder implementation
        return []
    
    def _calculate_surprise_factor(self, event: EconomicEvent) -> float:
        """Calculate surprise factor based on actual vs forecast."""
        if event.actual is None or event.forecast is None:
            return 0.0
        
        if event.forecast == 0:
            return 0.0
        
        # Calculate percentage difference
        surprise = (event.actual - event.forecast) / abs(event.forecast)
        
        # Normalize to [-1, 1] range
        return max(-1.0, min(1.0, surprise))
    
    async def _get_historical_volatility(self, event_name: str) -> float:
        """Get historical market volatility for this event type."""
        # This would typically query a database of historical market reactions
        # For now, return default values based on event type
        
        high_volatility_events = [
            'Non Farm Payrolls', 'Federal Funds Rate', 'GDP',
            'CPI', 'Retail Sales', 'Industrial Production'
        ]
        
        medium_volatility_events = [
            'Unemployment Rate', 'Consumer Confidence', 'PMI',
            'Housing Starts', 'Durable Goods Orders'
        ]
        
        event_lower = event_name.lower()
        
        for high_vol_event in high_volatility_events:
            if high_vol_event.lower() in event_lower:
                return 0.8
        
        for med_vol_event in medium_volatility_events:
            if med_vol_event.lower() in event_lower:
                return 0.5
        
        return 0.3  # Default low volatility
    
    def _calculate_expected_impact(
        self,
        event: EconomicEvent,
        surprise_factor: float
    ) -> Dict[str, float]:
        """Calculate expected impact on different asset classes."""
        base_impact = self._get_base_impact(event.name, event.country)
        
        # Scale by surprise factor and importance
        importance_multiplier = self._importance_score(event.importance)
        
        expected_impact = {}
        for asset, impact in base_impact.items():
            expected_impact[asset] = impact * surprise_factor * importance_multiplier
        
        return expected_impact
    
    def _get_base_impact(self, event_name: str, country: str) -> Dict[str, float]:
        """Get base impact patterns for different events."""
        # Simplified impact patterns
        # In practice, this would be based on historical analysis
        
        impact_patterns = {
            'Non Farm Payrolls': {
                'USD': 0.8, 'SPY': 0.6, 'TLT': -0.5, 'GLD': -0.3
            },
            'Federal Funds Rate': {
                'USD': 1.0, 'SPY': -0.7, 'TLT': -0.9, 'GLD': -0.6
            },
            'CPI': {
                'USD': 0.6, 'SPY': -0.4, 'TLT': -0.8, 'GLD': 0.5
            },
            'GDP': {
                'USD': 0.5, 'SPY': 0.8, 'TLT': -0.3, 'GLD': -0.2
            }
        }
        
        event_lower = event_name.lower()
        
        for pattern_event, impacts in impact_patterns.items():
            if pattern_event.lower() in event_lower:
                return impacts
        
        # Default neutral impact
        return {'USD': 0.0, 'SPY': 0.0, 'TLT': 0.0, 'GLD': 0.0}
    
    def _calculate_confidence(
        self,
        event: EconomicEvent,
        historical_volatility: float
    ) -> float:
        """Calculate confidence in impact analysis."""
        confidence = 0.5  # Base confidence
        
        # Higher confidence for events with forecast data
        if event.forecast is not None:
            confidence += 0.2
        
        # Higher confidence for high-importance events
        if event.importance in [EventImportance.HIGH, EventImportance.CRITICAL]:
            confidence += 0.2
        
        # Higher confidence for events with historical volatility data
        if historical_volatility > 0.5:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _parse_importance(self, importance_str: str) -> EventImportance:
        """Parse importance string to enum."""
        importance_lower = importance_str.lower()
        
        if 'high' in importance_lower or '3' in importance_str:
            return EventImportance.HIGH
        elif 'medium' in importance_lower or '2' in importance_str:
            return EventImportance.MEDIUM
        elif 'low' in importance_lower or '1' in importance_str:
            return EventImportance.LOW
        else:
            return EventImportance.MEDIUM
    
    def _importance_score(self, importance: EventImportance) -> float:
        """Convert importance to numeric score."""
        scores = {
            EventImportance.LOW: 0.25,
            EventImportance.MEDIUM: 0.5,
            EventImportance.HIGH: 0.75,
            EventImportance.CRITICAL: 1.0
        }
        return scores.get(importance, 0.5)
    
    def _safe_float(self, value: Any) -> Optional[float]:
        """Safely convert value to float."""
        if value is None or value == '':
            return None
        
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _load_event_weights(self) -> Dict[str, float]:
        """Load event importance weights."""
        return {
            'Non Farm Payrolls': 1.0,
            'Federal Funds Rate': 1.0,
            'GDP': 0.9,
            'CPI': 0.9,
            'Unemployment Rate': 0.8,
            'Retail Sales': 0.7,
            'Consumer Confidence': 0.6,
            'PMI': 0.6,
            'Housing Starts': 0.5,
            'Industrial Production': 0.7
        }
    
    def _empty_impact(self, event: EconomicEvent) -> EventImpact:
        """Return empty impact analysis."""
        return EventImpact(
            event=event,
            surprise_factor=0.0,
            historical_volatility=0.0,
            expected_impact={},
            confidence=0.0
        )
