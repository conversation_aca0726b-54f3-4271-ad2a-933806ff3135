"""
Resource tuning system for AI Trading Bot production deployment.

This module provides intelligent system resource optimization to ensure
optimal performance under varying load conditions:

Resource Management:
- CPU utilization optimization
- Memory allocation tuning
- I/O optimization
- Network resource management
- Database connection pooling
- Thread/async task management

Optimization Features:
- Dynamic resource allocation
- Load-based scaling
- Performance bottleneck detection
- Resource usage monitoring
- Automatic tuning recommendations
"""

import asyncio
import psutil
import gc
import logging
import threading
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
import numpy as np

from ....core.config import Config
from ....utils.logger import get_logger

logger = get_logger(__name__)


class ResourceType(Enum):
    """Types of system resources."""
    CPU = "cpu"
    MEMORY = "memory"
    DISK = "disk"
    NETWORK = "network"
    DATABASE = "database"
    THREADS = "threads"


class OptimizationLevel(Enum):
    """Resource optimization levels."""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"


@dataclass
class ResourceMetrics:
    """System resource metrics."""
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    # CPU metrics
    cpu_percent: float = 0.0
    cpu_cores: int = 0
    cpu_frequency: float = 0.0
    load_average: List[float] = field(default_factory=list)
    
    # Memory metrics
    memory_total: int = 0
    memory_available: int = 0
    memory_used: int = 0
    memory_percent: float = 0.0
    swap_total: int = 0
    swap_used: int = 0
    
    # Disk metrics
    disk_total: int = 0
    disk_used: int = 0
    disk_free: int = 0
    disk_percent: float = 0.0
    disk_io_read: int = 0
    disk_io_write: int = 0
    
    # Network metrics
    network_bytes_sent: int = 0
    network_bytes_recv: int = 0
    network_packets_sent: int = 0
    network_packets_recv: int = 0
    
    # Process metrics
    process_count: int = 0
    thread_count: int = 0
    open_files: int = 0
    connections: int = 0


@dataclass
class ResourceLimits:
    """Resource usage limits and thresholds."""
    # CPU limits
    cpu_warning_threshold: float = 70.0
    cpu_critical_threshold: float = 85.0
    cpu_max_usage: float = 90.0
    
    # Memory limits
    memory_warning_threshold: float = 75.0
    memory_critical_threshold: float = 85.0
    memory_max_usage: float = 90.0
    
    # Disk limits
    disk_warning_threshold: float = 80.0
    disk_critical_threshold: float = 90.0
    
    # Connection limits
    max_database_connections: int = 100
    max_api_connections: int = 200
    max_threads: int = 50


@dataclass
class OptimizationRecommendation:
    """Resource optimization recommendation."""
    resource_type: ResourceType
    priority: str  # "low", "medium", "high", "critical"
    description: str
    action: str
    expected_impact: str
    implementation_effort: str  # "low", "medium", "high"


class ResourceTuner:
    """
    Intelligent resource tuning system for production trading bot.
    
    Monitors system resources and automatically optimizes performance
    based on current load and usage patterns.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.optimization_level = OptimizationLevel.BALANCED
        self.limits = ResourceLimits()
        self.metrics_history: List[ResourceMetrics] = []
        self.is_monitoring = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Resource optimization settings
        self.gc_threshold_memory = 80.0  # Trigger GC at 80% memory
        self.connection_pool_sizes = {
            'database': 20,
            'redis': 10,
            'api': 50
        }
        
        # Performance tuning parameters
        self.async_task_limit = 100
        self.io_buffer_size = 64 * 1024  # 64KB
        self.batch_size_limits = {
            'market_data': 1000,
            'ml_inference': 100,
            'database_writes': 500
        }
    
    async def initialize(self):
        """Initialize resource tuner."""
        logger.info("Initializing resource tuner...")
        
        # Get initial system metrics
        initial_metrics = await self._collect_system_metrics()
        self.metrics_history.append(initial_metrics)
        
        # Apply initial optimizations
        await self._apply_initial_optimizations()
        
        # Start monitoring
        await self.start_monitoring()
        
        logger.info("Resource tuner initialized successfully")
    
    async def start_monitoring(self):
        """Start resource monitoring."""
        if self.is_monitoring:
            logger.warning("Resource monitoring already started")
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Resource monitoring started")
    
    async def stop_monitoring(self):
        """Stop resource monitoring."""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Resource monitoring stopped")
    
    async def get_current_metrics(self) -> ResourceMetrics:
        """Get current system resource metrics."""
        return await self._collect_system_metrics()
    
    async def get_optimization_recommendations(self) -> List[OptimizationRecommendation]:
        """Get resource optimization recommendations."""
        if not self.metrics_history:
            return []
        
        current_metrics = self.metrics_history[-1]
        recommendations = []
        
        # CPU optimization recommendations
        if current_metrics.cpu_percent > self.limits.cpu_warning_threshold:
            recommendations.extend(self._get_cpu_recommendations(current_metrics))
        
        # Memory optimization recommendations
        if current_metrics.memory_percent > self.limits.memory_warning_threshold:
            recommendations.extend(self._get_memory_recommendations(current_metrics))
        
        # Disk optimization recommendations
        if current_metrics.disk_percent > self.limits.disk_warning_threshold:
            recommendations.extend(self._get_disk_recommendations(current_metrics))
        
        # General performance recommendations
        recommendations.extend(self._get_performance_recommendations(current_metrics))
        
        return recommendations
    
    async def apply_optimizations(self, auto_apply: bool = False):
        """Apply resource optimizations."""
        recommendations = await self.get_optimization_recommendations()
        
        if not recommendations:
            logger.info("No optimizations needed")
            return
        
        logger.info(f"Found {len(recommendations)} optimization opportunities")
        
        for rec in recommendations:
            if auto_apply or rec.priority in ["high", "critical"]:
                await self._apply_optimization(rec)
            else:
                logger.info(f"Recommendation: {rec.description} - {rec.action}")
    
    async def tune_for_workload(self, workload_type: str):
        """Tune resources for specific workload type."""
        logger.info(f"Tuning resources for workload: {workload_type}")
        
        if workload_type == "high_frequency_trading":
            await self._tune_for_hft()
        elif workload_type == "ml_inference":
            await self._tune_for_ml()
        elif workload_type == "data_processing":
            await self._tune_for_data_processing()
        else:
            await self._tune_for_balanced()
        
        logger.info(f"Resource tuning completed for {workload_type}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                # Collect metrics
                metrics = await self._collect_system_metrics()
                self.metrics_history.append(metrics)
                
                # Keep only last 1000 metrics (about 16 hours at 1-minute intervals)
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-1000:]
                
                # Check for critical resource issues
                await self._check_critical_resources(metrics)
                
                # Apply automatic optimizations if enabled
                if self.optimization_level == OptimizationLevel.AGGRESSIVE:
                    await self.apply_optimizations(auto_apply=True)
                
                # Sleep for monitoring interval
                await asyncio.sleep(60)  # Monitor every minute
                
            except Exception as e:
                logger.error(f"Resource monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _collect_system_metrics(self) -> ResourceMetrics:
        """Collect current system metrics."""
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
        
        # Memory metrics
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Disk metrics
        disk = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        
        # Network metrics
        network = psutil.net_io_counters()
        
        # Process metrics
        process_count = len(psutil.pids())
        current_process = psutil.Process()
        thread_count = current_process.num_threads()
        
        try:
            open_files = len(current_process.open_files())
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            open_files = 0
        
        try:
            connections = len(current_process.connections())
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            connections = 0
        
        return ResourceMetrics(
            cpu_percent=cpu_percent,
            cpu_cores=cpu_count,
            cpu_frequency=cpu_freq.current if cpu_freq else 0,
            load_average=list(load_avg),
            memory_total=memory.total,
            memory_available=memory.available,
            memory_used=memory.used,
            memory_percent=memory.percent,
            swap_total=swap.total,
            swap_used=swap.used,
            disk_total=disk.total,
            disk_used=disk.used,
            disk_free=disk.free,
            disk_percent=(disk.used / disk.total) * 100,
            disk_io_read=disk_io.read_bytes if disk_io else 0,
            disk_io_write=disk_io.write_bytes if disk_io else 0,
            network_bytes_sent=network.bytes_sent,
            network_bytes_recv=network.bytes_recv,
            network_packets_sent=network.packets_sent,
            network_packets_recv=network.packets_recv,
            process_count=process_count,
            thread_count=thread_count,
            open_files=open_files,
            connections=connections
        )

    async def _check_critical_resources(self, metrics: ResourceMetrics):
        """Check for critical resource usage and take immediate action."""
        # Critical CPU usage
        if metrics.cpu_percent > self.limits.cpu_critical_threshold:
            logger.warning(f"Critical CPU usage: {metrics.cpu_percent}%")
            await self._handle_high_cpu()

        # Critical memory usage
        if metrics.memory_percent > self.limits.memory_critical_threshold:
            logger.warning(f"Critical memory usage: {metrics.memory_percent}%")
            await self._handle_high_memory()

        # Critical disk usage
        if metrics.disk_percent > self.limits.disk_critical_threshold:
            logger.warning(f"Critical disk usage: {metrics.disk_percent}%")
            await self._handle_high_disk()

    async def _handle_high_cpu(self):
        """Handle high CPU usage."""
        logger.info("Applying CPU optimization measures...")

        # Force garbage collection
        gc.collect()

        # Reduce batch sizes
        for key in self.batch_size_limits:
            self.batch_size_limits[key] = max(self.batch_size_limits[key] // 2, 10)

        # Reduce async task limit
        self.async_task_limit = max(self.async_task_limit // 2, 10)

        logger.info("CPU optimization measures applied")

    async def _handle_high_memory(self):
        """Handle high memory usage."""
        logger.info("Applying memory optimization measures...")

        # Force aggressive garbage collection
        for _ in range(3):
            gc.collect()

        # Reduce connection pool sizes
        for key in self.connection_pool_sizes:
            self.connection_pool_sizes[key] = max(self.connection_pool_sizes[key] // 2, 5)

        # Reduce buffer sizes
        self.io_buffer_size = max(self.io_buffer_size // 2, 8192)

        logger.info("Memory optimization measures applied")

    async def _handle_high_disk(self):
        """Handle high disk usage."""
        logger.info("Applying disk optimization measures...")

        # This would trigger log rotation, cache cleanup, etc.
        # For now, just log the issue
        logger.warning("High disk usage detected - manual intervention may be required")

    async def _apply_initial_optimizations(self):
        """Apply initial system optimizations."""
        logger.info("Applying initial optimizations...")

        # Set garbage collection thresholds
        gc.set_threshold(700, 10, 10)

        # Enable garbage collection debugging in development
        if hasattr(self.config, 'debug') and self.config.debug:
            gc.set_debug(gc.DEBUG_STATS)

        logger.info("Initial optimizations applied")

    def _get_cpu_recommendations(self, metrics: ResourceMetrics) -> List[OptimizationRecommendation]:
        """Get CPU optimization recommendations."""
        recommendations = []

        if metrics.cpu_percent > 85:
            recommendations.append(OptimizationRecommendation(
                resource_type=ResourceType.CPU,
                priority="critical",
                description="CPU usage is critically high",
                action="Reduce batch sizes, optimize algorithms, consider horizontal scaling",
                expected_impact="Significant performance improvement",
                implementation_effort="medium"
            ))
        elif metrics.cpu_percent > 70:
            recommendations.append(OptimizationRecommendation(
                resource_type=ResourceType.CPU,
                priority="high",
                description="CPU usage is high",
                action="Profile code for bottlenecks, optimize hot paths",
                expected_impact="Moderate performance improvement",
                implementation_effort="medium"
            ))

        return recommendations

    def _get_memory_recommendations(self, metrics: ResourceMetrics) -> List[OptimizationRecommendation]:
        """Get memory optimization recommendations."""
        recommendations = []

        if metrics.memory_percent > 85:
            recommendations.append(OptimizationRecommendation(
                resource_type=ResourceType.MEMORY,
                priority="critical",
                description="Memory usage is critically high",
                action="Force garbage collection, reduce cache sizes, check for memory leaks",
                expected_impact="Prevent out-of-memory errors",
                implementation_effort="low"
            ))
        elif metrics.memory_percent > 75:
            recommendations.append(OptimizationRecommendation(
                resource_type=ResourceType.MEMORY,
                priority="high",
                description="Memory usage is high",
                action="Optimize data structures, implement memory pooling",
                expected_impact="Reduced memory pressure",
                implementation_effort="medium"
            ))

        return recommendations

    def _get_disk_recommendations(self, metrics: ResourceMetrics) -> List[OptimizationRecommendation]:
        """Get disk optimization recommendations."""
        recommendations = []

        if metrics.disk_percent > 90:
            recommendations.append(OptimizationRecommendation(
                resource_type=ResourceType.DISK,
                priority="critical",
                description="Disk usage is critically high",
                action="Clean up logs, implement log rotation, archive old data",
                expected_impact="Prevent disk full errors",
                implementation_effort="low"
            ))
        elif metrics.disk_percent > 80:
            recommendations.append(OptimizationRecommendation(
                resource_type=ResourceType.DISK,
                priority="medium",
                description="Disk usage is high",
                action="Implement data compression, optimize storage usage",
                expected_impact="Increased available storage",
                implementation_effort="medium"
            ))

        return recommendations

    def _get_performance_recommendations(self, metrics: ResourceMetrics) -> List[OptimizationRecommendation]:
        """Get general performance recommendations."""
        recommendations = []

        # Check thread count
        if metrics.thread_count > self.limits.max_threads:
            recommendations.append(OptimizationRecommendation(
                resource_type=ResourceType.THREADS,
                priority="medium",
                description="High thread count detected",
                action="Optimize async operations, reduce thread pool sizes",
                expected_impact="Reduced context switching overhead",
                implementation_effort="medium"
            ))

        # Check connection count
        if metrics.connections > 100:
            recommendations.append(OptimizationRecommendation(
                resource_type=ResourceType.NETWORK,
                priority="medium",
                description="High connection count",
                action="Implement connection pooling, close unused connections",
                expected_impact="Reduced network overhead",
                implementation_effort="low"
            ))

        return recommendations

    async def _apply_optimization(self, recommendation: OptimizationRecommendation):
        """Apply a specific optimization recommendation."""
        logger.info(f"Applying optimization: {recommendation.description}")

        try:
            if recommendation.resource_type == ResourceType.CPU:
                await self._apply_cpu_optimization(recommendation)
            elif recommendation.resource_type == ResourceType.MEMORY:
                await self._apply_memory_optimization(recommendation)
            elif recommendation.resource_type == ResourceType.DISK:
                await self._apply_disk_optimization(recommendation)
            elif recommendation.resource_type == ResourceType.THREADS:
                await self._apply_thread_optimization(recommendation)

            logger.info(f"Optimization applied successfully: {recommendation.action}")

        except Exception as e:
            logger.error(f"Failed to apply optimization: {e}")

    async def _apply_cpu_optimization(self, recommendation: OptimizationRecommendation):
        """Apply CPU-specific optimizations."""
        if "batch sizes" in recommendation.action:
            # Reduce batch sizes
            for key in self.batch_size_limits:
                self.batch_size_limits[key] = max(self.batch_size_limits[key] // 2, 10)

        if "algorithms" in recommendation.action:
            # This would trigger algorithm optimization
            logger.info("Algorithm optimization triggered")

    async def _apply_memory_optimization(self, recommendation: OptimizationRecommendation):
        """Apply memory-specific optimizations."""
        if "garbage collection" in recommendation.action:
            # Force garbage collection
            for _ in range(3):
                gc.collect()

        if "cache sizes" in recommendation.action:
            # This would trigger cache size reduction
            logger.info("Cache size optimization triggered")

    async def _apply_disk_optimization(self, recommendation: OptimizationRecommendation):
        """Apply disk-specific optimizations."""
        if "logs" in recommendation.action:
            # This would trigger log cleanup
            logger.info("Log cleanup optimization triggered")

    async def _apply_thread_optimization(self, recommendation: OptimizationRecommendation):
        """Apply thread-specific optimizations."""
        if "thread pool" in recommendation.action:
            # Reduce async task limit
            self.async_task_limit = max(self.async_task_limit // 2, 10)

    async def _tune_for_hft(self):
        """Tune for high-frequency trading workload."""
        logger.info("Tuning for high-frequency trading...")

        # Optimize for low latency
        self.batch_size_limits['market_data'] = 100  # Smaller batches
        self.io_buffer_size = 32 * 1024  # Smaller buffers for lower latency
        self.async_task_limit = 200  # More concurrent tasks

        # Aggressive garbage collection
        gc.set_threshold(500, 5, 5)

    async def _tune_for_ml(self):
        """Tune for ML inference workload."""
        logger.info("Tuning for ML inference...")

        # Optimize for throughput
        self.batch_size_limits['ml_inference'] = 200  # Larger batches
        self.connection_pool_sizes['database'] = 30  # More DB connections

        # Less frequent garbage collection
        gc.set_threshold(1000, 15, 15)

    async def _tune_for_data_processing(self):
        """Tune for data processing workload."""
        logger.info("Tuning for data processing...")

        # Optimize for large data handling
        self.io_buffer_size = 128 * 1024  # Larger buffers
        self.batch_size_limits['database_writes'] = 1000  # Larger batches

        # Moderate garbage collection
        gc.set_threshold(800, 10, 10)

    async def _tune_for_balanced(self):
        """Tune for balanced workload."""
        logger.info("Tuning for balanced workload...")

        # Reset to default values
        self.batch_size_limits = {
            'market_data': 1000,
            'ml_inference': 100,
            'database_writes': 500
        }
        self.io_buffer_size = 64 * 1024
        self.async_task_limit = 100

        # Default garbage collection
        gc.set_threshold(700, 10, 10)

    async def shutdown(self):
        """Shutdown resource tuner."""
        logger.info("Shutting down resource tuner...")
        await self.stop_monitoring()
        logger.info("Resource tuner shutdown completed")
