# Production Deployment Configuration for AI Trading Bot
# This file contains production-ready deployment settings

# Deployment Configuration
deployment:
  environment: "production"
  mode: "paper"  # Start with paper trading, switch to "live" after validation
  version: "1.0.0"
  build_date: ""
  git_commit: ""
  
  # Resource Configuration
  resources:
    cpu_cores: 2.0
    memory_gb: 4.0
    disk_gb: 100.0
    max_connections: 100
    max_concurrent_orders: 10
  
  # Auto-scaling Configuration
  scaling:
    min_replicas: 2
    max_replicas: 5
    target_cpu_utilization: 70
    target_memory_utilization: 80
    scale_up_cooldown: 300
    scale_down_cooldown: 600
  
  # Health Check Configuration
  health_check:
    enabled: true
    interval: 30
    timeout: 10
    retries: 3
    startup_delay: 60
    endpoints:
      - "/health"
      - "/ready"
  
  # Monitoring Configuration
  monitoring:
    metrics_enabled: true
    metrics_port: 9090
    metrics_path: "/metrics"
    logging_level: "INFO"
    log_format: "json"
    alert_cooldown: 300
  
  # Security Configuration
  security:
    enable_tls: true
    require_auth: true
    api_key_rotation_days: 30
    session_timeout: 3600
    max_login_attempts: 3
    lockout_duration: 900
  
  # Backup Configuration
  backup:
    enabled: true
    frequency: "daily"
    retention_days: 30
    backup_location: "/opt/trading-bot/backups"
    compress: true
    encrypt: true

# Trading Configuration for Production
trading:
  mode: "paper"  # paper, live, simulation
  start_capital: 100000
  max_daily_loss: 0.02
  max_positions: 20
  max_daily_trades: 100
  position_check_interval: 30
  
  # Paper Trading Settings
  paper_trading:
    slippage: 0.001  # 0.1% slippage simulation
    commission: 1.0  # $1 per trade
    market_impact: 0.0005  # 0.05% market impact
    fill_probability: 0.98  # 98% fill rate

# Risk Management Configuration
risk:
  max_daily_loss: 0.02
  max_position_size: 0.05
  max_sector_exposure: 0.30
  max_drawdown_threshold: 0.10
  emergency_stop_loss: 0.05
  
  # Circuit Breakers
  circuit_breakers:
    daily_loss_2pct: 0.02
    daily_loss_5pct: 0.05
    daily_loss_10pct: 0.10
  
  # Position Sizing
  position_sizing:
    method: "kelly_criterion"
    max_kelly_fraction: 0.25
    min_position_size: 0.01
    volatility_lookback: 20

# API Configuration
api:
  rate_limit_multiplier: 0.8  # Stay below API limits
  connection_timeout: 5
  retry_attempts: 3
  retry_delay: 1
  max_retries_per_minute: 30
  
  # Webull API Settings
  webull:
    rate_limit_data: 30  # requests per second
    rate_limit_orders: 3  # requests per second
    connection_pool_size: 10

# Database Configuration
database:
  # PostgreSQL
  postgres:
    pool_size: 20
    max_overflow: 10
    connection_timeout: 30
    query_timeout: 30
    
  # Redis
  redis:
    pool_size: 20
    connection_timeout: 5
    max_connections: 100
    
  # MongoDB
  mongodb:
    pool_size: 20
    connection_timeout: 30
    server_selection_timeout: 30

# ML Configuration
ml:
  inference_batch_size: 32
  model_cache_size: 5
  gpu_memory_fraction: 0.8
  prediction_timeout: 5
  model_update_frequency: "daily"
  confidence_threshold: 0.7

# Performance Targets (milliseconds)
performance:
  targets:
    market_data_processing: 5
    feature_calculation: 10
    ml_inference: 50
    order_execution: 100
    risk_checks: 20
    total_loop_time: 200
  
  # Monitoring Thresholds
  thresholds:
    response_time_warning: 1000
    response_time_critical: 5000
    error_rate_warning: 0.05
    error_rate_critical: 0.10
    memory_warning: 0.85
    memory_critical: 0.95
    cpu_warning: 0.80
    cpu_critical: 0.90

# Feature Flags
feature_flags:
  enable_sentiment_analysis: true
  enable_options_trading: false
  enable_crypto_trading: false
  enable_advanced_analytics: true
  enable_real_time_alerts: true
  enable_portfolio_optimization: true

# Alerting Configuration
alerts:
  # Trading Alerts
  trading:
    daily_loss_threshold: 0.01  # 1%
    position_size_breach: true
    risk_limit_breach: true
    model_accuracy_drop: 0.60  # Below 60%
  
  # System Alerts
  system:
    high_cpu_usage: 0.80
    high_memory_usage: 0.85
    disk_space_low: 0.90
    api_error_rate: 0.05
    database_latency: 100  # milliseconds
  
  # Notification Channels
  notifications:
    email:
      enabled: true
      recipients: ["<EMAIL>"]
    slack:
      enabled: false
      webhook_url: ""
    telegram:
      enabled: true
      bot_token: ""
      chat_id: ""

# Maintenance Configuration
maintenance:
  # Daily Tasks
  daily:
    - "performance_report_review"
    - "risk_limit_verification"
    - "model_accuracy_check"
    - "system_health_check"
  
  # Weekly Tasks
  weekly:
    - "strategy_performance_review"
    - "model_retraining_evaluation"
    - "database_optimization"
    - "security_updates"
  
  # Monthly Tasks
  monthly:
    - "full_system_audit"
    - "disaster_recovery_test"
    - "performance_optimization"
    - "strategy_rebalancing"

# Go-Live Phases
go_live:
  # Phase 1: Limited Trading (Week 1-2)
  phase_1:
    duration_days: 14
    capital_percentage: 0.10
    max_positions: 5
    max_trade_size: 1000
    manual_approval_threshold: 1000
    
  # Phase 2: Expanded Trading (Week 3-4)
  phase_2:
    duration_days: 14
    capital_percentage: 0.25
    max_positions: 10
    max_trade_size: 5000
    manual_approval_threshold: 5000
    
  # Phase 3: Full Operations (Week 5+)
  phase_3:
    capital_percentage: 1.0
    max_positions: 20
    max_trade_size: 50000
    manual_approval_threshold: 50000

# Launch Criteria Checklist
launch_criteria:
  paper_trading_days: 30
  min_win_rate: 0.55
  min_sharpe_ratio: 1.0
  max_drawdown: 0.15
  min_success_rate: 0.95
  max_critical_failures: 0
  
  required_tests:
    - "integration_tests"
    - "stress_tests"
    - "performance_tests"
    - "paper_trading_validation"
    - "disaster_recovery_test"
  
  required_documentation:
    - "system_architecture"
    - "api_documentation"
    - "strategy_descriptions"
    - "risk_management_policies"
    - "operational_runbook"
    - "emergency_procedures"

# Rollback Configuration
rollback:
  # Automatic Rollback Triggers
  triggers:
    daily_loss_threshold: 0.05  # 5%
    error_rate_threshold: 0.10  # 10%
    system_failure: true
    manual_trigger: true
  
  # Rollback Procedures
  procedures:
    - "stop_all_trading"
    - "close_open_positions"
    - "revert_configuration"
    - "restore_database_snapshot"
    - "notify_stakeholders"
  
  # Recovery Time Objectives
  rto:
    critical_failure: 300  # 5 minutes
    data_corruption: 1800  # 30 minutes
    complete_system: 7200  # 2 hours
