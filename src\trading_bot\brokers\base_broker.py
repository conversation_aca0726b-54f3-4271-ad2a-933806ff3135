"""
Base Broker Interface

Abstract base class that all broker implementations must inherit from.
Defines the standard interface for all trading operations.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime

class BaseBroker(ABC):
    """Abstract base class for all broker implementations"""
    
    def __init__(self, paper_trading: bool = True):
        self.paper_trading = paper_trading
        self.connected = False
        self.account_info = {}
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to the broker"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """Disconnect from the broker"""
        pass
    
    @abstractmethod
    async def is_session_valid(self) -> bool:
        """Check if session is valid"""
        pass
    
    @abstractmethod
    async def refresh_token(self) -> bool:
        """Refresh authentication token"""
        pass
    
    @abstractmethod
    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        pass
    
    @abstractmethod
    async def get_buying_power(self) -> float:
        """Get available buying power"""
        pass
    
    @abstractmethod
    async def get_positions(self) -> List[Dict[str, Any]]:
        """Get current positions"""
        pass
    
    @abstractmethod
    async def get_pending_orders(self) -> List[Dict[str, Any]]:
        """Get pending orders"""
        pass
    
    @abstractmethod
    async def get_order_history(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get order history"""
        pass
    
    @abstractmethod
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get quote for a symbol"""
        pass
    
    @abstractmethod
    async def get_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get quotes for multiple symbols"""
        pass
    
    @abstractmethod
    async def get_historical_data(self, symbol: str, interval: str, days: int) -> List[Dict[str, Any]]:
        """Get historical data"""
        pass
    
    @abstractmethod
    async def get_options_chain(self, symbol: str) -> Dict[str, Any]:
        """Get options chain"""
        pass
    
    @abstractmethod
    async def get_market_hours(self) -> Dict[str, Any]:
        """Get market hours"""
        pass
    
    @abstractmethod
    async def place_order(self, 
                         symbol: str,
                         quantity: int,
                         side: str,
                         order_type: str,
                         price: Optional[float] = None,
                         stop_price: Optional[float] = None,
                         time_in_force: str = "DAY",
                         **kwargs) -> Dict[str, Any]:
        """Place an order"""
        pass
    
    @abstractmethod
    async def modify_order(self, order_id: str, **kwargs) -> Dict[str, Any]:
        """Modify an existing order"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str) -> Dict[str, Any]:
        """Get order status"""
        pass
