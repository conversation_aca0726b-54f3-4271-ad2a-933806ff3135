"""
Database query optimization for AI Trading Bot.

This module provides comprehensive database query analysis and optimization
to ensure optimal database performance for trading operations.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import asyncpg
import redis.asyncio as redis
from motor.motor_asyncio import AsyncIOMotorClient

from ...core.config import Config
from ...utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class QueryMetrics:
    """Metrics for a database query."""
    query_hash: str
    query_text: str
    execution_time_ms: float
    rows_examined: int
    rows_returned: int
    index_usage: List[str]
    table_scans: int
    join_operations: int
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    @property
    def efficiency_ratio(self) -> float:
        """Calculate query efficiency ratio."""
        if self.rows_examined == 0:
            return 1.0
        return self.rows_returned / self.rows_examined


@dataclass
class QueryAnalysis:
    """Comprehensive query analysis result."""
    query_hash: str
    query_text: str
    avg_execution_time: float
    max_execution_time: float
    min_execution_time: float
    execution_count: int
    total_time: float
    efficiency_score: float
    optimization_suggestions: List[str] = field(default_factory=list)
    index_recommendations: List[str] = field(default_factory=list)
    
    @property
    def performance_grade(self) -> str:
        """Get performance grade (A-F)."""
        if self.avg_execution_time < 10:
            return "A"
        elif self.avg_execution_time < 50:
            return "B"
        elif self.avg_execution_time < 100:
            return "C"
        elif self.avg_execution_time < 500:
            return "D"
        else:
            return "F"


class QueryOptimizer:
    """
    Comprehensive database query optimizer.
    
    Provides query analysis and optimization for:
    - PostgreSQL query optimization
    - Redis operation optimization
    - MongoDB query optimization
    - Index recommendation
    - Query plan analysis
    - Performance monitoring
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.query_metrics: Dict[str, List[QueryMetrics]] = {}
        self.pg_pool = None
        self.redis_client = None
        self.mongo_client = None
        
        # Performance thresholds (in milliseconds)
        self.performance_thresholds = {
            'fast': 10,
            'acceptable': 50,
            'slow': 100,
            'critical': 500
        }
    
    async def initialize(self):
        """Initialize database connections for optimization."""
        try:
            # Initialize PostgreSQL connection pool
            self.pg_pool = await asyncpg.create_pool(
                host=self.config.database.postgres.host,
                port=self.config.database.postgres.port,
                user=self.config.database.postgres.user,
                password=self.config.database.postgres.password,
                database=self.config.database.postgres.database,
                min_size=5,
                max_size=20
            )
            
            # Initialize Redis client
            self.redis_client = redis.Redis(
                host=self.config.database.redis.host,
                port=self.config.database.redis.port,
                password=self.config.database.redis.password,
                decode_responses=True
            )
            
            # Initialize MongoDB client
            self.mongo_client = AsyncIOMotorClient(
                f"mongodb://{self.config.database.mongodb.host}:{self.config.database.mongodb.port}"
            )
            
            logger.info("Query optimizer initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize query optimizer: {e}")
            raise
    
    async def cleanup(self):
        """Clean up database connections."""
        try:
            if self.pg_pool:
                await self.pg_pool.close()
            if self.redis_client:
                await self.redis_client.close()
            if self.mongo_client:
                self.mongo_client.close()
            
            logger.info("Query optimizer cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup query optimizer: {e}")
    
    async def analyze_postgresql_queries(self) -> List[QueryAnalysis]:
        """Analyze PostgreSQL query performance."""
        if not self.pg_pool:
            raise RuntimeError("PostgreSQL pool not initialized")
        
        try:
            async with self.pg_pool.acquire() as conn:
                # Get query statistics from pg_stat_statements
                query_stats = await conn.fetch("""
                    SELECT 
                        query,
                        calls,
                        total_time,
                        mean_time,
                        max_time,
                        min_time,
                        rows,
                        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
                    FROM pg_stat_statements 
                    WHERE query NOT LIKE '%pg_stat_statements%'
                    ORDER BY total_time DESC
                    LIMIT 50
                """)
                
                analyses = []
                for row in query_stats:
                    analysis = await self._analyze_postgresql_query(conn, row)
                    analyses.append(analysis)
                
                return analyses
                
        except Exception as e:
            logger.error(f"Failed to analyze PostgreSQL queries: {e}")
            return []
    
    async def _analyze_postgresql_query(self, conn, query_row) -> QueryAnalysis:
        """Analyze a single PostgreSQL query."""
        query_text = query_row['query']
        query_hash = str(hash(query_text))
        
        # Get query plan
        try:
            plan = await conn.fetchrow(f"EXPLAIN (FORMAT JSON) {query_text}")
            plan_data = plan[0] if plan else {}
        except:
            plan_data = {}
        
        # Generate optimization suggestions
        suggestions = self._generate_postgresql_suggestions(query_text, query_row, plan_data)
        
        # Generate index recommendations
        index_recommendations = self._generate_index_recommendations(query_text, plan_data)
        
        # Calculate efficiency score
        hit_percent = query_row.get('hit_percent', 0) or 0
        efficiency_score = min(100, hit_percent + (100 - query_row['mean_time']))
        
        return QueryAnalysis(
            query_hash=query_hash,
            query_text=query_text[:200] + "..." if len(query_text) > 200 else query_text,
            avg_execution_time=query_row['mean_time'],
            max_execution_time=query_row['max_time'],
            min_execution_time=query_row['min_time'],
            execution_count=query_row['calls'],
            total_time=query_row['total_time'],
            efficiency_score=efficiency_score,
            optimization_suggestions=suggestions,
            index_recommendations=index_recommendations
        )
    
    def _generate_postgresql_suggestions(self, query_text: str, query_row: Dict, 
                                       plan_data: Dict) -> List[str]:
        """Generate PostgreSQL optimization suggestions."""
        suggestions = []
        
        # Check execution time
        if query_row['mean_time'] > self.performance_thresholds['slow']:
            suggestions.append("Query execution time is slow - consider optimization")
        
        # Check for table scans
        if 'Seq Scan' in str(plan_data):
            suggestions.append("Query uses sequential scan - consider adding indexes")
        
        # Check for missing WHERE clauses
        if 'WHERE' not in query_text.upper() and 'SELECT' in query_text.upper():
            suggestions.append("Consider adding WHERE clause to limit result set")
        
        # Check for SELECT *
        if 'SELECT *' in query_text.upper():
            suggestions.append("Avoid SELECT * - specify only needed columns")
        
        # Check for subqueries
        if query_text.count('SELECT') > 1:
            suggestions.append("Consider optimizing subqueries or using JOINs")
        
        # Check for ORDER BY without LIMIT
        if 'ORDER BY' in query_text.upper() and 'LIMIT' not in query_text.upper():
            suggestions.append("Consider adding LIMIT to ORDER BY queries")
        
        # Check buffer hit ratio
        hit_percent = query_row.get('hit_percent', 0) or 0
        if hit_percent < 95:
            suggestions.append(f"Low buffer hit ratio ({hit_percent:.1f}%) - consider increasing shared_buffers")
        
        return suggestions
    
    def _generate_index_recommendations(self, query_text: str, plan_data: Dict) -> List[str]:
        """Generate index recommendations for PostgreSQL."""
        recommendations = []
        
        # Extract table and column information
        query_upper = query_text.upper()
        
        # Look for WHERE clauses
        if 'WHERE' in query_upper:
            # Simple pattern matching for common cases
            if 'symbol =' in query_text.lower():
                recommendations.append("CREATE INDEX idx_symbol ON table_name (symbol)")
            
            if 'timestamp >' in query_text.lower() or 'timestamp <' in query_text.lower():
                recommendations.append("CREATE INDEX idx_timestamp ON table_name (timestamp)")
            
            if 'created_at >' in query_text.lower():
                recommendations.append("CREATE INDEX idx_created_at ON table_name (created_at)")
        
        # Look for JOIN conditions
        if 'JOIN' in query_upper:
            recommendations.append("Ensure JOIN columns are indexed")
        
        # Look for ORDER BY
        if 'ORDER BY' in query_upper:
            recommendations.append("Consider index on ORDER BY columns")
        
        return recommendations
    
    async def analyze_redis_operations(self) -> Dict[str, Any]:
        """Analyze Redis operation performance."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            # Get Redis info
            info = await self.redis_client.info()
            
            # Get slow log
            slow_log = await self.redis_client.slowlog_get(10)
            
            # Analyze memory usage
            memory_info = await self.redis_client.info('memory')
            
            analysis = {
                'connection_info': {
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': memory_info.get('used_memory_human', '0'),
                    'hit_rate': self._calculate_redis_hit_rate(info)
                },
                'slow_operations': [
                    {
                        'command': entry['command'].decode() if isinstance(entry['command'], bytes) else str(entry['command']),
                        'duration_ms': entry['duration'] / 1000,  # Convert microseconds to milliseconds
                        'timestamp': entry['timestamp']
                    }
                    for entry in slow_log
                ],
                'recommendations': self._generate_redis_recommendations(info, memory_info, slow_log)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze Redis operations: {e}")
            return {}
    
    def _calculate_redis_hit_rate(self, info: Dict) -> float:
        """Calculate Redis cache hit rate."""
        hits = info.get('keyspace_hits', 0)
        misses = info.get('keyspace_misses', 0)
        
        if hits + misses == 0:
            return 0.0
        
        return (hits / (hits + misses)) * 100
    
    def _generate_redis_recommendations(self, info: Dict, memory_info: Dict, 
                                      slow_log: List) -> List[str]:
        """Generate Redis optimization recommendations."""
        recommendations = []
        
        # Check hit rate
        hit_rate = self._calculate_redis_hit_rate(info)
        if hit_rate < 90:
            recommendations.append(f"Low cache hit rate ({hit_rate:.1f}%) - review caching strategy")
        
        # Check memory usage
        used_memory = memory_info.get('used_memory', 0)
        max_memory = memory_info.get('maxmemory', 0)
        
        if max_memory > 0 and used_memory / max_memory > 0.8:
            recommendations.append("High memory usage - consider increasing maxmemory or implementing eviction")
        
        # Check slow operations
        if slow_log:
            recommendations.append("Slow operations detected - review command complexity")
        
        # Check connected clients
        connected_clients = info.get('connected_clients', 0)
        if connected_clients > 100:
            recommendations.append("High number of connected clients - consider connection pooling")
        
        # General recommendations
        recommendations.extend([
            "Use pipelining for multiple operations",
            "Implement appropriate TTL for cached data",
            "Use Redis Cluster for horizontal scaling if needed"
        ])
        
        return recommendations
    
    async def optimize_trading_queries(self) -> Dict[str, Any]:
        """Optimize queries specific to trading operations."""
        optimizations = {
            'market_data_queries': [],
            'order_queries': [],
            'position_queries': [],
            'analytics_queries': []
        }
        
        # Market data optimizations
        optimizations['market_data_queries'] = [
            "CREATE INDEX idx_market_data_symbol_timestamp ON market_data (symbol, timestamp DESC)",
            "CREATE INDEX idx_market_data_timestamp ON market_data (timestamp DESC)",
            "PARTITION market_data table by timestamp for better performance"
        ]
        
        # Order optimizations
        optimizations['order_queries'] = [
            "CREATE INDEX idx_orders_status_timestamp ON orders (status, created_at DESC)",
            "CREATE INDEX idx_orders_symbol_status ON orders (symbol, status)",
            "CREATE INDEX idx_orders_user_timestamp ON orders (user_id, created_at DESC)"
        ]
        
        # Position optimizations
        optimizations['position_queries'] = [
            "CREATE INDEX idx_positions_symbol_active ON positions (symbol, is_active)",
            "CREATE INDEX idx_positions_user_symbol ON positions (user_id, symbol)",
            "CREATE UNIQUE INDEX idx_positions_unique ON positions (user_id, symbol) WHERE is_active = true"
        ]
        
        # Analytics optimizations
        optimizations['analytics_queries'] = [
            "CREATE INDEX idx_trades_timestamp ON trades (executed_at DESC)",
            "CREATE INDEX idx_trades_symbol_timestamp ON trades (symbol, executed_at DESC)",
            "CREATE MATERIALIZED VIEW daily_pnl AS SELECT date, symbol, SUM(pnl) FROM trades GROUP BY date, symbol"
        ]
        
        return optimizations
    
    async def monitor_query_performance(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """Monitor query performance over a specified duration."""
        logger.info(f"Starting query performance monitoring for {duration_minutes} minutes...")
        
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        performance_data = {
            'postgresql': [],
            'redis': [],
            'mongodb': []
        }
        
        while datetime.utcnow() < end_time:
            try:
                # Monitor PostgreSQL
                if self.pg_pool:
                    pg_analysis = await self.analyze_postgresql_queries()
                    performance_data['postgresql'].extend(pg_analysis)
                
                # Monitor Redis
                if self.redis_client:
                    redis_analysis = await self.analyze_redis_operations()
                    performance_data['redis'].append(redis_analysis)
                
                # Wait before next monitoring cycle
                await asyncio.sleep(60)  # Monitor every minute
                
            except Exception as e:
                logger.error(f"Error during query performance monitoring: {e}")
        
        # Generate summary report
        summary = self._generate_monitoring_summary(performance_data)
        
        logger.info("Query performance monitoring completed")
        return summary
    
    def _generate_monitoring_summary(self, performance_data: Dict) -> Dict[str, Any]:
        """Generate summary of monitoring results."""
        summary = {
            'monitoring_duration': len(performance_data['postgresql']),
            'slow_queries_detected': 0,
            'optimization_opportunities': [],
            'overall_health': 'good'
        }
        
        # Analyze PostgreSQL data
        pg_data = performance_data['postgresql']
        if pg_data:
            slow_queries = [q for q in pg_data if q.avg_execution_time > self.performance_thresholds['slow']]
            summary['slow_queries_detected'] = len(slow_queries)
            
            if slow_queries:
                summary['optimization_opportunities'].extend([
                    f"Optimize slow query: {q.query_text[:50]}..." for q in slow_queries[:3]
                ])
        
        # Analyze Redis data
        redis_data = performance_data['redis']
        if redis_data:
            for data in redis_data:
                if data.get('connection_info', {}).get('hit_rate', 100) < 90:
                    summary['optimization_opportunities'].append("Improve Redis cache hit rate")
        
        # Determine overall health
        if summary['slow_queries_detected'] > 10:
            summary['overall_health'] = 'poor'
        elif summary['slow_queries_detected'] > 5:
            summary['overall_health'] = 'fair'
        
        return summary
