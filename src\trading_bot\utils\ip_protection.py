"""
Advanced IP Protection Configuration
Protects against rate limiting and IP blocking when using Yahoo Finance
"""

import time
import random
from datetime import datetime, timedelta
from typing import List, Optional
import asyncio
import logging

logger = logging.getLogger(__name__)


class AdvancedIPProtection:
    """Advanced protection strategies for Yahoo Finance API usage."""
    
    def __init__(self, config: Optional[dict] = None):
        self.config = config or {}
        
        # Time-based restrictions
        self.MARKET_HOURS_ONLY = self.config.get('market_hours_only', True)
        self.WEEKEND_ENABLED = self.config.get('weekend_enabled', False)
        self.NIGHT_MODE = self.config.get('night_mode', True)
        
        # Geographic distribution
        self.USE_PROXIES = self.config.get('use_proxies', False)
        self.PROXY_LIST = self.config.get('proxy_list', [])
        
        # Request patterns
        self.HUMAN_BEHAVIOR = self.config.get('human_behavior', True)
        self.RANDOM_SYMBOLS = self.config.get('random_symbols', True)
        
        # Rate limiting
        self.BASE_DELAY = self.config.get('base_delay', 2.0)
        self.MAX_DELAY = self.config.get('max_delay', 10.0)
        self.BURST_PROTECTION = self.config.get('burst_protection', True)
        
        # Request tracking
        self.request_history = []
        self.last_request_time = {}
        
    def is_safe_time(self) -> bool:
        """Check if it's safe to make requests."""
        now = datetime.now()
        
        # Check weekend
        if not self.WEEKEND_ENABLED and now.weekday() >= 5:
            logger.info("🚫 Weekend - Skipping to protect IP")
            return False
        
        # Check market hours (9:30 AM - 4:00 PM ET)
        if self.MARKET_HOURS_ONLY:
            # Convert to ET (simplified - doesn't handle DST)
            et_hour = (now.hour - 5) % 24  # Rough ET conversion
            
            if not (9 <= et_hour <= 16):
                logger.info("🌙 Outside market hours - Limited requests only")
                return True  # Still safe but with limitations
        
        return True
    
    def get_safe_delay(self, symbol: Optional[str] = None) -> float:
        """Get delay based on time of day and request history."""
        hour = datetime.now().hour
        
        # Base delay calculation
        if self.NIGHT_MODE and (hour < 6 or hour > 22):
            # Longer delays at night
            base_delay = random.uniform(5, 10)
        elif 9 <= hour <= 16:
            # Normal delays during market hours
            base_delay = random.uniform(2, 5)
        else:
            # Medium delays outside market
            base_delay = random.uniform(3, 7)
        
        # Burst protection - increase delay if too many recent requests
        if self.BURST_PROTECTION:
            recent_requests = self._count_recent_requests(60)  # Last minute
            if recent_requests > 10:
                burst_multiplier = min(recent_requests / 10, 3)  # Max 3x delay
                base_delay *= burst_multiplier
                logger.warning(f"Burst protection: {recent_requests} requests in last minute, delay increased to {base_delay:.1f}s")
        
        # Symbol-specific delay (if same symbol requested recently)
        if symbol and symbol in self.last_request_time:
            time_since_last = time.time() - self.last_request_time[symbol]
            if time_since_last < self.BASE_DELAY:
                additional_delay = self.BASE_DELAY - time_since_last
                base_delay += additional_delay
        
        return min(base_delay, self.MAX_DELAY)
    
    async def mimic_human_behavior(self):
        """Add human-like patterns to requests."""
        if not self.HUMAN_BEHAVIOR:
            return
        
        # Random longer pauses (coffee break, lunch, etc.)
        if random.random() < 0.1:  # 10% chance
            pause = random.uniform(30, 120)  # 30s to 2min pause
            logger.info(f"☕ Taking a human-like break ({pause:.0f}s)...")
            await asyncio.sleep(pause)
        
        # Vary request patterns
        if random.random() < 0.05:  # 5% chance
            logger.info("📱 Simulating distraction...")
            await asyncio.sleep(random.uniform(10, 30))
        
        # Occasional "typing" delays
        if random.random() < 0.2:  # 20% chance
            typing_delay = random.uniform(0.5, 2.0)
            await asyncio.sleep(typing_delay)
    
    def randomize_symbol_order(self, symbols: List[str]) -> List[str]:
        """Randomize symbol order to avoid predictable patterns."""
        if not self.RANDOM_SYMBOLS:
            return symbols
        
        randomized = symbols.copy()
        random.shuffle(randomized)
        return randomized
    
    async def safe_request_wrapper(self, request_func, symbol: str, *args, **kwargs):
        """Wrapper for safe API requests with protection."""
        try:
            # Check if it's safe time
            if not self.is_safe_time():
                logger.warning(f"Unsafe time for requests, skipping {symbol}")
                return None
            
            # Get safe delay
            delay = self.get_safe_delay(symbol)
            
            # Apply delay
            if delay > 0:
                logger.debug(f"Applying {delay:.1f}s delay before requesting {symbol}")
                await asyncio.sleep(delay)
            
            # Mimic human behavior
            await self.mimic_human_behavior()
            
            # Record request
            self._record_request(symbol)
            
            # Make the actual request
            result = await request_func(*args, **kwargs)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in safe request for {symbol}: {e}")
            # Add extra delay on error to avoid hammering
            await asyncio.sleep(random.uniform(5, 15))
            return None
    
    def _record_request(self, symbol: str):
        """Record request for tracking."""
        now = time.time()
        self.request_history.append(now)
        self.last_request_time[symbol] = now
        
        # Clean old history (keep last hour)
        cutoff = now - 3600
        self.request_history = [t for t in self.request_history if t > cutoff]
    
    def _count_recent_requests(self, seconds: int) -> int:
        """Count requests in the last N seconds."""
        cutoff = time.time() - seconds
        return len([t for t in self.request_history if t > cutoff])
    
    def get_protection_stats(self) -> dict:
        """Get protection statistics."""
        now = time.time()
        return {
            'total_requests': len(self.request_history),
            'requests_last_minute': self._count_recent_requests(60),
            'requests_last_hour': self._count_recent_requests(3600),
            'unique_symbols_requested': len(self.last_request_time),
            'protection_active': True,
            'safe_time': self.is_safe_time()
        }
    
    def reset_protection(self):
        """Reset protection state (for testing or manual override)."""
        self.request_history.clear()
        self.last_request_time.clear()
        logger.info("IP protection state reset")


class SafeScheduler:
    """Safe scheduler for running trading bot at optimal times."""
    
    def __init__(self, protection: AdvancedIPProtection):
        self.protection = protection
        self.scheduled_tasks = []
    
    def schedule_safe_run(self, func, interval_minutes: int = 30):
        """Schedule function to run at safe intervals."""
        async def safe_runner():
            while True:
                try:
                    if self.protection.is_safe_time():
                        logger.info("Running scheduled task...")
                        await func()
                    else:
                        logger.info("Skipping scheduled task - unsafe time")
                    
                    # Wait for next interval
                    await asyncio.sleep(interval_minutes * 60)
                    
                except Exception as e:
                    logger.error(f"Error in scheduled task: {e}")
                    await asyncio.sleep(300)  # 5 minute delay on error
        
        task = asyncio.create_task(safe_runner())
        self.scheduled_tasks.append(task)
        return task
    
    async def stop_all_tasks(self):
        """Stop all scheduled tasks."""
        for task in self.scheduled_tasks:
            task.cancel()
        
        if self.scheduled_tasks:
            await asyncio.gather(*self.scheduled_tasks, return_exceptions=True)
        
        self.scheduled_tasks.clear()


# Example usage configuration
DEFAULT_PROTECTION_CONFIG = {
    'market_hours_only': True,
    'weekend_enabled': False,
    'night_mode': True,
    'human_behavior': True,
    'random_symbols': True,
    'base_delay': 2.0,
    'max_delay': 10.0,
    'burst_protection': True,
    'use_proxies': False,
    'proxy_list': []
}


def create_ip_protection(config: Optional[dict] = None) -> AdvancedIPProtection:
    """Create IP protection instance with default or custom config."""
    final_config = DEFAULT_PROTECTION_CONFIG.copy()
    if config:
        final_config.update(config)
    
    return AdvancedIPProtection(final_config)
