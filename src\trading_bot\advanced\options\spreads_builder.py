"""Complex options spreads builder and analyzer."""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from ...core.config import settings
from ...core.logger import get_logger
from .options_pricing import OptionsPricer, OptionContract, OptionType, Greeks

logger = get_logger(__name__)


class SpreadType(Enum):
    """Options spread types."""
    VERTICAL_CALL = "vertical_call"
    VERTICAL_PUT = "vertical_put"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    CALENDAR = "calendar"
    DIAGONAL = "diagonal"
    STRADDLE = "straddle"
    STRANGLE = "strangle"
    RISK_REVERSAL = "risk_reversal"


@dataclass
class SpreadLeg:
    """Individual leg of an options spread."""
    option: OptionContract
    quantity: int  # Positive for long, negative for short
    action: str  # 'buy' or 'sell'


@dataclass
class OptionsSpread:
    """Complete options spread definition."""
    spread_type: SpreadType
    underlying: str
    legs: List[SpreadLeg]
    net_premium: float  # Net cost/credit
    max_profit: float
    max_loss: float
    breakeven_points: List[float]
    probability_of_profit: float
    greeks: Greeks
    expiration: datetime
    description: str


@dataclass
class SpreadOpportunity:
    """Options spread trading opportunity."""
    spread: OptionsSpread
    signal_strength: float
    expected_return: float
    risk_reward_ratio: float
    confidence: float
    reasoning: List[str]
    market_outlook: str  # 'bullish', 'bearish', 'neutral'


class SpreadsBuilder:
    """Build and analyze complex options spreads."""
    
    def __init__(self):
        self.pricer = OptionsPricer()
        
    async def find_spread_opportunities(
        self,
        underlying: str,
        option_chain: List[OptionContract],
        spot_price: float,
        market_outlook: str = "neutral",
        max_risk: float = 1000.0
    ) -> List[SpreadOpportunity]:
        """
        Find optimal spread opportunities.
        
        Args:
            underlying: Underlying symbol
            option_chain: Available options
            spot_price: Current spot price
            market_outlook: 'bullish', 'bearish', 'neutral'
            max_risk: Maximum risk per trade
            
        Returns:
            List of spread opportunities
        """
        try:
            opportunities = []
            
            # Generate different spread types based on outlook
            if market_outlook == "bullish":
                spreads = [
                    *self._find_bull_call_spreads(option_chain, spot_price),
                    *self._find_bull_put_spreads(option_chain, spot_price),
                    *self._find_call_calendars(option_chain, spot_price)
                ]
            elif market_outlook == "bearish":
                spreads = [
                    *self._find_bear_call_spreads(option_chain, spot_price),
                    *self._find_bear_put_spreads(option_chain, spot_price),
                    *self._find_put_calendars(option_chain, spot_price)
                ]
            else:  # neutral
                spreads = [
                    *self._find_iron_condors(option_chain, spot_price),
                    *self._find_iron_butterflies(option_chain, spot_price),
                    *self._find_straddles(option_chain, spot_price),
                    *self._find_strangles(option_chain, spot_price)
                ]
            
            # Analyze each spread
            for spread in spreads:
                if abs(spread.max_loss) <= max_risk:
                    opportunity = self._analyze_spread_opportunity(spread, spot_price, market_outlook)
                    if opportunity.confidence > 0.5:
                        opportunities.append(opportunity)
            
            # Sort by expected return and confidence
            opportunities.sort(
                key=lambda x: x.expected_return * x.confidence,
                reverse=True
            )
            
            return opportunities[:10]  # Top 10 opportunities
            
        except Exception as e:
            logger.error(f"Error finding spread opportunities: {e}")
            return []
    
    def _find_bull_call_spreads(
        self,
        option_chain: List[OptionContract],
        spot_price: float
    ) -> List[OptionsSpread]:
        """Find bull call spread opportunities."""
        spreads = []
        
        try:
            # Get call options
            calls = [opt for opt in option_chain if opt.option_type == OptionType.CALL]
            
            # Group by expiration
            expirations = list(set(opt.expiration for opt in calls))
            
            for expiration in expirations:
                exp_calls = [opt for opt in calls if opt.expiration == expiration]
                exp_calls.sort(key=lambda x: x.strike)
                
                # Find ITM and OTM strikes
                for i in range(len(exp_calls) - 1):
                    long_call = exp_calls[i]
                    short_call = exp_calls[i + 1]
                    
                    # Skip if strikes are too close
                    if short_call.strike - long_call.strike < spot_price * 0.02:
                        continue
                    
                    # Create spread
                    spread = self._create_bull_call_spread(long_call, short_call, spot_price)
                    if spread:
                        spreads.append(spread)
            
            return spreads
            
        except Exception as e:
            logger.error(f"Error finding bull call spreads: {e}")
            return []
    
    def _find_iron_condors(
        self,
        option_chain: List[OptionContract],
        spot_price: float
    ) -> List[OptionsSpread]:
        """Find iron condor opportunities."""
        spreads = []
        
        try:
            # Group by expiration
            expirations = list(set(opt.expiration for opt in option_chain))
            
            for expiration in expirations:
                exp_options = [opt for opt in option_chain if opt.expiration == expiration]
                calls = [opt for opt in exp_options if opt.option_type == OptionType.CALL]
                puts = [opt for opt in exp_options if opt.option_type == OptionType.PUT]
                
                calls.sort(key=lambda x: x.strike)
                puts.sort(key=lambda x: x.strike)
                
                # Find suitable strikes for iron condor
                # Structure: Sell put, buy lower put, sell call, buy higher call
                
                for put_short in puts:
                    if put_short.strike >= spot_price * 0.95:  # Skip ITM puts
                        continue
                        
                    for put_long in puts:
                        if put_long.strike >= put_short.strike:
                            continue
                            
                        for call_short in calls:
                            if call_short.strike <= spot_price * 1.05:  # Skip ITM calls
                                continue
                                
                            for call_long in calls:
                                if call_long.strike <= call_short.strike:
                                    continue
                                
                                # Create iron condor
                                spread = self._create_iron_condor(
                                    put_long, put_short, call_short, call_long, spot_price
                                )
                                if spread:
                                    spreads.append(spread)
            
            return spreads[:20]  # Limit to top 20
            
        except Exception as e:
            logger.error(f"Error finding iron condors: {e}")
            return []
    
    def _create_bull_call_spread(
        self,
        long_call: OptionContract,
        short_call: OptionContract,
        spot_price: float
    ) -> Optional[OptionsSpread]:
        """Create bull call spread."""
        try:
            # Calculate net premium (debit)
            long_premium = (long_call.bid + long_call.ask) / 2
            short_premium = (short_call.bid + short_call.ask) / 2
            net_premium = long_premium - short_premium
            
            # Calculate max profit/loss
            strike_diff = short_call.strike - long_call.strike
            max_profit = strike_diff - net_premium
            max_loss = net_premium
            
            # Breakeven
            breakeven = long_call.strike + net_premium
            
            # Calculate combined Greeks
            time_to_expiry = (long_call.expiration - datetime.now()).total_seconds() / (365.25 * 24 * 3600)
            
            long_greeks = self.pricer.calculate_greeks(
                spot_price, long_call.strike, time_to_expiry, 0.2, OptionType.CALL
            )
            short_greeks = self.pricer.calculate_greeks(
                spot_price, short_call.strike, time_to_expiry, 0.2, OptionType.CALL
            )
            
            combined_greeks = Greeks(
                delta=long_greeks.delta - short_greeks.delta,
                gamma=long_greeks.gamma - short_greeks.gamma,
                theta=long_greeks.theta - short_greeks.theta,
                vega=long_greeks.vega - short_greeks.vega,
                rho=long_greeks.rho - short_greeks.rho
            )
            
            # Create spread legs
            legs = [
                SpreadLeg(long_call, 1, 'buy'),
                SpreadLeg(short_call, -1, 'sell')
            ]
            
            return OptionsSpread(
                spread_type=SpreadType.VERTICAL_CALL,
                underlying=long_call.underlying,
                legs=legs,
                net_premium=net_premium,
                max_profit=max_profit,
                max_loss=max_loss,
                breakeven_points=[breakeven],
                probability_of_profit=self._calculate_pop(spot_price, [breakeven]),
                greeks=combined_greeks,
                expiration=long_call.expiration,
                description=f"Bull Call Spread {long_call.strike}/{short_call.strike}"
            )
            
        except Exception as e:
            logger.error(f"Error creating bull call spread: {e}")
            return None
    
    def _create_iron_condor(
        self,
        put_long: OptionContract,
        put_short: OptionContract,
        call_short: OptionContract,
        call_long: OptionContract,
        spot_price: float
    ) -> Optional[OptionsSpread]:
        """Create iron condor spread."""
        try:
            # Calculate net premium (credit)
            put_long_premium = (put_long.bid + put_long.ask) / 2
            put_short_premium = (put_short.bid + put_short.ask) / 2
            call_short_premium = (call_short.bid + call_short.ask) / 2
            call_long_premium = (call_long.bid + call_long.ask) / 2
            
            net_premium = put_short_premium + call_short_premium - put_long_premium - call_long_premium
            
            # Calculate max profit/loss
            put_width = put_short.strike - put_long.strike
            call_width = call_long.strike - call_short.strike
            max_width = max(put_width, call_width)
            
            max_profit = net_premium
            max_loss = max_width - net_premium
            
            # Breakeven points
            lower_breakeven = put_short.strike - net_premium
            upper_breakeven = call_short.strike + net_premium
            
            # Create spread legs
            legs = [
                SpreadLeg(put_long, 1, 'buy'),
                SpreadLeg(put_short, -1, 'sell'),
                SpreadLeg(call_short, -1, 'sell'),
                SpreadLeg(call_long, 1, 'buy')
            ]
            
            # Calculate combined Greeks (simplified)
            combined_greeks = Greeks(0, 0, 0, 0, 0)  # Would need detailed calculation
            
            return OptionsSpread(
                spread_type=SpreadType.IRON_CONDOR,
                underlying=put_long.underlying,
                legs=legs,
                net_premium=net_premium,
                max_profit=max_profit,
                max_loss=max_loss,
                breakeven_points=[lower_breakeven, upper_breakeven],
                probability_of_profit=self._calculate_pop(spot_price, [lower_breakeven, upper_breakeven]),
                greeks=combined_greeks,
                expiration=put_long.expiration,
                description=f"Iron Condor {put_long.strike}/{put_short.strike}/{call_short.strike}/{call_long.strike}"
            )
            
        except Exception as e:
            logger.error(f"Error creating iron condor: {e}")
            return None
    
    def _find_bear_call_spreads(self, option_chain: List[OptionContract], spot_price: float) -> List[OptionsSpread]:
        """Find bear call spread opportunities."""
        # Similar to bull call spreads but reversed
        return []
    
    def _find_bull_put_spreads(self, option_chain: List[OptionContract], spot_price: float) -> List[OptionsSpread]:
        """Find bull put spread opportunities."""
        return []
    
    def _find_bear_put_spreads(self, option_chain: List[OptionContract], spot_price: float) -> List[OptionsSpread]:
        """Find bear put spread opportunities."""
        return []
    
    def _find_call_calendars(self, option_chain: List[OptionContract], spot_price: float) -> List[OptionsSpread]:
        """Find call calendar spread opportunities."""
        return []
    
    def _find_put_calendars(self, option_chain: List[OptionContract], spot_price: float) -> List[OptionsSpread]:
        """Find put calendar spread opportunities."""
        return []
    
    def _find_iron_butterflies(self, option_chain: List[OptionContract], spot_price: float) -> List[OptionsSpread]:
        """Find iron butterfly opportunities."""
        return []
    
    def _find_straddles(self, option_chain: List[OptionContract], spot_price: float) -> List[OptionsSpread]:
        """Find straddle opportunities."""
        return []
    
    def _find_strangles(self, option_chain: List[OptionContract], spot_price: float) -> List[OptionsSpread]:
        """Find strangle opportunities."""
        return []
    
    def _analyze_spread_opportunity(
        self,
        spread: OptionsSpread,
        spot_price: float,
        market_outlook: str
    ) -> SpreadOpportunity:
        """Analyze spread opportunity."""
        try:
            # Calculate expected return
            expected_return = spread.max_profit / abs(spread.max_loss) if spread.max_loss != 0 else 0
            
            # Calculate risk-reward ratio
            risk_reward_ratio = abs(spread.max_profit / spread.max_loss) if spread.max_loss != 0 else 0
            
            # Calculate signal strength based on various factors
            signal_strength = min(spread.probability_of_profit * risk_reward_ratio, 1.0)
            
            # Calculate confidence
            confidence = spread.probability_of_profit * 0.7 + (risk_reward_ratio / 3) * 0.3
            confidence = min(confidence, 1.0)
            
            # Generate reasoning
            reasoning = [
                f"Max profit: ${spread.max_profit:.2f}",
                f"Max loss: ${abs(spread.max_loss):.2f}",
                f"Risk/Reward: {risk_reward_ratio:.2f}",
                f"Probability of profit: {spread.probability_of_profit:.1%}"
            ]
            
            return SpreadOpportunity(
                spread=spread,
                signal_strength=signal_strength,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward_ratio,
                confidence=confidence,
                reasoning=reasoning,
                market_outlook=market_outlook
            )
            
        except Exception as e:
            logger.error(f"Error analyzing spread opportunity: {e}")
            return self._empty_opportunity(spread, market_outlook)
    
    def _calculate_pop(self, spot_price: float, breakeven_points: List[float]) -> float:
        """Calculate probability of profit (simplified)."""
        try:
            if len(breakeven_points) == 1:
                # Single breakeven (e.g., vertical spread)
                distance = abs(breakeven_points[0] - spot_price) / spot_price
                return max(0.3, 1 - distance * 2)  # Simplified calculation
            
            elif len(breakeven_points) == 2:
                # Two breakevens (e.g., iron condor)
                lower, upper = sorted(breakeven_points)
                if lower <= spot_price <= upper:
                    return 0.7  # In profit zone
                else:
                    distance = min(abs(spot_price - lower), abs(spot_price - upper)) / spot_price
                    return max(0.2, 0.7 - distance * 2)
            
            return 0.5  # Default
            
        except Exception as e:
            logger.error(f"Error calculating POP: {e}")
            return 0.5
    
    def _empty_opportunity(self, spread: OptionsSpread, market_outlook: str) -> SpreadOpportunity:
        """Return empty spread opportunity."""
        return SpreadOpportunity(
            spread=spread,
            signal_strength=0.0,
            expected_return=0.0,
            risk_reward_ratio=0.0,
            confidence=0.0,
            reasoning=[],
            market_outlook=market_outlook
        )
