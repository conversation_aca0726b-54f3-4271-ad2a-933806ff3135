# src/trading_bot/automation/webull_data_extractor.py
"""
Enhanced Webull data extraction with better element detection
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd

logger = logging.getLogger(__name__)

class WebullDataExtractor:
    """Enhanced data extraction for Webull's current UI"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def initialize_driver(self, headless=False):
        """Initialize Chrome driver with updated settings"""
        try:
            options = uc.ChromeOptions()
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            if headless:
                options.add_argument('--headless')
                
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--start-maximized')
            
            self.driver = uc.Chrome(options=options)
            self.wait = WebDriverWait(self.driver, 10)
            
            # Set additional stealth properties
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize driver: {e}")
            return False
    
    def extract_stock_data(self, symbol: str) -> Dict:
        """Extract stock data with multiple fallback methods"""
        try:
            # Navigate to stock page
            url = f"https://app.webull.com/stocks/{symbol}"
            self.driver.get(url)
            
            # Wait for page to load
            time.sleep(3)
            
            data = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'price': None,
                'change': None,
                'volume': None,
                'market_cap': None,
                'pe_ratio': None,
                'bid': None,
                'ask': None
            }
            
            # Method 1: Try specific selectors for Webull's current UI
            price_selectors = [
                "div[class*='stock-price'] span",
                "span[class*='last-price']",
                "div[class*='quote-price']",
                "//div[contains(@class, 'price')]//span[contains(text(), '$')]",
                "//span[contains(@class, 'stock') and contains(@class, 'price')]"
            ]
            
            for selector in price_selectors:
                try:
                    if selector.startswith('//'):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    price_text = element.text.strip()
                    if '$' in price_text:
                        data['price'] = float(price_text.replace('$', '').replace(',', ''))
                        break
                except:
                    continue
            
            # Method 2: Extract from page text using patterns
            if not data['price']:
                page_text = self.driver.find_element(By.TAG_NAME, 'body').text
                import re
                
                # Look for price patterns
                price_pattern = r'\$(\d+(?:\.\d{2})?)'
                prices = re.findall(price_pattern, page_text)
                if prices:
                    # Take the first large number as likely stock price
                    for price in prices:
                        if float(price) > 1:  # Likely a stock price, not percentage
                            data['price'] = float(price)
                            break
            
            # Extract volume
            volume_patterns = [
                r'Volume[:\s]+([0-9,]+(?:\.[0-9]+)?[MKB]?)',
                r'Vol[:\s]+([0-9,]+(?:\.[0-9]+)?[MKB]?)'
            ]
            
            for pattern in volume_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    volume_str = match.group(1)
                    data['volume'] = self._parse_volume(volume_str)
                    break
            
            return data
            
        except Exception as e:
            logger.error(f"Failed to extract data for {symbol}: {e}")
            return {'symbol': symbol, 'error': str(e)}
    
    def _parse_volume(self, volume_str: str) -> int:
        """Parse volume string with K/M/B suffixes"""
        volume_str = volume_str.replace(',', '')
        
        multipliers = {
            'K': 1_000,
            'M': 1_000_000,
            'B': 1_000_000_000
        }
        
        for suffix, multiplier in multipliers.items():
            if suffix in volume_str.upper():
                number = float(volume_str.upper().replace(suffix, ''))
                return int(number * multiplier)
        
        return int(float(volume_str))
    
    def login_with_credentials(self, username: str, password: str) -> bool:
        """Login to Webull with better element detection"""
        try:
            # Navigate to login
            self.driver.get("https://app.webull.com/login")
            time.sleep(3)
            
            # Find login form elements with multiple strategies
            email_selectors = [
                "input[type='email']",
                "input[placeholder*='Email']",
                "input[name='email']",
                "input[id*='email']",
                "//input[@type='email' or contains(@placeholder, 'Email')]"
            ]
            
            email_input = None
            for selector in email_selectors:
                try:
                    if selector.startswith('//'):
                        email_input = self.wait.until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                    else:
                        email_input = self.wait.until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                    break
                except:
                    continue
            
            if not email_input:
                logger.error("Could not find email input field")
                return False
            
            # Enter credentials
            email_input.clear()
            email_input.send_keys(username)
            time.sleep(1)
            
            # Find password field
            password_input = self.driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            password_input.clear()
            password_input.send_keys(password)
            time.sleep(1)
            
            # Find and click login button
            login_button_selectors = [
                "button[type='submit']",
                "button:contains('Log In')",
                "button:contains('Sign In')",
                "//button[contains(text(), 'Log') or contains(text(), 'Sign')]"
            ]
            
            for selector in login_button_selectors:
                try:
                    if selector.startswith('//'):
                        button = self.driver.find_element(By.XPATH, selector)
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    button.click()
                    break
                except:
                    continue
            
            # Wait for login to complete
            time.sleep(5)
            
            # Check if logged in
            return self._check_logged_in()
            
        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False
    
    def _check_logged_in(self) -> bool:
        """Check if successfully logged in"""
        logged_in_indicators = [
            "Portfolio",
            "Positions",
            "Orders",
            "Account"
        ]
        
        page_text = self.driver.find_element(By.TAG_NAME, 'body').text
        
        for indicator in logged_in_indicators:
            if indicator in page_text:
                return True
                
        return False
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass

# Test the enhanced extractor
def test_enhanced_extraction():
    """Test the enhanced data extraction"""
    import time
    
    extractor = WebullDataExtractor()
    
    try:
        print("🔍 Testing Enhanced Webull Data Extraction")
        print("=" * 50)
        
        if not extractor.initialize_driver(headless=False):
            print("❌ Failed to initialize driver")
            return
        
        print("✅ Driver initialized")
        
        # Test stock data extraction
        symbols = ['AAPL', 'MSFT', 'GOOGL']
        
        for symbol in symbols:
            print(f"\n📊 Extracting data for {symbol}...")
            data = extractor.extract_stock_data(symbol)
            
            print(f"   Symbol: {data.get('symbol')}")
            print(f"   Price: ${data.get('price', 'N/A')}")
            print(f"   Volume: {data.get('volume', 'N/A')}")
            
            time.sleep(2)  # Avoid rapid requests
        
        print("\n✅ Data extraction test complete!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    finally:
        extractor.cleanup()

if __name__ == "__main__":
    test_enhanced_extraction()