#!/usr/bin/env python3
"""
Fixed ML-Enhanced Paper Trading System
- Removes delisted stocks
- Relaxes ML filtering  
- Better error handling
"""
import asyncio
import sys
import os
import joblib
import pandas as pd
import numpy as np
from datetime import datetime

# Import our standalone modules
from test_volatility_hunter import VolatilityHunter, VolatilityOpportunity
from test_paper_trading_standalone import PaperTradingEngine

class FixedVolatilityHunter(VolatilityHunter):
    """
    Fixed volatility hunter with clean symbol list and relaxed ML filtering
    """
    def __init__(self):
        super().__init__()
        # Updated watchlist - removed delisted stocks
        self.volatility_watchlist = [
            # Leveraged ETFs (these are reliable)
            'TQQQ', 'SQQQ', 'UVXY', 'SOXL', 'SOXS',
            'LABU', 'LABD', 'TNA', 'TZA', 'UPRO', 'SPXU',
            # Meme stocks (still active)
            'GME', 'AMC', 'BB', 'NOK',
            # Crypto-related (active)
            'RIOT', 'MARA', 'COIN', 'MSTR',
            # Biotech volatility (active)
            'MRNA', 'BNTX', 'NVAX'
            # Removed: MULN, FFIE, NKLA, RIDE, WKHS (delisted/problematic)
        ]
        
        self.models = {}
        self.feature_names = []
        self.model_dir = "models/volatility"
        self._load_models()
    
    def _load_models(self):
        """Load trained ML models"""
        try:
            if os.path.exists(f"{self.model_dir}/random_forest.pkl"):
                self.models['random_forest'] = joblib.load(f"{self.model_dir}/random_forest.pkl")
                print("✅ Loaded Random Forest model")
            
            if os.path.exists(f"{self.model_dir}/feature_names.pkl"):
                self.feature_names = joblib.load(f"{self.model_dir}/feature_names.pkl")
                print(f"✅ Loaded {len(self.feature_names)} feature names")
                
        except Exception as e:
            print(f"⚠️ Error loading models: {e}")
    
    def _get_ml_prediction(self, symbol: str) -> float:
        """Get ML prediction for a symbol (simplified)"""
        if not self.models:
            return 0.5  # Default neutral prediction
        
        try:
            # For demo purposes, use simplified features
            # In production, would calculate real technical indicators
            features = {
                'returns_1d': np.random.normal(0, 0.02),
                'returns_5d': np.random.normal(0, 0.05),
                'gap': np.random.normal(0, 0.01),
                'gap_abs': abs(np.random.normal(0, 0.01)),
                'intraday_range': np.random.uniform(0.02, 0.08),
                'volume_ratio': np.random.uniform(0.8, 3.0),
                'volume_spike': np.random.choice([0, 1], p=[0.8, 0.2]),
                'rsi': np.random.uniform(20, 80),
                'rsi_oversold': np.random.choice([0, 1], p=[0.9, 0.1]),
                'bb_position': np.random.uniform(0, 1),
                'atr_ratio': np.random.uniform(0.01, 0.05),
                'volatility_20d': np.random.uniform(0.01, 0.04),
                'gap_fill_setup': np.random.choice([0, 1], p=[0.95, 0.05]),
                'oversold_setup': np.random.choice([0, 1], p=[0.95, 0.05])
            }
            
            # Create feature vector in correct order
            feature_vector = []
            for fname in self.feature_names:
                feature_vector.append(features.get(fname, 0))
            
            X = np.array(feature_vector).reshape(1, -1)
            
            # Get prediction from Random Forest
            if 'random_forest' in self.models:
                prediction = self.models['random_forest'].predict_proba(X)[0, 1]
                return prediction
            else:
                return 0.5
                
        except Exception as e:
            print(f"Error getting ML prediction for {symbol}: {e}")
            return 0.5
    
    def scan_for_opportunities(self, 
                             additional_symbols=None,
                             portfolio_value: float = 10000) -> list:
        """
        Enhanced scan with relaxed ML filtering
        """
        # Get base opportunities
        opportunities = super().scan_for_opportunities(additional_symbols, portfolio_value)
        
        if not self.models:
            print("⚠️ No ML models loaded, using base volatility hunter")
            return opportunities[:5]  # Return top 5 without ML
        
        # Enhance with ML predictions
        enhanced_opportunities = []
        
        for opp in opportunities:
            try:
                ml_confidence = self._get_ml_prediction(opp.symbol)
                
                # Combine rule-based confidence with ML (more weight on rules)
                combined_confidence = (opp.confidence * 0.8) + (ml_confidence * 0.2)
                
                # Update opportunity with combined confidence
                opp.confidence = combined_confidence
                opp.signals['ml_confidence'] = ml_confidence
                
                # RELAXED FILTER: Accept if either rule-based OR ML confidence is good
                if opp.confidence > 0.55 or ml_confidence > 0.6:
                    enhanced_opportunities.append(opp)
                    
            except Exception as e:
                print(f"Error enhancing {opp.symbol}: {e}")
                # If ML fails, keep the opportunity anyway
                enhanced_opportunities.append(opp)
        
        # Sort by combined confidence
        enhanced_opportunities.sort(key=lambda x: x.confidence, reverse=True)
        
        print(f"🤖 ML enhanced: {len(enhanced_opportunities)}/{len(opportunities)} opportunities passed filter")
        return enhanced_opportunities

class FixedPaperTradingEngine(PaperTradingEngine):
    """
    Fixed paper trading engine
    """
    def __init__(self, starting_capital: float = 10000):
        super().__init__(starting_capital)
        self.volatility_hunter = FixedVolatilityHunter()
        print("🔧 Fixed ML-Enhanced Paper Trading Engine initialized")

async def main():
    print("🔧 FIXED ML-Enhanced Volatility Trading System")
    print("="*60)
    
    # Step 1: Check ML model status
    if os.path.exists("models/volatility/random_forest.pkl"):
        print("✅ ML models found and will be used")
    else:
        print("⚠️ No ML models found - will use rule-based only")
    
    # Step 2: Start fixed paper trading
    print("\n💰 Starting fixed paper trading...")
    engine = FixedPaperTradingEngine(starting_capital=10000)
    await engine.run_paper_trading(max_iterations=3)
    
    # Step 3: Display results
    print("\n🎯 Fixed Trading Session Complete!")
    
    # Check for trades
    if os.path.exists("paper_trades.json"):
        import json
        try:
            with open("paper_trades.json", 'r') as f:
                data = json.load(f)
                closed_trades = data.get('closed_trades', [])
                print(f"\n📊 Results:")
                print(f"   Starting Capital: ${data.get('starting_capital', 10000):,.2f}")
                print(f"   Final Capital: ${data.get('capital', 10000):,.2f}")
                print(f"   Closed Trades: {len(closed_trades)}")
                if closed_trades:
                    wins = sum(1 for t in closed_trades if t['pnl'] > 0)
                    total = len(closed_trades)
                    win_rate = (wins / total) * 100 if total > 0 else 0
                    total_pnl = sum(t['pnl'] for t in closed_trades)
                    print(f"   Win Rate: {win_rate:.1f}% ({wins}/{total})")
                    print(f"   Total P&L: ${total_pnl:.2f}")
        except Exception as e:
            print(f"Error reading trade log: {e}")

if __name__ == "__main__":
    asyncio.run(main())