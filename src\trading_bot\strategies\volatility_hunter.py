"""
Volatility Hunter Strategy
Scans for stocks that move 5-10% daily for quick day trading profits
Implements gap fills, oversold bounces, and breakout pullbacks
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from ..core.config import settings
from ..core.logger import get_logger
from ..data.yahoo_data_source import YahooDataSource
from ..risk.position_sizing import PositionSizer
from ..ml.features.technical_features import TechnicalFeatures

logger = get_logger(__name__)

@dataclass
class VolatilityOpportunity:
    """Represents a high-volatility trading opportunity"""
    symbol: str
    strategy_type: str  # 'gap_fill', 'oversold_bounce', 'breakout_pullback'
    entry_price: float
    target_price: float
    stop_loss: float
    daily_volatility: float
    volume_ratio: float
    confidence: float
    kelly_fraction: float
    position_size: int
    expected_return: float
    risk_reward_ratio: float
    signals: Dict[str, float]
    timestamp: datetime


class VolatilityHunter:
    """
    High-Probability Volatility Trading Strategy
    Focus: Stocks that move 5-10% daily with predictable patterns
    """
    
    def __init__(self, data_source: YahooDataSource = None):
        self.data_source = data_source or YahooDataSource()
        self.position_sizer = PositionSizer()
        self.tech_features = TechnicalFeatures()
        
        # Target stocks for high volatility
        self.volatility_watchlist = [
            # Leveraged ETFs
            'TQQQ', 'SQQQ', 'UVXY', 'SVXY', 'SOXL', 'SOXS',
            'LABU', 'LABD', 'TNA', 'TZA', 'UPRO', 'SPXU',
            # Meme stocks
            'GME', 'AMC', 'BBBY', 'BB', 'NOK',
            # Penny stocks / High volatility
            'MULN', 'FFIE', 'NKLA', 'RIDE', 'WKHS',
            # Crypto-related
            'RIOT', 'MARA', 'COIN', 'MSTR',
            # Biotech volatility
            'MRNA', 'BNTX', 'NVAX', 'INO'
        ]
        
        # Strategy parameters
        self.min_daily_range = 0.05  # 5% minimum daily movement
        self.max_daily_range = 0.15  # 15% maximum (avoid pump and dumps)
        self.min_volume = 1_000_000  # Minimum daily volume
        self.gap_fill_probability = 0.80  # Historical 80% win rate
        
    async def scan_for_opportunities(self, 
                                   additional_symbols: List[str] = None,
                                   portfolio_value: float = 10000) -> List[VolatilityOpportunity]:
        """
        Scan for high-volatility trading opportunities across all strategies
        """
        opportunities = []
        
        # Combine watchlist with any additional symbols
        symbols = list(set(self.volatility_watchlist + (additional_symbols or [])))
        
        logger.info(f"🔍 Scanning {len(symbols)} symbols for volatility opportunities...")
        
        for symbol in symbols:
            try:
                # Get current quote and historical data
                quote = await self.data_source.get_realtime_quote(symbol)
                bars = await self.data_source.get_historical_bars(
                    symbol=symbol,
                    interval='1d',
                    period='1mo'
                )
                
                if not bars or len(bars) < 20:
                    continue
                
                # Convert to DataFrame
                df = pd.DataFrame([{
                    'timestamp': bar.timestamp,
                    'open': bar.open,
                    'high': bar.high,
                    'low': bar.low,
                    'close': bar.close,
                    'volume': bar.volume
                } for bar in bars])
                df.set_index('timestamp', inplace=True)
                
                # Calculate technical indicators
                df = self._calculate_indicators(df)
                
                # Check each strategy
                # 1. Gap Fill Strategy
                gap_opp = await self._check_gap_fill(symbol, quote, df, portfolio_value)
                if gap_opp:
                    opportunities.append(gap_opp)
                
                # 2. Oversold Bounce Strategy
                oversold_opp = await self._check_oversold_bounce(symbol, quote, df, portfolio_value)
                if oversold_opp:
                    opportunities.append(oversold_opp)
                
                # 3. Breakout Pullback Strategy
                breakout_opp = await self._check_breakout_pullback(symbol, quote, df, portfolio_value)
                if breakout_opp:
                    opportunities.append(breakout_opp)
                    
            except Exception as e:
                logger.error(f"Error scanning {symbol}: {e}")
                continue
        
        # Sort by expected return
        opportunities.sort(key=lambda x: x.expected_return, reverse=True)
        
        logger.info(f"✅ Found {len(opportunities)} volatility opportunities")
        return opportunities[:10]  # Return top 10 opportunities
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for analysis"""
        # Price movement
        df['daily_range'] = (df['high'] - df['low']) / df['open']
        df['daily_return'] = df['close'].pct_change()
        df['gap'] = df['open'] / df['close'].shift(1) - 1
        
        # Volume
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # RSI
        df['rsi'] = self._calculate_rsi(df['close'], 14)
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        df['bb_std'] = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (2 * df['bb_std'])
        df['bb_lower'] = df['bb_middle'] - (2 * df['bb_std'])
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # ATR for volatility
        df['atr'] = self._calculate_atr(df, 14)
        df['atr_ratio'] = df['atr'] / df['close']
        
        # Support/Resistance levels
        df['resistance'] = df['high'].rolling(20).max()
        df['support'] = df['low'].rolling(20).min()
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = abs(df['high'] - df['close'].shift())
        low_close = abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(period).mean()
    
    async def _check_gap_fill(self, symbol: str, quote, df: pd.DataFrame, 
                             portfolio_value: float) -> Optional[VolatilityOpportunity]:
        """
        Gap Fill Strategy - 80% win rate
        Look for morning gaps that tend to fill during the day
        """
        try:
            latest = df.iloc[-1]
            prev_close = df.iloc[-2]['close']
            
            # Check for significant gap
            gap_size = abs(latest['gap'])
            if gap_size < 0.02 or gap_size > 0.10:  # 2-10% gaps
                return None
            
            # Ensure good volume
            if latest['volume_ratio'] < 1.5:
                return None
            
            # Determine direction
            is_gap_up = latest['gap'] > 0
            
            # Entry at current price, target at gap fill
            entry_price = quote.price
            target_price = prev_close
            
            # Stop loss beyond the gap extreme
            if is_gap_up:
                stop_loss = latest['high'] * 1.01
                if entry_price >= target_price:  # Gap already filled
                    return None
            else:
                stop_loss = latest['low'] * 0.99
                if entry_price <= target_price:  # Gap already filled
                    return None
            
            # Calculate position size using Kelly Criterion
            win_rate = self.gap_fill_probability
            avg_win = abs(target_price - entry_price) / entry_price
            avg_loss = abs(stop_loss - entry_price) / entry_price
            
            kelly_fraction = self.position_sizer.calculate_kelly_position_size(
                symbol=symbol,
                entry_price=entry_price,
                stop_loss=stop_loss,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                safety_factor=0.25
            )
            
            # Calculate expected return
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss if avg_loss > 0 else 0
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='gap_fill',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=win_rate,
                kelly_fraction=kelly_fraction / portfolio_value,
                position_size=kelly_fraction,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals={
                    'gap_size': gap_size,
                    'volume_surge': latest['volume_ratio'],
                    'atr_ratio': latest['atr_ratio']
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error in gap fill check for {symbol}: {e}")
            return None
    
    async def _check_oversold_bounce(self, symbol: str, quote, df: pd.DataFrame,
                                   portfolio_value: float) -> Optional[VolatilityOpportunity]:
        """
        Oversold Bounce Strategy
        Look for RSI < 30 with high volume for bounce plays
        """
        try:
            latest = df.iloc[-1]
            
            # Check if oversold
            if latest['rsi'] > 35:  # Not oversold enough
                return None
            
            # Check for high volatility
            if latest['daily_range'] < self.min_daily_range:
                return None
            
            # Ensure good volume (capitulation volume)
            if latest['volume_ratio'] < 2.0:
                return None
            
            # Check if near support
            support_distance = (quote.price - latest['support']) / quote.price
            if support_distance > 0.02:  # More than 2% above support
                return None
            
            # Entry at current price
            entry_price = quote.price
            
            # Target at 20-period SMA or previous resistance
            target_price = min(latest['bb_middle'], latest['resistance'] * 0.98)
            
            # Stop loss below recent low
            stop_loss = latest['support'] * 0.98
            
            # Win rate based on RSI extremes
            win_rate = 0.65 + (30 - latest['rsi']) * 0.01  # Higher win rate for more oversold
            win_rate = min(win_rate, 0.75)
            
            avg_win = (target_price - entry_price) / entry_price
            avg_loss = (entry_price - stop_loss) / entry_price
            
            # Skip if risk/reward is poor
            if avg_win < avg_loss * 1.5:
                return None
            
            kelly_fraction = self.position_sizer.calculate_kelly_position_size(
                symbol=symbol,
                entry_price=entry_price,
                stop_loss=stop_loss,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                safety_factor=0.20  # More conservative for oversold bounces
            )
            
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss if avg_loss > 0 else 0
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='oversold_bounce',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=win_rate,
                kelly_fraction=kelly_fraction / portfolio_value,
                position_size=kelly_fraction,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals={
                    'rsi': latest['rsi'],
                    'bb_position': latest['bb_position'],
                    'volume_surge': latest['volume_ratio'],
                    'support_distance': support_distance
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error in oversold bounce check for {symbol}: {e}")
            return None
    
    async def _check_breakout_pullback(self, symbol: str, quote, df: pd.DataFrame,
                                     portfolio_value: float) -> Optional[VolatilityOpportunity]:
        """
        Breakout Pullback Strategy
        Look for pullbacks to breakout levels with momentum
        """
        try:
            latest = df.iloc[-1]
            prev_1 = df.iloc[-2]
            prev_2 = df.iloc[-3]
            
            # Check for recent breakout (within 3 days)
            recent_high = df['high'][-5:-2].max()
            breakout_level = df['resistance'][-10:-3].mean()
            
            # Confirm breakout happened
            if recent_high < breakout_level * 1.02:
                return None
            
            # Check for pullback to breakout level
            pullback_distance = abs(quote.price - breakout_level) / breakout_level
            if pullback_distance > 0.03:  # More than 3% away from breakout
                return None
            
            # Ensure still above breakout with momentum
            if quote.price < breakout_level:
                return None
            
            # Volume confirmation
            if latest['volume_ratio'] < 1.2:
                return None
            
            # Entry at current price
            entry_price = quote.price
            
            # Target at recent high or measured move
            measured_move = breakout_level + (breakout_level - latest['support'])
            target_price = min(measured_move, recent_high * 1.05)
            
            # Stop loss below breakout level
            stop_loss = breakout_level * 0.98
            
            # Win rate based on momentum strength
            momentum_score = (latest['rsi'] - 50) / 50  # 0 to 1 scale
            win_rate = 0.60 + momentum_score * 0.15
            win_rate = min(max(win_rate, 0.55), 0.75)
            
            avg_win = (target_price - entry_price) / entry_price
            avg_loss = (entry_price - stop_loss) / entry_price
            
            # Skip if risk/reward is poor
            if avg_win < avg_loss * 2:
                return None
            
            kelly_fraction = self.position_sizer.calculate_kelly_position_size(
                symbol=symbol,
                entry_price=entry_price,
                stop_loss=stop_loss,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                safety_factor=0.25
            )
            
            expected_return = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
            risk_reward = avg_win / avg_loss if avg_loss > 0 else 0
            
            return VolatilityOpportunity(
                symbol=symbol,
                strategy_type='breakout_pullback',
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                daily_volatility=latest['daily_range'],
                volume_ratio=latest['volume_ratio'],
                confidence=win_rate,
                kelly_fraction=kelly_fraction / portfolio_value,
                position_size=kelly_fraction,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward,
                signals={
                    'rsi': latest['rsi'],
                    'breakout_distance': pullback_distance,
                    'momentum_score': momentum_score,
                    'volume_confirmation': latest['volume_ratio']
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error in breakout pullback check for {symbol}: {e}")
            return None
    
    async def execute_opportunity(self, opportunity: VolatilityOpportunity) -> Dict:
        """
        Execute a trading opportunity with proper risk management
        """
        logger.info(f"🎯 Executing {opportunity.strategy_type} trade on {opportunity.symbol}")
        logger.info(f"   Entry: ${opportunity.entry_price:.2f}")
        logger.info(f"   Target: ${opportunity.target_price:.2f} ({opportunity.expected_return*100:.1f}% expected)")
        logger.info(f"   Stop: ${opportunity.stop_loss:.2f}")
        logger.info(f"   Position: {opportunity.position_size} shares")
        logger.info(f"   Risk/Reward: {opportunity.risk_reward_ratio:.2f}")
        
        # This would integrate with your execution system
        return {
            'symbol': opportunity.symbol,
            'strategy': opportunity.strategy_type,
            'position_size': opportunity.position_size,
            'entry_price': opportunity.entry_price,
            'target_price': opportunity.target_price,
            'stop_loss': opportunity.stop_loss,
            'timestamp': opportunity.timestamp
        }
    
    def get_daily_summary(self, opportunities: List[VolatilityOpportunity]) -> str:
        """Generate a summary of daily opportunities"""
        if not opportunities:
            return "No volatility opportunities found today."
        
        summary = f"\n📊 VOLATILITY HUNTER DAILY SUMMARY - {datetime.now().strftime('%Y-%m-%d')}\n"
        summary += "=" * 60 + "\n"
        
        # Group by strategy
        gap_fills = [o for o in opportunities if o.strategy_type == 'gap_fill']
        oversold = [o for o in opportunities if o.strategy_type == 'oversold_bounce']
        breakouts = [o for o in opportunities if o.strategy_type == 'breakout_pullback']
        
        summary += f"\n🎯 Total Opportunities: {len(opportunities)}\n"
        summary += f"   - Gap Fills: {len(gap_fills)}\n"
        summary += f"   - Oversold Bounces: {len(oversold)}\n"
        summary += f"   - Breakout Pullbacks: {len(breakouts)}\n"
        
        summary += "\n📈 TOP 5 OPPORTUNITIES:\n"
        for i, opp in enumerate(opportunities[:5], 1):
            summary += f"\n{i}. {opp.symbol} - {opp.strategy_type.upper()}\n"
            summary += f"   Expected Return: {opp.expected_return*100:.1f}%\n"
            summary += f"   Risk/Reward: {opp.risk_reward_ratio:.2f}\n"
            summary += f"   Confidence: {opp.confidence*100:.0f}%\n"
            summary += f"   Entry: ${opp.entry_price:.2f} | Target: ${opp.target_price:.2f}\n"
        
        # Calculate potential daily return
        total_expected = sum(o.expected_return * o.kelly_fraction for o in opportunities[:10])
        summary += f"\n💰 Potential Daily Return (10 trades): {total_expected*100:.1f}%\n"
        
        return summary


# Example usage function
async def run_volatility_hunter():
    """Example of running the volatility hunter"""
    hunter = VolatilityHunter()
    
    # Initialize data source
    await hunter.data_source.initialize()
    
    # Scan for opportunities
    opportunities = await hunter.scan_for_opportunities(portfolio_value=10000)
    
    # Print summary
    print(hunter.get_daily_summary(opportunities))
    
    # Execute top opportunity (if any)
    if opportunities:
        top_opp = opportunities[0]
        await hunter.execute_opportunity(top_opp)
    
    # Cleanup
    await hunter.data_source.cleanup()


if __name__ == "__main__":
    asyncio.run(run_volatility_hunter())